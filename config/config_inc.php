<?php
$g_hostname               = 'localhost';
$g_db_type                = 'mysqli';
$g_database_name          = 'psii';
$g_db_username            = 'phpad';
$g_db_password            = 'mar#4i5';

$g_db_table_prefix          = 'psii';
$g_db_table_plugin_prefix   = 'psii';

$g_default_timezone       = 'Europe/Berlin';

$g_crypto_master_salt     = 'UXKL8CinT5mbolOZuDo5ncq0cG+KTrGkOtbim6rllsU=';

$g_path                   = 'https://psii.mareval.net/';

////////////////////////////////////////////////////////////////////////////
// Attachments / File Uploads
////////////////////////////////////////////////////////////////////////////

$g_allow_file_upload = ON;
$g_file_upload_method = DISK; # DATEBASE or DISK
$g_absolute_path_default_upload_folder = '/var/www/html/psii/files/'; # used with DISK, must contain trailing \ or /.
$g_max_file_size = 5000000;
# Permission to folder
// root@ubuntu:/var/www/html/psii# chgrp www-data files/
// root@ubuntu:/var/www/html/psii# chown www-data files/
// root@ubuntu:/var/www/html/psii# chmod -R 777 files/

////////////////////////////////////////////////////////////////////////////
// Logging
////////////////////////////////////////////////////////////////////////////

$g_show_detailed_errors = ON;
$g_show_errors = ON;
$g_stop_on_errors = ON;
$g_display_errors = array(
	E_WARNING           => DISPLAY_ERROR_HALT,
	E_ALL               => DISPLAY_ERROR_INLINE,
);

// $g_log_level = LOG_ALL & ~LOG_DATABASE;
// $g_log_destination = 'page';

////////////////////////////////////////////////////////////////////////////
// Email Settings
////////////////////////////////////////////////////////////////////////////

/**
 * Select the method to mail by:
 * PHPMAILER_METHOD_MAIL - mail()
 * PHPMAILER_METHOD_SENDMAIL - sendmail
 * PHPMAILER_METHOD_SMTP - SMTP
 * @global integer $g_phpMailer_method
 */
$g_phpMailer_method = PHPMAILER_METHOD_SMTP;

/**
 * It is recommended to use a cronjob or a scheduler task to send emails. The
 * cronjob should typically run every 5 minutes.  If no cronjob is used,then
 * user will have to wait for emails to be sent after performing an action
 * which triggers notifications.  This slows user performance.
 * @global integer $g_email_send_using_cronjob
 */
$g_email_send_using_cronjob = OFF;

/**
 * Webmaster email address. This is shown publicly at the bottom of each page
 * and thus may be susceptible to being detected by spam email harvesters.
 * @global string $g_webmaster_email
 */
$g_webmaster_email = '<EMAIL>';

/**
 * the sender email, part of 'From: ' header in emails
 * @global string $g_from_email
 */
$g_from_email = '<EMAIL>';

/**
 * the sender name, part of 'From: ' header in emails
 * @global string $g_from_name
 */
$g_from_name = 'MARCOM | Mareval Construction Monitoring';

/**
 * the return address for bounced mail
 * @global string $g_return_path_email
 */
$g_return_path_email = '<EMAIL>';

/**
 * Remote SMTP Host(s)
 * Either a single hostname or multiple semicolon-delimited hostnames.
 * You can specify for each host a port other than the default, using format:
 * [hostname:port] (e.g. "smtp1.example.com:25;smtp2.example.com").
 * Hosts will be tried in the given order.
 * NOTE: This is only used with PHPMAILER_METHOD_SMTP.
 * @see $g_smtp_port
 * @global string $g_smtp_host
 */
$g_smtp_host = 'smtp.strato.de';

/**
 * Allow secure connection to the SMTP server
 * Valid values are '' (no encryption), 'ssl' or 'tls'
 * @global string $g_smtp_connection_mode
 */
$g_smtp_connection_mode = 'tls';

/**
 * Default SMTP port
 * Typical ports are 25 and 587.
 * This can be overridden individually for specific hosts.
 * @see $g_smtp_host
 * @global integer $g_smtp_port
 */
$g_smtp_port = 587; 

/**
 * SMTP Server Authentication user
 * NOTE: must be set to '' if the SMTP host does not require authentication.
 * @see $g_smtp_password
 * @global string $g_smtp_username
 */
$g_smtp_username = '<EMAIL>';

/**
 * SMTP Server Authentication password
 * Not used when $g_smtp_username = ''
 * @see $g_smtp_username
 * @global string $g_smtp_password
 */
$g_smtp_password = 'beqkym-pexgaz-8dehfA';

