/* ================================
   Filters (from ace.css) - Material 3 Updated
   ================================ */
.filter-box {
  padding: 0;
  background: transparent;
  border: 0;
  border-radius: var(--border-radius-xl);
  margin-bottom: var(--space-6);
  display: flex;
  flex-wrap: nowrap;
  gap: var(--space-4);
  align-items: center;
  box-shadow: none;
  overflow-x: auto;
}

.filter-box input,
.filter-box select,
.filter-box .form-control {
  border-radius: 0;
  min-width: 120px;
  background-color: var(--md-surface-container-lowest);
  border: 1px solid var(--md-outline-variant);
  color: var(--md-on-surface);
  padding: var(--space-3) var(--space-4);
}

.filter-box input:focus,
.filter-box select:focus,
.filter-box .form-control:focus {
  border-color: var(--md-primary-40);
  box-shadow: 0 0 0 2px rgba(103, 80, 164, 0.12);
  outline: none;
}

/* styles from the modern1 styles */
div.filter-box {
  border-radius: 0;
  background: var(--md-surface-container-low);
}

#filter {
  border: none;
  background-color: var(--md-surface-container-lowest);
  color: var(--md-on-surface);
}

div.widget-toolbox {
  background-color: transparent;
  color: var(--md-primary-10);
  padding: var(--space-2);
  border-radius: 0;
  border: 0;
}

/* Filter responsive layout */
.filter-box .btn-group {
  flex-shrink: 0;
  white-space: nowrap;
}

.filter-box input,
.filter-box select {
  min-width: 120px;
  flex-shrink: 1;
}

/* Keep filter buttons together on mobile */
@media (max-width: 768px) {
  .filter-box {
    padding: var(--space-4);
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--md-outline-variant) transparent;
  }
  
  .filter-box::-webkit-scrollbar {
    height: 4px;
  }
  
  .filter-box::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .filter-box::-webkit-scrollbar-thumb {
    background: var(--md-outline-variant);
    border-radius: 2px;
  }
  
  .filter-box .btn-group {
    min-width: fit-content;
  }
}

/* Collapsed filter bar layout */
#filter-bar-queries {
  display: flex !important;
  align-items: center;
  gap: var(--space-4);
  background: transparent;
  border: none;
  border-radius: 0;
}

#filter-bar-queries form {
  display: flex !important;
  align-items: center;
  gap: var(--space-3);
  flex: 1;
  margin: 0;
}

#filter-bar-queries input,
#filter-bar-queries select,
#filter-bar-queries .form-control {
  flex: 1;
  min-width: 120px;
  margin: 0;
  background-color: var(--md-surface-container-lowest);
  border: 1px solid var(--md-outline-variant);
  border-radius: 0;
  color: var(--md-on-surface);
  padding: var(--space-3) var(--space-4);
}

#filter-bar-queries .btn-group {
  flex-shrink: 0;
  white-space: nowrap;
}

#filter-bar-queries .btn {
  margin: 0;
}

/* Responsive collapsed filter bar */
@media (max-width: 768px) {
  #filter-bar-queries {
    padding: var(--space-2) var(--space-3);
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--md-outline-variant) transparent;
  }
  
  #filter-bar-queries::-webkit-scrollbar {
    height: 4px;
  }
  
  #filter-bar-queries::-webkit-scrollbar-track {
    background: transparent;
  }
  
  #filter-bar-queries::-webkit-scrollbar-thumb {
    background: var(--md-outline-variant);
    border-radius: 2px;
  }
  
  #filter-bar-queries form {
    min-width: fit-content;
  }
}