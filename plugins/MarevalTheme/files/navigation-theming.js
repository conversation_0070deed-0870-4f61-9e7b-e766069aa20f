/**
 * Navigation Theming Script for MarevalTheme
 * Automatically applies project-state colors to navigation items
 * based on page content and URL patterns
 */

(function() {
    'use strict';

    // Project state mapping based on your requirements
    const PROJECT_STATE_MAPPING = {
        // My View - Project State 0 (Blue)
        'my_view_page.php': 0,
        
        // Documents - Project State 35 (Green)
        'view_all_bug_page.php?project_id=266': 35,
        'bug_report_page.php?project_id=266': 35,
        
        // Comments - Project State 45 (Yellow/Gold)
        'view_all_bug_page.php?project_id=267': 45,
        'bug_report_page.php?project_id=267': 45,
        
        // Meetings - Project State 55 (Purple)
        'bug_report_page.php?project_id=278': 55,
        'view_all_bug_page.php?project_id=278': 55,
        
        // Minutes of Meeting - Project State 65 (Red/Orange)
        'view_all_bug_page.php?project_id=279': 65,
        'bug_report_page.php?project_id=279': 65,
        
        // Reports - Project State 0 (Blue)
        'MarevalReporting/report_all_bug_page.php': 0,
        
        // Planning - Project State 0 (Blue)
        'roadmap_page.php': 0,
        
        // Summary - Project State 0 (Blue)
        'summary_page.php': 0,
        
        // Manage - Project State 0 (Blue)
        'manage_overview_page.php': 0
    };

    // Navigation type mapping for text-based detection
    const NAV_TYPE_MAPPING = {
        'documents': ['document', 'view documents', 'report documents'],
        'comments': ['comment', 'view comments', 'report comments'],
        'meetings': ['meeting', 'meetings'],
        'minutes': ['minutes', 'minutes of meeting']
    };

    /**
     * Detect current page project state from multiple sources
     */
    function detectCurrentProjectState() {
        const currentUrl = window.location.href;
        const currentPage = window.location.pathname;
        const pageContent = document.body.textContent.toLowerCase();
        const pageTitle = document.title.toLowerCase();

        // Enhanced keyword detection with more variations
        const DETECTION_PATTERNS = {
            55: [ // Purple - Meetings
                'viewing meetings', 'meetings', 'meeting', 'besprechung', 'besprechungen',
                'termin', 'termine', 'konferenz', 'sitzung'
            ],
            35: [ // Green - Documents
                'viewing documents', 'documents', 'document', 'dokument', 'dokumente',
                'datei', 'dateien', 'file', 'files', 'anhang', 'anhänge'
            ],
            45: [ // Yellow/Gold - Comments
                'viewing comments', 'comments', 'comment', 'kommentar', 'kommentare',
                'bemerkung', 'bemerkungen', 'notiz', 'notizen'
            ],
            65: [ // Red/Orange - Minutes
                'minutes', 'protokoll', 'protokolle', 'meeting minutes', 'sitzungsprotokoll',
                'besprechungsprotokoll', 'minutes of meeting'
            ]
        };

        // 1. Check URL for project_id parameter and cross-reference with content
        const projectIdMatch = currentUrl.match(/project_id=(\d+)/);
        if (projectIdMatch) {
            const projectId = projectIdMatch[1];

            // Try to determine project type from page content and title
            for (const [state, keywords] of Object.entries(DETECTION_PATTERNS)) {
                if (keywords.some(keyword =>
                    pageContent.includes(keyword) ||
                    pageTitle.includes(keyword)
                )) {
                    console.log(`MarevalTheme: Detected project state ${state} from content/title with project_id ${projectId}`);
                    return parseInt(state);
                }
            }
        }

        // 2. Check page content and title for specific keywords
        for (const [state, keywords] of Object.entries(DETECTION_PATTERNS)) {
            if (keywords.some(keyword =>
                pageContent.includes(keyword) ||
                pageTitle.includes(keyword)
            )) {
                console.log(`MarevalTheme: Detected project state ${state} from content/title keywords`);
                return parseInt(state);
            }
        }

        // 3. Check widget headers for context clues
        const widgetHeaders = document.querySelectorAll('.widget-header, .widget-title, .panel-heading, h1, h2, h3');
        for (const header of widgetHeaders) {
            const headerText = header.textContent.toLowerCase();
            for (const [state, keywords] of Object.entries(DETECTION_PATTERNS)) {
                if (keywords.some(keyword => headerText.includes(keyword))) {
                    console.log(`MarevalTheme: Detected project state ${state} from widget header: "${headerText}"`);
                    return parseInt(state);
                }
            }
        }

        // 4. Check active navigation item
        const activeNavItem = document.querySelector('#sidebar ul.nav.nav-list li.active a');
        if (activeNavItem) {
            const navText = activeNavItem.textContent.toLowerCase();
            const navHref = activeNavItem.getAttribute('href') || '';

            for (const [state, keywords] of Object.entries(DETECTION_PATTERNS)) {
                if (keywords.some(keyword => navText.includes(keyword))) {
                    console.log(`MarevalTheme: Detected project state ${state} from active navigation: "${navText}"`);
                    return parseInt(state);
                }
            }
        }

        // 5. Check for specific page patterns
        if (currentPage.includes('my_view')) {
            return 0; // Blue
        } else if (currentPage.includes('roadmap') || currentPage.includes('summary') || currentPage.includes('manage')) {
            return 0; // Blue
        }

        // 6. Check breadcrumbs for context
        const breadcrumbs = document.querySelectorAll('.breadcrumb, .breadcrumbs, nav[aria-label="breadcrumb"]');
        for (const breadcrumb of breadcrumbs) {
            const breadcrumbText = breadcrumb.textContent.toLowerCase();
            for (const [state, keywords] of Object.entries(DETECTION_PATTERNS)) {
                if (keywords.some(keyword => breadcrumbText.includes(keyword))) {
                    console.log(`MarevalTheme: Detected project state ${state} from breadcrumbs: "${breadcrumbText}"`);
                    return parseInt(state);
                }
            }
        }

        // 7. Fallback to default
        console.log('MarevalTheme: Using default project state 0 (Blue)');
        return 0; // Blue (default)
    }

    /**
     * Apply project state theming to navigation items
     */
    function applyNavigationTheming() {
        const sidebar = document.querySelector('#sidebar');
        if (!sidebar) return;

        const navItems = sidebar.querySelectorAll('ul.nav.nav-list li a');

        navItems.forEach(link => {
            const href = link.getAttribute('href') || '';
            const text = link.textContent.toLowerCase().trim();
            const listItem = link.closest('li');

            // Check for direct URL matches
            let projectState = null;
            let navType = null;

            // Check URL patterns
            for (const [urlPattern, state] of Object.entries(PROJECT_STATE_MAPPING)) {
                if (href.includes(urlPattern)) {
                    projectState = state;
                    break;
                }
            }

            // Check text content for navigation type
            for (const [type, keywords] of Object.entries(NAV_TYPE_MAPPING)) {
                if (keywords.some(keyword => text.includes(keyword))) {
                    navType = type;

                    // Map nav type to project state
                    switch (type) {
                        case 'documents':
                            projectState = projectState || 35;
                            break;
                        case 'comments':
                            projectState = projectState || 45;
                            break;
                        case 'meetings':
                            projectState = projectState || 55;
                            break;
                        case 'minutes':
                            projectState = projectState || 65;
                            break;
                    }
                    break;
                }
            }

            // Apply data attributes for CSS targeting
            if (projectState !== null) {
                link.setAttribute('data-project-state', projectState);
            }

            if (navType) {
                listItem.setAttribute('data-nav-type', navType);
                listItem.classList.add(`nav-${navType}`);
            }
        });
    }

    /**
     * Handle dynamic project ID detection
     * This function tries to detect the actual project IDs for each user
     */
    function detectDynamicProjectIds() {
        const navItems = document.querySelectorAll('#sidebar ul.nav.nav-list li a');
        const projectIdPattern = /project_id=(\d+)/;
        
        const detectedProjects = {
            documents: null,
            comments: null,
            meetings: null,
            minutes: null
        };
        
        navItems.forEach(link => {
            const href = link.getAttribute('href') || '';
            const text = link.textContent.toLowerCase().trim();
            const match = href.match(projectIdPattern);
            
            if (match) {
                const projectId = match[1];
                
                // Try to determine project type based on text content
                if (text.includes('document') && !detectedProjects.documents) {
                    detectedProjects.documents = projectId;
                } else if (text.includes('comment') && !detectedProjects.comments) {
                    detectedProjects.comments = projectId;
                } else if (text.includes('meeting') && !text.includes('minutes') && !detectedProjects.meetings) {
                    detectedProjects.meetings = projectId;
                } else if (text.includes('minutes') && !detectedProjects.minutes) {
                    detectedProjects.minutes = projectId;
                }
            }
        });
        
        return detectedProjects;
    }

    /**
     * Apply widget header theming based on current project state
     */
    function applyWidgetHeaderTheming() {
        const currentProjectState = detectCurrentProjectState();

        // Project state color mapping
        const PROJECT_STATE_COLORS = {
            0: '#3B82F6',   // Blue
            35: '#10B981',  // Green
            45: '#B68929',  // Yellow/Gold
            55: '#8B5CF6',  // Purple
            65: '#DC4C35'   // Red/Orange
        };

        const backgroundColor = PROJECT_STATE_COLORS[currentProjectState] || PROJECT_STATE_COLORS[0];

        // Find all widget headers
        const widgetHeaders = document.querySelectorAll('.widget-header, .widget-title, .panel-heading, .card-header, [class*="header"]');

        // Create dynamic CSS for widget headers
        const style = document.createElement('style');
        style.id = 'mareval-widget-header-theming';

        const css = `
            .widget-header,
            .widget-title,
            .panel-heading,
            .card-header,
            .widget-box .widget-header,
            .widget-container .widget-header,
            [class*="widget"] [class*="header"] {
                background-color: ${backgroundColor} !important;
                color: white !important;
                border-color: ${backgroundColor} !important;
            }

            /* Specific targeting for common MantisBT widget patterns */
            .widget-box > .widget-header,
            .widget-container > .widget-header,
            .box > .box-header,
            .panel > .panel-heading {
                background-color: ${backgroundColor} !important;
                color: white !important;
            }

            /* Target headers with specific text content */
            .widget-header:contains("Viewing"),
            .widget-header:contains("Report"),
            .widget-header:contains("Filter"),
            .widget-header:contains("Search") {
                background-color: ${backgroundColor} !important;
                color: white !important;
            }
        `;

        // Remove existing widget header styles
        const existingStyle = document.getElementById('mareval-widget-header-theming');
        if (existingStyle) {
            existingStyle.remove();
        }

        style.textContent = css;
        document.head.appendChild(style);

        // Also apply inline styles as fallback
        widgetHeaders.forEach(header => {
            header.style.backgroundColor = backgroundColor + ' !important';
            header.style.color = 'white !important';
            header.style.borderColor = backgroundColor + ' !important';
        });

        console.log(`MarevalTheme: Applied widget header theming - Project State: ${currentProjectState}, Color: ${backgroundColor}`);
    }

    /**
     * Apply theming based on detected project IDs
     */
    function applyDynamicTheming() {
        const detectedProjects = detectDynamicProjectIds();

        // Create dynamic CSS rules for detected project IDs
        const style = document.createElement('style');
        style.id = 'mareval-dynamic-navigation-theming';

        let css = '';

        if (detectedProjects.documents) {
            css += `
                #sidebar ul.nav.nav-list li a[href*="project_id=${detectedProjects.documents}"] {
                    border-color: #10B981 !important;
                    background-color: #10B981 !important;
                }
                #sidebar ul.nav.nav-list li:hover a[href*="project_id=${detectedProjects.documents}"] {
                    background-color: #059669 !important;
                    border-color: #059669 !important;
                }
                #sidebar ul.nav.nav-list li.active a[href*="project_id=${detectedProjects.documents}"] {
                    background-color: #047857 !important;
                    border-color: #047857 !important;
                }
            `;
        }
        
        if (detectedProjects.comments) {
            css += `
                #sidebar ul.nav.nav-list li a[href*="project_id=${detectedProjects.comments}"] {
                    border-color: #B68929 !important;
                    background-color: #B68929 !important;
                }
                #sidebar ul.nav.nav-list li:hover a[href*="project_id=${detectedProjects.comments}"] {
                    background-color: #A67C1B !important;
                    border-color: #A67C1B !important;
                }
                #sidebar ul.nav.nav-list li.active a[href*="project_id=${detectedProjects.comments}"] {
                    background-color: #92630F !important;
                    border-color: #92630F !important;
                }
            `;
        }
        
        if (detectedProjects.meetings) {
            css += `
                #sidebar ul.nav.nav-list li a[href*="project_id=${detectedProjects.meetings}"] {
                    border-color: #8B5CF6 !important;
                    background-color: #8B5CF6 !important;
                }
                #sidebar ul.nav.nav-list li:hover a[href*="project_id=${detectedProjects.meetings}"] {
                    background-color: #7C3AED !important;
                    border-color: #7C3AED !important;
                }
                #sidebar ul.nav.nav-list li.active a[href*="project_id=${detectedProjects.meetings}"] {
                    background-color: #6D28D9 !important;
                    border-color: #6D28D9 !important;
                }
            `;
        }
        
        if (detectedProjects.minutes) {
            css += `
                #sidebar ul.nav.nav-list li a[href*="project_id=${detectedProjects.minutes}"] {
                    border-color: #DC4C35 !important;
                    background-color: #DC4C35 !important;
                }
                #sidebar ul.nav.nav-list li:hover a[href*="project_id=${detectedProjects.minutes}"] {
                    background-color: #C53927 !important;
                    border-color: #C53927 !important;
                }
                #sidebar ul.nav.nav-list li.active a[href*="project_id=${detectedProjects.minutes}"] {
                    background-color: #B02A1A !important;
                    border-color: #B02A1A !important;
                }
            `;
        }
        
        style.textContent = css;
        
        // Remove existing dynamic styles
        const existingStyle = document.getElementById('mareval-dynamic-navigation-theming');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        // Add new styles
        document.head.appendChild(style);
    }

    /**
     * Debug function to help troubleshoot project state detection
     */
    function debugProjectStateDetection() {
        const currentUrl = window.location.href;
        const pageContent = document.body.textContent.toLowerCase();
        const pageTitle = document.title.toLowerCase();
        const detectedState = detectCurrentProjectState();

        console.group('MarevalTheme: Project State Detection Debug');
        console.log('Current URL:', currentUrl);
        console.log('Page Title:', document.title);
        console.log('Detected Project State:', detectedState);

        // Check for project_id in URL
        const projectIdMatch = currentUrl.match(/project_id=(\d+)/);
        if (projectIdMatch) {
            console.log('Found project_id in URL:', projectIdMatch[1]);
        }

        // Check widget headers
        const widgetHeaders = document.querySelectorAll('.widget-header, .widget-title, .panel-heading, h1, h2, h3');
        console.log('Widget headers found:', widgetHeaders.length);
        widgetHeaders.forEach((header, index) => {
            console.log(`Header ${index + 1}:`, header.textContent.trim());
        });

        // Check active navigation
        const activeNavItem = document.querySelector('#sidebar ul.nav.nav-list li.active a');
        if (activeNavItem) {
            console.log('Active navigation item:', activeNavItem.textContent.trim());
            console.log('Active navigation href:', activeNavItem.getAttribute('href'));
        }

        console.groupEnd();
    }

    /**
     * Initialize navigation theming
     */
    function initNavigationTheming() {
        // Apply static theming
        applyNavigationTheming();

        // Apply dynamic theming for project IDs
        applyDynamicTheming();

        // Apply widget header theming
        applyWidgetHeaderTheming();

        // Debug information
        debugProjectStateDetection();

        console.log('MarevalTheme: Navigation and widget header theming applied');
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initNavigationTheming);
    } else {
        initNavigationTheming();
    }

    // Re-apply theming when navigation changes (for AJAX updates)
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.target.closest('#sidebar')) {
                setTimeout(initNavigationTheming, 100);
            }
        });
    });

    // Start observing sidebar changes
    const sidebar = document.querySelector('#sidebar');
    if (sidebar) {
        observer.observe(sidebar, {
            childList: true,
            subtree: true
        });
    }

})();
