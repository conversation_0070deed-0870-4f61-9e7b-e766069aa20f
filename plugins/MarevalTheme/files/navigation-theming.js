/**
 * Navigation Theming Script for MarevalTheme
 * Automatically applies project-state colors to navigation items and widget headers
 *
 * CONFIGURATION INSTRUCTIONS:
 * 1. Find your project IDs by checking the URLs when you navigate to each section
 * 2. Edit the MANUAL_PROJECT_CONFIG below with your specific project IDs
 * 3. Set to null to use auto-detection (may be unreliable in old systems)
 *
 * EXAMPLE:
 * If your documents URL is: view_all_bug_page.php?project_id=123
 * Set: documents_project_id: '123'
 */

(function() {
    'use strict';

    // Manual project ID configuration - EDIT THESE VALUES FOR YOUR SYSTEM
    // Since project IDs are dynamic per user, you can manually set them here
    const MANUAL_PROJECT_CONFIG = {
        // Set these to null to use auto-detection, or set specific project IDs
        documents_project_id: null,    // Set to your documents project ID (e.g., '266')
        comments_project_id: null,     // Set to your comments project ID (e.g., '267')
        meetings_project_id: null,     // Set to your meetings project ID (e.g., '278')
        minutes_project_id: null       // Set to your minutes project ID (e.g., '279')
    };

    // Project state mapping based on your requirements
    const PROJECT_STATE_MAPPING = {
        // My View - Project State 0 (Blue)
        'my_view_page.php': 0,

        // Documents - Project State 35 (Green)
        'view_all_bug_page.php?project_id=266': 35,
        'bug_report_page.php?project_id=266': 35,

        // Comments - Project State 45 (Yellow/Gold)
        'view_all_bug_page.php?project_id=267': 45,
        'bug_report_page.php?project_id=267': 45,

        // Meetings - Project State 55 (Purple)
        'bug_report_page.php?project_id=278': 55,
        'view_all_bug_page.php?project_id=278': 55,

        // Minutes of Meeting - Project State 65 (Red/Orange)
        'view_all_bug_page.php?project_id=279': 65,
        'bug_report_page.php?project_id=279': 65,

        // Reports - Project State 0 (Blue)
        'MarevalReporting/report_all_bug_page.php': 0,

        // Planning - Project State 0 (Blue)
        'roadmap_page.php': 0,

        // Summary - Project State 0 (Blue)
        'summary_page.php': 0,

        // Manage - Project State 0 (Blue)
        'manage_overview_page.php': 0
    };

    // Navigation type mapping for text-based detection
    const NAV_TYPE_MAPPING = {
        'documents': ['document', 'view documents', 'report documents'],
        'comments': ['comment', 'view comments', 'report comments'],
        'meetings': ['meeting', 'meetings'],
        'minutes': ['minutes', 'minutes of meeting']
    };

    /**
     * Detect current page project state from multiple sources
     * Focus on URL patterns since content is unreliable in this old system
     */
    function detectCurrentProjectState() {
        const currentUrl = window.location.href;
        const currentPage = window.location.pathname;

        // 1. PRIORITY: Check for specific page patterns first (most reliable)
        if (currentPage.includes('my_view')) {
            console.log('MarevalTheme: Detected project state 0 from my_view page pattern');
            return 0; // Blue
        } else if (currentPage.includes('roadmap') || currentPage.includes('summary') || currentPage.includes('manage')) {
            console.log('MarevalTheme: Detected project state 0 from general page pattern');
            return 0; // Blue
        } else if (currentPage.includes('MarevalReporting')) {
            console.log('MarevalTheme: Detected project state 0 from reports page pattern');
            return 0; // Blue
        }

        // 2. Check URL for project_id parameter - this is the most reliable indicator
        const projectIdMatch = currentUrl.match(/project_id=(\d+)/);
        if (projectIdMatch) {
            const projectId = projectIdMatch[1];
            console.log(`MarevalTheme: Found project_id ${projectId} in URL`);

            // Use manual configuration first, then fall back to auto-detection
            if (MANUAL_PROJECT_CONFIG.documents_project_id && projectId === MANUAL_PROJECT_CONFIG.documents_project_id) {
                console.log(`MarevalTheme: Detected project state 35 (Documents) from manual config project_id ${projectId}`);
                return 35; // Green
            } else if (MANUAL_PROJECT_CONFIG.comments_project_id && projectId === MANUAL_PROJECT_CONFIG.comments_project_id) {
                console.log(`MarevalTheme: Detected project state 45 (Comments) from manual config project_id ${projectId}`);
                return 45; // Yellow/Gold
            } else if (MANUAL_PROJECT_CONFIG.meetings_project_id && projectId === MANUAL_PROJECT_CONFIG.meetings_project_id) {
                console.log(`MarevalTheme: Detected project state 55 (Meetings) from manual config project_id ${projectId}`);
                return 55; // Purple
            } else if (MANUAL_PROJECT_CONFIG.minutes_project_id && projectId === MANUAL_PROJECT_CONFIG.minutes_project_id) {
                console.log(`MarevalTheme: Detected project state 65 (Minutes) from manual config project_id ${projectId}`);
                return 65; // Red/Orange
            }

            // Fall back to auto-detection
            const detectedProjects = detectDynamicProjectIds();

            if (detectedProjects.documents && projectId === detectedProjects.documents) {
                console.log(`MarevalTheme: Detected project state 35 (Documents) from auto-detected project_id ${projectId}`);
                return 35; // Green
            } else if (detectedProjects.comments && projectId === detectedProjects.comments) {
                console.log(`MarevalTheme: Detected project state 45 (Comments) from auto-detected project_id ${projectId}`);
                return 45; // Yellow/Gold
            } else if (detectedProjects.meetings && projectId === detectedProjects.meetings) {
                console.log(`MarevalTheme: Detected project state 55 (Meetings) from auto-detected project_id ${projectId}`);
                return 55; // Purple
            } else if (detectedProjects.minutes && projectId === detectedProjects.minutes) {
                console.log(`MarevalTheme: Detected project state 65 (Minutes) from auto-detected project_id ${projectId}`);
                return 65; // Red/Orange
            }

            // Fallback: Try to guess from navigation context
            const activeNavItem = document.querySelector('#sidebar ul.nav.nav-list li.active a');
            if (activeNavItem) {
                const navText = activeNavItem.textContent.toLowerCase().trim();
                const navHref = activeNavItem.getAttribute('href') || '';

                // Check if the active nav item contains this project_id
                if (navHref.includes(`project_id=${projectId}`)) {
                    if (navText.includes('meeting') && !navText.includes('minutes')) {
                        console.log(`MarevalTheme: Detected project state 55 (Meetings) from active nav "${navText}" with project_id ${projectId}`);
                        return 55; // Purple
                    } else if (navText.includes('document')) {
                        console.log(`MarevalTheme: Detected project state 35 (Documents) from active nav "${navText}" with project_id ${projectId}`);
                        return 35; // Green
                    } else if (navText.includes('comment')) {
                        console.log(`MarevalTheme: Detected project state 45 (Comments) from active nav "${navText}" with project_id ${projectId}`);
                        return 45; // Yellow/Gold
                    } else if (navText.includes('minutes')) {
                        console.log(`MarevalTheme: Detected project state 65 (Minutes) from active nav "${navText}" with project_id ${projectId}`);
                        return 65; // Red/Orange
                    }
                }
            }

            // Last resort: Use your original static mapping as fallback
            if (projectId === '266') {
                console.log(`MarevalTheme: Detected project state 35 (Documents) from static mapping project_id ${projectId}`);
                return 35; // Green - Documents
            } else if (projectId === '267') {
                console.log(`MarevalTheme: Detected project state 45 (Comments) from static mapping project_id ${projectId}`);
                return 45; // Yellow/Gold - Comments
            } else if (projectId === '278') {
                console.log(`MarevalTheme: Detected project state 55 (Meetings) from static mapping project_id ${projectId}`);
                return 55; // Purple - Meetings
            } else if (projectId === '279') {
                console.log(`MarevalTheme: Detected project state 65 (Minutes) from static mapping project_id ${projectId}`);
                return 65; // Red/Orange - Minutes
            }
        }

        // 3. Fallback to default for any other case
        console.log('MarevalTheme: Using default project state 0 (Blue) - no project_id found or no match');
        return 0; // Blue (default)
    }

    /**
     * Apply project state theming to navigation items
     */
    function applyNavigationTheming() {
        const sidebar = document.querySelector('#sidebar');
        if (!sidebar) return;

        const navItems = sidebar.querySelectorAll('ul.nav.nav-list li a');

        navItems.forEach(link => {
            const href = link.getAttribute('href') || '';
            const text = link.textContent.toLowerCase().trim();
            const listItem = link.closest('li');

            // Check for direct URL matches
            let projectState = null;
            let navType = null;

            // Check URL patterns
            for (const [urlPattern, state] of Object.entries(PROJECT_STATE_MAPPING)) {
                if (href.includes(urlPattern)) {
                    projectState = state;
                    break;
                }
            }

            // Check text content for navigation type
            for (const [type, keywords] of Object.entries(NAV_TYPE_MAPPING)) {
                if (keywords.some(keyword => text.includes(keyword))) {
                    navType = type;

                    // Map nav type to project state
                    switch (type) {
                        case 'documents':
                            projectState = projectState || 35;
                            break;
                        case 'comments':
                            projectState = projectState || 45;
                            break;
                        case 'meetings':
                            projectState = projectState || 55;
                            break;
                        case 'minutes':
                            projectState = projectState || 65;
                            break;
                    }
                    break;
                }
            }

            // Apply data attributes for CSS targeting
            if (projectState !== null) {
                link.setAttribute('data-project-state', projectState);
            }

            if (navType) {
                listItem.setAttribute('data-nav-type', navType);
                listItem.classList.add(`nav-${navType}`);
            }
        });
    }

    /**
     * Handle dynamic project ID detection
     * This function tries to detect the actual project IDs for each user
     */
    function detectDynamicProjectIds() {
        const navItems = document.querySelectorAll('#sidebar ul.nav.nav-list li a');
        const projectIdPattern = /project_id=(\d+)/;
        
        const detectedProjects = {
            documents: null,
            comments: null,
            meetings: null,
            minutes: null
        };
        
        navItems.forEach(link => {
            const href = link.getAttribute('href') || '';
            const text = link.textContent.toLowerCase().trim();
            const match = href.match(projectIdPattern);
            
            if (match) {
                const projectId = match[1];
                
                // Try to determine project type based on text content
                if (text.includes('document') && !detectedProjects.documents) {
                    detectedProjects.documents = projectId;
                } else if (text.includes('comment') && !detectedProjects.comments) {
                    detectedProjects.comments = projectId;
                } else if (text.includes('meeting') && !text.includes('minutes') && !detectedProjects.meetings) {
                    detectedProjects.meetings = projectId;
                } else if (text.includes('minutes') && !detectedProjects.minutes) {
                    detectedProjects.minutes = projectId;
                }
            }
        });
        
        return detectedProjects;
    }

    /**
     * Apply widget header theming based on current project state
     */
    function applyWidgetHeaderTheming() {
        const currentProjectState = detectCurrentProjectState();

        // Project state color mapping
        const PROJECT_STATE_COLORS = {
            0: '#3B82F6',   // Blue
            35: '#10B981',  // Green
            45: '#B68929',  // Yellow/Gold
            55: '#8B5CF6',  // Purple
            65: '#DC4C35'   // Red/Orange
        };

        const backgroundColor = PROJECT_STATE_COLORS[currentProjectState] || PROJECT_STATE_COLORS[0];

        // Find all widget headers
        const widgetHeaders = document.querySelectorAll('.widget-header, .widget-title, .panel-heading, .card-header, [class*="header"]');

        // Create dynamic CSS for widget headers
        const style = document.createElement('style');
        style.id = 'mareval-widget-header-theming';

        const css = `
            .widget-header,
            .widget-title,
            .panel-heading,
            .card-header,
            .widget-box .widget-header,
            .widget-container .widget-header,
            [class*="widget"] [class*="header"] {
                background-color: ${backgroundColor} !important;
                color: white !important;
                border-color: ${backgroundColor} !important;
            }

            /* Specific targeting for common MantisBT widget patterns */
            .widget-box > .widget-header,
            .widget-container > .widget-header,
            .box > .box-header,
            .panel > .panel-heading {
                background-color: ${backgroundColor} !important;
                color: white !important;
            }

            /* Target headers with specific text content */
            .widget-header:contains("Viewing"),
            .widget-header:contains("Report"),
            .widget-header:contains("Filter"),
            .widget-header:contains("Search") {
                background-color: ${backgroundColor} !important;
                color: white !important;
            }
        `;

        // Remove existing widget header styles
        const existingStyle = document.getElementById('mareval-widget-header-theming');
        if (existingStyle) {
            existingStyle.remove();
        }

        style.textContent = css;
        document.head.appendChild(style);

        // Also apply inline styles as fallback
        widgetHeaders.forEach(header => {
            header.style.backgroundColor = backgroundColor + ' !important';
            header.style.color = 'white !important';
            header.style.borderColor = backgroundColor + ' !important';
        });

        console.log(`MarevalTheme: Applied widget header theming - Project State: ${currentProjectState}, Color: ${backgroundColor}`);
    }

    /**
     * Apply theming based on detected project IDs
     */
    function applyDynamicTheming() {
        const detectedProjects = detectDynamicProjectIds();

        // Create dynamic CSS rules for detected project IDs
        const style = document.createElement('style');
        style.id = 'mareval-dynamic-navigation-theming';

        let css = '';

        if (detectedProjects.documents) {
            css += `
                #sidebar ul.nav.nav-list li a[href*="project_id=${detectedProjects.documents}"] {
                    border-color: #10B981 !important;
                    /* background-color: #10B981 !important; */
                }
                #sidebar ul.nav.nav-list li:hover a[href*="project_id=${detectedProjects.documents}"] {
                    border-color: #059669 !important;
                    /* background-color: #059669 !important; */
                }
                #sidebar ul.nav.nav-list li.active a[href*="project_id=${detectedProjects.documents}"] {
                    border-color: #047857 !important;
                    /* background-color: #047857 !important; */
                }
            `;
        }
        
        if (detectedProjects.comments) {
            css += `
                #sidebar ul.nav.nav-list li a[href*="project_id=${detectedProjects.comments}"] {
                    border-color: #B68929 !important;
                    /* background-color: #B68929 !important; */
                }
                #sidebar ul.nav.nav-list li:hover a[href*="project_id=${detectedProjects.comments}"] {
                    border-color: #A67C1B !important;
                    /* background-color: #A67C1B !important; */
                }
                #sidebar ul.nav.nav-list li.active a[href*="project_id=${detectedProjects.comments}"] {
                    border-color: #92630F !important;
                    /* background-color: #92630F !important; */
                }
            `;
        }
        
        if (detectedProjects.meetings) {
            css += `
                #sidebar ul.nav.nav-list li a[href*="project_id=${detectedProjects.meetings}"] {
                    border-color: #8B5CF6 !important;
                    /* background-color: #8B5CF6 !important; */
                }
                #sidebar ul.nav.nav-list li:hover a[href*="project_id=${detectedProjects.meetings}"] {
                    border-color: #7C3AED !important;
                    /* background-color: #7C3AED !important; */
                }
                #sidebar ul.nav.nav-list li.active a[href*="project_id=${detectedProjects.meetings}"] {
                    border-color: #6D28D9 !important;
                   /* background-color: #6D28D9 !important; */
                }
            `;
        }
        
        if (detectedProjects.minutes) {
            css += `
                #sidebar ul.nav.nav-list li a[href*="project_id=${detectedProjects.minutes}"] {
                    border-color: #DC4C35 !important;
                    /* background-color: #DC4C35 !important; */
                }
                #sidebar ul.nav.nav-list li:hover a[href*="project_id=${detectedProjects.minutes}"] {
                    border-color: #C53927 !important;
                    /* background-color: #C53927 !important; */
                }
                #sidebar ul.nav.nav-list li.active a[href*="project_id=${detectedProjects.minutes}"] {
                    border-color: #B02A1A !important;
                    /* background-color: #B02A1A !important; */
                }
            `;
        }
        
        style.textContent = css;
        
        // Remove existing dynamic styles
        const existingStyle = document.getElementById('mareval-dynamic-navigation-theming');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        // Add new styles
        document.head.appendChild(style);
    }

    /**
     * Debug function to help troubleshoot project state detection
     */
    function debugProjectStateDetection() {
        const currentUrl = window.location.href;
        const currentPage = window.location.pathname;
        const detectedState = detectCurrentProjectState();
        const detectedProjects = detectDynamicProjectIds();

        console.group('MarevalTheme: Project State Detection Debug');
        console.log('Current URL:', currentUrl);
        console.log('Current Page:', currentPage);
        console.log('Page Title:', document.title);
        console.log('Detected Project State:', detectedState);

        // Check for project_id in URL
        const projectIdMatch = currentUrl.match(/project_id=(\d+)/);
        if (projectIdMatch) {
            console.log('Found project_id in URL:', projectIdMatch[1]);
        }

        // Show detected project mappings
        console.log('Detected Project Mappings:', detectedProjects);

        // Check active navigation
        const activeNavItem = document.querySelector('#sidebar ul.nav.nav-list li.active a');
        if (activeNavItem) {
            console.log('Active navigation item:', activeNavItem.textContent.trim());
            console.log('Active navigation href:', activeNavItem.getAttribute('href'));
        }

        // Show all navigation items with project IDs
        const navItemsWithProjectIds = document.querySelectorAll('#sidebar ul.nav.nav-list li a[href*="project_id="]');
        console.log('Navigation items with project_id:', navItemsWithProjectIds.length);
        navItemsWithProjectIds.forEach((item, index) => {
            const href = item.getAttribute('href');
            const text = item.textContent.trim();
            const projectIdMatch = href.match(/project_id=(\d+)/);
            if (projectIdMatch) {
                console.log(`Nav ${index + 1}: "${text}" -> project_id=${projectIdMatch[1]}`);
            }
        });

        console.groupEnd();
    }

    /**
     * Initialize navigation theming
     */
    function initNavigationTheming() {
        // Apply static theming
        applyNavigationTheming();

        // Apply dynamic theming for project IDs
        applyDynamicTheming();

        // Apply widget header theming
        applyWidgetHeaderTheming();

        // Debug information
        debugProjectStateDetection();

        console.log('MarevalTheme: Navigation and widget header theming applied');
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initNavigationTheming);
    } else {
        initNavigationTheming();
    }

    // Re-apply theming when navigation changes (for AJAX updates)
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.target.closest('#sidebar')) {
                setTimeout(initNavigationTheming, 100);
            }
        });
    });

    // Start observing sidebar changes
    const sidebar = document.querySelector('#sidebar');
    if (sidebar) {
        observer.observe(sidebar, {
            childList: true,
            subtree: true
        });
    }

})();
