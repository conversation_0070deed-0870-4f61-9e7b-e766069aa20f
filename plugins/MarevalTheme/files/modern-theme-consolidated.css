/* ================================
   CONSOLIDATED MAREVAL THEME
   Single file approach - replaces 11+ modular files
   Material Design 3 + Blue Primary Theme
   ================================ */

/* ================================
   MATERIAL DESIGN 3 VARIABLES
   ================================ */
:root {
    /* Blue Primary Palette (Target Color) */
    --md-primary-10: #001122;
    --md-primary-20: #1e3a5f;
    --md-primary-30: #2c5282;
    --md-primary-40: #3b82f6;  /* Main blue */
    --md-primary-50: #60a5fa;
    --md-primary-60: #93c5fd;
    --md-primary-70: #bfdbfe;
    --md-primary-80: #dbeafe;
    --md-primary-90: #eff6ff;
    --md-primary-95: #f8fafc;
    --md-primary-100: #ffffff;
    
    --md-on-primary: #ffffff;
    --md-on-primary-container: #001122;
    
    /* Surface Colors */
    --md-surface: #fefefe;
    --md-surface-variant: #f1f5f9;
    --md-surface-container-lowest: #ffffff;
    --md-surface-container-low: #f8fafc;
    --md-surface-container: #f1f5f9;
    --md-surface-container-high: #e2e8f0;
    --md-surface-container-highest: #cbd5e1;
    
    --md-on-surface: #0f172a;
    --md-on-surface-variant: #475569;
    
    /* Outline Colors */
    --md-outline: #64748b;
    --md-outline-variant: #cbd5e1;
    
    /* Success, Warning, Error */
    --md-success-40: #10b981;
    --md-success-90: #d1fae5;
    --md-success-10: #064e3b;
    --md-on-success: #ffffff;
    
    --md-warning-50: #f59e0b;
    --md-warning-40: #d97706;
    --md-warning-30: #b45309;
    --md-warning-10: #451a03;
    --md-warning-95: #fef3c7;
    
    --md-error-40: #dc2626;
    --md-error-30: #b91c1c;
    --md-error-20: #991b1b;
    --md-on-error: #ffffff;
    
    /* Typography */
    --md-font-family: "Roboto", "Google Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    --md-body-large: 16px;
    --md-body-medium: 14px;
    --md-label-large: 14px;
    --md-line-height-normal: 1.5;
    
    /* Spacing (Material 3 8px base) */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-6: 24px;
    --space-8: 32px;
    --space-16: 64px;
    
    /* Border Radius */
    --border-radius: 8px;
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-2xl: 16px;
    --border-radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Project State Colors - Simple approach */
    --link-color: var(--md-primary-40);
    --hover-color: var(--md-primary-30);
}

/* ================================
   GLOBAL RESET & TYPOGRAPHY
   ================================ */
body {
    font-family: var(--md-font-family);
    font-size: var(--md-body-large);
    line-height: var(--md-line-height-normal);
    color: var(--md-on-surface);
    background-color: var(--md-surface);
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--md-font-family);
    font-weight: 600;
    color: inherit;
    margin-bottom: var(--space-3);
}

/* ================================
   SIDEBAR NAVIGATION
   ================================ */
#sidebar {
    background-color: var(--md-primary-40);
    color: var(--md-on-primary);
    width: 280px;
    min-height: 100vh;
}

.sidebar .nav-list li a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    padding: var(--space-3);
    display: block;
    border-radius: var(--border-radius);
    margin: var(--space-1);
    transition: all var(--transition-fast);
}

.sidebar .nav-list li.active a {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
}

.sidebar .nav-list li:hover:not(.active) a {
    background-color: rgba(255, 255, 255, 0.08);
    color: white;
}

/* ================================
   MAIN CONTENT LAYOUT
   ================================ */
.main-content {
    margin-left: 280px;
    padding: var(--space-6);
    background-color: var(--md-surface);
    min-height: 100vh;
}

@media (max-width: 991px) {
    .main-content {
        margin-left: 0;
        max-width: 100vw;
    }
}

/* ================================
   BUTTONS
   ================================ */
.btn {
    font-family: var(--md-font-family);
    border-radius: var(--border-radius-md);
    padding: var(--space-3) var(--space-16);
    min-height: 40px;
    border: 1px solid transparent;
    font-size: var(--md-body-medium);
    font-weight: 500;
    transition: all var(--transition-fast);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    box-shadow: none;
    text-shadow: none;
    background-image: none;
}

/* Button Icons */
.btn .ace-icon,
.btn i {
    margin-right: 6px;
    display: inline;
    vertical-align: middle;
}

/* Primary Buttons */
.btn-primary {
    background-color: var(--md-primary-40);
    color: var(--md-on-primary);
    border-color: var(--md-primary-40);
}

.btn-primary:hover {
    background-color: var(--md-primary-30);
    color: var(--md-on-primary);
}

/* Success Buttons */
.btn-success {
    background-color: var(--md-success-40);
    color: white;
    border-color: var(--md-success-40);
}

.btn-success:hover {
    background-color: var(--md-success-30);
    color: white;
}

/* Warning Buttons */
.btn-warning {
    background-color: var(--md-warning-50);
    color: var(--md-warning-10);
    border-color: var(--md-warning-50);
}

.btn-warning:hover {
    background-color: var(--md-warning-40);
    color: var(--md-warning-10);
}

/* Danger Buttons */
.btn-danger {
    background-color: var(--md-error-40);
    color: white;
    border-color: var(--md-error-40);
}

.btn-danger:hover {
    background-color: var(--md-error-30);
    color: white;
}

/* Default Buttons */
.btn-default {
    background-color: var(--md-surface-container-low);
    color: var(--md-on-surface);
    border-color: var(--md-outline-variant);
}

.btn-default:hover {
    background-color: var(--md-surface-container);
    color: var(--md-on-surface);
}

/* ================================
   FORMS
   ================================ */
.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea {
    font-family: var(--md-font-family);
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--border-radius-md);
    padding: var(--space-3) var(--space-4);
    font-size: var(--md-body-medium);
    background-color: var(--md-surface-container-lowest);
    color: var(--md-on-surface);
    transition: all var(--transition-fast);
    min-height: 40px;
    box-shadow: none;
}

.form-control:focus,
input:focus,
textarea:focus {
    border-color: var(--md-primary-40);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.12);
    outline: none;
}

.form-control:hover,
input:hover,
textarea:hover {
    border-color: var(--md-outline);
}

/* Select Fields */
select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: var(--md-surface-container-lowest);
    background-image: url("data:image/svg+xml;charset=UTF-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><polyline points='6,9 12,15 18,9'></polyline></svg>");
    background-repeat: no-repeat;
    background-position: calc(100% - 12px) center;
    background-size: 16px;
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--border-radius-md);
    padding: var(--space-3) var(--space-4);
    padding-right: 40px;
    color: var(--md-on-surface);
    font-size: var(--md-body-medium);
    min-height: 40px;
    transition: all var(--transition-fast);
}

select:focus {
    border-color: var(--md-primary-40);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.12);
    outline: none;
}

select:hover {
    border-color: var(--md-outline);
}

/* ================================
   TABLES
   ================================ */
.table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--md-font-family);
    background-color: var(--md-surface-container-lowest);
    border-radius: var(--border-radius-md);
    overflow: hidden;
}

.table th,
.table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--md-outline-variant);
    color: var(--md-on-surface);
}

.table th {
    background-color: var(--md-primary-90);
    color: var(--md-primary-10);
    font-weight: 500;
    font-size: var(--md-label-large);
    border-bottom: 2px solid var(--md-primary-40);
}

.table th:hover {
    background-color: var(--md-primary-80);
}

.table tbody tr:hover {
    background-color: rgba(59, 130, 246, 0.08);
}

/* ================================
   WIDGETS
   ================================ */
.widget-box {
    background-color: var(--md-surface-container-lowest);
    border-radius: var(--border-radius-2xl);
    margin-bottom: var(--space-6);
    box-shadow: none;
    border: 1px solid var(--md-outline-variant);
    padding: var(--space-4);
}

.widget-header {
    background-color: var(--link-color);
    color: white;
    padding: var(--space-3) var(--space-4);
    margin: calc(-1 * var(--space-4)) calc(-1 * var(--space-4)) var(--space-4) calc(-1 * var(--space-4));
    border-radius: var(--border-radius-2xl) var(--border-radius-2xl) 0 0;
    font-weight: 600;
}

/* ================================
   LINKS
   ================================ */
a {
    color: var(--link-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--hover-color);
    text-decoration: underline;
}

/* Keep navigation links with inherited colors */
.nav a,
.navbar a,
.dropdown-menu a,
.nav-list a,
.menu a,
.sidebar a,
.breadcrumb a,
.pagination a {
    color: inherit;
}

/* ================================
   ALERTS & MODALS
   ================================ */
.alert {
    border-radius: var(--border-radius-md);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
    border: 1px solid transparent;
}

.alert-success {
    background-color: var(--md-success-90);
    color: var(--md-success-10);
    border-color: var(--md-success-40);
}

.alert-warning {
    background-color: var(--md-warning-95);
    color: var(--md-warning-10);
    border-color: var(--md-warning-50);
}

.alert-danger {
    background-color: var(--md-error-90);
    color: var(--md-error-10);
    border-color: var(--md-error-40);
}

.modal-content {
    border-radius: var(--border-radius-2xl);
    background-color: var(--md-surface-container-lowest);
    border: 1px solid var(--md-outline-variant);
}

/* ================================
   FILTERS
   ================================ */
.filter-box {
    background-color: var(--md-surface-container-low);
    border-radius: var(--border-radius-2xl);
    padding: var(--space-4);
    margin-bottom: var(--space-6);
    border: 1px solid var(--md-outline-variant);
}

/* ================================
   RESPONSIVE
   ================================ */
@media (max-width: 991px) {
    .main-content {
        margin-left: 0;
        max-width: 100vw;
    }
    
    #sidebar {
        width: 100%;
        position: fixed;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform var(--transition-fast);
    }
    
    #sidebar.open {
        transform: translateX(0);
    }
}

/* ================================
   DISABLE TIME PICKER DROPDOWNS
   ================================ */
ul[id*="autocomplete"],
ul[id*="field"][id*="values"],
ul.dropdown-menu.list.dropdown-yellow,
.dropdown-menu.auto-values {
    display: none !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

input[data-toggle="dropdown"],
input[aria-haspopup="true"],
input.dropdown-toggle {
    background-image: none !important;
    cursor: text !important;
}
