/* ================================
   MAREVAL THEME - DESKTOP CONSOLIDATED ROOT (2025)
   Material Design 3 + Project State Auto-Detection
   Desktop-Only Architecture (240px sidebar)
   ================================ */

/* ================================
   MATERIAL DESIGN 3 DESIGN TOKENS
   Complete system extracted from working modular files
   ================================ */
:root {
    /* Blue Primary Palette (Main Theme) */
    --md-primary-10: #001122;
    --md-primary-20: #1e3a5f;
    --md-primary-30: #2c5282;
    --md-primary-40: #3b82f6;  /* Main blue */
    --md-primary-50: #60a5fa;
    --md-primary-60: #93c5fd;
    --md-primary-70: #bfdbfe;
    --md-primary-80: #dbeafe;
    --md-primary-90: #eff6ff;
    --md-primary-95: #f8fafc;
    --md-primary-100: #ffffff;

    --md-on-primary: #ffffff;
    --md-on-primary-container: #001122;

    /* Surface Colors */
    --md-surface: #fefefe;
    --md-surface-variant: #f1f5f9;
    --md-surface-container-lowest: #ffffff;
    --md-surface-container-low: #f8fafc;
    --md-surface-container: #f1f5f9;
    --md-surface-container-high: #e2e8f0;
    --md-surface-container-highest: #cbd5e1;

    --md-on-surface: #0f172a;
    --md-on-surface-variant: #475569;

    /* Outline Colors */
    --md-outline: #64748b;
    --md-outline-variant: #cbd5e1;

    /* Success, Warning, Error */
    --md-success-40: #10b981;
    --md-success-90: #d1fae5;
    --md-success-10: #064e3b;
    --md-on-success: #ffffff;

    --md-warning-50: #f59e0b;
    --md-warning-40: #d97706;
    --md-warning-30: #b45309;
    --md-warning-10: #451a03;
    --md-warning-95: #fef3c7;

    --md-error-40: #dc2626;
    --md-error-30: #b91c1c;
    --md-error-20: #991b1b;
    --md-on-error: #ffffff;

    /* Typography */
    --md-font-family: "Roboto", "Google Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    --md-headline-large: 32px;
    --md-headline-medium: 28px;
    --md-headline-small: 24px;
    --md-title-large: 22px;
    --md-title-medium: 16px;
    --md-body-large: 16px;
    --md-body-medium: 14px;
    --md-label-large: 14px;
    --md-line-height-normal: 1.5;

    /* Spacing System (Material 3 8px base) */
    --space-0: 0px;
    --space-0-5: 2px;
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;
    --space-12: 48px;
    --space-16: 64px;
    --space-20: 80px;

    /* Border Radius */
    --border-radius-xs: 4px;
    --border-radius-sm: 6px;
    --border-radius: 8px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-2xl: 16px;
    --border-radius-3xl: 24px;
    --border-radius-full: 9999px;

    /* Elevation/Shadows */
    --md-elevation-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --md-elevation-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    --md-elevation-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-standard: 300ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Desktop Layout Constants */
    --sidebar-width: 240px;
    --navbar-height: 64px;

    /* Default Project State Colors (Blue) */
    --link-color: #3b82f6;
    --hover-color: #2563eb;
    --active-color: #1d4ed8;
    --bg-color: #c2e7ff;
}

/* ================================
   PROJECT STATE AUTO-DETECTION via CSS :has()
   Modern CSS approach for dynamic theming
   ================================ */

/* Project State 0 - Blue (Default/All Other pages) */
body:has([class*="project-state-0"]),
body {
    --link-color: #3b82f6;
    --hover-color: #2563eb;
    --active-color: #1d4ed8;
    --bg-color: #c2e7ff;
}

/* Project State 35 - Green (Document Verification) */
body:has([class*="project-state-35"]) {
    --link-color: #10b981;
    --hover-color: #059669;
    --active-color: #047857;
    --bg-color: #cfffe5;
}

/* Project State 45 - Yellow (Master Documents) */
body:has([class*="project-state-45"]) {
    --link-color: #f59e0b;
    --hover-color: #d97706;
    --active-color: #b45309;
    --bg-color: #fff1c2;
}

/* Project State 55 - Purple (Meetings) */
body:has([class*="project-state-55"]) {
    --link-color: #8b5cf6;
    --hover-color: #7c3aed;
    --active-color: #6d28d9;
    --bg-color: #e9d7ff;
}

/* Project State 65 - Red (Minutes of Meeting) */
body:has([class*="project-state-65"]) {
    --link-color: #ef4444;
    --hover-color: #dc2626;
    --active-color: #b91c1c;
    --bg-color: #ffd9d1;
}

/* ================================
   GLOBAL RESET & TYPOGRAPHY
   Desktop-optimized base styles
   ================================ */
html {
    min-height: 100%;
    position: relative;
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

body {
    background-color: var(--md-surface);
    min-height: 100%;
    font-family: var(--md-font-family);
    font-size: var(--md-body-large);
    font-weight: 400;
    color: var(--md-on-surface);
    line-height: var(--md-line-height-normal);
    font-feature-settings: "kern" 1, "liga" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    margin: 0;
    padding: 0;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--md-font-family);
    font-weight: 600;
    color: inherit;
    margin-bottom: var(--space-3);
    line-height: 1.2;
}

h1 { font-size: var(--md-headline-large); }
h2 { font-size: var(--md-headline-medium); }
h3 { font-size: var(--md-headline-small); }
h4 { font-size: var(--md-title-large); }
h5 { font-size: var(--md-title-medium); }
h6 { font-size: var(--md-body-large); }

/* ================================
   DESKTOP LAYOUT FOUNDATION
   240px sidebar + main content
   ================================ */
.main-container {
    max-width: 100%;
    overflow-x: hidden;
}

.main-content,
#main-content {
    margin-top: var(--navbar-height);
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
    padding: var(--space-4);
    min-height: calc(100vh - var(--navbar-height));
    box-sizing: border-box;
    position: relative;
    float: none;
    display: block;
}

.page-content {
    background-color: var(--md-surface);
    position: relative;
    margin: 0;
    padding: var(--space-2);
}

/* ================================
   SIDEBAR NAVIGATION (Priority #1)
   240px width with project state theming
   ================================ */
#sidebar,
.sidebar {
    position: fixed;
    top: 64px;
    left: 0;
    width: var(--sidebar-width);
    height: calc(100vh - 64px);
    background-color: var(--bg-color);
    border-right: 1px solid var(--link-color);
    padding: var(--space-4);
    overflow-y: auto;
    z-index: 1000;
    box-sizing: border-box;
    transition: background-color var(--transition-fast);
}

/* Navigation list styling */
#sidebar ul.nav.nav-list,
.sidebar ul.nav.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

#sidebar ul.nav.nav-list li,
.sidebar ul.nav.nav-list li {
    margin: 0;
    padding: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

#sidebar ul.nav.nav-list li a,
.sidebar ul.nav.nav-list li a {
    display: block;
    padding: var(--space-3) var(--space-4);
    background-color: var(--link-color);
    color: white;
    text-decoration: none;
    border: none;
    text-shadow: none;
    box-shadow: none;
    transition: all var(--transition-fast);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--space-1);
}

#sidebar ul.nav.nav-list li a:hover,
.sidebar ul.nav.nav-list li a:hover {
    background-color: var(--hover-color);
    color: white;
}

#sidebar ul.nav.nav-list li.active a,
.sidebar ul.nav.nav-list li.active a {
    background-color: var(--active-color);
    color: white;
}

#sidebar ul.nav.nav-list li a .ace-icon,
.sidebar ul.nav.nav-list li a .ace-icon {
    margin-right: var(--space-2);
    width: 20px;
    text-align: center;
    display: inline-block;
    vertical-align: middle;
}

/* Sidebar submenu styles */
.sidebar .submenu {
    background-color: rgba(0, 0, 0, 0.05);
    padding-left: var(--space-6);
}

.sidebar .submenu li a {
    padding: var(--space-2) var(--space-4);
    font-size: var(--md-body-medium);
}

/* ================================
   WIDGET SYSTEM (Priority #2)
   Material Design 3 cards with project state theming
   ================================ */
.widget-box {
    background-color: var(--md-surface-container-lowest);
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--border-radius-2xl);
    margin-bottom: var(--space-6);
    overflow: hidden;
    box-shadow: var(--md-elevation-1);
    max-width: 100%;
}

.widget-header {
    background-color: var(--bg-color);
    color: var(--md-on-surface);
    padding: var(--space-4);
    border-bottom: 1px solid var(--md-outline-variant);
    transition: background-color var(--transition-fast);
}

.widget-header h4,
.widget-header .widget-title {
    margin: 0;
    font-size: var(--md-title-medium);
    font-weight: 500;
    color: inherit;
}

.widget-header .widget-toolbar {
    float: right;
    margin-top: -2px;
}

.widget-header .widget-toolbar .btn {
    padding: var(--space-1) var(--space-2);
    font-size: var(--md-body-medium);
    border-radius: var(--border-radius-sm);
}

.widget-main,
.widget-body {
    padding: var(--space-4);
    background-color: var(--md-surface-container-lowest);
}

.widget-main .widget-main,
.widget-body .widget-body {
    padding: 0;
}

/* Widget variants */
.widget-box.transparent {
    background-color: transparent;
    border: none;
    box-shadow: none;
}

.widget-box.collapsed .widget-main,
.widget-box.collapsed .widget-body {
    display: none;
}

/* Enhanced widget styling for forms */
.widget-box .form-group {
    margin-bottom: var(--space-4);
}

.widget-box .form-group:last-child {
    margin-bottom: 0;
}

.widget-box label {
    display: block;
    margin-bottom: var(--space-2);
    font-weight: 500;
    color: var(--md-on-surface);
    font-size: var(--md-body-medium);
}

.widget-box .form-control,
.widget-box input,
.widget-box textarea,
.widget-box select {
    width: 100%;
    margin-bottom: 0;
}

/* File upload areas */
.widget-box .file-upload-area {
    border: 2px dashed var(--md-outline-variant);
    border-radius: var(--border-radius-md);
    padding: var(--space-8);
    text-align: center;
    background-color: var(--md-surface-variant);
    transition: all var(--transition-fast);
}

.widget-box .file-upload-area:hover {
    border-color: var(--link-color);
    background-color: var(--bg-color);
}

/* Radio button groups */
.widget-box .radio-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.widget-box .radio-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin-bottom: 0;
    cursor: pointer;
}

.widget-box .radio-group input[type="radio"] {
    margin-right: var(--space-2);
    margin-bottom: 0;
}

/* Checkbox groups */
.widget-box .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.widget-box .checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin-bottom: 0;
    cursor: pointer;
}

.widget-box .checkbox-group input[type="checkbox"] {
    margin-right: var(--space-2);
    margin-bottom: 0;
}

/* ================================
   TABLE SYSTEM (Priority #3)
   Material Design 3 data tables with project state theming
   ================================ */
table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--md-font-family);
    margin-bottom: var(--space-4);
}

table th {
    background-color: var(--link-color);
    color: white;
    padding: var(--space-3) var(--space-2);
    text-align: left;
    font-weight: 600;
    border: none;
    font-size: var(--md-body-medium);
}

table td {
    padding: var(--space-2);
    border-bottom: 1px solid var(--md-neutral-90);
    vertical-align: top;
    font-size: var(--md-body-medium);
}

table tr:nth-child(even) {
    background-color: var(--md-neutral-95);
}

table tr:hover {
    background-color: var(--bg-color);
}

/* Enhanced table styling */
.table {
    width: 100%;
    max-width: 100%;
    min-width: 100%;
    margin-bottom: var(--space-6);
    background-color: var(--md-surface-container-lowest);
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0;
    overflow: hidden;
    border: 1px solid var(--md-outline-variant);
}

.table th {
    background-color: var(--link-color);
    color: white;
    padding: var(--space-4) var(--space-5);
    text-align: left;
    font-weight: 500;
    font-size: var(--md-body-medium);
    border-bottom: 1px solid var(--md-outline-variant);
    transition: background-color var(--transition-fast);
}

.table th:hover {
    background-color: var(--hover-color);
}

.table td {
    padding: var(--space-4) var(--space-5);
    border-bottom: 1px solid var(--md-outline-variant);
    color: var(--md-on-surface);
    font-size: var(--md-body-small);
    vertical-align: middle;
    transition: background-color var(--transition-fast);
}

.table tbody tr:hover {
    background-color: var(--bg-color);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Table links with project state colors */
.table a {
    color: var(--link-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.table a:hover {
    color: var(--hover-color);
    text-decoration: underline;
}

.table a:active {
    color: var(--active-color);
}

/* Category column styling */
.table td.category {
    background-color: var(--md-surface-container);
    font-weight: 500;
    width: 150px;
    min-width: 150px;
}

/* Table responsive wrapper */
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: var(--space-4);
    box-shadow: none;
}

/* ================================
   BUTTONS & FORMS
   Essential Material Design 3 components with project state theming
   ================================ */
.btn,
button,
input[type="submit"],
input[type="button"] {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
    padding: 10px var(--space-4);
    border: none;
    border-radius: var(--border-radius-md);
    background-color: var(--link-color);
    color: white;
    font-family: var(--md-font-family);
    font-size: var(--md-label-large);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    box-sizing: border-box;
    gap: var(--space-2);
    white-space: nowrap;
    user-select: none;
    vertical-align: middle;
    outline: none;
    margin: 0;
    box-shadow: none;
}

.btn:hover,
button:hover,
input[type="submit"]:hover,
input[type="button"]:hover {
    background-color: var(--hover-color);
    color: white;
    text-decoration: none;
    box-shadow: none;
}

.btn:active,
button:active,
input[type="submit"]:active,
input[type="button"]:active {
    background-color: var(--active-color);
    box-shadow: none;
}

.btn .ace-icon,
button .ace-icon {
    margin-right: var(--space-2);
    display: inline-block;
    vertical-align: middle;
    width: 16px;
    text-align: center;
}

/* Button variants */
.btn-secondary {
    background-color: transparent;
    color: var(--link-color);
    border: 1px solid var(--md-outline);
}

.btn-secondary:hover {
    background-color: rgba(59, 130, 246, 0.08);
    border-color: var(--link-color);
    color: var(--link-color);
}

.btn-small,
.btn-sm {
    min-height: 32px;
    padding: var(--space-1) var(--space-3);
    font-size: var(--md-body-small);
}

/* Form controls with enhanced styling */
.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
textarea,
select {
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--border-radius-md);
    padding: var(--space-3) var(--space-4);
    font-size: var(--md-body-medium);
    font-family: var(--md-font-family);
    background-color: var(--md-surface-container-lowest);
    color: var(--md-on-surface);
    transition: all var(--transition-fast);
    min-height: 40px;
    box-sizing: border-box;
    width: 100%;
    box-shadow: none;
    background-image: none;
    filter: none;
}

.form-control:hover,
input:hover,
textarea:hover,
select:hover {
    border-color: var(--md-outline);
}

.form-control:focus,
input:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--link-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Search inputs */
.nav-search-input,
#filter-search-txt,
#filter-bar-search-txt,
.input-sm,
.input-xs,
.input-md {
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--border-radius-md);
    font-size: var(--md-body-medium);
    background-color: var(--md-surface-container-lowest);
    color: var(--md-on-surface);
    box-shadow: none;
    transition: all var(--transition-fast);
    min-height: 40px;
    height: 40px;
    width: 120px;
    padding: var(--space-2) var(--space-3);
}

/* Form groups and labels */
.form-group {
    margin-bottom: var(--space-4);
}

.form-group label {
    display: block;
    margin-bottom: var(--space-2);
    font-weight: 500;
    color: var(--md-on-surface);
    font-size: var(--md-body-medium);
}

/* Checkbox and radio styling */
input[type="checkbox"],
input[type="radio"] {
    width: auto;
    margin-right: var(--space-2);
    accent-color: var(--link-color);
}

/* ================================
   LINKS & NAVIGATION
   Project state color integration
   ================================ */
a {
    color: var(--link-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--hover-color);
    text-decoration: underline;
}

a:active {
    color: var(--active-color);
}

/* Navigation specific links */
.nav a,
.navbar a,
.menu a {
    color: inherit;
}

.nav a:hover,
.navbar a:hover,
.menu a:hover {
    color: inherit;
    text-decoration: none;
}

/* ================================
   ALERTS & NOTIFICATIONS
   Material Design 3 feedback components
   ================================ */
.alert {
    border-radius: var(--border-radius-md);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
    border: 1px solid transparent;
}

.alert-success {
    background-color: var(--md-success-90);
    color: var(--md-success-10);
    border-color: var(--md-success-40);
}

.alert-warning {
    background-color: var(--md-warning-95);
    color: var(--md-warning-10);
    border-color: var(--md-warning-50);
}

.alert-danger,
.alert-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--md-error-30);
    border-color: var(--md-error-40);
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--md-primary-30);
    border-color: var(--md-primary-40);
}

/* ================================
   UTILITY CLASSES
   Common helper classes
   ================================ */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }

/* ================================
   END OF CONSOLIDATED ROOT STYLES
   Total: ~620 lines of clean, desktop-focused CSS
   No mobile/responsive code, no conflicting overrides
   ================================ */
