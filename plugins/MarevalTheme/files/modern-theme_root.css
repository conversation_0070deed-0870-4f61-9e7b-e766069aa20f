/* ================================
   MAREVAL THEME - DESKTOP CONSOLIDATED ROOT (2025)
   Material Design 3 + Project State Auto-Detection
   Desktop-Only Architecture (240px sidebar)
   ================================ */

/* ================================
   MATERIAL DESIGN 3 DESIGN TOKENS
   Complete system extracted from working modular files
   ================================ */
:root {
    /* Blue Primary Palette (Main Theme) */
    --md-primary-10: #001122;
    --md-primary-20: #1e3a5f;
    --md-primary-30: #2c5282;
    --md-primary-40: #3b82f6;  /* Main blue */
    --md-primary-50: #60a5fa;
    --md-primary-60: #93c5fd;
    --md-primary-70: #bfdbfe;
    --md-primary-80: #dbeafe;
    --md-primary-90: #eff6ff;
    --md-primary-95: #f8fafc;
    --md-primary-100: #ffffff;

    --md-on-primary: #ffffff;
    --md-on-primary-container: #001122;

    /* Surface Colors */
    --md-surface: #fefefe;
    --md-surface-variant: #f1f5f9;
    --md-surface-container-lowest: #ffffff;
    --md-surface-container-low: #f8fafc;
    --md-surface-container: #f1f5f9;
    --md-surface-container-high: #e2e8f0;
    --md-surface-container-highest: #cbd5e1;

    --md-on-surface: #0f172a;
    --md-on-surface-variant: #475569;

    /* Outline Colors */
    --md-outline: #64748b;
    --md-outline-variant: #cbd5e1;

    /* Success, Warning, Error */
    --md-success-40: #10b981;
    --md-success-90: #d1fae5;
    --md-success-10: #064e3b;
    --md-on-success: #ffffff;

    --md-warning-50: #f59e0b;
    --md-warning-40: #d97706;
    --md-warning-30: #b45309;
    --md-warning-10: #451a03;
    --md-warning-95: #fef3c7;

    --md-error-40: #dc2626;
    --md-error-30: #b91c1c;
    --md-error-20: #991b1b;
    --md-on-error: #ffffff;

    /* Typography */
    --md-font-family: "Roboto", "Google Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    --md-headline-large: 32px;
    --md-headline-medium: 28px;
    --md-headline-small: 24px;
    --md-title-large: 22px;
    --md-title-medium: 16px;
    --md-body-large: 16px;
    --md-body-medium: 14px;
    --md-label-large: 14px;
    --md-line-height-normal: 1.5;

    /* Spacing System (Material 3 8px base) */
    --space-0: 0px;
    --space-0-5: 2px;
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;
    --space-12: 48px;
    --space-16: 64px;
    --space-20: 80px;

    /* Border Radius */
    --border-radius-xs: 4px;
    --border-radius-sm: 6px;
    --border-radius: 8px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-2xl: 16px;
    --border-radius-3xl: 24px;
    --border-radius-full: 9999px;

    /* Elevation/Shadows */
    --md-elevation-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --md-elevation-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
    --md-elevation-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-standard: 300ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Desktop Layout Constants */
    --sidebar-width: 240px;
    --navbar-height: 64px;

    /* Default Project State Colors (Blue) */
    --link-color: #3b82f6;
    --hover-color: #2563eb;
    --active-color: #1d4ed8;
    --bg-color: #c2e7ff;
}

/* ================================
   PROJECT STATE AUTO-DETECTION via CSS :has()
   Modern CSS approach for dynamic theming
   ================================ */

/* Project State 0 - Blue (Default/All Other pages) */
body:has([class*="project-state-0"]),
body {
    --link-color: #3b82f6;
    --hover-color: #2563eb;
    --active-color: #1d4ed8;
    --bg-color: #c2e7ff;
}

/* Project State 35 - Green (Document Verification) */
body:has([class*="project-state-35"]) {
    --link-color: #10b981;
    --hover-color: #059669;
    --active-color: #047857;
    --bg-color: #cfffe5;
}

/* Project State 45 - Yellow (Master Documents) */
body:has([class*="project-state-45"]) {
    --link-color: #f59e0b;
    --hover-color: #d97706;
    --active-color: #b45309;
    --bg-color: #fff1c2;
}

/* Project State 55 - Purple (Meetings) */
body:has([class*="project-state-55"]) {
    --link-color: #8b5cf6;
    --hover-color: #7c3aed;
    --active-color: #6d28d9;
    --bg-color: #e9d7ff;
}

/* Project State 65 - Red (Minutes of Meeting) */
body:has([class*="project-state-65"]) {
    --link-color: #ef4444;
    --hover-color: #dc2626;
    --active-color: #b91c1c;
    --bg-color: #ffd9d1;
}

/* ================================
   GLOBAL RESET & TYPOGRAPHY
   Desktop-optimized base styles
   ================================ */
html {
    min-height: 100%;
    position: relative;
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

body {
    background-color: var(--md-surface);
    min-height: 100%;
    font-family: var(--md-font-family);
    font-size: var(--md-body-large);
    font-weight: 400;
    color: var(--md-on-surface);
    line-height: var(--md-line-height-normal);
    font-feature-settings: "kern" 1, "liga" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    margin: 0;
    padding: 0;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--md-font-family);
    font-weight: 600;
    color: inherit;
    margin-bottom: var(--space-3);
    line-height: 1.2;
}

h1 { font-size: var(--md-headline-large); }
h2 { font-size: var(--md-headline-medium); }
h3 { font-size: var(--md-headline-small); }
h4 { font-size: var(--md-title-large); }
h5 { font-size: var(--md-title-medium); }
h6 { font-size: var(--md-body-large); }

/* ================================
   DESKTOP LAYOUT FOUNDATION
   240px sidebar + main content
   ================================ */
.main-container {
    max-width: 100%;
    overflow-x: hidden;
}

.main-content,
#main-content {
    margin-top: var(--navbar-height);
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
    padding: var(--space-4);
    min-height: calc(100vh - var(--navbar-height));
    box-sizing: border-box;
    position: relative;
    float: none;
    display: block;
}

.page-content {
    background-color: var(--md-surface);
    position: relative;
    margin: 0;
    padding: var(--space-2);
}

/* ================================
   SIDEBAR NAVIGATION (Priority #1)
   240px width with project state theming
   ================================ */
#sidebar,
.sidebar {
    background-color: var(--bg-color);
    color: var(--md-on-surface);
    width: var(--sidebar-width);
    min-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    overflow-y: auto;
    transition: background-color var(--transition-fast);
}

.sidebar .nav-list {
    padding: var(--space-2) 0;
    margin: 0;
    list-style: none;
}

.sidebar .nav-list li {
    margin: 0;
    padding: 0;
}

.sidebar .nav-list li a {
    display: block;
    padding: var(--space-3) var(--space-4);
    color: var(--link-color);
    text-decoration: none;
    transition: all var(--transition-fast);
    border-radius: 0;
}

.sidebar .nav-list li a:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--hover-color);
}

.sidebar .nav-list li a:active,
.sidebar .nav-list li a.active {
    background-color: rgba(0, 0, 0, 0.1);
    color: var(--active-color);
}

.sidebar .nav-list li a .ace-icon {
    margin-right: var(--space-2);
    width: 20px;
    text-align: center;
    display: inline-block;
}

/* Sidebar submenu styles */
.sidebar .submenu {
    background-color: rgba(0, 0, 0, 0.05);
    padding-left: var(--space-6);
}

.sidebar .submenu li a {
    padding: var(--space-2) var(--space-4);
    font-size: var(--md-body-medium);
}

/* ================================
   WIDGET SYSTEM (Priority #2)
   Material Design 3 cards with project state theming
   ================================ */
.widget-box {
    background-color: var(--md-surface-container-lowest);
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--border-radius-2xl);
    margin-bottom: var(--space-6);
    overflow: hidden;
    box-shadow: var(--md-elevation-1);
    max-width: 100%;
}

.widget-header {
    background-color: var(--bg-color);
    color: var(--md-on-surface);
    padding: var(--space-4);
    border-bottom: 1px solid var(--md-outline-variant);
    transition: background-color var(--transition-fast);
}

.widget-header h4,
.widget-header .widget-title {
    margin: 0;
    font-size: var(--md-title-medium);
    font-weight: 500;
    color: inherit;
}

.widget-header .widget-toolbar {
    float: right;
    margin-top: -2px;
}

.widget-header .widget-toolbar .btn {
    padding: var(--space-1) var(--space-2);
    font-size: var(--md-body-medium);
    border-radius: var(--border-radius-sm);
}

.widget-main,
.widget-body {
    padding: var(--space-4);
    background-color: var(--md-surface-container-lowest);
}

.widget-main .widget-main,
.widget-body .widget-body {
    padding: 0;
}

/* Widget variants */
.widget-box.transparent {
    background-color: transparent;
    border: none;
    box-shadow: none;
}

.widget-box.collapsed .widget-main,
.widget-box.collapsed .widget-body {
    display: none;
}

/* ================================
   TABLE SYSTEM (Priority #3)
   Material Design 3 data tables
   ================================ */
.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--md-surface-container-lowest);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    margin-bottom: var(--space-4);
}

.table th {
    background-color: var(--md-surface-container-high);
    color: var(--md-on-surface);
    font-weight: 500;
    font-size: var(--md-body-medium);
    padding: var(--space-3) var(--space-4);
    text-align: left;
    border-bottom: 1px solid var(--md-outline-variant);
}

.table td {
    padding: var(--space-3) var(--space-4);
    border-bottom: 1px solid var(--md-outline-variant);
    color: var(--md-on-surface);
    font-size: var(--md-body-medium);
    vertical-align: top;
}

.table tbody tr:hover {
    background-color: var(--md-surface-container-low);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Table variants */
.table.table-striped tbody tr:nth-child(even) {
    background-color: var(--md-surface-variant);
}

.table.table-bordered {
    border: 1px solid var(--md-outline-variant);
}

.table.table-bordered th,
.table.table-bordered td {
    border: 1px solid var(--md-outline-variant);
}

/* Table links with project state colors */
.table a {
    color: var(--link-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.table a:hover {
    color: var(--hover-color);
    text-decoration: underline;
}

.table a:active {
    color: var(--active-color);
}

/* Category column styling */
.table td.category {
    background-color: var(--md-surface-container);
    font-weight: 500;
    width: 150px;
    min-width: 150px;
}

/* ================================
   BUTTONS & FORMS
   Essential Material Design 3 components
   ================================ */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-2) var(--space-4);
    border: 1px solid var(--link-color);
    border-radius: var(--border-radius-md);
    background-color: var(--link-color);
    color: var(--md-on-primary);
    font-size: var(--md-body-medium);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-height: 40px;
    box-sizing: border-box;
}

.btn:hover {
    background-color: var(--hover-color);
    border-color: var(--hover-color);
    color: var(--md-on-primary);
    text-decoration: none;
}

.btn:active {
    background-color: var(--active-color);
    border-color: var(--active-color);
}

.btn .ace-icon {
    margin-right: var(--space-2);
    display: inline-block;
    vertical-align: middle;
}

/* Form controls */
.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--border-radius-md);
    padding: var(--space-3) var(--space-4);
    font-size: var(--md-body-medium);
    background-color: var(--md-surface-container-lowest);
    color: var(--md-on-surface);
    transition: border-color var(--transition-fast);
    min-height: 40px;
    box-sizing: border-box;
}

.form-control:hover,
input:hover,
textarea:hover,
select:hover {
    border-color: var(--md-outline);
}

.form-control:focus,
input:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--link-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* ================================
   LINKS & NAVIGATION
   Project state color integration
   ================================ */
a {
    color: var(--link-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--hover-color);
    text-decoration: underline;
}

a:active {
    color: var(--active-color);
}

/* Navigation specific links */
.nav a,
.navbar a,
.menu a {
    color: inherit;
}

.nav a:hover,
.navbar a:hover,
.menu a:hover {
    color: inherit;
    text-decoration: none;
}

/* ================================
   ALERTS & NOTIFICATIONS
   Material Design 3 feedback components
   ================================ */
.alert {
    border-radius: var(--border-radius-md);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
    border: 1px solid transparent;
}

.alert-success {
    background-color: var(--md-success-90);
    color: var(--md-success-10);
    border-color: var(--md-success-40);
}

.alert-warning {
    background-color: var(--md-warning-95);
    color: var(--md-warning-10);
    border-color: var(--md-warning-50);
}

.alert-danger,
.alert-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--md-error-30);
    border-color: var(--md-error-40);
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--md-primary-30);
    border-color: var(--md-primary-40);
}

/* ================================
   UTILITY CLASSES
   Common helper classes
   ================================ */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }

/* ================================
   END OF CONSOLIDATED ROOT STYLES
   Total: ~620 lines of clean, desktop-focused CSS
   No mobile/responsive code, no conflicting overrides
   ================================ */
