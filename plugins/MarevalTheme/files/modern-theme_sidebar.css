/* ================================
   Modern Sidebar Layout & Navigation (2025)
   MantisBT Compatible Structure
   ================================ */

/* ================================
   MAIN CONTAINER LAYOUT
   ================================ */

/* Main container layout - MantisBT structure */
.main-container,
#main-container {

    width: 100% !important;
    min-height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* ================================
   SIDEBAR POSITIONING & STYLING
   ================================ */

/* Sidebar positioning and sizing - MantisBT sidebar */
#sidebar,
.sidebar {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 280px;
    height: 100vh !important;
    background-color: var(--sidebar-bg) !important;
    border-right: 1px solid var(--link-color) !important;
    padding: var(--space-4) !important;
    overflow-y: auto !important;
    z-index: 1000 !important;
    box-sizing: border-box !important;
    margin-top: 64px !important; 
}

.sidebar.menu-min, .sidebar.menu-min.compact, .sidebar.menu-min.navbar-collapse {
    width: 105px !important;
}

/* Navigation list styling */
#sidebar ul.nav.nav-list,
.sidebar ul.nav.nav-list {
    list-style: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

#sidebar ul.nav.nav-list li,
.sidebar ul.nav.nav-list li {
    margin: var(--space-2) 0 !important;
    border-radius: var(--border-radius-sm) !important;
    border: 2px solid var(--link-color) !important;
    overflow: hidden !important;
}

#sidebar ul.nav.nav-list li a,
.sidebar ul.nav.nav-list li a {
    padding: var(--space-3) var(--space-4) !important;
    color: #404040 !important;
    text-decoration: none !important;
    background-color: transparent !important;
    border-radius: var(--border-radius-sm) !important;
    transition: all var(--transition-fast) !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: start !important;
}

#sidebar ul.nav.nav-list li a:hover,
.sidebar ul.nav.nav-list li a:hover {
    background-color: var(--nav-hover) !important;
    color: white !important;
    transform: translateX(2px) !important;
}

#sidebar ul.nav.nav-list li.active,
.sidebar ul.nav.nav-list li.active {
    border-color: var(--nav-active) !important;
}

#sidebar ul.nav.nav-list li.active a,
.sidebar ul.nav.nav-list li.active a {
    background-color: var(--nav-active) !important;
    color: white !important;
    font-weight: 600 !important;
}

/* ================================
   HIGH-SPECIFICITY OVERRIDES
   ================================ */

/* Ensure sidebar styling takes precedence over any legacy CSS */
body #sidebar ul.nav.nav-list li a {
    background-color: transparent !important;
    color: #404040 !important;
    border: none !important;
    text-shadow: none !important;
    box-shadow: none !important;
}

body #sidebar ul.nav.nav-list li a:hover {
    background-color: var(--nav-hover) !important;
    color: white !important;
}

body #sidebar ul.nav.nav-list li.active a {
    background-color: var(--nav-active) !important;
    color: white !important;
}

/* Icon color transitions - match text color changes */
#sidebar ul.nav.nav-list li a .menu-icon,
.sidebar ul.nav.nav-list li a .menu-icon,
#sidebar ul.nav.nav-list li a .ace-icon,
.sidebar ul.nav.nav-list li a .ace-icon {
    color: #404040 !important;
    transition: color var(--transition-fast) !important;
}

#sidebar ul.nav.nav-list li a:hover .menu-icon,
.sidebar ul.nav.nav-list li a:hover .menu-icon,
#sidebar ul.nav.nav-list li a:hover .ace-icon,
.sidebar ul.nav.nav-list li a:hover .ace-icon,
#sidebar ul.nav.nav-list li.active a .menu-icon,
.sidebar ul.nav.nav-list li.active a .menu-icon,
#sidebar ul.nav.nav-list li.active a .ace-icon,
.sidebar ul.nav.nav-list li.active a .ace-icon {
    color: white !important;
}


/* Override any ACE CSS that might interfere */
body #main-container > #sidebar {
    float: none !important;
    display: block !important;
}

/* ================================
   RESPONSIVE ADJUSTMENTS
   ================================ */
@media (max-width: 768px) {
    #sidebar,
    .sidebar {
        width: 180px !important;
    }
}

@media (max-width: 480px) {
    #sidebar,
    .sidebar {
        width: 160px !important;
    }
}

/* ================================
   Navigation List Styles
   ================================ */
.nav-list {
    margin: 0;
    padding: 0;
    list-style: none;
}
.nav-list > li,
.nav-list > li > a,
.nav-list .nav-header {
    margin: 0;
}
.nav-list li > a:focus {
    outline: none;
}
.nav-list > li {
    display: block;
    position: relative;
    float: none;
    padding: 0;
    border-style: solid;
    border-width: 1px 0 0;
}
.nav-list > li:last-child {
    border-bottom-width: 1px;
}
.nav-list > li.active > a {
    font-weight: bold;
}
.nav-list > li > a {
    display: block;
    height: 39px;
    line-height: 17px;
    padding-left: 7px;
    text-shadow: none !important;
    font-size: 13px;
    text-decoration: none;
}
.nav-list > li > a > .menu-icon {
    min-width: 30px;
    margin-right: 2px;
    vertical-align: sub;
    text-align: center;
    font-size: 18px;
    font-weight: normal;
}

@media only screen and (min-width: 992px) {
    .main-container.container .sidebar:not(.h-sidebar) .nav-list > li:before {
        left: -2px;
    }
}
.main-container.container .sidebar.sidebar-fixed:not(.h-sidebar) .nav-list > li:before {
    left: 0;
}
.nav-list > li:hover:before {
    display: block;
}

/* Separators */
.nav-list > li.separator {
    height: 3px;
    background-color: transparent;
    position: static;
    margin: 1px 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}

/* ================================
   Submenu Styles
   ================================ */
.nav-list > li .submenu {
    display: none;
    list-style: none;
    margin: 0;
    padding: 0;
    line-height: 1.5;
    position: relative;
}
.nav-list > li .submenu > li {
    margin-left: 0;
    position: relative;
}
.nav-list > li .submenu > li > a {
    display: block;
    position: relative;
    padding: 7px 0 9px 37px;
    margin: 0;
    border-top-width: 1px;
    border-top-style: dotted;
}
.nav-list > li .submenu > li > a:hover {
    text-decoration: none;
}
.nav-list > li .submenu > li a > .menu-icon {
    display: none;
    font-size: 12px;
    font-weight: normal;
    width: 18px;
    height: auto;
    line-height: 12px;
    text-align: center;
    position: absolute;
    left: 10px;
    top: 11px;
    z-index: 1;
    background-color: inherit;
}
.nav-list > li .submenu > li.active > a > .menu-icon,
.nav-list > li .submenu > li:hover > a > .menu-icon {
    display: inline-block;
}

/* Submenu borders and indicators */
.nav-list > li > .submenu {
    border-top: 1px solid;
}
.nav-list > li > .submenu > li:before {
    content: "";
    display: block;
    width: 7px;
    position: absolute;
    z-index: 1;
    left: 20px;
    top: 17px;
    border: 1px dotted;
    border-width: 1px 0 0;
}
.nav-list > li > .submenu:before {
    content: "";
    display: block;
    position: absolute;
    z-index: 1;
    left: 18px;
    top: 0;
    bottom: 0;
    border: 1px dotted;
    border-width: 0 0 0 1px;
}

.skin-3 .nav-list li.active>a:before {
    display: none !important;
    content: "";
    position: absolute;
    border-color: transparent #91bad6 transparent transparent;
    right: 0;
    border-width: 12px 8px;
    top: 7px;
}

/* Active and open states */
.nav-list li:hover > a > .arrow,
.nav-list li.active > a > .arrow,
.nav-list li.open > a > .arrow {
    color: inherit;
}
.nav-list li.open > .submenu,
.nav-list > li > .submenu li.open > .submenu {
    display: block;
}
.nav-list > li .submenu.collapsing {
    display: block;
}

/* Multi-level submenus */
.nav-list > li > .submenu li > .submenu {
    display: none;
}
.nav-list > li > .submenu li.active > .submenu,
.nav-list > li > .submenu li > .submenu.collapsing {
    display: block;
}
.nav-list > li > .submenu a > .arrow {
    right: 10px;
    top: 10px;
    font-size: 16px;
}
.nav-list > li > .submenu > li:first-child > a {
    border-top-width: 0;
}
.nav-list > li > .submenu li > .submenu > li {
    line-height: 18px;
}
.nav-list > li > .submenu li > .submenu > li:before {
    display: none;
}
.nav-list > li > .submenu li > .submenu > li > a {
    margin-left: 20px;
    padding-left: 22px;
}
.nav-list > li > .submenu li > .submenu > li > .submenu > li > a {
    margin-left: 20px;
    padding-left: 38px;
}
.nav-list > li > .submenu li > .submenu > li a > .menu-icon {
    display: inline-block;
    width: auto;
    position: static;
    background-color: transparent;
    margin-right: 4px;
    color: inherit;
}

/* Badges and labels */
.nav-list a .badge,
.nav-list a .label {
    position: absolute;
    top: 9px;
    right: 10px;
    opacity: 0.88;
    font-size: 12px;
    padding-left: 6px;
    padding-right: 6px;
}
.nav-list a .badge .ace-icon,
.nav-list a .label .ace-icon {
    vertical-align: middle;
    margin: 0;
}
.nav-list a.dropdown-toggle .badge,
.nav-list a.dropdown-toggle .label {
    right: 26px;
}
.nav-list li:hover > a .badge,
.nav-list li:hover > a .label {
    opacity: 1;
}
.nav-list .submenu .submenu a .badge,
.nav-list .submenu .submenu a .label {
    top: 6px;
}

/* ================================
   Sidebar Fixed Positioning
   ================================ */
.sidebar.sidebar-fixed {
    position: fixed;
    top: auto;
    float: none !important;
    z-index: 1027;
}
.sidebar.sidebar-fixed:before {
    height: 0;
    top: auto;
    bottom: auto;
}
.sidebar-scroll-native {
    overflow-y: scroll !important;
    overflow-x: hidden;
}

/* Mobile safari fixes */
body.mob-safari .sidebar.sidebar-fixed {
    top: 45px;
}
@media (max-width: 479px) {
    body.mob-safari .navbar-fixed-top:not(.navbar-collapse) + .main-container .sidebar-fixed {
        top: 90px;
    }
}

/* ================================
   Sidebar Toggle
   ================================ */
.sidebar .sidebar-toggle {
    border-style: solid;
    border-width: 0 0 1px;
    text-align: center;
    padding: 3px 0;
    position: relative;
}
.sidebar .sidebar-toggle > .ace-icon {
    padding: 0 5px;
    line-height: 18px;
    cursor: pointer;
    font-size: 14px;
    border-radius: 100%;
    border: 1px solid;
    position: relative;
}
.sidebar .sidebar-toggle:before {
    content: "";
    display: block;
    height: 0;
    border-top: 1px solid;
    border-color: inherit;
    position: absolute;
    left: 15px;
    right: 15px;
    top: 13px;
}

/* ================================
   Sidebar Shortcuts
   ================================ */
.sidebar .sidebar-shortcuts {
    text-align: center;
    min-height: 40px;
    margin-bottom: 0;
    overflow: hidden;
    position: relative;
    border: 0 solid;
}
.sidebar .sidebar-shortcuts:empty {
    display: none;
}
.sidebar .sidebar-shortcuts-large {
    line-height: 37px;
}
.sidebar .sidebar-shortcuts-large > .btn {
    text-align: center;
    width: 41px;
    line-height: 24px;
    padding: 0;
    border-width: 4px;
}
.sidebar .sidebar-shortcuts-large > .btn > .ace-icon {
    margin: 0;
}
.sidebar .sidebar-shortcuts-mini {
    display: none;
    font-size: 0;
    width: 42px;
    padding-top: 2px;
    padding-bottom: 2px;
}
.sidebar .sidebar-shortcuts-mini > .btn {
    border-width: 0;
    font-size: 0;
    line-height: 0;
    padding: 8px;
    margin: 1px;
    border-radius: 0;
    opacity: 0.85;
    filter: alpha(opacity=85);
}

/* ================================
   Active Item Indicators
   ================================ */
.nav-list li.active > a:after {

    content: "";
    position: absolute;
    right: 0;
    top: 4px;
    border: 8px solid transparent;
    border-width: 14px 10px;
}
.nav-list li.open > a:after {
    display: none;
}

.nav-list > li li.active > a:after {
    top: 2px;
    border-width: 14px 8px;
}
.nav-list li.active:not(.open) li.active > a:after {
    display: none !important;
}

/* ================================
   Compact Sidebar Styles
   ================================ */
@media (min-width: 992px) {
    .sidebar.compact,
    .sidebar.compact.navbar-collapse {
        width: 105px;
    }
    .sidebar.compact .sidebar-shortcuts {
        max-height: 40px;
    }
    .sidebar.compact .nav-list > li:before {
        height: auto;
        bottom: -1px;
    }
    .sidebar.compact .nav-list > li > a {
        height: auto;
        line-height: 16px;
        padding: 2px 0 8px;
        text-align: center;
    }
    .sidebar.compact .nav-list > li > a > .menu-icon {
        margin: 0;
        vertical-align: inherit;
        line-height: 32px;
        height: 32px;
        font-size: 20px;
    }
    .sidebar.compact .nav-list > li > a > .arrow {
        display: none;
    }
    .sidebar.compact .nav-list a .badge,
    .sidebar.compact .nav-list a .label {
        right: 12px;
    }
    .sidebar.compact .nav-list > li.active > .submenu,
    .sidebar.compact .nav-list > li.open > .submenu {
        display: none;
    }
    .sidebar.compact + .main-content {
        margin-left: 105px;
    }
}

/* Responsive sidebar */
@media only screen and (max-width: 991px) {
    .responsive.sidebar-fixed {
        left: auto;
        margin-left: auto;
    }
    .navbar-fixed-top + .main-container > .responsive {
        position: fixed;
        left: auto;
        margin-left: auto;
    }
    .navbar-fixed-top + .main-container > .menu-toggler + .responsive {
        position: absolute;
        left: 190px;
        margin-left: -190px;
    }
    .navbar-fixed-top + .main-container > .menu-toggler + .responsive.sidebar-fixed {
        position: fixed;
        left: auto;
        margin-left: auto;
    }
    .sidebar.responsive.sidebar-fixed {
        position: fixed;
        left: auto;
        margin-left: auto;
    }
    .main-container .menu-toggler.fixed {
        position: fixed;
        left: auto;
        z-index: 1026;
    }
    .sidebar.sidebar-fixed.responsive-max {
        position: fixed;
        left: auto;
        margin-left: auto;
    }
}

/* ================================
   Project State Dynamic Theming - Sidebar
   ================================ */

/* Project State Color Definitions */
.project-state-0 { 
    --bg-color: #C2E7FF; 
    --link-color: #3B82F6; 
    --hover-color: #2563EB;
    --active-color: #1D4ED8;
} /* All Projects - Blue */

.project-state-35 { 
    --bg-color: #CFFFE5; 
    --link-color: #10B981; 
    --hover-color: #059669;
    --active-color: #047857;
} /* Document Verification - Green */

.project-state-40 { 
    --bg-color: #D1FAE5; 
    --link-color: #059669; 
    --hover-color: #047857;
    --active-color: #065F46;
} /* Enhanced Document Processing - Dark Green */

.project-state-45 { 
    --bg-color: #FFF1C2; 
    --link-color: #B68929; 
    --hover-color: #A67C1B;
    --active-color: #92630F;
} /* Master Documents - Yellow/Gold */

.project-state-50 { 
    --bg-color: #DDD6FE; 
    --link-color: #7C3AED; 
    --hover-color: #6D28D9;
    --active-color: #5B21B6;
} /* Enhanced Meetings - Light Purple */

.project-state-55 { 
    --bg-color: #E9D7FF; 
    --link-color: #8B5CF6; 
    --hover-color: #7C3AED;
    --active-color: #6D28D9;
} /* Meetings - Purple */

.project-state-60 { 
    --bg-color: #FED7D7; 
    --link-color: #E53E3E; 
    --hover-color: #C53030;
    --active-color: #9B2C2C;
} /* Critical Issues - Light Red */

.project-state-65 { 
    --bg-color: #FFD9D1; 
    --link-color: #DC4C35; 
    --hover-color: #C53927;
    --active-color: #B02A1A;
} /* Minutes of Meeting - Red/Orange */

/* Additional project states for expanded navigation */
.project-state-70 { 
    --bg-color: #E0F2FE; 
    --link-color: #0891B2; 
    --hover-color: #0E7490;
    --active-color: #155E75;
} /* Reports - Cyan */

.project-state-75 { 
    --bg-color: #F3E8FF; 
    --link-color: #9333EA; 
    --hover-color: #7C3AED;
    --active-color: #6D28D9;
} /* Administration - Deep Purple */

/* Sidebar Background Theming */
.project-state-0 #sidebar,
.project-state-35 #sidebar,
.project-state-40 #sidebar,
.project-state-45 #sidebar,
.project-state-50 #sidebar,
.project-state-55 #sidebar,
.project-state-60 #sidebar,
.project-state-65 #sidebar,
.project-state-70 #sidebar,
.project-state-75 #sidebar {
    background-color: var(--bg-color) !important;
}

/* Sidebar Navigation Items - Transparent Background with Colored Borders */
.project-state-0 #sidebar ul.nav.nav-list li a,
.project-state-35 #sidebar ul.nav.nav-list li a,
.project-state-40 #sidebar ul.nav.nav-list li a,
.project-state-45 #sidebar ul.nav.nav-list li a,
.project-state-50 #sidebar ul.nav.nav-list li a,
.project-state-55 #sidebar ul.nav.nav-list li a,
.project-state-60 #sidebar ul.nav.nav-list li a,
.project-state-65 #sidebar ul.nav.nav-list li a,
.project-state-70 #sidebar ul.nav.nav-list li a,
.project-state-75 #sidebar ul.nav.nav-list li a {
    background-color: transparent !important;
    color: #404040 !important;
}

/* Project-state navigation icons - match text color */
.project-state-0 #sidebar ul.nav.nav-list li a .menu-icon,
.project-state-35 #sidebar ul.nav.nav-list li a .menu-icon,
.project-state-40 #sidebar ul.nav.nav-list li a .menu-icon,
.project-state-45 #sidebar ul.nav.nav-list li a .menu-icon,
.project-state-50 #sidebar ul.nav.nav-list li a .menu-icon,
.project-state-55 #sidebar ul.nav.nav-list li a .menu-icon,
.project-state-60 #sidebar ul.nav.nav-list li a .menu-icon,
.project-state-65 #sidebar ul.nav.nav-list li a .menu-icon,
.project-state-70 #sidebar ul.nav.nav-list li a .menu-icon,
.project-state-75 #sidebar ul.nav.nav-list li a .menu-icon,
.project-state-0 #sidebar ul.nav.nav-list li a .ace-icon,
.project-state-35 #sidebar ul.nav.nav-list li a .ace-icon,
.project-state-40 #sidebar ul.nav.nav-list li a .ace-icon,
.project-state-45 #sidebar ul.nav.nav-list li a .ace-icon,
.project-state-50 #sidebar ul.nav.nav-list li a .ace-icon,
.project-state-55 #sidebar ul.nav.nav-list li a .ace-icon,
.project-state-60 #sidebar ul.nav.nav-list li a .ace-icon,
.project-state-65 #sidebar ul.nav.nav-list li a .ace-icon,
.project-state-70 #sidebar ul.nav.nav-list li a .ace-icon,
.project-state-75 #sidebar ul.nav.nav-list li a .ace-icon {
    color: #404040 !important;
}

/* Sidebar Navigation Borders */
.project-state-0 #sidebar ul.nav.nav-list li,
.project-state-35 #sidebar ul.nav.nav-list li,
.project-state-40 #sidebar ul.nav.nav-list li,
.project-state-45 #sidebar ul.nav.nav-list li,
.project-state-50 #sidebar ul.nav.nav-list li,
.project-state-55 #sidebar ul.nav.nav-list li,
.project-state-60 #sidebar ul.nav.nav-list li,
.project-state-65 #sidebar ul.nav.nav-list li,
.project-state-70 #sidebar ul.nav.nav-list li,
.project-state-75 #sidebar ul.nav.nav-list li {
    border-color: var(--link-color) !important;
}

/* Active Navigation Items */
.project-state-0 #sidebar ul.nav.nav-list li.active,
.project-state-35 #sidebar ul.nav.nav-list li.active,
.project-state-40 #sidebar ul.nav.nav-list li.active,
.project-state-45 #sidebar ul.nav.nav-list li.active,
.project-state-50 #sidebar ul.nav.nav-list li.active,
.project-state-55 #sidebar ul.nav.nav-list li.active,
.project-state-60 #sidebar ul.nav.nav-list li.active,
.project-state-65 #sidebar ul.nav.nav-list li.active,
.project-state-70 #sidebar ul.nav.nav-list li.active,
.project-state-75 #sidebar ul.nav.nav-list li.active {
    border-color: var(--active-color) !important;
}

.project-state-0 #sidebar ul.nav.nav-list li.active a,
.project-state-35 #sidebar ul.nav.nav-list li.active a,
.project-state-40 #sidebar ul.nav.nav-list li.active a,
.project-state-45 #sidebar ul.nav.nav-list li.active a,
.project-state-50 #sidebar ul.nav.nav-list li.active a,
.project-state-55 #sidebar ul.nav.nav-list li.active a,
.project-state-60 #sidebar ul.nav.nav-list li.active a,
.project-state-65 #sidebar ul.nav.nav-list li.active a,
.project-state-70 #sidebar ul.nav.nav-list li.active a,
.project-state-75 #sidebar ul.nav.nav-list li.active a {
    background-color: var(--active-color) !important;
    color: white !important;
    font-weight: 500 !important;
}

/* Hover Navigation Items */
.project-state-0 #sidebar ul.nav.nav-list li:hover:not(.active),
.project-state-35 #sidebar ul.nav.nav-list li:hover:not(.active),
.project-state-40 #sidebar ul.nav.nav-list li:hover:not(.active),
.project-state-45 #sidebar ul.nav.nav-list li:hover:not(.active),
.project-state-50 #sidebar ul.nav.nav-list li:hover:not(.active),
.project-state-55 #sidebar ul.nav.nav-list li:hover:not(.active),
.project-state-60 #sidebar ul.nav.nav-list li:hover:not(.active),
.project-state-65 #sidebar ul.nav.nav-list li:hover:not(.active),
.project-state-70 #sidebar ul.nav.nav-list li:hover:not(.active),
.project-state-75 #sidebar ul.nav.nav-list li:hover:not(.active) {
    border-color: var(--hover-color) !important;
}

/* Hover Navigation Items - Colored Background with White Text */
.project-state-0 #sidebar ul.nav.nav-list li:hover:not(.active) a,
.project-state-35 #sidebar ul.nav.nav-list li:hover:not(.active) a,
.project-state-40 #sidebar ul.nav.nav-list li:hover:not(.active) a,
.project-state-45 #sidebar ul.nav.nav-list li:hover:not(.active) a,
.project-state-50 #sidebar ul.nav.nav-list li:hover:not(.active) a,
.project-state-55 #sidebar ul.nav.nav-list li:hover:not(.active) a,
.project-state-60 #sidebar ul.nav.nav-list li:hover:not(.active) a,
.project-state-65 #sidebar ul.nav.nav-list li:hover:not(.active) a,
.project-state-70 #sidebar ul.nav.nav-list li:hover:not(.active) a,
.project-state-75 #sidebar ul.nav.nav-list li:hover:not(.active) a {
    background-color: var(--hover-color) !important;
    color: white !important;
}

/* Hover and Active Navigation Icons - White on colored backgrounds */
.project-state-0 #sidebar ul.nav.nav-list li:hover:not(.active) a .menu-icon,
.project-state-35 #sidebar ul.nav.nav-list li:hover:not(.active) a .menu-icon,
.project-state-40 #sidebar ul.nav.nav-list li:hover:not(.active) a .menu-icon,
.project-state-45 #sidebar ul.nav.nav-list li:hover:not(.active) a .menu-icon,
.project-state-50 #sidebar ul.nav.nav-list li:hover:not(.active) a .menu-icon,
.project-state-55 #sidebar ul.nav.nav-list li:hover:not(.active) a .menu-icon,
.project-state-60 #sidebar ul.nav.nav-list li:hover:not(.active) a .menu-icon,
.project-state-65 #sidebar ul.nav.nav-list li:hover:not(.active) a .menu-icon,
.project-state-70 #sidebar ul.nav.nav-list li:hover:not(.active) a .menu-icon,
.project-state-75 #sidebar ul.nav.nav-list li:hover:not(.active) a .menu-icon,
.project-state-0 #sidebar ul.nav.nav-list li:hover:not(.active) a .ace-icon,
.project-state-35 #sidebar ul.nav.nav-list li:hover:not(.active) a .ace-icon,
.project-state-40 #sidebar ul.nav.nav-list li:hover:not(.active) a .ace-icon,
.project-state-45 #sidebar ul.nav.nav-list li:hover:not(.active) a .ace-icon,
.project-state-50 #sidebar ul.nav.nav-list li:hover:not(.active) a .ace-icon,
.project-state-55 #sidebar ul.nav.nav-list li:hover:not(.active) a .ace-icon,
.project-state-60 #sidebar ul.nav.nav-list li:hover:not(.active) a .ace-icon,
.project-state-65 #sidebar ul.nav.nav-list li:hover:not(.active) a .ace-icon,
.project-state-70 #sidebar ul.nav.nav-list li:hover:not(.active) a .ace-icon,
.project-state-75 #sidebar ul.nav.nav-list li:hover:not(.active) a .ace-icon,
.project-state-0 #sidebar ul.nav.nav-list li.active a .menu-icon,
.project-state-35 #sidebar ul.nav.nav-list li.active a .menu-icon,
.project-state-40 #sidebar ul.nav.nav-list li.active a .menu-icon,
.project-state-45 #sidebar ul.nav.nav-list li.active a .menu-icon,
.project-state-50 #sidebar ul.nav.nav-list li.active a .menu-icon,
.project-state-55 #sidebar ul.nav.nav-list li.active a .menu-icon,
.project-state-60 #sidebar ul.nav.nav-list li.active a .menu-icon,
.project-state-65 #sidebar ul.nav.nav-list li.active a .menu-icon,
.project-state-70 #sidebar ul.nav.nav-list li.active a .menu-icon,
.project-state-75 #sidebar ul.nav.nav-list li.active a .menu-icon,
.project-state-0 #sidebar ul.nav.nav-list li.active a .ace-icon,
.project-state-35 #sidebar ul.nav.nav-list li.active a .ace-icon,
.project-state-40 #sidebar ul.nav.nav-list li.active a .ace-icon,
.project-state-45 #sidebar ul.nav.nav-list li.active a .ace-icon,
.project-state-50 #sidebar ul.nav.nav-list li.active a .ace-icon,
.project-state-55 #sidebar ul.nav.nav-list li.active a .ace-icon,
.project-state-60 #sidebar ul.nav.nav-list li.active a .ace-icon,
.project-state-65 #sidebar ul.nav.nav-list li.active a .ace-icon,
.project-state-70 #sidebar ul.nav.nav-list li.active a .ace-icon,
.project-state-75 #sidebar ul.nav.nav-list li.active a .ace-icon {
    color: white !important;
}

/* Breadcrumb Theming */
.project-state-0 .breadcrumbs,
.project-state-35 .breadcrumbs,
.project-state-40 .breadcrumbs,
.project-state-45 .breadcrumbs,
.project-state-50 .breadcrumbs,
.project-state-55 .breadcrumbs,
.project-state-60 .breadcrumbs,
.project-state-65 .breadcrumbs,
.project-state-70 .breadcrumbs,
.project-state-75 .breadcrumbs {
    background-color: var(--bg-color) !important;
    border-color: var(--link-color) !important;
}

/* Widget Headers Theming */
.project-state-0 .widget-header,
.project-state-35 .widget-header,
.project-state-40 .widget-header,
.project-state-45 .widget-header,
.project-state-50 .widget-header,
.project-state-55 .widget-header,
.project-state-60 .widget-header,
.project-state-65 .widget-header,
.project-state-70 .widget-header,
.project-state-75 .widget-header {
    background-color: var(--link-color) !important;
    color: white !important;
}

/* ================================
   PAGE-BASED NAVIGATION THEMING
   Map specific navigation items to project-state colors
   ================================ */

/* My View - Project State 0 (Blue) */
body[data-page*="my_view"] #sidebar ul.nav.nav-list li a[href*="my_view_page.php"],
#sidebar ul.nav.nav-list li a[href*="my_view_page.php"] {
    border-color: #3B82F6 !important;
}

/* My View Active State */
body[data-page*="my_view"] #sidebar ul.nav.nav-list li.active a[href*="my_view_page.php"],
#sidebar ul.nav.nav-list li.active a[href*="my_view_page.php"] {
    background-color: #3B82F6 !important;
    color: white !important;
}

body[data-page*="my_view"] #sidebar ul.nav.nav-list li.active a[href*="my_view_page.php"] .menu-icon,
body[data-page*="my_view"] #sidebar ul.nav.nav-list li.active a[href*="my_view_page.php"] .ace-icon,
#sidebar ul.nav.nav-list li.active a[href*="my_view_page.php"] .menu-icon,
#sidebar ul.nav.nav-list li.active a[href*="my_view_page.php"] .ace-icon {
    color: white !important;
}

/* Documents - Project State 35 (Green) */
body[data-page*="view_all_bug"] #sidebar ul.nav.nav-list li a[href*="view_all_bug_page.php"],
body[data-page*="bug_report"] #sidebar ul.nav.nav-list li a[href*="bug_report_page.php"],
#sidebar ul.nav.nav-list li a[href*="project_id=266"] {
    border-color: #10B981 !important;
    /* background-color: #10B981 !important; */
}

/* Comments - Project State 45 (Yellow/Gold) */
#sidebar ul.nav.nav-list li a[href*="project_id=267"] {
    border-color: #B68929 !important;
    /* background-color: #B68929 !important; */
}
/*  */
/* Meetings - Project State 55 (Purple) */
#sidebar ul.nav.nav-list li a[href*="project_id=278"] {
    border-color: #8B5CF6 !important;
    /* background-color: #8B5CF6 !important; */
}

/* Minutes of Meeting - Project State 65 (Red/Orange) */
#sidebar ul.nav.nav-list li a[href*="project_id=279"] {
    border-color: #DC4C35 !important;
    /* background-color: #DC4C35 !important; */
}

/* Reports - Project State 0 (Blue) */
#sidebar ul.nav.nav-list li a[href*="MarevalReporting"] {
    border-color: #3B82F6 !important;
    /* background-color: #3B82F6 !important; */
}

/* Planning - Project State 0 (Blue) */
#sidebar ul.nav.nav-list li a[href*="roadmap_page.php"] {
    border-color: #3B82F6 !important;
    /* background-color: #3B82F6 !important; */
}

/* Summary - Project State 0 (Blue) */
#sidebar ul.nav.nav-list li a[href*="summary_page.php"] {
    border-color: #3B82F6 !important;
    /* background-color: #3B82F6 !important; */
}

/* Manage - Project State 0 (Blue) */
#sidebar ul.nav.nav-list li a[href*="manage_overview_page.php"] {
    border-color: #3B82F6 !important;
    /* background-color: #3B82F6 !important; */
}

/* ================================
   NAVIGATION ITEM SPECIFIC THEMING
   Map navigation items by class/data attributes
   ================================ */

/* Documents navigation items (Green) */
#sidebar ul.nav.nav-list li[data-nav-type="documents"] a,
#sidebar ul.nav.nav-list li.nav-documents a,
#sidebar ul.nav.nav-list li a[data-project-state="35"] {
    border-color: #10B981 !important;
    /* background-color: #10B981 !important; */
}

/* Comments navigation items (Yellow/Gold) */
#sidebar ul.nav.nav-list li[data-nav-type="comments"] a,
#sidebar ul.nav.nav-list li.nav-comments a,
#sidebar ul.nav.nav-list li a[data-project-state="45"] {
    border-color: #B68929 !important;
    /* background-color: #B68929 !important; */
}

/* Meetings navigation items (Purple) */
#sidebar ul.nav.nav-list li[data-nav-type="meetings"] a,
#sidebar ul.nav.nav-list li.nav-meetings a,
#sidebar ul.nav.nav-list li a[data-project-state="55"] {
    border-color: #8B5CF6 !important;
    /* background-color: #8B5CF6 !important; */
}

/* Minutes of Meeting navigation items (Red/Orange) */
#sidebar ul.nav.nav-list li[data-nav-type="minutes"] a,
#sidebar ul.nav.nav-list li.nav-minutes a,
#sidebar ul.nav.nav-list li a[data-project-state="65"] {
    border-color: #DC4C35 !important;
    /* background-color: #DC4C35 !important; */
}

/* ================================
   HOVER AND ACTIVE STATES FOR PAGE-BASED THEMING
   ================================ */

/* Documents - Hover and Active States */
#sidebar ul.nav.nav-list li[data-nav-type="documents"]:hover a,
#sidebar ul.nav.nav-list li.nav-documents:hover a,
#sidebar ul.nav.nav-list li:hover a[href*="project_id=266"],
#sidebar ul.nav.nav-list li:hover a[data-project-state="35"] {
    border-color: #059669 !important;
    /* background-color: #059669 !important; */
}

/* Higher specificity to override JavaScript injection */
body #sidebar ul.nav.nav-list li[data-nav-type="documents"].active a,
body #sidebar ul.nav.nav-list li.nav-documents.active a,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=266"],
body #sidebar ul.nav.nav-list li.active a[data-project-state="35"] {
    border-color: #047857 !important;
    background-color: #047857 !important;
    color: white !important;
}

body #sidebar ul.nav.nav-list li[data-nav-type="documents"].active a .menu-icon,
body #sidebar ul.nav.nav-list li[data-nav-type="documents"].active a .ace-icon,
body #sidebar ul.nav.nav-list li.nav-documents.active a .menu-icon,
body #sidebar ul.nav.nav-list li.nav-documents.active a .ace-icon,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=266"] .menu-icon,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=266"] .ace-icon,
body #sidebar ul.nav.nav-list li.active a[data-project-state="35"] .menu-icon,
body #sidebar ul.nav.nav-list li.active a[data-project-state="35"] .ace-icon {
    color: white !important;
}

/* Comments - Hover and Active States */
#sidebar ul.nav.nav-list li[data-nav-type="comments"]:hover a,
#sidebar ul.nav.nav-list li.nav-comments:hover a,
#sidebar ul.nav.nav-list li:hover a[href*="project_id=267"],
#sidebar ul.nav.nav-list li:hover a[data-project-state="45"] {
    border-color: #A67C1B !important;
    /* background-color: #A67C1B !important; */
}

/* Higher specificity to override JavaScript injection */
body #sidebar ul.nav.nav-list li.active a[href*="project_id=267"] {
    border-color: #92630F !important;
    background-color: #92630F !important;
    color: white !important;
}

body #sidebar ul.nav.nav-list li.active a[href*="project_id=267"] .menu-icon,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=267"] .ace-icon {
    color: white !important;
}

/* Meetings - Hover and Active States */
#sidebar ul.nav.nav-list li[data-nav-type="meetings"]:hover a,
#sidebar ul.nav.nav-list li.nav-meetings:hover a,
#sidebar ul.nav.nav-list li:hover a[href*="project_id=278"],
#sidebar ul.nav.nav-list li:hover a[data-project-state="55"] {
    border-color: #7C3AED !important;
    /* background-color: #7C3AED !important; */
}

/* Higher specificity to override JavaScript injection */
body #sidebar ul.nav.nav-list li[data-nav-type="meetings"].active a,
body #sidebar ul.nav.nav-list li.nav-meetings.active a,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=278"],
body #sidebar ul.nav.nav-list li.active a[data-project-state="55"] {
    border-color: #6D28D9 !important;
    background-color: #6D28D9 !important;
    color: white !important;
}

body #sidebar ul.nav.nav-list li[data-nav-type="meetings"].active a .menu-icon,
body #sidebar ul.nav.nav-list li[data-nav-type="meetings"].active a .ace-icon,
body #sidebar ul.nav.nav-list li.nav-meetings.active a .menu-icon,
body #sidebar ul.nav.nav-list li.nav-meetings.active a .ace-icon,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=278"] .menu-icon,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=278"] .ace-icon,
body #sidebar ul.nav.nav-list li.active a[data-project-state="55"] .menu-icon,
body #sidebar ul.nav.nav-list li.active a[data-project-state="55"] .ace-icon {
    color: white !important;
}

/* Minutes of Meeting - Hover and Active States */
#sidebar ul.nav.nav-list li[data-nav-type="minutes"]:hover a,
#sidebar ul.nav.nav-list li.nav-minutes:hover a,
#sidebar ul.nav.nav-list li:hover a[href*="project_id=279"],
#sidebar ul.nav.nav-list li:hover a[data-project-state="65"] {
    border-color: #C53927 !important;
    /* background-color: #C53927 !important; */
}

/* Higher specificity to override JavaScript injection */
body #sidebar ul.nav.nav-list li[data-nav-type="minutes"].active a,
body #sidebar ul.nav.nav-list li.nav-minutes.active a,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=279"],
body #sidebar ul.nav.nav-list li.active a[data-project-state="65"] {
    border-color: #B02A1A !important;
    background-color: #B02A1A !important;
    color: white !important;
}

body #sidebar ul.nav.nav-list li[data-nav-type="minutes"].active a .menu-icon,
body #sidebar ul.nav.nav-list li[data-nav-type="minutes"].active a .ace-icon,
body #sidebar ul.nav.nav-list li.nav-minutes.active a .menu-icon,
body #sidebar ul.nav.nav-list li.nav-minutes.active a .ace-icon,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=279"] .menu-icon,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=279"] .ace-icon,
body #sidebar ul.nav.nav-list li.active a[data-project-state="65"] .menu-icon,
body #sidebar ul.nav.nav-list li.active a[data-project-state="65"] .ace-icon {
    color: white !important;
}

/* ================================
   COLLAPSED SIDEBAR HOVER TOOLTIP REMOVAL
   Hide the nasty gray tooltip/submenu that appears on hover
   ================================ */

/* Hide collapsed sidebar hover tooltips - we don't need them */
.sidebar.menu-min .nav-list > li:hover > .submenu,
.sidebar.menu-min .nav-list > li.hover-show > .submenu,
.sidebar.menu-min .nav-list > li.hover > .submenu {
    display: none !important;
}

/* Hide the triangle arrows on collapsed sidebar */
.sidebar.menu-min .nav-list > li > .arrow,
.sidebar.menu-min .nav-list > li > .arrow:after,
.sidebar.menu-min .nav-list > li > .arrow:before {
    display: none !important;
}

/* Hide the menu-text hover tooltip that pops up from ace.min.css */
.sidebar.menu-min .nav-list > li > a > .menu-text {
    display: none !important;
    position: static !important;
    z-index: auto !important;
    width: auto !important;
    height: auto !important;
    border: none !important;
    padding: 0 !important;
}



/* ================================
   ADDITIONAL NAVIGATION ACTIVE STATES
   For other common navigation items
   ================================ */

/* Reports Active State */
#sidebar ul.nav.nav-list li.active a[href*="summary_page.php"],
#sidebar ul.nav.nav-list li.active a[href*="report"],
body[data-page*="summary"] #sidebar ul.nav.nav-list li.active a[href*="summary_page.php"] {
    background-color: var(--link-color) !important;
    color: white !important;
}

#sidebar ul.nav.nav-list li.active a[href*="summary_page.php"] .menu-icon,
#sidebar ul.nav.nav-list li.active a[href*="summary_page.php"] .ace-icon,
#sidebar ul.nav.nav-list li.active a[href*="report"] .menu-icon,
#sidebar ul.nav.nav-list li.active a[href*="report"] .ace-icon {
    color: white !important;
}

/* Planning Active State */
#sidebar ul.nav.nav-list li.active a[href*="roadmap"],
#sidebar ul.nav.nav-list li.active a[href*="plan"],
body[data-page*="roadmap"] #sidebar ul.nav.nav-list li.active a[href*="roadmap"] {
    background-color: var(--link-color) !important;
    color: white !important;
}

#sidebar ul.nav.nav-list li.active a[href*="roadmap"] .menu-icon,
#sidebar ul.nav.nav-list li.active a[href*="roadmap"] .ace-icon,
#sidebar ul.nav.nav-list li.active a[href*="plan"] .menu-icon,
#sidebar ul.nav.nav-list li.active a[href*="plan"] .ace-icon {
    color: white !important;
}

/* Manage Active State */
#sidebar ul.nav.nav-list li.active a[href*="manage"],
#sidebar ul.nav.nav-list li.active a[href*="admin"],
body[data-page*="manage"] #sidebar ul.nav.nav-list li.active a[href*="manage"] {
    background-color: var(--link-color) !important;
    color: white !important;
}

#sidebar ul.nav.nav-list li.active a[href*="manage"] .menu-icon,
#sidebar ul.nav.nav-list li.active a[href*="manage"] .ace-icon,
#sidebar ul.nav.nav-list li.active a[href*="admin"] .menu-icon,
#sidebar ul.nav.nav-list li.active a[href*="admin"] .ace-icon {
    color: white !important;
}

/* Completion Active State */
#sidebar ul.nav.nav-list li.active a[href*="changelog"],
#sidebar ul.nav.nav-list li.active a[href*="completion"],
body[data-page*="changelog"] #sidebar ul.nav.nav-list li.active a[href*="changelog"] {
    background-color: var(--link-color) !important;
    color: white !important;
}

#sidebar ul.nav.nav-list li.active a[href*="changelog"] .menu-icon,
#sidebar ul.nav.nav-list li.active a[href*="changelog"] .ace-icon,
#sidebar ul.nav.nav-list li.active a[href*="completion"] .menu-icon,
#sidebar ul.nav.nav-list li.active a[href*="completion"] .ace-icon {
    color: white !important;
}

/* ================================
   FALLBACK STATIC PROJECT ID THEMING
   For when JavaScript is disabled or not loaded yet
   ================================ */

/* Static project ID 266 - Documents (Green) - Transparent Design */
#sidebar ul.nav.nav-list li:not(.active) a[href*="project_id=266"] {
    background-color: transparent !important;
    color: #404040 !important;
}

/* Documents border - Green */
#sidebar ul.nav.nav-list li:has(a[href*="project_id=266"]) {
    border-color: #10B981 !important;
}

#sidebar ul.nav.nav-list li:not(.active) a[href*="project_id=266"] .menu-icon,
#sidebar ul.nav.nav-list li:not(.active) a[href*="project_id=266"] .ace-icon {
    color: #404040 !important;
}

#sidebar ul.nav.nav-list li:hover a[href*="project_id=266"] {
    background-color: #059669 !important;
    color: white !important;
}

#sidebar ul.nav.nav-list li:hover a[href*="project_id=266"] .menu-icon,
#sidebar ul.nav.nav-list li:hover a[href*="project_id=266"] .ace-icon {
    color: white !important;
}

/* Higher specificity to override JavaScript injection */
body #sidebar ul.nav.nav-list li.active a[href*="project_id=266"] {
    background-color: #047857 !important;
    color: white !important;
}

body #sidebar ul.nav.nav-list li.active a[href*="project_id=266"] .menu-icon,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=266"] .ace-icon {
    color: white !important;
}

/* Static project ID 267 - Comments (Yellow/Gold) - Transparent Design */
#sidebar ul.nav.nav-list li:not(.active) a[href*="project_id=267"] {
    background-color: transparent !important;
    color: #404040 !important;
}

/* Comments border - Yellow/Gold */
#sidebar ul.nav.nav-list li:has(a[href*="project_id=267"]) {
    border-color: #B68929 !important;
}

#sidebar ul.nav.nav-list li:not(.active) a[href*="project_id=267"] .menu-icon,
#sidebar ul.nav.nav-list li:not(.active) a[href*="project_id=267"] .ace-icon {
    color: #404040 !important;
}

#sidebar ul.nav.nav-list li:hover a[href*="project_id=267"] {
    background-color: #A67C1B !important;
    color: white !important;
}

#sidebar ul.nav.nav-list li:hover a[href*="project_id=267"] .menu-icon,
#sidebar ul.nav.nav-list li:hover a[href*="project_id=267"] .ace-icon {
    color: white !important;
}

/* Higher specificity to override JavaScript injection */
body #sidebar ul.nav.nav-list li.active a[href*="project_id=267"] {
    background-color: #92630F !important;
    color: white !important;
}

body #sidebar ul.nav.nav-list li.active a[href*="project_id=267"] .menu-icon,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=267"] .ace-icon {
    color: white !important;
}

/* Static project ID 278 - Meetings (Purple) - Transparent Design */
#sidebar ul.nav.nav-list li:not(.active) a[href*="project_id=278"] {
    background-color: transparent !important;
    color: #404040 !important;
}

/* Meetings border - Purple */
#sidebar ul.nav.nav-list li:has(a[href*="project_id=278"]) {
    border-color: #8B5CF6 !important;
}

#sidebar ul.nav.nav-list li:not(.active) a[href*="project_id=278"] .menu-icon,
#sidebar ul.nav.nav-list li:not(.active) a[href*="project_id=278"] .ace-icon {
    color: #404040 !important;
}

#sidebar ul.nav.nav-list li:hover a[href*="project_id=278"] {
    background-color: #7C3AED !important;
    color: white !important;
}

#sidebar ul.nav.nav-list li:hover a[href*="project_id=278"] .menu-icon,
#sidebar ul.nav.nav-list li:hover a[href*="project_id=278"] .ace-icon {
    color: white !important;
}

/* Higher specificity to override JavaScript injection */
body #sidebar ul.nav.nav-list li.active a[href*="project_id=278"] {
    background-color: #6D28D9 !important;
    color: white !important;
}

body #sidebar ul.nav.nav-list li.active a[href*="project_id=278"] .menu-icon,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=278"] .ace-icon {
    color: white !important;
}

/* Static project ID 279 - Minutes of Meeting (Red/Orange) - Transparent Design */
#sidebar ul.nav.nav-list li:not(.active) a[href*="project_id=279"] {
    background-color: transparent !important;
    color: #404040 !important;
}

/* Minutes border - Red/Orange */
#sidebar ul.nav.nav-list li:has(a[href*="project_id=279"]) {
    border-color: #DC4C35 !important;
}

#sidebar ul.nav.nav-list li:not(.active) a[href*="project_id=279"] .menu-icon,
#sidebar ul.nav.nav-list li:not(.active) a[href*="project_id=279"] .ace-icon {
    color: #404040 !important;
}

#sidebar ul.nav.nav-list li:hover a[href*="project_id=279"] {
    background-color: #C53927 !important;
    color: white !important;
}

#sidebar ul.nav.nav-list li:hover a[href*="project_id=279"] .menu-icon,
#sidebar ul.nav.nav-list li:hover a[href*="project_id=279"] .ace-icon {
    color: white !important;
}

/* Higher specificity to override JavaScript injection */
body #sidebar ul.nav.nav-list li.active a[href*="project_id=279"] {
    background-color: #B02A1A !important;
    color: white !important;
}

body #sidebar ul.nav.nav-list li.active a[href*="project_id=279"] .menu-icon,
body #sidebar ul.nav.nav-list li.active a[href*="project_id=279"] .ace-icon {
    color: white !important;
}

/* ================================
   WIDGET HEADER PROJECT STATE THEMING
   Dynamic theming handled by JavaScript, these are fallbacks
   ================================ */

/* Default widget header styling (Blue - Project State 0) */
.widget-header,
.widget-title,
.panel-heading,
.card-header,
.widget-box .widget-header,
.widget-container .widget-header {
    background-color: #3B82F6 !important;
    color: white !important;
    border-color: #3B82F6 !important;
}

/* Page-specific widget header theming based on URL patterns */
/* These will be enhanced by JavaScript for dynamic project IDs */

/* Documents pages - Green headers */
body[data-page*="view_all_bug"][data-page*="266"] .widget-header,
body[data-page*="bug_report"][data-page*="266"] .widget-header {
    background-color: #10B981 !important;
    color: white !important;
    border-color: #10B981 !important;
}

/* Comments pages - Yellow/Gold headers */
body[data-page*="view_all_bug"][data-page*="267"] .widget-header,
body[data-page*="bug_report"][data-page*="267"] .widget-header {
    background-color: #B68929 !important;
    color: white !important;
    border-color: #B68929 !important;
}

/* Meetings pages - Purple headers */
body[data-page*="view_all_bug"][data-page*="278"] .widget-header,
body[data-page*="bug_report"][data-page*="278"] .widget-header {
    background-color: #8B5CF6 !important;
    color: white !important;
    border-color: #8B5CF6 !important;
}

/* Minutes pages - Red/Orange headers */
body[data-page*="view_all_bug"][data-page*="279"] .widget-header,
body[data-page*="bug_report"][data-page*="279"] .widget-header {
    background-color: #DC4C35 !important;
    color: white !important;
    border-color: #DC4C35 !important;
}

