/* ================================
   MINIMAL ACE OVERRIDES
   Strategic !important declarations to combat ace.min.css
   Only essential overrides for 240px sidebar layout
   ================================ */

/* ================================
   LAYOUT OVERRIDES (Essential)
   ================================ */
.sidebar + .main-content,
.main-content {
    margin-left: var(--sidebar-width) !important;
    width: calc(100% - var(--sidebar-width)) !important;
}

.main-container {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* ================================
   BUTTON ICON FIXES (ACE makes icons block/float)
   ================================ */
.btn .ace-icon,
.btn i,
.navbar .btn .ace-icon,
.navbar .btn i {
    display: inline !important;
    vertical-align: middle !important;
    margin-right: var(--space-2) !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    float: none !important;
}

.btn span,
.navbar .btn span {
    display: inline !important;
    vertical-align: middle !important;
}

/* ================================
   WIDGET OVERRIDES (Priority #2)
   ================================ */
.widget-box {
    box-shadow: none !important;
    background-image: none !important;
    filter: none !important;
    max-width: 100% !important;
    overflow-x: auto !important;
}

.widget-header {
    background-image: none !important;
    text-shadow: none !important;
}

/* ================================
   TABLE OVERRIDES (Priority #3)
   ================================ */
.table,
.table th,
.table td {
    box-shadow: none !important;
    text-shadow: none !important;
    background-image: none !important;
    filter: none !important;
}

.table th {
    background-image: none !important;
}

/* ================================
   FORM OVERRIDES (Essential)
   ================================ */
.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
    box-shadow: none !important;
    background-image: none !important;
    filter: none !important;
}

/* ================================
   NAVIGATION OVERRIDES (Keep inheritance)
   ================================ */
.nav a,
.navbar a,
.menu a,
.dropdown-menu a {
    color: inherit !important;
}

.nav a:hover,
.navbar a:hover,
.menu a:hover {
    color: inherit !important;
    text-decoration: none !important;
}

/* ================================
   END OF MINIMAL ACE OVERRIDES
   Total: ~100 lines of strategic !important declarations
   Only essential overrides to combat ace.min.css
   ================================ */
