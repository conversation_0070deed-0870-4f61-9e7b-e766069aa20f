/* ================================
   MINIMAL ACE OVERRIDES
   Strategic !important declarations to combat ace.min.css
   Only essential overrides for 240px sidebar layout
   ================================ */

/* ================================
   LAYOUT OVERRIDES (Essential)
   ================================ */
.sidebar + .main-content,
.main-content {
    margin-left: var(--sidebar-width) !important;
    width: calc(100% - var(--sidebar-width)) !important;
}

.main-container {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* ================================
   BUTTON ICON FIXES (ACE makes icons block/float)
   ================================ */
.btn .ace-icon,
.btn i,
.navbar .btn .ace-icon,
.navbar .btn i {
    display: inline !important;
    vertical-align: middle !important;
    margin-right: var(--space-2) !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    float: none !important;
}

.btn span,
.navbar .btn span {
    display: inline !important;
    vertical-align: middle !important;
}

/* ================================
   BUTTON OVERRIDES (Essential)
   ================================ */
.btn,
button,
input[type="submit"],
input[type="button"] {
    background-color: var(--link-color) !important;
    color: white !important;
    border: none !important;
    border-radius: var(--border-radius-md) !important;
    padding: 10px var(--space-4) !important;
    font-size: var(--md-label-large) !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    cursor: pointer !important;
    transition: all var(--transition-fast) !important;
    box-shadow: none !important;
    background-image: none !important;
    filter: none !important;
    text-shadow: none !important;
}

.btn:hover,
button:hover,
input[type="submit"]:hover,
input[type="button"]:hover {
    background-color: var(--hover-color) !important;
    color: white !important;
    text-decoration: none !important;
    box-shadow: none !important;
    background-image: none !important;
    filter: none !important;
}

.btn:active,
.btn:focus,
button:active,
button:focus,
input[type="submit"]:active,
input[type="submit"]:focus,
input[type="button"]:active,
input[type="button"]:focus {
    background-color: var(--active-color) !important;
    box-shadow: none !important;
    background-image: none !important;
    filter: none !important;
    outline: none !important;
}

/* ================================
   WIDGET OVERRIDES (Priority #2)
   ================================ */
.widget-box {
    box-shadow: none !important;
    background-image: none !important;
    filter: none !important;
    max-width: 100% !important;
    overflow-x: auto !important;
}

.widget-header {
    background-image: none !important;
    text-shadow: none !important;
}

/* ================================
   TABLE OVERRIDES (Priority #3)
   ================================ */
.table,
.table th,
.table td {
    box-shadow: none !important;
    text-shadow: none !important;
    background-image: none !important;
    filter: none !important;
}

.table th {
    background-image: none !important;
}

/* ================================
   FORM OVERRIDES (Essential)
   ================================ */
.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
textarea,
select {
    box-shadow: none !important;
    background-image: none !important;
    filter: none !important;
    border: 1px solid var(--md-outline-variant) !important;
    border-radius: var(--border-radius-md) !important;
    padding: var(--space-3) var(--space-4) !important;
    font-size: var(--md-body-medium) !important;
    background-color: var(--md-surface-container-lowest) !important;
    color: var(--md-on-surface) !important;
    transition: all var(--transition-fast) !important;
}

.form-control:hover,
input:hover,
textarea:hover,
select:hover {
    border-color: var(--md-outline) !important;
}

.form-control:focus,
input:focus,
textarea:focus,
select:focus {
    border-color: var(--link-color) !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

/* Search input specific overrides */
.nav-search-input,
#filter-search-txt,
#filter-bar-search-txt,
.input-sm,
.input-xs,
.input-md {
    border: 1px solid var(--md-outline-variant) !important;
    border-radius: var(--border-radius-md) !important;
    font-size: var(--md-body-medium) !important;
    background-color: var(--md-surface-container-lowest) !important;
    color: var(--md-on-surface) !important;
    box-shadow: none !important;
    transition: all var(--transition-fast) !important;
    min-height: 40px !important;
    height: 40px !important;
    width: 120px !important;
}

/* ================================
   NAVIGATION OVERRIDES (Keep inheritance)
   ================================ */
.nav a,
.navbar a,
.menu a,
.dropdown-menu a {
    color: inherit !important;
}

.nav a:hover,
.navbar a:hover,
.menu a:hover {
    color: inherit !important;
    text-decoration: none !important;
}

/* ================================
   END OF MINIMAL ACE OVERRIDES
   Total: ~100 lines of strategic !important declarations
   Only essential overrides to combat ace.min.css
   ================================ */
