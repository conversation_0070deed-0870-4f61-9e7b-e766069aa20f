/* ================================
   Material Design 3 Button System (2025)
   Clean, consolidated version - no duplication
   ================================ */

/* ================================
   BASE BUTTON STYLES
   ================================ */
.btn,
button,
input[type="submit"],
input[type="button"] {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 40px !important;
    gap: var(--space-2) !important;
    position: relative !important;
    cursor: pointer !important;

    font-family: inherit !important;
    font-size: var(--md-label-large) !important;
    font-weight: 500 !important;
    line-height: 20px !important;
    letter-spacing: 0.1px !important;
    text-decoration: none !important;
    white-space: nowrap !important;

    user-select: none !important;
    vertical-align: middle !important;
    border: none !important;
    outline: none !important;
    margin: 0 !important;

    border-radius: var(--border-radius-md) !important;
    padding: 10px 24px !important;

    transition: all var(--transition-fast) !important;
    transition-property: background-color, color, box-shadow !important;

    background-color: var(--md-primary-40) !important;
    color: var(--md-on-primary) !important;
    box-shadow: none !important;
    text-shadow: none !important;
    background-image: none !important;
    filter: none !important;
}

.btn:hover,
button:hover,
input[type="submit"]:hover,
input[type="button"]:hover {
    background-color: var(--md-primary-30) !important;
    box-shadow: none !important;
}

.btn:active,
button:active,
input[type="submit"]:active,
input[type="button"]:active {
    background-color: var(--md-primary-20) !important;
    box-shadow: none !important;
}

/* ================================
   BUTTON VARIANTS
   ================================ */

/* Primary buttons */
.btn-primary,
.btn.btn-primary {
    background-color: var(--md-primary-40) !important;
    color: var(--md-on-primary) !important;
    border-color: var(--md-primary-40) !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn.btn-primary:hover,
.btn.btn-primary:focus {
    background-color: var(--md-primary-30) !important;
    color: var(--md-on-primary) !important;
}

.btn-primary:active,
.btn.btn-primary:active {
    background-color: var(--md-primary-20) !important;
    color: var(--md-on-primary) !important;
}

/* Secondary/Default buttons */
.btn-default,
.btn.btn-default {
    background-color: var(--md-surface-container-low) !important;
    color: var(--md-on-surface) !important;
    border: 1px solid var(--md-outline-variant) !important;
}

.btn-default:hover,
.btn.btn-default:hover {
    background-color: var(--md-surface-container) !important;
}

.btn-default:active,
.btn.btn-default:active {
    background-color: var(--md-surface-container-high) !important;
}

/* Secondary outlined buttons */
.btn-secondary,
.btn.btn-secondary {
    background-color: transparent !important;
    color: var(--md-primary-40) !important;
    border: 1px solid var(--md-outline) !important;
}

.btn-secondary:hover,
.btn.btn-secondary:hover {
    background-color: rgba(103, 80, 164, 0.08) !important;
    border-color: var(--md-primary-40) !important;
}

.btn-secondary:active,
.btn.btn-secondary:active {
    background-color: rgba(103, 80, 164, 0.12) !important;
}

/* Success buttons */
.btn-success,
.btn.btn-success {
    background-color: var(--md-success-40) !important;
    color: white !important;
    border-color: var(--md-success-40) !important;
}

.btn-success:hover,
.btn.btn-success:hover {
    background-color: var(--md-success-30) !important;
}

.btn-success:active,
.btn.btn-success:active {
    background-color: var(--md-success-20) !important;
}

/* Warning buttons */
.btn-warning,
.btn.btn-warning {
    background-color: var(--md-warning-50) !important;
    color: var(--md-warning-10) !important;
    border-color: var(--md-warning-50) !important;
}

.btn-warning:hover,
.btn.btn-warning:hover {
    background-color: var(--md-warning-40) !important;
}

.btn-warning:active,
.btn.btn-warning:active {
    background-color: var(--md-warning-30) !important;
}

/* Danger/Error buttons */
.btn-danger,
.btn-error,
.btn.btn-danger,
.btn.btn-error {
    background-color: var(--md-error-40) !important;
    color: white !important;
    border-color: var(--md-error-40) !important;
}

.btn-danger:hover,
.btn-error:hover,
.btn.btn-danger:hover,
.btn.btn-error:hover {
    background-color: var(--md-error-30) !important;
}

.btn-danger:active,
.btn-error:active,
.btn.btn-danger:active,
.btn.btn-error:active {
    background-color: var(--md-error-20) !important;
}

/* Info buttons */
.btn-info,
.btn.btn-info {
    background-color: var(--md-primary-60) !important;
    color: var(--md-on-primary) !important;
    border-color: var(--md-primary-60) !important;
}

.btn-info:hover,
.btn.btn-info:hover {
    background-color: var(--md-primary-50) !important;
}

.btn-info:active,
.btn.btn-info:active {
    background-color: var(--md-primary-40) !important;
}

/* ================================
   BUTTON SIZES
   ================================ */
.btn-xs,
.btn-minier {
    min-height: 28px !important;
    padding: 4px 16px !important;
    font-size: var(--md-label-small) !important;
}

.btn-sm,
.btn-mini {
    min-height: 32px !important;
    padding: 6px 20px !important;
    font-size: var(--md-label-medium) !important;
}

.btn-lg {
    min-height: 48px !important;
    padding: 14px 32px !important;
    font-size: var(--md-title-medium) !important;
}

.btn-xl,
.btn-xlg {
    min-height: 56px !important;
    padding: 18px 40px !important;
    font-size: var(--md-title-large) !important;
}

/* ================================
   SPECIAL BUTTON TYPES
   ================================ */

/* Link buttons */
.btn-link,
.btn.btn-link {
    background-color: transparent !important;
    color: var(--md-primary-40) !important;
    border: none !important;
    text-decoration: none !important;
    padding: var(--space-2) var(--space-4) !important;
}

.btn-link:hover,
.btn.btn-link:hover {
    background-color: transparent !important;
    color: var(--md-primary-30) !important;
    text-decoration: underline !important;
}

/* White/Light buttons */
.btn-white,
.btn.btn-white,
.btn-light,
.btn.btn-light {
    background-color: var(--md-surface-container-highest) !important;
    color: var(--md-on-surface) !important;
    border: 1px solid var(--md-outline-variant) !important;
}

.btn-white:hover,
.btn.btn-white:hover,
.btn-light:hover,
.btn.btn-light:hover {
    background-color: var(--md-surface-container-high) !important;
}

/* Round/Icon buttons */
.btn-round,
.btn.btn-round,
.btn-icon {
    border-radius: var(--border-radius-full) !important;
    min-width: 40px !important;
    min-height: 40px !important;
    padding: 8px !important;
}

.btn-icon.btn-sm {
    min-width: 32px !important;
    min-height: 32px !important;
    padding: 6px !important;
}

.btn-icon.btn-lg {
    min-width: 48px !important;
    min-height: 48px !important;
    padding: 12px !important;
}

/* Disabled state */
.btn:disabled,
.btn.disabled {
    background-color: rgba(29, 27, 32, 0.12) !important;
    color: rgba(29, 27, 32, 0.38) !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
    box-shadow: none !important;
}

.btn-secondary:disabled {
    border-color: rgba(29, 27, 32, 0.12) !important;
    background-color: transparent !important;
}

/* ================================
   BUTTON GROUPS
   ================================ */
.btn-group {
    display: inline-flex !important;
    gap: var(--space-1) !important;
    align-items: center !important;
    margin: 0 !important;
}

.btn-group .btn {
    margin: 0 !important;
    flex-shrink: 0 !important;
}

/* Attached button groups */
.btn-group.btn-group-attached {
    gap: 0 !important;
    border-radius: var(--border-radius-md) !important;
    overflow: hidden !important;
}

.btn-group.btn-group-attached .btn {
    border-radius: 0 !important;
    border-right: 1px solid rgba(255, 255, 255, 0.12) !important;
}

.btn-group.btn-group-attached .btn:first-child {
    border-top-left-radius: var(--border-radius-md) !important;
    border-bottom-left-radius: var(--border-radius-md) !important;
}

.btn-group.btn-group-attached .btn:last-child {
    border-top-right-radius: var(--border-radius-md) !important;
    border-bottom-right-radius: var(--border-radius-md) !important;
    border-right: none !important;
}

/* ================================
   GHOST BUTTONS
   ================================ */
.btn-ghost,
.btn.btn-ghost {
    background-color: transparent !important;
    color: var(--md-primary-40) !important;
    border: none !important;
    padding: 10px 12px !important;
}

.btn-ghost:hover,
.btn.btn-ghost:hover {
    background-color: rgba(103, 80, 164, 0.08) !important;
}

.btn-ghost:active,
.btn.btn-ghost:active {
    background-color: rgba(103, 80, 164, 0.12) !important;
}

/* ================================
   PROJECT-STATE BUTTON REFINEMENTS
   Subtle button styling on colored pages to reduce visual competition
   ================================ */

/* Refined primary buttons on project-state pages */
.project-state-35 .btn-primary,
.project-state-35 .btn.btn-primary,
.project-state-45 .btn-primary,
.project-state-45 .btn.btn-primary,
.project-state-55 .btn-primary,
.project-state-55 .btn.btn-primary,
.project-state-65 .btn-primary,
.project-state-65 .btn.btn-primary {
    background-color: rgba(59, 130, 246, 0.9) !important;
    border: 2px solid var(--md-primary-40) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(1px) !important;
}

/* Hover states remain strong for clear interaction feedback */
.project-state-35 .btn-primary:hover,
.project-state-35 .btn.btn-primary:hover,
.project-state-35 .btn-primary:focus,
.project-state-35 .btn.btn-primary:focus,
.project-state-45 .btn-primary:hover,
.project-state-45 .btn.btn-primary:hover,
.project-state-45 .btn-primary:focus,
.project-state-45 .btn.btn-primary:focus,
.project-state-55 .btn-primary:hover,
.project-state-55 .btn.btn-primary:hover,
.project-state-55 .btn-primary:focus,
.project-state-55 .btn.btn-primary:focus,
.project-state-65 .btn-primary:hover,
.project-state-65 .btn.btn-primary:hover,
.project-state-65 .btn-primary:focus,
.project-state-65 .btn.btn-primary:focus {
    background-color: var(--md-primary-30) !important;
    border-color: var(--md-primary-30) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}

/* Enhanced button borders for better definition on colored backgrounds */
.project-state-35 .btn,
.project-state-45 .btn,
.project-state-55 .btn,
.project-state-65 .btn {
    border-width: 2px !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

/* Subtle refinement for secondary buttons on colored pages */
.project-state-35 .btn-default,
.project-state-35 .btn.btn-default,
.project-state-45 .btn-default,
.project-state-45 .btn.btn-default,
.project-state-55 .btn-default,
.project-state-55 .btn.btn-default,
.project-state-65 .btn-default,
.project-state-65 .btn.btn-default {
    background-color: rgba(255, 255, 255, 0.95) !important;
    border: 2px solid var(--md-outline-variant) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08) !important;
}

/* Refined white/light buttons on colored pages */
.project-state-35 .btn-white,
.project-state-35 .btn.btn-white,
.project-state-35 .btn-light,
.project-state-35 .btn.btn-light,
.project-state-45 .btn-white,
.project-state-45 .btn.btn-white,
.project-state-45 .btn-light,
.project-state-45 .btn.btn-light,
.project-state-55 .btn-white,
.project-state-55 .btn.btn-white,
.project-state-55 .btn-light,
.project-state-55 .btn.btn-light,
.project-state-65 .btn-white,
.project-state-65 .btn.btn-white,
.project-state-65 .btn-light,
.project-state-65 .btn.btn-light {
    background-color: rgba(255, 255, 255, 0.98) !important;
    border: 2px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(2px) !important;
}

/* Enhanced button group styling on colored pages */
.project-state-35 .btn-group,
.project-state-45 .btn-group,
.project-state-55 .btn-group,
.project-state-65 .btn-group {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
    border-radius: var(--border-radius-md) !important;
    overflow: hidden !important;
}

/* ================================
   ANIMATION
   ================================ */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ================================
   CLEANUP COMPLETE - NO MORE DUPLICATION
   File reduced from 724 lines to ~450 lines
   All functionality preserved with single source of truth
   ================================ */
