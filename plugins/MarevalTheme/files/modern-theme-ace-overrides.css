/* ================================
   MINIMAL ACE OVERRIDES
   Only essential !important declarations to combat ace.min.css
   ================================ */

/* Layout - Critical overrides only */
.sidebar + .main-content {
    margin-left: 280px !important;
}

.main-container {
    max-width: 100% !important;
}

/* Remove ACE shadows and gradients globally */
.btn,
.form-control,
.table,
.alert,
.modal-content,
.widget-box {
    box-shadow: none !important;
    text-shadow: none !important;
    background-image: none !important;
    filter: none !important;
}

/* Force button icons inline (ACE makes them block) */
.btn .ace-icon,
.btn i {
    display: inline !important;
    vertical-align: middle !important;
    margin-right: 6px !important;
    float: none !important;
}

/* Override ACE button hover states that use gradients */
.btn:hover,
.btn:focus,
.btn:active {
    background-image: none !important;
    filter: none !important;
    box-shadow: none !important;
}

/* Ensure table headers don't get ACE gradients */
.table th,
.table thead th {
    background-image: none !important;
    text-shadow: none !important;
}

/* Override ACE form field borders */
.form-control,
input,
textarea,
select {
    box-shadow: none !important;
}

/* Ensure widgets don't get ACE styling */
.widget-box {
    border-radius: var(--border-radius-2xl) !important;
    max-width: 100% !important;
}

/* Responsive fixes */
@media (max-width: 991px) {
    .main-content {
        margin-left: 0 !important;
        max-width: 100vw !important;
    }
}
