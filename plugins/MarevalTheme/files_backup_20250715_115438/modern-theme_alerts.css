/* ================================
   Material Design 3 Alert System (2025)
   Based on Google Material You Guidelines
   ================================ */

.alert {
    padding: var(--space-4) var(--space-5) !important;
    border-radius: var(--border-radius) !important;
    text-shadow: none !important;
    box-shadow: none !important;
    margin-bottom: var(--space-6) !important;
    border: 1px solid var(--md-outline-variant) !important;
    background-color: var(--md-surface-container) !important;
    color: var(--md-on-surface) !important;
    font-size: var(--md-body-medium) !important;
    line-height: 1.5;
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
}

.alert .close {
    position: absolute;
    top: var(--space-3) !important;
    right: var(--space-3) !important;
    line-height: 1;
    opacity: 0.6 !important;
    font-size: var(--md-title-medium) !important;
    color: var(--md-on-surface) !important;
    text-shadow: none !important;
    background: none !important;
    border: none !important;
    cursor: pointer;
    transition: opacity var(--transition-fast) !important;
    padding: var(--space-1) !important;
    border-radius: var(--border-radius-sm) !important;
}

.alert .close:hover,
.alert .close:focus {
    color: var(--md-on-surface) !important;
    text-decoration: none !important;
    cursor: pointer;
    opacity: 1 !important;
    background-color: rgba(29, 27, 32, 0.08) !important;
}

.alert-block p + p {
    margin-top: var(--space-2) !important;
}

/* Material 3 Alert Variants with Purple Theme Integration */
.alert-success {
    color: var(--md-success-20) !important;
    background-color: var(--md-success-95) !important;
    border-color: var(--md-success-80) !important;
}

.alert-info {
    color: var(--md-primary-20) !important;
    background-color: var(--md-primary-95) !important;
    border-color: var(--md-primary-80) !important;
}

.alert-warning {
    color: var(--md-warning-20) !important;
    background-color: var(--md-warning-95) !important;
    border-color: var(--md-warning-80) !important;
}

.alert-danger,
.alert-error {
    color: var(--md-error-20) !important;
    background-color: var(--md-error-95) !important;
    border-color: var(--md-error-80) !important;
}

/* Block alerts with better spacing */
.alert-block {
    padding: var(--space-5) var(--space-6) !important;
}

.alert-block > p,
.alert-block > ul {
    margin-bottom: 0 !important;
}

.alert-block p + p {
    margin-top: var(--space-2) !important;
}

/* Dismissible alerts with proper spacing */
.alert-dismissible {
    padding-right: var(--space-12) !important;
}

.alert-dismissible .close {
    right: var(--space-4) !important;
    top: var(--space-4) !important;
    color: inherit !important;
}

/* Material 3 Icons in alerts */
.alert .ace-icon {
    font-size: var(--md-title-medium) !important;
    margin-right: var(--space-2) !important;
    opacity: 0.8;
    flex-shrink: 0;
}

/* Enhanced alert styling for better Material 3 look */
.alert-success .ace-icon {
    color: var(--md-success-40) !important;
}

.alert-info .ace-icon {
    color: var(--md-primary-40) !important;
}

.alert-warning .ace-icon {
    color: var(--md-warning-40) !important;
}

.alert-danger .ace-icon,
.alert-error .ace-icon {
    color: var(--md-error-40) !important;
}