/* ================================
   Helper Utilities (from ace.css)
   ================================ */

/* ================================
   GLOBAL CONTENT LINKS - PROJECT STATE THEMING
   ================================ */
a {
    color: white !important;
    text-decoration: none !important;
    transition: var(--transition-fast) !important;
}

a:hover {
    color: var(--hover-color) !important;
    text-decoration: underline !important;
}

a:focus,
a:active {
    color: var(--active-color) !important;
}

/* Ensure content links use project state colors */
body .main-content a,
body .widget-main a,
body .content a {
    color: inherit !important;
}

body .main-content a:hover,
body .widget-main a:hover,
body .content a:hover {
    color: var(--hover-color) !important;
}

/* ================================
   TYPOGRAPHY AND WEIGHT
   ================================ */
.lighter {
    font-weight: lighter;
}
.bolder {
    font-weight: bolder;
}

/* Display */
.inline {
    display: inline-block !important;
}
.block {
    display: block !important;
}

/* Text Alignment */
.center,
.align-center {
    text-align: center !important;
}
.align-left {
    text-align: left !important;
}
.align-right {
    text-align: right !important;
}
.align-justify {
    text-align: justify;
}

/* Vertical Alignment */
.middle {
    vertical-align: middle;
}
.align-middle {
    vertical-align: middle !important;
}
.align-top {
    vertical-align: top !important;
}
.align-bottom {
    vertical-align: bottom !important;
}

/* Positioning */
.position-relative,
.pos-rel {
    position: relative;
}
.position-absolute,
.pos-abs {
    position: absolute;
}
.no-float {
    float: none !important;
}

/* Line Height */
.line-height-normal {
    line-height: normal !important;
}
.line-height-0 {
    line-height: 0 !important;
}
.line-height-1 {
    line-height: 1 !important;
}
.line-height-125 {
    line-height: 1.25 !important;
}
.line-height-150 {
    line-height: 1.5 !important;
}

/* Colors */
.dark {
    color: #333 !important;
}
.white {
    color: #FFF !important;
}
.red {
    color: #DD5A43 !important;
}
.red2 {
    color: #E08374 !important;
}
.light-red {
    color: #FF7777 !important;
}
.blue {
    color: #478FCA !important;
}
.light-blue {
    color: #93CBF9 !important;
}
.green {
    color: #69AA46 !important;
}
.light-green {
    color: #B0D877 !important;
}
.orange {
    color: #FF892A !important;
}
.orange2 {
    color: #FEB902 !important;
}
.light-orange {
    color: #FCAC6F !important;
}
.purple {
    color: #A069C3 !important;
}
.pink {
    color: #C6699F !important;
}
.pink2 {
    color: #D6487E !important;
}
.brown {
    color: brown !important;
}
.grey {
    color: #777 !important;
}
.light-grey {
    color: #BBB !important;
}

/* Font Sizes */
.bigger-110 { font-size: 110% !important; }
.bigger-120 { font-size: 120% !important; }
.bigger-130 { font-size: 130% !important; }
.bigger-140 { font-size: 140% !important; }
.bigger-150 { font-size: 150% !important; }
.bigger-160 { font-size: 160% !important; }
.bigger-170 { font-size: 170% !important; }
.bigger-180 { font-size: 180% !important; }
.bigger-190 { font-size: 190% !important; }
.bigger-200 { font-size: 200% !important; }
.bigger-210 { font-size: 210% !important; }
.bigger-220 { font-size: 220% !important; }
.bigger-230 { font-size: 230% !important; }
.bigger-240 { font-size: 240% !important; }
.bigger-250 { font-size: 250% !important; }
.bigger-260 { font-size: 260% !important; }
.bigger-270 { font-size: 270% !important; }
.bigger-280 { font-size: 280% !important; }
.bigger-290 { font-size: 290% !important; }
.bigger-300 { font-size: 300% !important; }
.bigger-115 { font-size: 115% !important; }
.bigger-125 { font-size: 125% !important; }
.bigger-175 { font-size: 175% !important; }
.bigger-225 { font-size: 225% !important; }
.bigger-275 { font-size: 275% !important; }

.smaller-90 { font-size: 90% !important; }
.smaller-80 { font-size: 80% !important; }
.smaller-70 { font-size: 70% !important; }
.smaller-60 { font-size: 60% !important; }
.smaller-50 { font-size: 50% !important; }
.smaller-40 { font-size: 40% !important; }
.smaller-30 { font-size: 30% !important; }
.smaller-20 { font-size: 20% !important; }
.smaller-75 { font-size: 75% !important; }

/* Widths */
.width-20 { width: 20% !important; }
.width-25 { width: 25% !important; }
.width-30 { width: 30% !important; }
.width-35 { width: 35% !important; }
.width-40 { width: 40% !important; }
.width-45 { width: 45% !important; }
.width-50 { width: 50% !important; }
.width-55 { width: 55% !important; }
.width-60 { width: 60% !important; }
.width-65 { width: 65% !important; }
.width-70 { width: 70% !important; }
.width-75 { width: 75% !important; }
.width-80 { width: 80% !important; }
.width-85 { width: 85% !important; }
.width-90 { width: 90% !important; }
.width-95 { width: 95% !important; }
.width-100 { width: 100% !important; }
.width-auto { width: auto !important; }
.height-auto { height: auto !important; }

/* Spacing */
.no-padding { padding: 0 !important; }
.no-padding-bottom { padding-bottom: 0 !important; }
.no-padding-top { padding-top: 0 !important; }
.no-padding-left { padding-left: 0 !important; }
.no-padding-right { padding-right: 0 !important; }

.no-margin { margin: 0 !important; }
.no-margin-bottom { margin-bottom: 0 !important; }
.no-margin-top { margin-top: 0 !important; }
.no-margin-left { margin-left: 0 !important; }
.no-margin-right { margin-right: 0 !important; }

/* Borders */
.no-border { border-width: 0; }
.no-border-bottom { border-bottom-width: 0; }
.no-border-top { border-top-width: 0; }
.no-border-left { border-left-width: 0; }
.no-border-right { border-right-width: 0; }

/* Overflow */
.overflow-hidden { overflow: hidden !important; }
.overflow-scroll {
    overflow-x: hidden !important;
    overflow-y: scroll !important;
}

/* Misc */
.no-radius { border-radius: 0 !important; }
.no-text-shadow { text-shadow: none !important; }

/* Horizontal Rules */
.hr {
    display: block;
    height: 0;
    overflow: hidden;
    font-size: 0;
    border-width: 1px 0 0 0;
    border-top: 1px solid #E3E3E3;
    margin: 12px 0;
    border-top-color: rgba(0, 0, 0, 0.11);
}
.hr-double {
    height: 3px;
    border-top: 1px solid #E3E3E3;
    border-bottom: 1px solid #E3E3E3;
    border-top-color: rgba(0, 0, 0, 0.11);
    border-bottom-color: rgba(0, 0, 0, 0.11);
}
.hr.dotted,
.hr-dotted { border-style: dotted; }

/* HR Margins */
.hr-32, .hr32 { margin: 32px 0; }
.hr-30, .hr30 { margin: 30px 0; }
.hr-28, .hr28 { margin: 28px 0; }
.hr-26, .hr26 { margin: 26px 0; }
.hr-24, .hr24 { margin: 24px 0; }
.hr-22, .hr22 { margin: 22px 0; }
.hr-20, .hr20 { margin: 20px 0; }
.hr-18, .hr18 { margin: 18px 0; }
.hr-16, .hr16 { margin: 16px 0; }
.hr-14, .hr14 { margin: 14px 0; }
.hr-12, .hr12 { margin: 12px 0; }
.hr-10, .hr10 { margin: 10px 0; }
.hr-8, .hr8 { margin: 8px 0; }
.hr-6, .hr6 { margin: 6px 0; }
.hr-4, .hr4 { margin: 4px 0; }
.hr-2, .hr2 { margin: 2px 0; }

/* Space Classes */
.space-32, .space-30, .space-28, .space-26, .space-24, .space-22, .space-20,
.space-18, .space-16, .space-14, .space-12, .space-10, .space-8, .space-6,
.space-4, .space-2, .space-0 {
    max-height: 1px;
    min-height: 1px;
    overflow: hidden;
    margin: 12px 0;
}
.space-32 { margin: 32px 0 31px; }
.space-30 { margin: 30px 0 29px; }
.space-28 { margin: 28px 0 27px; }
.space-26 { margin: 26px 0 25px; }
.space-24 { margin: 24px 0 23px; }
.space-22 { margin: 22px 0 21px; }
.space-20 { margin: 20px 0 19px; }
.space-18 { margin: 18px 0 17px; }
.space-16 { margin: 16px 0 15px; }
.space-14 { margin: 14px 0 13px; }
.space-12 { margin: 12px 0 11px; }
.space-10 { margin: 10px 0 9px; }
.space-8 { margin: 8px 0 7px; }
.space-6 { margin: 6px 0 5px; }
.space-4 { margin: 4px 0 3px; }
.space-2 { margin: 2px 0 1px; }
.space-0 { margin: 0; }

/* ================================
   Material Design 3 Widget System (2025)
   Based on Google Material You Guidelines
   ================================ */

/* Override widget header colors to use Material 3 purple theme */
.widget-header {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    position: relative;
    min-height: 48px;
    background: var(--md-surface-container);
    color: var(--md-primary-40);
    border-bottom: 1px solid var(--md-outline-variant);
    padding-left: var(--space-4);
    border-radius: var(--border-radius-xs) var(--border-radius-xs) 0 0;
}

.widget-header:before,
.widget-header:after {
    content: "";
    display: table;
    line-height: 0;
}
.widget-header:after {
    clear: right;
}

.widget-box.collapsed > .widget-header {
    border-bottom-width: 0;
    border-radius: var(--border-radius-xs);
}

.collapsed.fullscreen > .widget-header {
    border-bottom-width: 1px;
}

.widget-header-flat {
    background: var(--md-surface-container-low);
}

.widget-header-large {
    min-height: 56px;
    padding-left: var(--space-5);
}

.widget-header-small {
    min-height: 40px;
    padding-left: var(--space-3);
}

.widget-header > .widget-title {
    line-height: 48px;
    padding: 0;
    margin: 0;
    display: inline;
    font-size: var(--md-title-medium);
    font-weight: 500;
    color: var(--md-primary-40);
}

.widget-header > .widget-title > .ace-icon {
    margin-right: var(--space-2);
    font-weight: normal;
    display: inline-block;
    color: var(--md-primary-40);
}

.widget-header-large > .widget-title {
    line-height: 56px;
    font-size: var(--md-title-large);
}

.widget-header-small > .widget-title {
    line-height: 40px;
    font-size: var(--md-title-small);
}

.widget-toolbar {
    display: inline-block;
    padding: 0 var(--space-3);
    line-height: 48px;
    float: right;
    position: relative;
}

.widget-header-large > .widget-toolbar {
    line-height: 56px;
}

.widget-header-small > .widget-toolbar {
    line-height: 40px;
}

.widget-toolbar.no-padding {
    padding: 0;
}

/* Material 3 Widget Box */
.widget-box {
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    border: 1px solid var(--md-outline-variant);
    background-color: var(--md-surface-container-lowest);
    overflow: hidden;
}

.widget-body {
    background-color: var(--md-surface-container-lowest);
}

.widget-main {
    padding: var(--space-4);
    background-color: var(--md-surface-container-lowest);
}

/* Material 3 Widget Color Variants */
.widget-color-blue > .widget-header,
.widget-color-blue2 > .widget-header {
    background: var(--md-primary-40);
    color: var(--md-on-primary);
    border-bottom-color: var(--md-primary-30);
}

.widget-color-blue > .widget-header > .widget-title,
.widget-color-blue2 > .widget-header > .widget-title,
.widget-color-blue > .widget-header > .widget-title > .ace-icon,
.widget-color-blue2 > .widget-header > .widget-title > .ace-icon {
    color: var(--md-on-primary);
}

.widget-color-green > .widget-header {
    background: var(--md-success-40);
    color: white;
    border-bottom-color: var(--md-success-30);
}

.widget-color-red > .widget-header {
    background: var(--md-error-40);
    color: white;
    border-bottom-color: var(--md-error-30);
}

.widget-color-orange > .widget-header {
    background: var(--md-warning-50);
    color: var(--md-warning-10);
    border-bottom-color: var(--md-warning-40);
}

.widget-color-purple > .widget-header {
    background: var(--md-tertiary-40);
    color: white;
    border-bottom-color: var(--md-tertiary-30);
}

/* ================================
   Material Design 3 Global Color Overrides (2025)
   Replace all blue accents with purple theme
   ================================ */

/* Links and text colors */
a {
    color: var(--md-primary-40);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover,
a:focus {
    color: var(--md-primary-30);
    text-decoration: none;
}

a:visited {
    color: var(--md-primary-50);
}

/* Override blue colors in various components */
.blue,
.text-primary {
    color: var(--md-primary-40) !important;
}

.bg-primary {
    background-color: var(--md-primary-40) !important;
    color: var(--md-on-primary) !important;
}

/* Badge and label overrides */
.badge-primary,
.label-primary {
    background-color: var(--md-primary-40);
    color: var(--md-on-primary);
}

.badge-info,
.label-info {
    background-color: var(--md-secondary-40);
    color: var(--md-on-primary);
}

/* Alert overrides */
.alert-info {
    background-color: var(--md-secondary-95);
    border-color: var(--md-secondary-80);
    color: var(--md-secondary-10);
}

/* Progress bar overrides */
.progress-bar,
.progress-bar-primary {
    background-color: var(--md-primary-40);
}

.progress-bar-info {
    background-color: var(--md-secondary-40);
}

/* Tab overrides */
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
    color: var(--md-primary-40);
    border-bottom-color: var(--md-primary-40);
}

.nav-tabs > li > a:hover {
    border-color: var(--md-outline-variant);
    color: var(--md-primary-40);
}

/* Dropdown overrides */
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus {
    background-color: var(--md-primary-40);
    color: var(--md-on-primary);
}

/* Form focus overrides */
.form-control:focus,
input:focus,
textarea:focus,
select:focus {
    border-color: var(--md-primary-40) !important;
    box-shadow: 0 0 0 2px rgba(103, 80, 164, 0.12) !important;
}

/* Icon colors */
.ace-icon.blue {
    color: var(--md-primary-40) !important;
}

/* Table header specific overrides */
.table > thead > tr > th {
    color: var(--md-primary-40) !important;
    background-color: var(--md-surface-container) !important;
}

/* Card/Panel header overrides */
.panel-primary > .panel-heading {
    background-color: var(--md-primary-40);
    border-color: var(--md-primary-40);
    color: var(--md-on-primary);
}

.panel-info > .panel-heading {
    background-color: var(--md-secondary-40);
    border-color: var(--md-secondary-40);
    color: var(--md-on-primary);
}

/* Generic blue color overrides */
.blue2,
.blue3,
.blue4 {
    color: var(--md-primary-40) !important;
}

/* Input group addon overrides */
.input-group-addon {
    background-color: var(--md-surface-container);
    border-color: var(--md-outline-variant);
    color: var(--md-on-surface-variant);
}

/* Well overrides */
.well {
    background-color: var(--md-surface-container-low);
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--border-radius);
}

/* Override any remaining hard-coded blue colors */
[style*="#2679B5"],
[style*="#438EB9"],
[style*="#2E6589"],
[style*="#669FC7"],
[style*="#2283C5"],
[style*="#6FAED9"] {
    color: var(--md-primary-40) !important;
}

/* ================================
   Material Design 3 Layout Fixes (2025)
   Ensure proper table and container sizing
   ================================ */

/* Force tables and containers to use full width */
.widget-box,
.widget-body,
.widget-main,
.table-responsive,
.content-wrapper,
.main-content {
    width: 100% !important;
    max-width: 100% !important;
}

/* Ensure table containers stretch */
.dataTables_wrapper,
.dataTable,
.table-container,
.table-wrapper {
    width: 100% !important;
}

/* Fix button group wrapping */
.btn-toolbar,
.button-group,
.action-buttons {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: var(--space-2) !important;
    align-items: center !important;
}

/* Responsive table improvements */
@media (max-width: 768px) {
    .table-responsive {
        font-size: var(--md-body-small);
    }
    
    .table th,
    .table td {
        padding: var(--space-2) var(--space-3);
        white-space: nowrap;
    }
    
    .btn-group {
        width: 100%;
        justify-content: center;
    }
}

/* Material 3 container system */
.container-fluid,
.container {
    padding-left: var(--space-4);
    padding-right: var(--space-4);
}

@media (min-width: 768px) {
    .container-fluid,
    .container {
        padding-left: var(--space-6);
        padding-right: var(--space-6);
    }
}