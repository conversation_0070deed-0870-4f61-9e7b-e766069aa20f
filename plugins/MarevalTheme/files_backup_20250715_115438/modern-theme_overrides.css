/* ================================
   CRITICAL OVERRIDES - Combat ace.min.css !important declarations
   This file uses !important strategically to override legacy ACE styles
   Load this LAST to ensure maximum specificity

   ================================ */

/* Layout Overrides */
.sidebar + .main-content {
    margin-left: 280px !important;
}

.main-container {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Fix button icons to be next to text, not above */
.btn .ace-icon,
.btn i,
.navbar .btn .ace-icon,
.navbar .btn i {
    display: inline !important;
    vertical-align: middle !important;
    margin-right: 6px !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    float: none !important;
}

/* Ensure button text stays inline with icon */
.btn span,
.navbar .btn span {
    display: inline !important;
    vertical-align: middle !important;
}

/* Widget Overrides */
.widget-box {
    margin-bottom: var(--space-6) !important;
    box-shadow: none !important;
    padding: var(--space-4) !important;
    max-width: 100% !important;
    overflow-x: auto !important;
}

/* Form Overrides */
.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
    border: 1px solid var(--md-outline-variant) !important;
    border-radius: var(--border-radius-md) !important;
    padding: var(--space-3) var(--space-4) !important;
    font-size: var(--md-body-medium) !important;
    background-color: var(--md-surface-container-lowest) !important;
    color: var(--md-on-surface) !important;
    box-shadow: none !important;
    transition: all var(--transition-fast) !important;
}

/* Specific search input overrides */
.nav-search-input,
#filter-search-txt,
#filter-bar-search-txt,
.input-sm,
.input-xs,
.input-md {
    border: 1px solid var(--md-outline-variant) !important;
    border-radius: var(--border-radius-md) !important;
    font-size: var(--md-body-medium) !important;
    background-color: var(--md-surface-container-lowest) !important;
    color: var(--md-on-surface) !important;
    box-shadow: none !important;
    transition: all var(--transition-fast) !important;
    min-height: 40px !important;
    height: 40px !important;
    width: 120px !important;
}

/* Hover states for all inputs */
.form-control:hover,
input:hover,
textarea:hover,
select:hover,
.nav-search-input:hover,
#filter-search-txt:hover,
#filter-bar-search-txt:hover,
.input-sm:hover,
.input-xs:hover,
.input-md:hover {
    border-color: var(--md-outline) !important;
}

/* Keep navigation and menu links with their default/inherited colors */
.nav a,
.navbar a,
.dropdown-menu a,
.nav-list a,
.menu a,
.sidebar a,
.widget-header a,
.widget-title a,
.breadcrumb a,
.pagination a,
.btn a {
    color: inherit !important;
}

.nav a:hover,
.navbar a:hover,
.dropdown-menu a:hover,
.nav-list a:hover,
.menu a:hover,
.sidebar a:hover,
.widget-header a:hover,
.widget-title a:hover,
.breadcrumb a:hover,
.pagination a:hover,
.btn a:hover {
    color: inherit !important;
}

/* Ensure Material 3 rounded corners everywhere */
.btn,
.form-control,
.alert,
.modal-content,
.widget-box {
    border-radius: var(--border-radius-2xl) !important;
}

/* Material 3 spacing consistency */
.widget-box,
.form-group {
    margin-bottom: var(--space-6) !important;
}

.btn {
    padding: var(--space-3) var(--space-16) !important;
    min-height: 40px !important;
}

/* Ensure proper Material 3 typography */
body,
.form-control,
.btn {
    font-family: "Roboto", "Google Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif !important;
}

/* Typography Overrides */
body {
    font-family: var(--md-font-family) !important;
    font-size: var(--md-body-large) !important;
    line-height: var(--md-line-height-normal) !important;
    color: var(--md-on-surface) !important;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--md-font-family) !important;
    font-weight: 600 !important;
    color: inherit !important;
    margin-bottom: var(--space-3) !important;
}

/* Responsive Overrides */
@media (max-width: 991px) {
    .main-content {
        margin-left: 0 !important;
        max-width: 100vw !important;
    }
}

@media (min-width: 992px) {
    .sidebar.compact + .main-content {
        margin-left: 280px !important;
        max-width: calc(100vw - 280px) !important;
    }
    
    .sidebar.menu-min + .main-content {
        margin-left: 90px !important;
        max-width: calc(100vw - 90px) !important;
    }
    .sidebar.compact .nav-list>li>a>.menu-icon {
        display: flex !important;
    }   
}

/* Color and Theme Overrides */
.page-content {
    background-color: var(--md-surface) !important;
    color: var(--md-on-surface) !important;
}

/* Navigation Overrides */
.nav-list > li > a {
    color: var(--md-on-surface-variant) !important;
    text-decoration: none !important;
    border-radius: var(--border-radius) !important;
    margin: var(--space-1) !important;
    padding: var(--space-3) !important;
    transition: all var(--transition-fast) !important;
}

.nav-list > li.active > a {
    background-color: var(--md-primary-40) !important;
    color: var(--md-on-primary) !important;
}

/* ================================
   COMPREHENSIVE BUTTON SYSTEM
   ================================ */

/* Critical ace.min.css overrides for ALL button variants */
.btn,
.btn.btn-default,
.btn.btn-primary,
.btn.btn-success,
.btn.btn-info,
.btn.btn-warning,
.btn.btn-danger,
.btn.btn-inverse,
.btn.btn-pink,
.btn.btn-purple,
.btn.btn-grey,
.btn.btn-yellow,
.btn.btn-light {
    box-shadow: none !important;
    text-shadow: none !important;
    background-image: none !important;
    filter: none !important;
    border-radius: var(--border-radius-md) !important;
}

/* Button sizes */
.btn-lg,
.btn-xlg,
.btn-sm,
.btn-xs,
.btn-mini,
.btn-minier {
    box-shadow: none !important;
    text-shadow: none !important;
    background-image: none !important;
    filter: none !important;
}

/* Link buttons - most generic, lowest priority */
.btn.btn-link {
    background-color: transparent !important;
    color: var(--md-primary-40) !important;
    border: none !important;
    text-decoration: none !important;
    padding: var(--space-2) var(--space-8) !important;
}

.btn.btn-link:hover,
.btn.btn-link:focus {
    background-color: transparent !important;
    color: var(--md-primary-30) !important;
    text-decoration: underline !important;
}

.btn.btn-link:active {
    background-color: transparent !important;
    color: var(--md-primary-20) !important;
}

/* White buttons - medium priority */
.btn.btn-white {
    background-color: var(--md-surface-container-highest) !important;
    color: var(--md-on-surface) !important;
    border: 1px solid var(--md-outline-variant) !important;
}

.btn.btn-white:hover,
.btn.btn-white:focus {
    background-color: var(--md-surface-container-high) !important;
    color: var(--md-on-surface) !important;
}

.btn.btn-white:active {
    background-color: var(--md-surface-container) !important;
    color: var(--md-on-surface) !important;
}

/* Round buttons - highest priority, should override white when both applied */
.btn.btn-round {
    border-radius: var(--border-radius-full) !important;
    background-color: var(--md-primary-40) !important;
    color: var(--md-on-primary) !important;
    border-color: var(--md-primary-40) !important;
    min-height: 40px !important;
    padding: var(--space-2) !important;
}

.btn.btn-round:hover,
.btn.btn-round:focus {
    background-color: var(--md-primary-30) !important;
    color: var(--md-on-primary) !important;
}

.btn.btn-round:active {
    background-color: var(--md-primary-20) !important;
    color: var(--md-on-primary) !important;
}

/* Compound classes - These should override individual classes */
.btn.btn-round.btn-white {
    border-radius: var(--border-radius-full) !important;
    background-color: var(--md-primary-40) !important;
    color: var(--md-primary-100) !important;
    border: 1px solid var(--md-outline-variant) !important;
}

.btn.btn-round.btn-white:hover,
.btn.btn-round.btn-white:focus {
    background-color: var(--md-surface-container-high) !important;
    color: var(--md-on-surface) !important;
}

.btn.btn-round.btn-white:active {
    background-color: var(--md-surface-container) !important;
    color: var(--md-on-surface) !important;
}

/* Primary buttons */
.btn-primary,
.btn.btn-primary {
    background-color: var(--md-primary-40) !important;
    color: var(--md-on-primary) !important;
    border-color: var(--md-primary-40) !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn.btn-primary:hover,
.btn.btn-primary:focus {
    background-color: var(--md-primary-30) !important;
    color: var(--md-on-primary) !important;
}

.btn-primary:active,
.btn.btn-primary:active {
    background-color: var(--md-primary-20) !important;
    color: var(--md-on-primary) !important;
}

/* Success buttons */
.btn-success,
.btn.btn-success {
    background-color: var(--md-success-40) !important;
    color: white !important;
    border-color: var(--md-success-40) !important;
}

.btn-success:hover,
.btn-success:focus,
.btn.btn-success:hover,
.btn.btn-success:focus {
    background-color: var(--md-success-30) !important;
    color: white !important;
}

.btn-success:active,
.btn.btn-success:active {
    background-color: var(--md-success-20) !important;
    color: white !important;
}

/* Warning buttons */
.btn-warning,
.btn.btn-warning {
    background-color: var(--md-warning-50) !important;
    color: var(--md-warning-10) !important;
    border-color: var(--md-warning-50) !important;
}

.btn-warning:hover,
.btn-warning:focus,
.btn.btn-warning:hover,
.btn.btn-warning:focus {
    background-color: var(--md-warning-40) !important;
    color: var(--md-warning-10) !important;
}

.btn-warning:active,
.btn.btn-warning:active {
    background-color: var(--md-warning-30) !important;
    color: var(--md-warning-10) !important;
}

/* Danger/Error buttons */
.btn-danger,
.btn-error,
.btn.btn-danger,
.btn.btn-error {
    background-color: var(--md-error-40) !important;
    color: white !important;
    border-color: var(--md-error-40) !important;
}

.btn-danger:hover,
.btn-error:hover,
.btn-danger:focus,
.btn-error:focus,
.btn.btn-danger:hover,
.btn.btn-error:hover,
.btn.btn-danger:focus,
.btn.btn-error:focus {
    background-color: var(--md-error-30) !important;
    color: white !important;
}

.btn-danger:active,
.btn-error:active,
.btn.btn-danger:active,
.btn.btn-error:active {
    background-color: var(--md-error-20) !important;
    color: white !important;
}

/* Info buttons */
.btn-info,
.btn-info2,
.btn.btn-info,
.btn.btn-info2 {
    background-color: var(--md-primary-60) !important;
    color: var(--md-on-primary) !important;
    border-color: var(--md-primary-60) !important;
}

.btn-info:hover,
.btn-info2:hover,
.btn-info:focus,
.btn-info2:focus,
.btn.btn-info:hover,
.btn.btn-info2:hover,
.btn.btn-info:focus,
.btn.btn-info2:focus {
    background-color: var(--md-primary-50) !important;
    color: var(--md-on-primary) !important;
    border-color: var(--md-primary-50) !important;
}

.btn-info:active,
.btn-info2:active,
.btn.btn-info:active,
.btn.btn-info2:active {
    background-color: var(--md-primary-40) !important;
    color: var(--md-on-primary) !important;
    border-color: var(--md-primary-40) !important;
}

/* Additional ACE button variants */
.btn-inverse,
.btn.btn-inverse {
    background-color: var(--md-surface-container-lowest) !important;
    color: var(--md-on-surface) !important;
}

.btn-pink,
.btn.btn-pink {
    background-color: #E91E63 !important;
    color: white !important;
    border-color: #E91E63 !important;
}

.btn-purple,
.btn.btn-purple {
    background-color: var(--md-primary-40) !important;
    color: var(--md-on-primary) !important;
    border-color: var(--md-primary-40) !important;
}

.btn-grey,
.btn.btn-grey {
    background-color: var(--md-surface-container-high) !important;
    color: var(--md-on-surface) !important;
    border-color: var(--md-outline-variant) !important;
}

.btn-yellow,
.btn.btn-yellow {
    background-color: var(--md-warning-60) !important;
    color: var(--md-warning-10) !important;
    border-color: var(--md-warning-60) !important;
}

.btn-light,
.btn.btn-light {
    background-color: var(--md-surface-container-highest) !important;
    color: var(--md-on-surface) !important;
    border-color: var(--md-outline-variant) !important;
}

/* Default button state overrides */
.btn,
.btn-default,
.btn.btn-default {
    background-color: var(--md-surface-container-low) !important;
    color: var(--md-on-surface) !important;
    border-color: var(--md-outline-variant) !important;
}

.btn:hover,
.btn-default:hover,
.btn:focus,
.btn-default:focus,
.btn.btn-default:hover,
.btn.btn-default:focus {
    background-color: var(--md-surface-container) !important;
    color: var(--md-on-surface) !important;
}

.btn:active,
.btn-default:active,
.btn.btn-default:active {
    background-color: var(--md-surface-container-high) !important;
    color: var(--md-on-surface) !important;
}

/* Comprehensive border-color freeze - no border color changes on hover/focus/active */
.btn:hover,
.btn:focus,
.btn:active,
.btn.btn-default:hover,
.btn.btn-default:focus,
.btn.btn-default:active,
.btn.btn-primary:hover,
.btn.btn-primary:focus,
.btn.btn-primary:active,
.btn.btn-success:hover,
.btn.btn-success:focus,
.btn.btn-success:active,
.btn.btn-warning:hover,
.btn.btn-warning:focus,
.btn.btn-warning:active,
.btn.btn-danger:hover,
.btn.btn-danger:focus,
.btn.btn-danger:active,
.btn.btn-info:hover,
.btn.btn-info:focus,
.btn.btn-info:active,
.btn.btn-inverse:hover,
.btn.btn-inverse:focus,
.btn.btn-inverse:active,
.btn.btn-white:hover,
.btn.btn-white:focus,
.btn.btn-white:active,
.btn.btn-round:hover,
.btn.btn-round:focus,
.btn.btn-round:active,
.btn.btn-link:hover,
.btn.btn-link:focus,
.btn.btn-link:active,
input[type="submit"]:hover,
input[type="submit"]:focus,
input[type="submit"]:active,
button:hover,
button:focus,
button:active {
    border-color: inherit !important;
}

/* Material 3 Segmented Button Groups - Create Pill Design */
.btn-group {
    display: inline-flex !important;
    gap: 0 !important;
    align-items: center !important;
    margin: 0 !important;
    border-radius: 0 !important;
    overflow: hidden !important;
    box-shadow: none !important;
}

.btn-group .btn {
    margin: 0 !important;
    flex-shrink: 0 !important;
    border-radius: 0 !important;
    border-right: 1px solid rgba(255, 255, 255, 0.12) !important;
    position: relative !important;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius-md) !important;
    border-bottom-left-radius: var(--border-radius-md) !important;
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius-md) !important;
    border-bottom-right-radius: var(--border-radius-md) !important;
    border-right: none !important;
}

/* Override round buttons when inside button groups */
.btn-group .btn.btn-round {
    border-radius: 0 !important;
    min-width: auto !important;
    width: auto !important;
    padding: var(--space-2) var(--space-3) !important;
}

.btn-group .btn.btn-round:first-child {
    border-top-left-radius: var(--border-radius-md) !important;
    border-bottom-left-radius: var(--border-radius-md) !important;
}

.btn-group .btn.btn-round:last-child {
    border-top-right-radius: var(--border-radius-md) !important;
    border-bottom-right-radius: var(--border-radius-md) !important;
}

/* Override round white buttons when inside button groups */
.btn-group .btn.btn-round.btn-white {
    border-radius: 0 !important;
    min-width: auto !important;
    width: auto !important;
    padding: var(--space-2) var(--space-3) !important;
}

.btn-group .btn.btn-round.btn-white:first-child {
    border-top-left-radius: var(--border-radius-md) !important;
    border-bottom-left-radius: var(--border-radius-md) !important;
}

.btn-group .btn.btn-round.btn-white:last-child {
    border-top-right-radius: var(--border-radius-md) !important;
    border-bottom-right-radius: var(--border-radius-md) !important;
}

.btn-group .btn.btn-round.btn-white,
.btn-group * * .btn.btn-round.btn-white,
.btn-group * * * * .btn.btn-round.btn-white {
    border-radius: var(--border-radius-md) !important;
}

/* Remove gaps for attached buttons */
.btn-group .btn + .btn {
    margin-left: 0 !important;
}

/* Ensure button groups don't wrap and buttons fit properly */
.btn-group {
    display: inline-flex !important;
    flex-wrap: nowrap !important;
    width: auto !important;
}

/* Override button sizing specifically inside button groups */
.btn-group .btn {
    min-width: auto !important;
    width: auto !important;
    flex-shrink: 1 !important;
    white-space: nowrap !important;
}

.btn-group {
    flex-wrap: nowrap !important;
}

/* Optional: Separated button groups variant */
.btn-group.btn-group-separated {
    gap: var(--space-1) !important;
    border-radius: 0 !important;
    overflow: visible !important;
}

.btn-group.btn-group-separated .btn {
    border-radius: var(--border-radius-md) !important;
    border-right: none !important;
}

/* ================================
   HIGH-PRIORITY FILTER OVERRIDES
   ================================ */

/* Force filter box styling without rounded corners */
.filter-box,
div.filter-box {
    border-radius: var(--border-radius-2xl) !important;
    background: var(--md-surface-container-low) !important;
    border: 0 !important;
}

/* Force filter input styling with consistent Material 3 borders */
.filter-box input,
.filter-box select,
.filter-box .form-control {
    border-radius: var(--border-radius-sm) !important;
    background-color: var(--md-surface-container-lowest) !important;
    border: 1px solid var(--md-outline-variant) !important;
    color: var(--md-on-surface) !important;
}

/* Filter input focus and hover states */
.filter-box input:focus,
.filter-box select:focus,
.filter-box .form-control:focus {
    border-color: var(--md-primary-40) !important;
    box-shadow: 0 0 0 2px rgba(103, 80, 164, 0.12) !important;
}

.filter-box input:hover,
.filter-box select:hover,
.filter-box .form-control:hover {
    border-color: var(--md-outline) !important;
}

/* Force widget toolbox styling without rounded corners */
div.widget-toolbox,
.widget-toolbox {
    background-color: var(--md-primary-95) !important;
    color: var(--md-primary-10) !important;
    border-radius: 0 !important;
    border: 0 !important;
}

/* Filter button alignment overrides */
.filter-box .btn-group,
.filter-box .btn-group .btn {
  flex-shrink: 0 !important;
}

.filter-box {
  overflow-x: auto !important;
  flex-wrap: nowrap !important;
}

.filter-box .btn-group:last-child {
  margin-left: auto;
}

/* Collapsed filter bar overrides */
#filter-bar-queries {
  display: flex !important;
  align-items: center !important;
  flex-wrap: nowrap !important;
}

#filter-bar-queries form {
  display: flex !important;
  align-items: center !important;
  gap: var(--space-3) !important;
  margin: 0 !important;
}

#filter-bar-queries .btn-group {
  flex-shrink: 0 !important;
  margin-left: auto !important;
}

/* ================================
   SIMPLIFIED PROJECT STATE THEMING
   Only affects widget headers and hyperlinks
   ================================ */

/* Widget headers use project state colors */
.widget-header {
    background-color: var(--link-color) !important;
    color: white !important;
    padding: var(--space-3) var(--space-4) !important;
    margin: calc(-1 * var(--space-4)) calc(-1 * var(--space-4)) var(--space-4) calc(-1 * var(--space-4)) !important;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    font-weight: 600 !important;
}

/* Hyperlinks use project state colors */
a,
a:visited,
a:active,
.link,
.link-tag {
    color: var(--link-color) !important;
}

a:hover,
.link:hover,
.link-tag:hover {
    color: var(--link-color) !important;
    filter: brightness(90%) !important;
    text-decoration: underline !important;
}

/* ================================
   INLINE DATE SELECT INPUTS
   ================================ */

/* Force select elements themselves to be inline */
select + select,
td select + td select,
select[name*="year"] ~ select,
select[name*="month"] ~ select,
select[name*="day"] ~ select {
    margin-left: var(--space-2) !important;
}

/* More specific targeting for date-related selects */
select[name*="year"],
select[name*="month"], 
select[name*="day"],
select[name*="date"] {
    display: inline-block !important;
    width: auto !important;
    min-width: 80px !important;
    max-width: 120px !important;
    margin-right: var(--space-2) !important;
    vertical-align: top !important;
}

/* ================================
   SELECT FIELD FIXES
   ================================ */

/* Fix double arrow issue and style all select fields with Material 3 */
select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-color: var(--md-surface-container-lowest) !important;
    background-image: url("data:image/svg+xml;charset=UTF-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'><polyline points='6,9 12,15 18,9'></polyline></svg>") !important;
    background-repeat: no-repeat !important;
    background-position: calc(100% - 12px) center !important;
    background-size: 16px !important;
    border: 1px solid var(--md-outline-variant) !important;
    border-radius: var(--border-radius-md) !important;
    padding: var(--space-3) var(--space-4) !important;
    padding-right: 40px !important;
    color: var(--md-on-surface) !important;
    font-size: var(--md-body-medium) !important;
    min-height: 40px !important;
    transition: all var(--transition-fast) !important;
}

/* Ensure consistent select styling across all contexts */
select::-ms-expand {
    display: none !important;
}

/* Select hover and focus states */
select:hover {
    border-color: var(--md-outline) !important;
}

select:focus {
    border-color: var(--md-primary-40) !important;
    box-shadow: 0 0 0 2px rgba(103, 80, 164, 0.12) !important;
    outline: none !important;
}

/* ================================
   DISABLE AUTOCOMPLETE TIME DROPDOWN MENUS
   ================================ */

/* Hide all autocomplete dropdown menus */
ul[id*="autocomplete"],
ul[id*="field"][id*="values"],
ul.dropdown-menu.list.dropdown-yellow,
.dropdown-menu.auto-values,
ul.user-menu.dropdown-menu.list.dropdown-yellow.no-margin.auto-values {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    height: 0 !important;
    overflow: hidden !important;
}

/* Specifically target time field autocomplete dropdowns */
ul[id*="autocomplete-field"][id*="values"] {
    display: none !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* Hide any dropdown that appears after clicking on time inputs */
input[data-toggle="dropdown"] + ul,
input[aria-expanded="true"] + ul,
input[aria-haspopup="true"] + ul {
    display: none !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* Style time input fields to look like regular text inputs */
input[data-toggle="dropdown"],
input[aria-haspopup="true"],
input.dropdown-toggle,
input[id*="field"][name*="field"] {
    background-color: var(--md-surface-container-lowest) !important;
    border: 1px solid var(--md-outline-variant) !important;
    border-radius: var(--border-radius-md) !important;
    padding: var(--space-3) var(--space-4) !important;
    color: var(--md-on-surface) !important;
    font-size: var(--md-body-medium) !important;
    min-height: 40px !important;
    transition: all var(--transition-fast) !important;
    cursor: text !important;
    
    /* Remove dropdown arrow styling */
    background-image: none !important;
    -webkit-appearance: textfield !important;
    -moz-appearance: textfield !important;
    appearance: textfield !important;
}

/* Remove any dropdown arrow icons */
input[data-toggle="dropdown"]::after,
input[aria-haspopup="true"]::after,
input.dropdown-toggle::after {
    display: none !important;
    content: none !important;
}

/* Hover and focus states for time inputs */
input[data-toggle="dropdown"]:hover,
input[aria-haspopup="true"]:hover,
input.dropdown-toggle:hover {
    border-color: var(--md-outline) !important;
}

input[data-toggle="dropdown"]:focus,
input[aria-haspopup="true"]:focus,
input.dropdown-toggle:focus {
    border-color: var(--md-primary-40) !important;
    box-shadow: 0 0 0 2px rgba(103, 80, 164, 0.12) !important;
    outline: none !important;
}

/* Disable click events on time input wrappers */
.dropdown.autocomplete-field,
div[class*="dropdown"][class*="autocomplete"] {
    pointer-events: none !important;
}

/* Re-enable pointer events on the input itself */
.dropdown.autocomplete-field input,
div[class*="dropdown"][class*="autocomplete"] input {
    pointer-events: auto !important;
}

/* Ensure Material 3 design tokens take precedence */
:root {
    /* Force Material 3 spacing */
    --space-0: 0px !important;
    --space-1: 4px !important;
    --space-2: 8px !important;
    --space-3: 12px !important;
    --space-4: 16px !important;
    --space-5: 20px !important;
    --space-6: 24px !important;
    --space-8: 32px !important;
}

/* ================================
   HIGH-PRIORITY TABLE OVERRIDES
   Ensure Material 3 colors override default.css and marevalcore.css
   ================================ */

/* Force Material 3 category cell styling over default.css */
table td.category,
table th.category,
.table td.category,
.table th.category,
table td.category label,
table th.category label,
.table td.category label,
.table th.category label {
    background-color: var(--md-primary-95) !important;
    color: var(--md-primary-10) !important;
    font-weight: 600 !important;
    vertical-align: top !important;
    border-radius: 0 !important;
}

/* Force filter table category colors */
table.filters td.category,
.table.filters td.category {
    color: var(--md-primary-40) !important;
    background-color: var(--md-primary-95) !important;
}

/* Force MarEval Core overrides */
table.filters td.category.mar-label-color-default,
.table.filters td.category.mar-label-color-default {
    color: var(--md-on-surface) !important;
    background-color: var(--md-surface-container-low) !important;
}

/* Force spacer row styling */
table tr.spacer,
.table tr.spacer {
    background-color: var(--md-surface) !important;
    color: var(--md-on-surface) !important;
    height: 5px !important;
}

/* Force sticky separator styling */
table .sticky-separator,
.table .sticky-separator,
table .test-langs th,
.table .test-langs th {
    background-color: var(--md-surface-container-high) !important;
    color: var(--md-on-surface) !important;
}

/* Force bug note styling */
table tr.bugnote .bugnote-note,
.table tr.bugnote .bugnote-note {
    background-color: var(--md-surface-container-low) !important;
    color: var(--md-on-surface) !important;
    width: 75% !important;
    vertical-align: top !important;
    border-radius: var(--border-radius-sm) !important;
}

/* Force private bug note styling */
table .bugnote-private,
.table .bugnote-private {
    background-color: var(--md-warning-95) !important;
    color: var(--md-warning-10) !important;
    border: 1px solid var(--md-warning-70) !important;
}

/* Force configuration color styling */
table .color-global,
.table .color-global {
    background-color: var(--md-primary-90) !important;
    color: var(--md-primary-10) !important;
    border: 1px solid var(--md-primary-70) !important;
}

table .color-project,
.table .color-project {
    background-color: var(--md-success-90) !important;
    color: var(--md-success-10) !important;
    border: 1px solid var(--md-success-70) !important;
}

/* Force due date styling */
table td.due-0,
table td.overdue,
.table td.due-0,
.table td.overdue {
    background-color: var(--md-error-40) !important;
    color: var(--md-on-error) !important;
    font-weight: 600 !important;
    border-radius: var(--border-radius-sm) !important;
}

table td.due-1,
.table td.due-1 {
    background-color: var(--md-warning-50) !important;
    color: var(--md-warning-10) !important;
    font-weight: 600 !important;
    border-radius: var(--border-radius-sm) !important;
}

table td.due-2,
.table td.due-2 {
    background-color: var(--md-success-40) !important;
    color: var(--md-on-success) !important;
    font-weight: 600 !important;
    border-radius: var(--border-radius-sm) !important;
}

/* Force bug list styling */
#buglist td,
#buglist th {
    text-align: center !important;
    background-color: var(--md-surface-container-lowest) !important;
    color: var(--md-on-surface) !important;
}

/* Force general table cell styling to override any legacy colors */
table td,
table th,
.table td,
.table th {
    background-color: inherit !important;
    color: var(--md-on-surface) !important;
    border-color: var(--md-outline-variant) !important;
}

/* Force table header styling */
table th,
table thead th,
.table th,
.table thead th {
    background-color: var(--md-primary-90) !important;
    color: var(--md-primary-10) !important;
    font-weight: 500 !important;
    border-bottom: 2px solid var(--md-primary-40) !important;
}

/* Force table row hover effects */
table tbody tr:hover,
.table tbody tr:hover,
table.table-hover tbody tr:hover,
.table.table-hover tbody tr:hover {
    background-color: rgba(103, 80, 164, 0.08) !important;
    color: var(--md-on-surface) !important;
}

/* ================================
   HIGH-PRIORITY FILTER OVERRIDES
   Remove rounded corners from filter boxes and components
   ================================ */

/* Force filter box styling without rounded corners */
.filter-box,
div.filter-box,
ul[id*="autocomplete-field"][id*="values"] {
    display: none !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* Hide any dropdown that appears after clicking on time inputs */
input[data-toggle="dropdown"] + ul,
input[aria-expanded="true"] + ul,
input[aria-haspopup="true"] + ul {
    display: none !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* Style time input fields to look like regular text inputs */
input[data-toggle="dropdown"],
input[aria-haspopup="true"],
input.dropdown-toggle,
input[id*="field"][name*="field"] {
    background-color: var(--md-surface-container-lowest) !important;
    border: 1px solid var(--md-outline-variant) !important;
    border-radius: var(--border-radius-md) !important;
    padding: var(--space-3) var(--space-4) !important;
    color: var(--md-on-surface) !important;
    font-size: var(--md-body-medium) !important;
    min-height: 40px !important;
    transition: all var(--transition-fast) !important;
    cursor: text !important;
    
    /* Remove dropdown arrow styling */
    background-image: none !important;
    -webkit-appearance: textfield !important;
    -moz-appearance: textfield !important;
    appearance: textfield !important;
}

/* Remove any dropdown arrow icons */
input[data-toggle="dropdown"]::after,
input[aria-haspopup="true"]::after,
input.dropdown-toggle::after {
    display: none !important;
    content: none !important;
}

/* Hover and focus states for time inputs */
input[data-toggle="dropdown"]:hover,
input[aria-haspopup="true"]:hover,
input.dropdown-toggle:hover {
    border-color: var(--md-outline) !important;
}

input[data-toggle="dropdown"]:focus,
input[aria-haspopup="true"]:focus,
input.dropdown-toggle:focus {
    border-color: var(--md-primary-40) !important;
    box-shadow: 0 0 0 2px rgba(103, 80, 164, 0.12) !important;
    outline: none !important;
}

/* Disable click events on time input wrappers */
.dropdown.autocomplete-field,
div[class*="dropdown"][class*="autocomplete"] {
    pointer-events: none !important;
}

/* Re-enable pointer events on the input itself */
.dropdown.autocomplete-field input,
div[class*="dropdown"][class*="autocomplete"] input {
    pointer-events: auto !important;
}
