/* ================================
   Material Design 3 Form System (2025)
   Based on Google Material You Guidelines
   ================================ */

/* ================================
   PROJECT STATE THEMED FORMS
   ================================ */
input,
textarea,
select {
    font-family: var(--font-family) !important;
    font-size: 14px !important;
    padding: var(--space-2) var(--space-3) !important;
    border: 1px solid var(--md-neutral-50) !important;
    border-radius: var(--border-radius-sm) !important;
    background-color: white !important;
    transition: var(--transition-fast) !important;
}

input:focus,
textarea:focus,
select:focus {
    outline: none !important;
    border-color: var(--link-color) !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* ================================
   LEGACY FORM STYLES (Below)
   ================================ */

/* Base form element reset and modernization */
input,
textarea,
select {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
    padding: 0;
    border: none;
    background: transparent;
    outline: none;
}

/* Material 3 form control base */
.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="date"],
input[type="datetime-local"],
input[type="month"],
input[type="time"],
input[type="week"],
textarea,
select {
    display: block;
    width: 100%;
    padding: var(--space-3) var(--space-4);
    font-size: var(--md-body-medium);
    font-weight: 400;
    line-height: 20px;
    color: var(--md-on-surface);
    background-color: var(--md-surface-container-highest);
    background-clip: padding-box;
    border: 1px solid var(--md-outline-variant);
    border-radius: var(--border-radius-xs);
    box-shadow: none;
    transition: var(--transition-fast);
    transition-property: border-color, box-shadow, background-color;
    min-height: 40px;
}

select.input-xs {
    line-height: 25px !important;
    padding-left: 12px !important;
}

/* Material 3 focus states */
.form-control:focus,
input:focus,
textarea:focus,
select:focus {
    border-color: var(--md-primary-40);
    background-color: var(--md-surface-container-highest);
    box-shadow: 0 0 0 2px rgba(103, 80, 164, 0.12);
    outline: none;
}

/* Hover states */
.form-control:hover:not(:disabled):not(:focus),
input:hover:not(:disabled):not(:focus),
textarea:hover:not(:disabled):not(:focus),
select:hover:not(:disabled):not(:focus) {
    border-color: var(--md-outline);
}

/* Disabled states */
.form-control:disabled,
input:disabled,
textarea:disabled,
select:disabled {
    background-color: var(--md-surface-container-low);
    color: var(--md-on-surface);
    border-color: var(--md-outline-variant);
    cursor: not-allowed;
    opacity: 0.38;
}

/* Readonly states */
.form-control:read-only,
input:read-only,
textarea:read-only {
    background-color: var(--md-surface-container);
    cursor: default;
}

/* Placeholder styling */
.form-control::placeholder,
input::placeholder,
textarea::placeholder {
    color: var(--md-on-surface-variant);
    opacity: 1;
}

/* Material 3 form sizes */
.form-control-sm,
input.form-control-sm,
textarea.form-control-sm,
select.form-control-sm {
    padding: var(--space-2) var(--space-3);
    font-size: var(--md-body-small);
    border-radius: var(--border-radius-xs);
    min-height: 40px;
}

.form-control-lg,
input.form-control-lg,
textarea.form-control-lg,
select.form-control-lg {
    padding: var(--space-4) var(--space-5);
    font-size: var(--md-body-large);
    border-radius: var(--border-radius);
    min-height: 48px;
}

/* Material 3 Labels */
label {
    display: block;
    margin-bottom: var(--space-2);
    font-size: var(--md-body-small);
    font-weight: 500;
    color: var(--md-on-surface-variant);
    line-height: 16px;
    letter-spacing: 0.4px;
}

.label-required::after {
    content: " *";
    color: var(--md-error-40);
}

/* Form groups */
.form-group {
    margin-bottom: var(--space-6);
}

.form-group:last-child {
    margin-bottom: 0;
}

/* Material 3 checkbox and radio styling */
input[type="checkbox"],
input[type="radio"] {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid var(--md-outline);
    border-radius: var(--border-radius-xs);
    background-color: var(--md-surface-container-highest);
    position: relative;
    cursor: pointer;
    transition: var(--transition-fast);
    flex-shrink: 0;
    margin-right: var(--space-2);
}

input[type="radio"] {
    border-radius: var(--border-radius-full);
}

input[type="checkbox"]:checked,
input[type="radio"]:checked {
    background-color: var(--md-primary-40);
    border-color: var(--color-primary-500);
}

input[type="checkbox"]:checked::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: inherit;
    font-size: 12px;
    font-weight: bold;
}

input[type="radio"]:checked::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    border-radius: var(--border-radius-full);
    background-color: var(--color-white);
}

input[type="checkbox"]:focus,
input[type="radio"]:focus {
    box-shadow: var(--shadow-focus);
    outline: none;
}

/* Checkbox and radio labels */
.checkbox-label,
.radio-label {
    display: flex;
    align-items: flex-start;
    gap: var(--space-2);
    cursor: pointer;
    margin-bottom: var(--space-2);
}

.checkbox-label:last-child,
.radio-label:last-child {
    margin-bottom: 0;
}

/* Modern select styling */
select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-3) center;
    background-repeat: no-repeat;
    background-size: 16px 16px;
    padding-right: var(--space-8);
}

select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236366f1' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}

/* Modern textarea */
textarea {
    min-height: 80px;
    resize: vertical;
}

textarea.resize-none {
    resize: none;
}

textarea.resize-horizontal {
    resize: horizontal;
}

/* Input groups */
.input-group {
    display: flex;
    align-items: stretch;
    width: 100%;
}

.input-group .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}

.input-group-addon,
.input-group-prepend,
.input-group-append {
    display: flex;
    align-items: center;
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--color-text-secondary);
    text-align: center;
    white-space: nowrap;
    background-color: var(--color-bg-muted);
    border: 1px solid var(--color-border);
}

.input-group-prepend {
    border-right: 0;
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
}

.input-group-append {
    border-left: 0;
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
}

.input-group .form-control:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.input-group .form-control:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* Validation states */
.has-success .form-control,
.form-control.is-valid {
    border-color: var(--color-success-500);
}

.has-success .form-control:focus,
.form-control.is-valid:focus {
    border-color: var(--color-success-500);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.15);
}

.has-warning .form-control,
.form-control.is-warning {
    border-color: var(--color-warning-500);
}

.has-warning .form-control:focus,
.form-control.is-warning:focus {
    border-color: var(--color-warning-500);
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.15);
}

.has-error .form-control,
.form-control.is-invalid {
    border-color: var(--color-error-500);
}

.has-error .form-control:focus,
.form-control.is-invalid:focus {
    border-color: var(--color-error-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.15);
}

/* Help text and feedback */
.form-text,
.help-block {
    display: block;
    margin-top: var(--space-1);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
    color: var(--color-text-muted);
}

.invalid-feedback,
.error-message {
    display: block;
    margin-top: var(--space-1);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
    color: var(--color-error-500);
}

.valid-feedback,
.success-message {
    display: block;
    margin-top: var(--space-1);
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
    color: var(--color-success-500);
}

/* Modern form layouts */
.form-horizontal .form-group {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--space-4);
}

.form-horizontal .form-group label {
    flex: 0 0 auto;
    width: 150px;
    margin-bottom: 0;
    margin-right: var(--space-4);
    padding-top: var(--space-3);
}

.form-horizontal .form-group .form-control-wrapper {
    flex: 1;
}

.form-inline {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--space-4);
}

.form-inline .form-group {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.form-inline label {
    margin-bottom: 0;
    white-space: nowrap;
}

/* Modern file input */
input[type="file"] {
    border: 2px dashed var(--color-border);
    border-radius: var(--border-radius);
    padding: var(--space-6);
    text-align: center;
    cursor: pointer;
    transition: var(--transition-fast);
}

input[type="file"]:hover {
    border-color: var(--color-primary-500);
    background-color: var(--color-primary-50);
}

input[type="file"]:focus {
    border-color: var(--color-primary-500);
    box-shadow: var(--shadow-focus);
    outline: none;
}

/* Modern range slider */
input[type="range"] {
    appearance: none;
    width: 100%;
    height: 6px;
    border-radius: var(--border-radius-full);
    background: var(--color-gray-200);
    outline: none;
    cursor: pointer;
}

input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: var(--border-radius-full);
    background: var(--color-primary-500);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);
}

input[type="range"]::-webkit-slider-thumb:hover {
    background: var(--color-primary-600);
    box-shadow: var(--shadow-md);
}

input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border: none;
    border-radius: var(--border-radius-full);
    background: var(--color-primary-500);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);
}

input[type="range"]::-moz-range-thumb:hover {
    background: var(--color-primary-600);
    box-shadow: var(--shadow-md);
}

fieldset {
    padding: var(--space-2);
}

legend {
    font-weight: var(--font-weight-semibold);
    color: var(--color-text);
    padding: 0 var(--space-2);
    margin-bottom: var(--space-2);
}

/* Responsive form adjustments */
@media (max-width: 768px) {
    .form-horizontal .form-group {
        flex-direction: column;
    }
    
    .form-horizontal .form-group label {
        width: 100%;
        margin-right: 0;
        margin-bottom: var(--space-2);
        padding-top: 0;
    }
    
    .form-inline {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-inline .form-group {
        flex-direction: column;
        align-items: stretch;
    }
}