/**
 * Project State Manager for MARCOM
 * Handles dynamic project state detection and theming
 */

class ProjectStateManager {
    constructor() {
        this.currentState = null;
        this.stateConfig = {
            0: {
                name: 'All Projects',
                color: 'blue',
                pages: [
                    // All my_view_page variants and general project overview
                    'my_view_page.php', 'view_all_bug_page.php'
                ],
                description: 'Overview of all projects and general bug tracking'
            },
            35: {
                name: 'Document Verification',
                color: 'green',
                pages: [
                    // Document verification specific pages
                    'bug_report_page.php', 'bug_view_page.php', 'bug_update_page.php',
                    'bug_view_advanced_page.php', 'bugnote_add_page.php', 'bugnote_edit_page.php'
                ],
                description: 'Document verification and bug tracking'
            },
            40: {
                name: 'Enhanced Document Processing',
                color: 'dark-green',
                pages: [
                    // Enhanced document processing pages
                    'file_download.php', 'csv_export.php', 'excel_xml_export.php'
                ],
                description: 'Advanced document processing and export'
            },
            45: {
                name: 'Master Documents',
                color: 'yellow',
                pages: [
                    // Master document management pages
                    'proj_doc_page.php', 'proj_doc_add_page.php', 'proj_doc_edit_page.php'
                ],
                description: 'Master document management and storage'
            },
            50: {
                name: 'Enhanced Meetings',
                color: 'light-purple',
                pages: [
                    // Enhanced meeting management pages
                    'bug_reminder_page.php', 'bug_relationship_graph.php'
                ],
                description: 'Advanced meeting management and scheduling'
            },
            55: {
                name: 'Meetings',
                color: 'purple',
                pages: [
                    // Basic meeting management pages
                    'news_view_page.php', 'news_list_page.php', 'news_menu_page.php'
                ],
                description: 'Meeting management and scheduling'
            },
            60: {
                name: 'Critical Issues',
                color: 'light-red',
                pages: [
                    // Critical issue tracking pages
                    'bug_change_status_page.php', 'bug_actiongroup_page.php'
                ],
                description: 'Critical issue tracking and resolution'
            },
            65: {
                name: 'Minutes of Meeting',
                color: 'red',
                pages: [
                    // Meeting minutes and documentation pages
                    'news_edit_page.php', 'bugnote_view_page.php'
                ],
                description: 'Meeting minutes and documentation'
            },
            70: {
                name: 'Reports',
                color: 'cyan',
                pages: [
                    // Reporting and analytics pages
                    'summary_page.php', 'roadmap_page.php', 'changelog_page.php',
                    'print_all_bug_page.php', 'print_all_bug_options_page.php'
                ],
                description: 'Reporting and analytics'
            },
            75: {
                name: 'Administration',
                color: 'deep-purple',
                pages: [
                    // System administration pages (note: these might also be "normal" pages)
                    'manage_overview_page.php', 'manage_plugin_page.php'
                ],
                description: 'System administration and management'
            }
        };
        
        this.init();
    }

    init() {
        // Only detect current state from body class (set by PHP)
        this.detectCurrentState();
        
        // Set up mutation observer to watch for state changes
        this.setupStateObserver();
        
        // Add navigation enhancement (but don't force color changes)
        this.enhanceNavigation();
        
        // DISABLED: Navigation fallback was too aggressive
        // this.setupNavigationFallback();
    }

    detectCurrentState() {
        const bodyClasses = document.body.classList;
        
        for (const className of bodyClasses) {
            if (className.startsWith('project-state-')) {
                const stateNumber = parseInt(className.replace('project-state-', ''));
                if (this.stateConfig[stateNumber]) {
                    this.currentState = stateNumber;
                    this.onStateChange(stateNumber);
                    break;
                }
            }
        }
        
        return this.currentState;
    }

    setupStateObserver() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const newState = this.detectCurrentState();
                    if (newState !== this.currentState) {
                        this.currentState = newState;
                        this.onStateChange(newState);
                    }
                }
            });
        });

        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });
    }

    onStateChange(stateNumber) {
        const config = this.stateConfig[stateNumber];
        if (config) {
            console.log(`Project state changed to: ${config.name} (${stateNumber})`);
            
            // Update page title context
            this.updatePageContext(config);
            
            // Trigger custom event for other components
            document.dispatchEvent(new CustomEvent('projectStateChanged', {
                detail: { stateNumber, config }
            }));
        }
    }

    updatePageContext(config) {
        // Add state indicator to page if it doesn't exist
        let indicator = document.getElementById('project-state-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'project-state-indicator';
            indicator.style.cssText = `
                position: fixed;
                top: 10px;
                left: 10px;
                background: var(--link-color);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                z-index: 9999;
                opacity: 0.8;
                transition: opacity 0.3s ease;
            `;
            document.body.appendChild(indicator);
        }
        
        indicator.textContent = `${config.name} (${this.currentState})`;
        indicator.title = config.description;
    }

    enhanceNavigation() {
        // Add project state awareness to navigation links
        const navLinks = document.querySelectorAll('#sidebar ul.nav.nav-list li a');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href) {
                // Find matching state for this link
                const matchingState = this.findStateForPage(href);
                if (matchingState) {
                    link.setAttribute('data-project-state', matchingState);
                    link.title = `${link.textContent.trim()} - ${this.stateConfig[matchingState].name}`;
                }
            }
        });
    }

    setupNavigationFallback() {
        // Define pages that should use default blue theme (non-project-state pages)
        const normalPages = [
            // Account/User Management
            'account_page.php', 'account_prefs_page.php', 'account_prof_edit_page.php',
            'account_prof_menu_page.php', 'account_sponsor_page.php', 'account_manage_columns_page.php',
            
            // Login/Authentication
            'login_page.php', 'login_password_page.php', 'login_select_proj_page.php', 
            'logout_page.php', 'lost_pwd_page.php', 'signup_page.php',
            
            // Administration
            'adm_config_page.php', 'adm_permissions_report.php', 'manage_overview_page.php',
            'manage_plugin_page.php', 'manage_user_page.php', 'manage_proj_page.php',
            'manage_config_columns_page.php', 'manage_config_email_page.php',
            'manage_config_workflow_page.php', 'manage_custom_field_page.php',
            'manage_filter_page.php', 'manage_tags_page.php', 'manage_proj_create_page.php',
            'manage_user_create_page.php', 'manage_proj_edit_page.php', 'manage_user_edit_page.php',
            
            // API/Billing
            'api_tokens_page.php', 'billing_page.php',
            
            // News/Documentation
            'news_list_page.php', 'news_menu_page.php', 'news_view_page.php',
            'proj_doc_page.php', 'proj_doc_add_page.php', 'proj_doc_edit_page.php',
            
            // System pages
            'main_page.php', 'index.php', 'summary_page.php', 'roadmap_page.php',
            'changelog_page.php', 'print_all_bug_options_page.php', 'query_store_page.php',
            'tag_view_page.php', 'tag_update_page.php', 'view_filters_page.php',
            'view_user_page.php', 'permalink_page.php', 'search.php'
        ];

        // Listen for all clicks on links
        document.addEventListener('click', (event) => {
            const link = event.target.closest('a');
            if (!link || !link.href) return;

            // Get the page name from the link
            const url = new URL(link.href, window.location.origin);
            const pathname = url.pathname;
            const pageName = pathname.split('/').pop();

            // Check if this is a normal page that should use blue theme
            if (normalPages.includes(pageName)) {
                // Remove project state class after a short delay to allow navigation
                setTimeout(() => {
                    this.clearProjectState();
                }, 100);
            }
        });

        // Also check current page on load
        const currentPage = window.location.pathname.split('/').pop();
        if (normalPages.includes(currentPage)) {
            this.clearProjectState();
        }
    }

    clearProjectState() {
        // Remove all project state classes
        const body = document.body;
        const classesToRemove = [];
        
        body.classList.forEach(className => {
            if (className.startsWith('project-state-') && className !== 'project-state-0') {
                classesToRemove.push(className);
            }
        });
        
        classesToRemove.forEach(className => {
            body.classList.remove(className);
        });
        
        // Add project-state-0 (blue) as fallback
        if (!body.classList.contains('project-state-0')) {
            body.classList.add('project-state-0');
        }
        
        // Update current state
        this.currentState = 0;
        this.onStateChange(0);
        
        console.log('Project state cleared, using default blue theme');
    }

    findStateForPage(href) {
        // Extract page name from href
        const url = new URL(href, window.location.origin);
        const pathname = url.pathname;
        const pageName = pathname.split('/').pop();
        
        // Search through all states to find matching pages
        for (const [stateNumber, config] of Object.entries(this.stateConfig)) {
            if (config.pages.includes(pageName) || config.pages.includes(href)) {
                return parseInt(stateNumber);
            }
        }
        
        return null;
    }

    getCurrentState() {
        return this.currentState;
    }

    getCurrentStateConfig() {
        return this.stateConfig[this.currentState];
    }

    getAllStates() {
        return this.stateConfig;
    }

    // Manual state switching for testing
    setProjectState(stateNumber) {
        if (this.stateConfig[stateNumber]) {
            // Remove existing project state classes
            document.body.classList.forEach(className => {
                if (className.startsWith('project-state-')) {
                    document.body.classList.remove(className);
                }
            });
            
            // Add new project state class
            document.body.classList.add(`project-state-${stateNumber}`);
            
            this.currentState = stateNumber;
            this.onStateChange(stateNumber);
        }
    }
}

// Initialize project state manager when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeProjectState);
} else {
    initializeProjectState();
}

function initializeProjectState() {
    // Read configuration from data attribute
    const configElement = document.getElementById('project-state-config');
    let config = { enabled: false, debug: false, autoDetect: false, fallbackState: 0 };
    
    if (configElement) {
        try {
            config = { ...config, ...JSON.parse(configElement.getAttribute('data-config')) };
        } catch (e) {
            console.error('Failed to parse project state config:', e);
        }
    }
    
    if (config.enabled) {
        // Only use PHP-detected state, don't try to auto-detect more
        const finalState = config.fallbackState || 0;
        
        // Apply project state class
        applyProjectStateClass(finalState, config.debug);
        
        if (config.debug) {
            console.log(`MarevalTheme: Using PHP-detected project state ${finalState}`);
        }
    } else {
        // If not enabled, ensure we use default blue (state 0)
        applyProjectStateClass(0, config.debug);
        if (config.debug) {
            console.log('MarevalTheme: Project state theming disabled, using default blue');
        }
    }
    
    // Initialize simplified project state manager (without auto-detection)
    window.projectStateManager = new ProjectStateManager();
}

function applyProjectStateClass(projectState, debug = false) {
    const body = document.body;
    
    // Remove any existing project-state classes
    body.className = body.className.replace(/project-state-\d+/g, "");
    
    // Add skin-3 if not present
    if (!body.classList.contains("skin-3")) {
        body.classList.add("skin-3");
    }
    
    // Add new project state class
    body.classList.add(`project-state-${projectState}`);
    
    if (debug) {
        console.log(`MarevalTheme: Applied project-state-${projectState} to body`);
    }
}

function detectProjectStateFromDOM() {
    // Method 1: Look for existing project-state classes in body
    const bodyClasses = document.body.className.split(' ');
    for (const className of bodyClasses) {
        if (className.startsWith('project-state-')) {
            const stateNumber = parseInt(className.replace('project-state-', ''));
            if (!isNaN(stateNumber)) {
                return stateNumber;
            }
        }
    }
    
    // Method 2: Look for injected DIV with project state information
    const projectStateDivs = document.querySelectorAll('[class*="project-state-"]');
    for (const div of projectStateDivs) {
        const classes = div.className.split(' ');
        for (const className of classes) {
            if (className.startsWith('project-state-')) {
                const stateNumber = parseInt(className.replace('project-state-', ''));
                if (!isNaN(stateNumber)) {
                    return stateNumber;
                }
            }
        }
    }
    
    // Method 3: Look for project ID in URL and map to project state
    const urlParams = new URLSearchParams(window.location.search);
    const projectId = urlParams.get('project_id');
    if (projectId) {
        return mapProjectIdToState(parseInt(projectId));
    }
    
    // Method 4: Look for specific data attributes or IDs
    const projectIndicators = [
        '[data-project-state]',
        '[data-project-id]',
        '#project-state-indicator',
        '.project-indicator'
    ];
    
    for (const selector of projectIndicators) {
        const element = document.querySelector(selector);
        if (element) {
            const projectState = element.getAttribute('data-project-state') ||
                               element.getAttribute('data-project-id') ||
                               element.textContent.match(/project-state-(\d+)/) ||
                               element.textContent.match(/state-(\d+)/);
            
            if (projectState) {
                const stateNumber = parseInt(Array.isArray(projectState) ? projectState[1] : projectState);
                if (!isNaN(stateNumber)) {
                    return stateNumber;
                }
            }
        }
    }
    
    return null;
}

function mapProjectIdToState(projectId) {
    // This mapping would need to be configured based on your actual project structure
    // For now, we'll use a simple modulo to demonstrate the concept
    const stateMapping = {
        0: 0,   // All Projects
        35: 35, // Document Verification  
        40: 40, // Enhanced Document Processing
        45: 45, // Master Documents
        50: 50, // Enhanced Meetings
        55: 55, // Meetings
        60: 60, // Critical Issues
        65: 65, // Minutes of Meeting
        70: 70, // Reports
        75: 75  // Administration
    };
    
    // Direct mapping if available
    if (stateMapping[projectId]) {
        return stateMapping[projectId];
    }
    
    // Fallback: map based on project ID ranges or patterns
    if (projectId >= 30 && projectId < 40) return 35; // Document Verification range
    if (projectId >= 40 && projectId < 50) return 45; // Master Documents range  
    if (projectId >= 50 && projectId < 60) return 55; // Meetings range
    if (projectId >= 60 && projectId < 70) return 65; // Minutes range
    if (projectId >= 70 && projectId < 80) return 70; // Reports range
    
    return 0; // Default to "All Projects"
}

// Export for global access
window.ProjectStateManager = ProjectStateManager;
