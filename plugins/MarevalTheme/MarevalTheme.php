<?php

class MarevalThemePlugin
extends MantisPlugin
{

	const C_PLUGIN_PATH = 'plugins/MarevalTheme/';
	const C_PLUGIN_FILES_PATH = self::C_PLUGIN_PATH . 'files/';
  	const C_PLUGIN_SKIN_MODERN = self::C_PLUGIN_FILES_PATH . 'mareval-theme-modern1.css';

	const C_CONFIG_THEME_MODERN1_ENABLED = 'theme_modern1_enabled';
	const C_CONFIG_PROJECT_STATE_ENABLED = 'project_state_theming_enabled';

    // Page to project state mapping (shared by all methods)
    private static $page_state_mapping = array(
        // Project State 0 - Blue (#3B82F6) - Default for most pages
        'my_view_page.php' => 0,
        'view_all_bug_page.php' => 0,
        'index.php' => 0,
        'main_page.php' => 0,
        'account_page.php' => 0,
        'account_prof_edit_page.php' => 0,
        'account_prof_menu_page.php' => 0,
        'account_manage_columns_page.php' => 0,
        'login_page.php' => 0,
        'manage_overview_page.php' => 0,
        'bug_view_page.php' => 0,
        'bug_update_page.php' => 0,
        'bug_view_advanced_page.php' => 0,
        'bugnote_add_page.php' => 0,
        'bugnote_edit_page.php' => 0,
        'summary_page.php' => 0,
        'roadmap_page.php' => 0,
        
        // Project State 35 - Green (#10B981) - Document Verification (only specific pages)
        'bug_report_page.php' => 35,
        
        // Project State 45 - Yellow (#B68929) - Master Documents
        'proj_doc_page.php' => 45,
        'proj_doc_add_page.php' => 45,
        'proj_doc_edit_page.php' => 45,
        'file_download.php' => 45,
        'csv_export.php' => 45,
        'excel_xml_export.php' => 45,
        
        // Project State 55 - Purple (#8B5CF6) - Meetings
        'news_view_page.php' => 55,
        'news_list_page.php' => 55,
        'news_menu_page.php' => 55,
        'bug_reminder_page.php' => 55,
        'bug_relationship_graph.php' => 55,
        
        // Project State 65 - Red (#DC4C35) - Minutes of Meeting
        'news_edit_page.php' => 65,
        'bugnote_view_page.php' => 65,
        'changelog_page.php' => 65
    );

	function register()
	{
		$this->name = 'Mareval Theme';
		$this->description = 'Material Design 3 theme for MARCOM with modular architecture and project state theming. Enable in Account > Preferences.';

		$this->version = '1.2.1';
		$this->requires = array(
			'MantisCore' => '2.0.0',
		);
		$this->author  = 'MAR|E|VAL | V.1.2.1 | 19.12.2024';               # Author/team name
		$this->url = 'https://www.mareval.de';
	}

	function hooks()
	{
		return array(
			'EVENT_LAYOUT_RESOURCES' => 'add_css_and_body_class',
			'EVENT_ACCOUNT_PREF_UPDATE_FORM' => 'account_update_form',
			'EVENT_ACCOUNT_PREF_UPDATE' => 'account_update',
		);
	}

	
	function init() {
	}

	function add_css_and_body_class( $p_event )
	{
		if( $this->is_enabled() ) {
			html_css_link(self::C_PLUGIN_SKIN_MODERN);

			// Add navigation theming JavaScript
			echo '<script type="text/javascript" src="' . plugin_file('navigation-theming.js') . '"></script>';

			// DISABLED: Project state theming temporarily disabled for debugging
			/*
			// Add project state body class if enabled
			if( $this->is_project_state_enabled() ) {
				// Add debugging info
				$current_page = basename($_SERVER['PHP_SELF']);
				$project_state = $this->get_project_state_number();

				echo "<!-- DEBUG: Current page: {$current_page}, Project state: {$project_state} -->";

				// Let JavaScript handle project state detection from DOM
				$js_config = json_encode(array(
					'enabled' => true,
					'debug' => true,
					'autoDetect' => true,
					'fallbackState' => $project_state // Use PHP-detected state as fallback
				), JSON_UNESCAPED_SLASHES);

				echo '<div id="project-state-config" data-config="' . htmlspecialchars($js_config) . '" style="display: none;"></div>';

				// Add external script for project state manager
				echo '<script type="text/javascript" src="' . plugin_file('project-state-manager.js') . '"></script>';

				// Force apply project state class immediately
				echo '<script>document.body.classList.add("project-state-' . $project_state . '");</script>';
			}
			*/
		}
	}

	function is_project_state_enabled()
	{
		return config_get( self::C_CONFIG_PROJECT_STATE_ENABLED, false, auth_get_current_user_id(), ALL_PROJECTS );
	}

	function get_project_state_class()
	{
		// Get current page
		$current_page = basename($_SERVER['PHP_SELF']);
		// Get project state for current page (default to 0 if not found)
		$project_state = isset(self::$page_state_mapping[$current_page]) ? self::$page_state_mapping[$current_page] : 0;
		// Return the complete class string including skin-3
		return "skin-3 project-state-{$project_state}";
	}

	function get_project_state_number()
	{
		// Get current page
		$current_page = basename($_SERVER['PHP_SELF']);
		// Get project state for current page (default to 0 if not found)
		return isset(self::$page_state_mapping[$current_page]) ? self::$page_state_mapping[$current_page] : 0;
	}

	function is_enabled()
	{
		return auth_is_user_authenticated() && config_get( self::C_CONFIG_THEME_MODERN1_ENABLED, false, auth_get_current_user_id(), ALL_PROJECTS );;
	}

	function account_update_form( $p_event, $p_user_id )
	{
		echo '<tr>' .
				 '<td class="category">' .
					'<label for="ThemeModernSwitch">Theme Modern1</label>' .
				 '</td>' .
				 '<td>' .
					 '<input id="ThemeModernSwitch" type="checkbox" name="' . self::C_CONFIG_THEME_MODERN1_ENABLED . '" value="1" ' . ( $this->is_enabled() ? 'checked' : '' ) . '/>' .
				 '</td>' .
			 '</tr>';
		
		echo '<tr>' .
				 '<td class="category">' .
					'<label for="ProjectStateSwitch">Project State Theming</label>' .
				 '</td>' .
				 '<td>' .
					 '<input id="ProjectStateSwitch" type="checkbox" name="' . self::C_CONFIG_PROJECT_STATE_ENABLED . '" value="1" ' . ( $this->is_project_state_enabled() ? 'checked' : '' ) . '/>' .
					 '<span class="lbl"> Enable sidebar color changes based on current page</span>' .
				 '</td>' .
			 '</tr>';
	}

	function account_update( $p_event, $p_user_id )
	{
		config_set( self::C_CONFIG_THEME_MODERN1_ENABLED, gpc_get_bool( self::C_CONFIG_THEME_MODERN1_ENABLED, false ), $p_user_id, ALL_PROJECTS );
		config_set( self::C_CONFIG_PROJECT_STATE_ENABLED, gpc_get_bool( self::C_CONFIG_PROJECT_STATE_ENABLED, false ), $p_user_id, ALL_PROJECTS );
	}
}