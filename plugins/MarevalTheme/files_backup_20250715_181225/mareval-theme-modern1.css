/* Base Design Tokens - Load First */
@import url('modern-theme_root.css');

/* Layout Components */
@import url('modern-theme_main-content.css');
@import url('modern-theme_sidebar.css');
@import url('modern-theme_header-footer.css');

/* UI Components */
@import url('modern-theme_buttons.css');
@import url('modern-theme_forms.css');
@import url('modern-theme_tables.css');
@import url('modern-theme_modals.css');
@import url('modern-theme_alerts.css');
@import url('modern-theme_filters.css');
@import url('modern-theme_badges.css');
@import url('modern-theme_helpers.css');

/* Content-Specific Components */
@import url('modern-theme_documents.css');
@import url('modern-theme_comments.css');
@import url('modern-theme_meetings.css');
@import url('modern-theme_meetingitems.css');

/* CRITICAL OVERRIDES - Load ABSOLUTELY LAST to override ace.min.css */
@import url('modern-theme_overrides.css');


/* Modern code styling */
code {
    background-color: var(--color-gray-100);
    color: var(--color-gray-800);
    padding: var(--space-0-5) var(--space-1);
    border-radius: var(--border-radius-xs);
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;
    font-size: 0.875em;
}

pre {
    background-color: var(--color-gray-900);
    color: var(--color-gray-100);
    padding: var(--space-4);
    border-radius: var(--border-radius);
    overflow-x: auto;
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;
}

pre code {
    background: transparent;
    color: inherit;
    padding: 0;
}

/* Modern blockquote styling */
blockquote {
    border-left: 4px solid var(--color-primary-500);
    background-color: var(--color-primary-50);
    padding: var(--space-4);
    margin: var(--space-4) 0;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    font-style: italic;
}

/* Enhanced print styles */
@media print {
    * {
        color: #000 !important;
        background: #fff !important;
        box-shadow: none !important;
    }
    
    .sidebar,
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
    }
}



