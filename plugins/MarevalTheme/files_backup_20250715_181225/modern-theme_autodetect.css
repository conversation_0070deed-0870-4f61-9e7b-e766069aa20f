/* ================================
   Project State Auto-Detection via CSS :has() Selector
   Modern CSS approach for dynamic project state theming
   ================================ */

/* Default fallback colors (project-state-0 blue) */
:root {
    --link-color: #3B82F6 !important; /* Blue fallback */
    --bg-color: #C2E7FF !important;
    --hover-color: #2563EB !important;
    --active-color: #1D4ED8 !important;
}

/* Auto-detect project state 35 (Document Verification) */
body:has([class*="project-state-35"]) {
    --link-color: #10B981 !important; /* Green */
    --bg-color: #CFFFE5 !important;
    --hover-color: #059669 !important;
    --active-color: #047857 !important;
}

/* Auto-detect project state 45 (Master Documents) */
body:has([class*="project-state-45"]) {
    --link-color: #B68929 !important; /* Yellow/Gold */
    --bg-color: #FFF1C2 !important;
    --hover-color: #A67C1B !important;
    --active-color: #92630F !important;
}

/* Auto-detect project state 55 (Meetings) */
body:has([class*="project-state-55"]) {
    --link-color: #8B5CF6 !important; /* Purple */
    --bg-color: #E9D7FF !important;
    --hover-color: #7C3AED !important;
    --active-color: #6D28D9 !important;
}

/* Auto-detect project state 65 (Minutes of Meeting) */
body:has([class*="project-state-65"]) {
    --link-color: #DC4C35 !important; /* Red */
    --bg-color: #FFD9D1 !important;
    --hover-color: #C73E1D !important;
    --active-color: #B91C1C !important;
}

/* Auto-detect project state 0 (All Projects) */
body:has([class*="project-state-0"]) {
    --link-color: #3B82F6 !important; /* Blue */
    --bg-color: #C2E7FF !important;
    --hover-color: #2563EB !important;
    --active-color: #1D4ED8 !important;
}

/* Auto-detect from project ID in URL parameters */
body:has([href*="project_id=35"]) {
    --link-color: #10B981 !important; /* Green */
    --bg-color: #CFFFE5 !important;
}

body:has([href*="project_id=45"]) {
    --link-color: #B68929 !important; /* Yellow/Gold */
    --bg-color: #FFF1C2 !important;
}

body:has([href*="project_id=55"]) {
    --link-color: #8B5CF6 !important; /* Purple */
    --bg-color: #E9D7FF !important;
}

body:has([href*="project_id=65"]) {
    --link-color: #DC4C35 !important; /* Red */
    --bg-color: #FFD9D1 !important;
}

/* Auto-detect from data attributes */
body:has([data-project-state="35"]) {
    --link-color: #10B981 !important; /* Green */
    --bg-color: #CFFFE5 !important;
}

body:has([data-project-state="45"]) {
    --link-color: #B68929 !important; /* Yellow/Gold */
    --bg-color: #FFF1C2 !important;
}

body:has([data-project-state="55"]) {
    --link-color: #8B5CF6 !important; /* Purple */
    --bg-color: #E9D7FF !important;
}

body:has([data-project-state="65"]) {
    --link-color: #DC4C35 !important; /* Red */
    --bg-color: #FFD9D1 !important;
}

/* Apply the detected colors to sidebar elements */
body:has([class*="project-state-"]) #sidebar ul.nav.nav-list li a,
body:has([href*="project_id="]) #sidebar ul.nav.nav-list li a,
body:has([data-project-state]) #sidebar ul.nav.nav-list li a {
    background-color: var(--link-color) !important;
    color: white !important;
}

body:has([class*="project-state-"]) #sidebar ul.nav.nav-list li,
body:has([href*="project_id="]) #sidebar ul.nav.nav-list li,
body:has([data-project-state]) #sidebar ul.nav.nav-list li {
    border-color: var(--link-color) !important;
}

/* Active navigation items */
body:has([class*="project-state-"]) #sidebar ul.nav.nav-list li.active,
body:has([href*="project_id="]) #sidebar ul.nav.nav-list li.active,
body:has([data-project-state]) #sidebar ul.nav.nav-list li.active {
    border-color: var(--active-color) !important;
}

body:has([class*="project-state-"]) #sidebar ul.nav.nav-list li.active a,
body:has([href*="project_id="]) #sidebar ul.nav.nav-list li.active a,
body:has([data-project-state]) #sidebar ul.nav.nav-list li.active a {
    background-color: var(--active-color) !important;
}

/* Hover states */
body:has([class*="project-state-"]) #sidebar ul.nav.nav-list li:hover:not(.active) a,
body:has([href*="project_id="]) #sidebar ul.nav.nav-list li:hover:not(.active) a,
body:has([data-project-state]) #sidebar ul.nav.nav-list li:hover:not(.active) a {
    background-color: var(--hover-color) !important;
}

/* Breadcrumbs and other elements */
body:has([class*="project-state-"]) .breadcrumbs,
body:has([href*="project_id="]) .breadcrumbs,
body:has([data-project-state]) .breadcrumbs {
    border-color: var(--link-color) !important;
}

body:has([class*="project-state-"]) .widget-header,
body:has([href*="project_id="]) .widget-header,
body:has([data-project-state]) .widget-header {
    background-color: var(--link-color) !important;
}

/* Fallback for browsers that don't support :has() */
@supports not (selector(:has(*))) {
    /* Fallback styles will be handled by JavaScript */
    body {
        --link-color: var(--md-primary-40); /* Material 3 purple fallback */
    }
}
