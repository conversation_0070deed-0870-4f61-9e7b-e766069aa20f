/* ================================
   Main Content and Page Styles - Material 3 (2025)
   ================================ */

/* ================================
   MAIN CONTENT LAYOUT POSITIONING
   ================================ */

/* Main content area adjustment - MantisBT main-content */
.main-content,
#main-content {
    margin-top: 64px;
    margin-left: 240px !important;
    width: calc(100% - 240px) !important;
    padding: var(--space-4) !important;
    min-height: 100vh !important;
    box-sizing: border-box !important;
}

/* Force main content positioning */
body .main-content,
body #main-content {
    margin-left: 240px !important;
    width: calc(100% - 240px) !important;
    padding: var(--space-4) !important;
    min-height: 100vh !important;
    box-sizing: border-box !important;
    position: relative !important;
    float: none !important;
    display: block !important;
}

/* Override any ACE CSS that might interfere */
body #main-container > .main-content {
    float: none !important;
    display: block !important;
    clear: none !important;
}

/* Responsive adjustments for main content */
@media (max-width: 768px) {
    .main-content,
    #main-content {
        margin-left: 180px !important;
        width: calc(100% - 180px) !important;
    }
}

@media (max-width: 480px) {
    .main-content,
    #main-content {
        margin-left: 160px !important;
        width: calc(100% - 160px) !important;
    }
}

/* Nav-recent */
.nav-recent {
    top: 50%;
    right: 150px;
    font-size: x-small;
    transform: translateY(-50%);
}

/* Legacy compatibility */
.main-content {
    min-width: 0; /* Prevents content overflow */
    min-height: 100%;
    overflow-x: hidden; /* Prevent horizontal scrollbar on main content */
}
.main-content:before,
.main-content:after {
    content: " ";
    display: table;
}
.main-content:after {
    clear: both;
}
.sidebar + .main-content {
    margin-left: 200px !important; /* Ensure consistent margin */
}

.page-content {
    background-color: var(--md-surface) !important;
    position: relative;
    margin: 0;
}

.page-header {
    margin: 0 0 var(--space-6) !important;
    border-bottom: 1px solid var(--md-outline-variant) !important;
    padding-bottom: var(--space-4) !important;
    padding-top: var(--space-3) !important;
}

.page-header h1 {
    padding: 0;
    margin: 0 var(--space-2) !important;
    font-size: var(--md-headline-small) !important;
    font-weight: 400 !important;
    color: var(--md-primary-40) !important;
    line-height: 1.2;
}

.page-header h1 small {
    margin: 0 var(--space-2) !important;
    font-size: var(--md-body-medium) !important;
    font-weight: 400 !important;
    color: var(--md-on-surface-variant) !important;
}

/* Action buttons with Material 3 styling */
.action-buttons a {
    margin: 0 var(--space-1) !important;
    display: inline-block;
    opacity: 0.87;
    transition: all var(--transition-fast) !important;
    color: var(--md-primary-40) !important;
    text-decoration: none !important;
}

.action-buttons a:hover {
    text-decoration: none !important;
    opacity: 1 !important;
    color: var(--md-primary-30) !important;
    transform: scale(1.1) !important;
}

/* ================================
   Main Content Material 3 Styling
   ================================ */
.main-content, .col-md-12, .col-md-8 {
    background-color: var(--md-surface) !important;
    border-radius: var(--border-radius) !important;
    margin-bottom: var(--space-8) !important;
}

table.table.table-bordered {
    border-radius: 0 !important;
    overflow: hidden !important;
    border: 0 !important;
}

.table thead th {
    background-color: var(--md-primary-90) !important;
    color: var(--md-primary-10) !important;
    font-weight: 500 !important;
    border-bottom: 2px solid var(--md-primary-40) !important;
}

.table tbody tr:hover {
    background-color: rgba(103, 80, 164, 0.08) !important;
}

.form-control, .form-select {
    border-radius: var(--border-radius) !important;
    border: 1px solid var(--md-outline) !important;
    background-color: var(--md-surface-container) !important;
    color: var(--md-on-surface) !important;
}

.btn {
    border-radius: var(--border-radius-xl) !important;
    padding: var(--space-3) var(--space-6) !important;
    font-size: var(--md-label-large) !important;
    font-weight: 500 !important;
}

.widget-box {
  border-radius: var(--border-radius);
  margin-bottom: 24px;
  box-shadow: none;
  padding: 1.2rem;
}

.widget-title {
  font-size: 1.2rem; /* 15.6px */
  font-weight: 600;
  margin-bottom: 12px;
}

#main-container {
    div.main-content {
        div.breadcrumbs {
            border-radius: 8px;
            min-height: 44px;
        }

        @media (min-width: 1200px) {
            div.page-content {
                padding: 8px 8px 8px;
            }
        }
        @media (min-width: 992px) {
            div.page-content {
                padding: 8px 8px 8px;
            }
        }
    }

    div.space-20 {
        margin-top: 0px;
        margin-bottom: 13px;;
    }

    a.btn-scroll-up.display {
        border-radius: 50%;
        border: gray 2px solid;
        right: 12px;
        bottom: 12px;
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    .btn.btn-bold, .btn.btn-round {
        border-bottom-width: 1px;
        min-width: 160px;
    }
}

.main-container {
    max-width: 100%;
    overflow-x: hidden;
}

/* Traditional layout approach - sidebar and main content side by side */
.main-content {
    margin-left: 210px !important; /* Force space for sidebar */
    min-width: 0; /* Prevents content overflow */
    max-width: calc(100vw - 210px); /* Ensure content doesn't exceed viewport width */
    overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Ensure all widgets and tables stay within bounds */
.widget-box,
.widget-main,
.page-content {
    max-width: 100%;
    width: 100% !important; /* Force full width */
    overflow-x: hidden; /* Prevent horizontal scrollbar */
}

/* Only table-responsive containers should have horizontal scroll */
.table-responsive {
    max-width: 100%;
    width: 100% !important;
    overflow-x: auto; /* Only tables get horizontal scroll when needed */
}

/* Make tables responsive */
.table {
    width: 100% !important;
    table-layout: auto !important; /* Allow cells to expand naturally */
    min-width: 100% !important;
}

.table th,
.table td {
    word-wrap: break-word;
    overflow-wrap: break-word;
    width: auto !important; /* Let cells size naturally */
}

/*********************************************/
/* Main Content responsive styles                   */
/* *******************************************/

/* Responsive behavior for mobile */
@media (max-width: 991px) {
    .main-content {
        margin-left: 0 !important;
        max-width: 100vw !important;
    }
}

@media (min-width: 992px) {
    .sidebar.compact + .main-content {
        margin-left: 240px !important;
        max-width: calc(100vw - 240px) !important;
    }
    
    .sidebar.menu-min + .main-content {
        margin-left: 90px !important;
        max-width: calc(100vw - 90px) !important;
    }
}