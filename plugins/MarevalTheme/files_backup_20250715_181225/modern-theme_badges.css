/* Badge and Label styles from Ace.css */

.badge,.label {
    font-weight: 400;
    background-color: var(--md-primary-100);
    color: var(--md-primary-50);
    text-shadow: none
}

.badge {
    padding-top: 1px;
    padding-bottom: 3px;
    line-height: 15px
}

.badge.radius-1 {
    border-radius: 1px
}

.badge.radius-2 {
    border-radius: 2px
}

.badge.radius-3 {
    border-radius: 3px
}

.badge.radius-4 {
    border-radius: 4px
}

.badge.radius-5 {
    border-radius: 5px
}

.badge.radius-6 {
    border-radius: 6px
}

.badge-transparent,.badge.badge-transparent,.label-transparent,.label.label-transparent {
    background-color: transparent
}

.badge-grey,.badge.badge-grey,.label-grey,.label.label-grey {
    background-color: #A0A0A0
}

.badge-info,.badge.badge-info,.label-info,.label.label-info {
    background-color: #3A87AD
}

.badge-primary,.badge.badge-primary,.label-primary,.label.label-primary {
    background-color: #428BCA
}

.badge-success,.badge.badge-success,.label-success,.label.label-success {
    background-color: #82AF6F
}

.badge-danger,.badge-important,.badge.badge-danger,.badge.badge-important,.label-danger,.label-important,.label.label-danger,.label.label-important {
    background-color: #D15B47
}

.badge-inverse,.badge.badge-inverse,.label-inverse,.label.label-inverse {
    background-color: #333
}

.badge-warning,.badge.badge-warning,.label-warning,.label.label-warning {
    background-color: #F89406
}

.badge-pink,.badge.badge-pink,.label-pink,.label.label-pink {
    background-color: #D6487E
}

.badge-purple,.badge.badge-purple,.label-purple,.label.label-purple {
    background-color: #9585BF
}

.badge-yellow,.badge.badge-yellow,.label-yellow,.label.label-yellow {
    background-color: #FEE188
}

.badge-light,.badge.badge-light,.label-light,.label.label-light {
    background-color: #E7E7E7
}

.badge-yellow,.label-yellow {
    color: #963;
    border-color: #FEE188
}

.badge-light,.label-light {
    color: #888
}

.label.arrowed,.label.arrowed-in {
    position: relative;
    z-index: 1
}

.label.arrowed-in:before,.label.arrowed:before {
    display: inline-block;
    content: "";
    position: absolute;
    top: 0;
    z-index: -1;
    border: 1px solid transparent;
    border-right-color: var(--md-primary-100)
}

.label.arrowed-in:before {
    border-color: #ABBAC3 #ABBAC3 #ABBAC3 transparent
}

.label.arrowed-in-right,.label.arrowed-right {
    position: relative;
    z-index: 1
}

.label.arrowed-in-right:after,.label.arrowed-right:after {
    display: inline-block;
    content: "";
    position: absolute;
    top: 0;
    z-index: -1;
    border: 1px solid transparent;
    border-left-color: #ABBAC3
}

.label.arrowed-in-right:after {
    border-color: #ABBAC3 transparent #ABBAC3 #ABBAC3
}

.label-info.arrowed:before {
    border-right-color: #3A87AD
}

.label-info.arrowed-in:before {
    border-color: #3A87AD #3A87AD #3A87AD transparent
}

.label-info.arrowed-right:after {
    border-left-color: #3A87AD
}

.label-info.arrowed-in-right:after {
    border-color: #3A87AD transparent #3A87AD #3A87AD
}

.label-primary.arrowed:before {
    border-right-color: #428BCA
}

.label-primary.arrowed-in:before {
    border-color: #428BCA #428BCA #428BCA transparent
}

.label-primary.arrowed-right:after {
    border-left-color: #428BCA
}

.label-primary.arrowed-in-right:after {
    border-color: #428BCA transparent #428BCA #428BCA
}

.label-success.arrowed:before {
    border-right-color: #82AF6F
}

.label-success.arrowed-in:before {
    border-color: #82AF6F #82AF6F #82AF6F transparent
}

.label-success.arrowed-right:after {
    border-left-color: #82AF6F
}

.label-success.arrowed-in-right:after {
    border-color: #82AF6F transparent #82AF6F #82AF6F
}

.label-warning.arrowed:before {
    border-right-color: #F89406
}

.label-danger.arrowed:before,.label-important.arrowed:before {
    border-right-color: #D15B47
}

.label-warning.arrowed-in:before {
    border-color: #F89406 #F89406 #F89406 transparent
}

.label-warning.arrowed-right:after {
    border-left-color: #F89406
}

.label-danger.arrowed-right:after,.label-important.arrowed-right:after {
    border-left-color: #D15B47
}

.label-warning.arrowed-in-right:after {
    border-color: #F89406 transparent #F89406 #F89406
}

.label-important.arrowed-in:before {
    border-color: #D15B47 #D15B47 #D15B47 transparent
}

.label-important.arrowed-in-right:after {
    border-color: #D15B47 transparent #D15B47 #D15B47
}

.label-danger.arrowed-in:before {
    border-color: #D15B47 #D15B47 #D15B47 transparent
}

.label-danger.arrowed-in-right:after {
    border-color: #D15B47 transparent #D15B47 #D15B47
}

.label-inverse.arrowed:before {
    border-right-color: #333
}

.label-inverse.arrowed-in:before {
    border-color: #333 #333 #333 transparent
}

.label-inverse.arrowed-right:after {
    border-left-color: #333
}

.label-inverse.arrowed-in-right:after {
    border-color: #333 transparent #333 #333
}

.label-pink.arrowed:before {
    border-right-color: #D6487E
}

.label-pink.arrowed-in:before {
    border-color: #D6487E #D6487E #D6487E transparent
}

.label-pink.arrowed-right:after {
    border-left-color: #D6487E
}

.label-pink.arrowed-in-right:after {
    border-color: #D6487E transparent #D6487E #D6487E
}

.label-purple.arrowed:before {
    border-right-color: #9585BF
}

.label-purple.arrowed-in:before {
    border-color: #9585BF #9585BF #9585BF transparent
}

.label-purple.arrowed-right:after {
    border-left-color: #9585BF
}

.label-purple.arrowed-in-right:after {
    border-color: #9585BF transparent #9585BF #9585BF
}

.label-yellow.arrowed:before {
    border-right-color: #FEE188
}

.label-yellow.arrowed-in:before {
    border-color: #FEE188 #FEE188 #FEE188 transparent
}

.label-yellow.arrowed-right:after {
    border-left-color: #FEE188
}

.label-yellow.arrowed-in-right:after {
    border-color: #FEE188 transparent #FEE188 #FEE188
}

.label-light.arrowed:before {
    border-right-color: #E7E7E7
}

.label-light.arrowed-in:before {
    border-color: #E7E7E7 #E7E7E7 #E7E7E7 transparent
}

.label-light.arrowed-right:after {
    border-left-color: #E7E7E7
}

.label-light.arrowed-in-right:after {
    border-color: #E7E7E7 transparent #E7E7E7 #E7E7E7
}

.label-grey.arrowed:before {
    border-right-color: #A0A0A0
}

.label-grey.arrowed-in:before {
    border-color: #A0A0A0 #A0A0A0 #A0A0A0 transparent
}

.label-grey.arrowed-right:after {
    border-left-color: #A0A0A0
}

.label-grey.arrowed-in-right:after {
    border-color: #A0A0A0 transparent #A0A0A0 #A0A0A0
}

.label {
    line-height: 1.15;
    height: 20px
}

.label.arrowed:before {
    left: -10px;
    border-width: 10px 5px;
}

.label-lg.arrowed,.label-lg.arrowed-in {
    margin-left: 6px
}

.label.arrowed-in:before {
    left: -5px;
    border-width: 10px 5px
}

.label.arrowed-right:after {
    right: -10px;
    border-width: 10px 5px
}

.label-lg.arrowed-in-right,.label-lg.arrowed-right {
    margin-right: 6px
}

.label.arrowed-in-right:after {
    right: -5px;
    border-width: 10px 5px
}

.label-lg {
    padding: .3em .6em .4em;
    font-size: 13px;
    line-height: 1.1;
    height: 24px
}

.label-lg.arrowed:before {
    left: -12px;
    border-width: 12px 6px
}

.label-xlg.arrowed,.label-xlg.arrowed-in {
    margin-left: 7px
}

.label-lg.arrowed-in:before {
    left: -6px;
    border-width: 12px 6px
}

.label-lg.arrowed-right:after {
    right: -12px;
    border-width: 12px 6px
}

.label-xlg.arrowed-in-right,.label-xlg.arrowed-right {
    margin-right: 7px
}

.label-lg.arrowed-in-right:after {
    right: -6px;
    border-width: 12px 6px
}

.label-xlg {
    padding: .3em .7em .4em;
    font-size: 14px;
    line-height: 1.3;
    height: 28px
}

.label-xlg.arrowed:before {
    left: -14px;
    border-width: 14px 7px
}

.label-sm.arrowed,.label-sm.arrowed-in {
    margin-left: 4px
}

.label-xlg.arrowed-in:before {
    left: -7px;
    border-width: 14px 7px
}

.label-xlg.arrowed-right:after {
    right: -14px;
    border-width: 14px 7px
}

.label-sm.arrowed-in-right,.label-sm.arrowed-right {
    margin-right: 4px
}

.label-xlg.arrowed-in-right:after {
    right: -7px;
    border-width: 14px 7px
}

.label-sm {
    padding: .2em .4em .3em;
    font-size: 11px;
    line-height: 1;
    height: 18px
}

.label-sm.arrowed:before {
    left: -8px;
    border-width: 9px 4px
}

.label-sm.arrowed-in:before {
    left: -4px;
    border-width: 9px 4px
}

.label-sm.arrowed-right:after {
    right: -8px;
    border-width: 9px 4px
}

.label-sm.arrowed-in-right:after {
    right: -4px;
    border-width: 9px 4px
}

.label>.ace-icon,.label>span {
    line-height: 1;
    vertical-align: bottom
}

.label.label-white {
    color: #879da9;
    border: 1px solid #ABBAC3;
    background-color: #f2f5f6;
    border-right-width: 1px;
    border-left-width: 2px
}

.label-white.label-success {
    color: #7b9e6c;
    border-color: #9fbf92;
    background-color: #edf3ea
}

.label-white.label-warning {
    color: #d9993e;
    border-color: #e4ae62;
    background-color: #fef6eb
}

.label-white.label-primary {
    color: #6688a6;
    border-color: #8aafce;
    background-color: #eaf2f8
}

.label-white.label-danger {
    color: #bd7f75;
    border-color: #d28679;
    background-color: #fcf4f2
}

.label-white.label-info {
    color: #4e7a8f;
    border-color: #7aa1b4;
    background-color: #eaf3f7
}

.label-white.label-inverse {
    color: #404040;
    border-color: #737373;
    background-color: #ededed
}

.label-white.label-pink {
    color: #af6f87;
    border-color: #d299ae;
    background-color: #fbeff4
}

.label-white.label-purple {
    color: #7d6fa2;
    border-color: #b7b1c6;
    background-color: #efedf5
}

.label-white.label-yellow {
    color: #cfa114;
    border-color: #ecd181;
    background-color: #fdf7e4
}

.label-white.label-grey {
    color: #878787;
    border-color: #cecece;
    background-color: #ededed
}
