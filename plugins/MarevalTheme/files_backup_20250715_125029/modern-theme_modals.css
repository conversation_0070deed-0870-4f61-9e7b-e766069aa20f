/* ================================
   Material Design 3 Modal System (2025)
   Based on Google Material You Guidelines
   ================================ */

/* Material 3 Modal Backdrop */
.modal-backdrop {
    background-color: rgba(29, 27, 32, 0.32);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
}

.modal-backdrop.in {
    opacity: 1;
}

/* Material 3 Modal Content */
.modal-content {
    border-radius: var(--border-radius-3xl);
    box-shadow: var(--md-elevation-3);
    border: none;
    background-color: var(--md-surface-container-high);
    overflow: hidden;
}

.modal-header {
    padding: var(--space-6) var(--space-6) var(--space-4);
    border-bottom: none;
    background-color: var(--md-surface-container-high);
    border-top-left-radius: var(--border-radius-3xl);
    border-top-right-radius: var(--border-radius-3xl);
}

.modal-title {
    font-size: var(--md-headline-small);
    font-weight: 400;
    color: var(--md-on-surface);
    margin: 0;
    line-height: 32px;
    letter-spacing: 0px;
}

.modal-body {
    padding: var(--space-6);
    color: var(--md-on-surface);
    line-height: 24px;
    font-size: var(--md-body-medium);
}

.modal-body.padding-25 {
    padding: var(--space-6) var(--space-6);
}

.modal-footer {
    padding: var(--space-4) var(--space-6) var(--space-6);
    border-top: none;
    background-color: var(--md-surface-container-high);
    border-bottom-left-radius: var(--border-radius-3xl);
    border-bottom-right-radius: var(--border-radius-3xl);
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
    align-items: center;
}

/* Material 3 Close Button */
.modal-header .close {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    background: none;
    border: none;
    font-size: var(--md-title-medium);
    font-weight: 400;
    color: var(--md-on-surface-variant);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--border-radius-xl);
    transition: var(--transition-fast);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.modal-header .close:hover {
    background-color: var(--md-secondary-90);
    color: var(--md-on-surface);
}

.modal-header .close:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(103, 80, 164, 0.12);
}

/* Material 3 Dark Modal Variant */
.modal.aside-dark .modal-content {
    background-color: var(--md-surface-container);
    color: var(--md-on-surface);
    border: 1px solid var(--md-outline-variant);
}

.modal.aside-dark .modal-header {
    background-color: var(--color-gray-900);
    border-bottom-color: var(--color-gray-700);
}

.modal.aside-dark .modal-footer {
    background-color: var(--color-gray-800);
    border-top-color: var(--color-gray-700);
}

.modal.aside-dark .close {
    color: var(--color-gray-300);
}

.modal.aside-dark .close:hover {
    color: var(--color-white);
    background-color: var(--color-gray-700);
}

/* Modern Aside Modals */
.modal.aside {
    z-index: var(--z-modal);
    position: absolute;
}

.navbar-fixed-top ~ .modal.aside-vc {
    z-index: var(--z-modal);
}

.modal.aside-fixed.aside-hz,
.navbar-fixed-top ~ .modal.aside-hz,
.navbar-fixed-bottom ~ .modal.aside-hz {
    position: fixed;
    z-index: var(--z-modal);
}

.modal.aside-fixed.aside-vc {
    position: fixed;
}

.modal.aside.in {
    z-index: var(--z-modal);
    position: fixed;
}

.aside.aside-contained {
    position: fixed;
}

/* Modern Vertical Center Aside Modal */
.modal.aside-vc {
    margin: auto;
    width: 0;
    left: auto;
    right: auto;
    top: 0;
    bottom: 0;
    display: block !important;
    overflow: visible;
}

.modal.in.aside-vc {
    width: 100%;
}

.modal.aside-vc .modal-dialog {
    margin: inherit;
    overflow: inherit;
    width: 320px;
    max-width: 90vw;
    height: inherit;
    position: inherit;
    right: inherit;
    top: inherit;
    bottom: inherit;
    left: inherit;
    opacity: 1;
    transition: transform var(--transition-base) var(--ease-out);
}

.modal.aside-vc .modal-content {
    height: 100%;
    overflow: hidden;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal.in.aside-vc .modal-dialog {
    transform: none;
    -webkit-transform: none;
    height: auto;
}

/* Aside trigger */
.modal.aside-vc .aside-trigger {
    position: absolute;
    top: 155px;
    right: auto;
    left: auto;
    bottom: auto;
    margin-top: -1px;
    width: 37px;
    outline: none;
}

.modal.aside-vc .aside-trigger.ace-settings-btn {
    width: 42px;
}

.modal.in.aside-vc .aside-trigger {
    z-index: -1;
}

.aside.modal.in .modal-backdrop.in + .modal-dialog .aside-trigger {
    z-index: auto;
}

/* Right side modal */
.modal.aside-right {
    right: 0;
}

.modal.aside-right .modal-content {
    border-width: 0 0 0 1px;
    box-shadow: -2px 1px 2px 0 rgba(0, 0, 0, 0.15);
}

.modal.aside-right .aside-trigger {
    right: 100%;
}

.modal.aside-right .modal-dialog {
    transform: translateX(100%);
    -webkit-transform: translateX(100%);
}

.modal.aside-right.in .modal-dialog {
    transform: translateX(0);
    -webkit-transform: translateX(0);
}

/* Modern Modal Animation */
.modal.fade .modal-dialog {
    transform: translateY(-50px) scale(0.95);
    transition: transform var(--transition-base) var(--ease-out),
                opacity var(--transition-base) var(--ease-out);
}

.modal.fade.in .modal-dialog {
    transform: translateY(0) scale(1);
}

/* Modern Modal Sizes */
.modal-sm .modal-dialog {
    width: 400px;
    max-width: 90vw;
}

.modal-lg .modal-dialog {
    width: 800px;
    max-width: 90vw;
}

.modal-xl .modal-dialog {
    width: 1200px;
    max-width: 95vw;
}

.modal-fullscreen .modal-dialog {
    width: 100vw;
    height: 100vh;
    margin: 0;
    max-width: none;
}

.modal-fullscreen .modal-content {
    height: 100vh;
    border-radius: 0;
}

/* Modern Scrollable Modal */
.modal-dialog-scrollable {
    height: calc(100vh - 3rem);
}

.modal-dialog-scrollable .modal-content {
    height: 100%;
    overflow: hidden;
}

.modal-dialog-scrollable .modal-body {
    overflow-y: auto;
}

/* Modern Modal Loading State */
.modal-loading .modal-content {
    position: relative;
    pointer-events: none;
}

.modal-loading .modal-content::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 32px;
    height: 32px;
    margin: -16px 0 0 -16px;
    border: 3px solid var(--color-gray-200);
    border-top-color: var(--color-primary-500);
    border-radius: var(--border-radius-full);
    animation: spin var(--duration-1000) linear infinite;
    z-index: var(--z-10);
}

/* Enhanced aside trigger styling */
.modal.aside-vc .aside-trigger {
    position: absolute;
    top: var(--space-20);
    right: auto;
    left: auto;
    bottom: auto;
    margin-top: -1px;
    width: 42px;
    height: 42px;
    outline: none;
    background-color: var(--color-primary-500);
    color: var(--color-white);
    border: none;
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
    box-shadow: var(--shadow-md);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal.aside-vc .aside-trigger:hover {
    background-color: var(--color-primary-600);
    box-shadow: var(--shadow-lg);
}

.modal.aside-vc .aside-trigger.ace-settings-btn {
    width: 48px;
    height: 48px;
}

.modal.in.aside-vc .aside-trigger {
    z-index: -1;
    opacity: 0;
}

.aside.modal.in .modal-backdrop.in + .modal-dialog .aside-trigger {
    z-index: auto;
    opacity: 1;
}

/* Modern Right Side Modal */
.modal.aside-right {
    right: 0;
}

.modal.aside-right .modal-content {
    border-left: 1px solid var(--color-border);
    border-radius: var(--border-radius-lg) 0 0 var(--border-radius-lg);
    box-shadow: var(--shadow-2xl);
}

.modal.aside-right .aside-trigger {
    right: 100%;
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

.modal.aside-right .modal-dialog {
    transform: translateX(100%);
}

.modal.aside-right.in .modal-dialog {
    transform: translateX(0);
}

/* Enhanced responsive modal adjustments */
@media (max-width: 576px) {
    .modal-dialog {
        margin: var(--space-4);
        width: calc(100% - 2rem);
    }
    
    .modal-header {
        padding: var(--space-4);
    }
    
    .modal-body {
        padding: var(--space-4);
    }
    
    .modal-footer {
        padding: var(--space-4);
        flex-direction: column-reverse;
        gap: var(--space-2);
    }
    
    .modal-footer .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 320px) {
    .modal.aside-vc .modal-dialog {
        width: 280px;
        max-width: 95vw;
    }
    
    .aside-contained.aside-vc .modal-dialog {
        width: 260px;
    }
}

@media (max-width: 240px) {
    .modal.aside-vc .modal-dialog {
        width: 200px;
    }
    
    .aside-contained.aside-vc .modal-dialog {
        width: 180px;
    }
}

@media (max-height: 500px) {
    .modal.aside-vc .aside-trigger {
        top: var(--space-16);
    }
    
    .modal-dialog {
        margin-top: var(--space-2);
        margin-bottom: var(--space-2);
    }
}

/* Navbar offset adjustments */
.modal.aside-vc.navbar-offset .modal-dialog {
    top: 50px;
}

.modal.aside-vc.navbar-offset .modal-dialog .aside-trigger {
    top: var(--space-16);
}

@media (max-width: 479px) {
    .navbar:not(.navbar-collapse) ~ .modal.aside-vc.navbar-offset .modal-dialog {
        top: 70px;
    }
}

/* Modern modal overlay effects */
.modal-backdrop.glass {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
}

/* Accessibility improvements */
.modal-content:focus {
    outline: none;
}

.modal[aria-hidden="true"] {
    display: none !important;
}

/* Modern modal animations for different entrance types */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal.slide-right.in .modal-dialog {
    animation: slideInRight var(--transition-base) var(--ease-out);
}

.modal.slide-left.in .modal-dialog {
    animation: slideInLeft var(--transition-base) var(--ease-out);
}

.modal.slide-up.in .modal-dialog {
    animation: slideInUp var(--transition-base) var(--ease-out);
}