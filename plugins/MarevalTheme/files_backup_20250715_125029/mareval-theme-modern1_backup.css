 
.project-state-0 { --bg-color: #C2E7FF; --link-color: #3B82F6; } /* All Other links */
.project-state-35 { --bg-color: #CFFFE5; --link-color: #10B981; } /* Document Verification */
.project-state-45 { --bg-color: #FFF1C2; --link-color: #B68929; } /* Master Documents */
.project-state-55 { --bg-color: #E9D7FF; --link-color: #8B5CF6; } /* Meetings */
.project-state-65 { --bg-color: #FFD9D1; --link-color: #DC4C35; } /* Minutes of Meeting */

/* Apply theme */
.sidebar, .tab, .project-label {
    background-color: var(--bg-color) !important;
}

a, a:visited, a:active {
    color: var(--link-color) !important;
}

a:hover {
    text-decoration: underline;
    filter: brightness(90%);
}

.btn, .link-tag {
    background-color: var(--link-color) !important;
    color: white !important;
    border-radius: 12px;
}

.btn:hover {
    filter: brightness(110%);
}


#navbar {

    border-bottom-right-radius: 8px;
    
    a.navbar-brand {
        padding-left: 0px;
        padding-right: 0px;
    }
    div.navbar-collapse {
        padding-left: 0px;
        padding-right: 0px;

        .btn-primary {
            min-width: 160px;
        }

        #dropdown_projects_menu {
            min-width: 160px;    
        }

        a.dropdown-toggle {
            min-width: 160px;    
        }
    }

}

#main-container {

    #sidebar {
        background-color: transparent;

        ul.nav.nav-list {
            flex-grow: 1;
            
            li {
                margin: 8px;
                border: #A4C6DD 2px solid;
                border-radius: 8px;

                a {                   
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;

                    padding: 4px 0px 4px 20px;
                    background-color: transparent;
                }
            }
            li:before {
                /* hide bold line on the left */
                display: none;
            }   
            li.active {
                border: #4D96CB 2px solid !important;
            }
            li.active>a {
                font-weight: 500;
                background-color: #4D96CB !important;
                color: #ffffff !important;                
                /* 
                border-radius: 8px;
                background-color: #f3faff !important;
                color: #4D96CB !important;
                */
            }

            li.active>a:before {
                /* hide marker on the right */
                 display: none;
            }
        }

        ul.nav.nav-list li:first-child {
            margin-bottom: 24px;
        }
        ul.nav.nav-list li:nth-child(3) {
            margin-bottom: 24px;
        }
        ul.nav.nav-list li:nth-child(5) {
            margin-bottom: 24px;
        }
        ul.nav.nav-list li:nth-child(7) {
            margin-bottom: 24px;
        }
        ul.nav.nav-list li:nth-child(9) {
            margin-bottom: 24px;
        }
        ul.nav.nav-list li:nth-child(10) {
            margin-bottom: 24px;
        }
        ul.nav.nav-list li:nth-child(13) {
            margin-bottom: 24px;
        }

        .sidebar-toggle {
            background-color: transparent; 
        }

        .sidebar-toggle>.ace-icon {
            border: none;
            font-size: 24px;
        }

        .sidebar-toggle:before {
            display: none;
        }

    }

    #sidebar.sidebar.sidebar-fixed.responsive.compact {
        /* width: 240px;  */
        display: flex;
        flex-direction: column;
        height: calc(100vh - 45px);
    }

    div.main-content {

        div.breadcrumbs {
            border-radius: 8px;
            min-height: 44px;
        }
       
        div.page-content {

            div.row {
                margin-right: 0px;
                margin-left: 0px;

                div.col-md-12.col-xs-12 {

                    padding-right: 0px;
                    padding-left: 0px;
                    margin-top: -3px;

                    div.space-10 {
                        /* Abstand zwischen den Panles */
                        margin: 0px 0 8px;
                    }

                    div.filter-box {
                        border-radius: 8px;

                        #filter {
                            border: none;
                            /* border-radius: 8px; */

                            div.widget-header {
                                border-top-left-radius: 8px;
                                border-top-right-radius: 8px;
                            }

                            div.widget-body {
                                border-bottom-left-radius: 8px;
                                border-bottom-right-radius: 8px;

                                div.widget-toolbox {
                                    background-color: #E7F2F8;
                                    padding: 8px;
                                }
                                div.widget-toolbox:first-child {
                                    border-bottom: none;
                                }

                                form div.widget-toolbox {
                                    border-bottom-left-radius: 8px;
                                    border-bottom-right-radius: 8px;
                                    background-color: #E7F2F8;
                                }
                            }
                        }
                        
                        #filter.collapsed {
                            border-radius: 8px;

                            div.widget-header {
                                border-radius: 8px;

                            .btn.btn-bold, .btn.btn-round {
                                min-width: 50px;
                            }

                            }
                        }
                   }

                    form {
                        border-radius: 8px;

                        div.widget-box {
                            border: none;
                            /* border-radius: 8px; */

                            div.widget-header {
                                border-top-left-radius: 8px;
                                border-top-right-radius: 8px;
                            }

                            div.widget-body {
                                div.widget-toolbox {
                                    background-color: #E7F2F8;
                                }
                            }

                            div.widget-main {
                                border-bottom-left-radius: 8px;
                                border-bottom-right-radius: 8px;

                                .table>thead>tr {
                                    background: none;
                                    background-image: none;
                                }

                                div.widget-toolbox {
                                    border-bottom-left-radius: 8px;
                                    border-bottom-right-radius: 8px;
                                    background-color: #E7F2F8;
                                }
                            }
                        }
                   }
                }
            }
        }

        @media (min-width: 1200px) {
            div.page-content {
                padding: 8px 8px 8px;
            }
        }
        @media (min-width: 992px) {
            div.page-content {
                padding: 8px 8px 8px;
            }
        }
    }

    div.space-20 {
        margin-top: 0px;
        margin-bottom: 13px;;
    }

    a.btn-scroll-up.display {
        border-radius: 50%;
        border: gray 2px solid;
        right: 12px;
        bottom: 12px;
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    .btn.btn-bold, .btn.btn-round {
        border-bottom-width: 1px;
        min-width: 160px;
    }
}

/*********************************************/
/* SIDEBAR                                   */
/* *******************************************/

@media (min-width: 1200px) {
    .sidebar.compact, .sidebar.compact.navbar-collapse {
        /* width: 125px; */
        width: 240px !important;
    }
}
@media (min-width: 992px) {
    .sidebar.compact, .sidebar.compact.navbar-collapse {
        /* width: 125px; */
        width: 240px !important;
    }
}
.sidebar.menu-min, .sidebar.menu-min.compact, .sidebar.menu-min.navbar-collapse {
    width: 90px !important;
}

/*********************************************/
/* MAIN                                      */
/* *******************************************/

@media (min-width: 1200px) {
    .sidebar.compact + div.main-content {
        margin-right: auto !important;
        /* margin-left: 125px !important; */
        margin-left: 240px !important;
    }
}
@media (min-width: 992px) {
    .sidebar.compact + div.main-content {
        margin-right: auto !important;
        /* margin-left: 125px !important; */
        margin-left: 240px !important;
    }
}
@media (min-width: 1200px) {
    .sidebar.menu-min + div.main-content {
        margin-right: auto !important;
        /* margin-left: 43px !important; */
        margin-left: 90px !important;
     }
}
@media (min-width: 992px) {
    .sidebar.menu-min + div.main-content {
        margin-right: auto !important;
        /* margin-left: 43px !important; */
        margin-left: 90px !important;
    }
}

/*********************************************/
/* FOOTER                                    */
/* *******************************************/

@media (min-width: 1200px) {
    .sidebar.compact ~ .footer .footer-inner {
        /* left: 125px; */
        left: 240px !important;
    }
}
@media (min-width: 992px) {
    .sidebar.compact ~ .footer .footer-inner {
        /* left: 125px; */
        left: 240px !important;
    }
}

@media (min-width: 1200px) {
    .sidebar.menu-min ~ .footer .footer-inner {
        /* left: 43px; */
        left: 90px !important;
    }
}
@media (min-width: 992px) {
    .sidebar.menu-min ~ .footer .footer-inner {
        /* left: 43px; */
        left: 90px !important;
    }
}
