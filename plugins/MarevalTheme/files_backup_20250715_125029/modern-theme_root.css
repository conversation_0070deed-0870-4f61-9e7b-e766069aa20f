/* ================================
   Modern Root Styles (2025)
   Material Design 3 Tokens & Project State Colors
   ================================ */

/* ================================
   SIMPLIFIED PROJECT STATE COLOR SYSTEM
   Only affects widget headers and hyperlinks
   ================================ */

/* Project State 0 - Blue (All Other links) */
body.project-state-0 {
    --link-color: #3b82f6;
}

/* Project State 35 - Green (Document Verification) */
body.project-state-35 {
    --link-color: #10b981;
}

/* Project State 45 - Yellow (Master Documents) */
body.project-state-45 {
    --link-color: #f59e0b;
}

/* Project State 55 - Purple (Meetings) */
body.project-state-55 {
    --link-color: #8b5cf6;
}

/* Project State 65 - Red (Minutes of Meeting) */
body.project-state-65 {
    --link-color: #ef4444;
}

html {
    min-height: 100%;
    position: relative;
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

body {
    background-color: var(--color-bg);
    min-height: 100%;
    padding-bottom: 0;
    font-family: var(--font-family) !important;
    font-size: var(--md-body-medium) !important;
    font-weight: 400;
    color: var(--color-text) !important;
    line-height: 1.5 !important;
    font-feature-settings: "kern" 1, "liga" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    margin: 0 !important;
    padding: 0 !important;
}

/* ================================
   Modern Layout and Container Styles (2025)
   ================================ */
.main-container:before {
    display: block;
    content: "";
    position: absolute;
    z-index: -2;
    width: 100%;
    max-width: inherit;
    bottom: 0;
    top: 0;
    background-color: var(--color-white);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
}

.main-container.container,
.rtl .main-container.container {
    padding-left: 0;
    padding-right: 0;
}

.main-container.container:before {
    box-shadow: var(--shadow-sm);
    width: inherit;
    border-radius: var(--border-radius);
}

@media (max-width: 767px) {
    .main-container.container:before {
        box-shadow: none;
        width: 100%;
        border-radius: 0;
    }
}

/* ================================
   Modern Ajax Loading Overlay (2025)
   ================================ */
.ajax-loading-overlay {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--color-bg-overlay);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: var(--z-modal-backdrop);
    display: none;
    border-radius: inherit;
}

.ajax-loading-overlay > .ajax-loading-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -16px;
    margin-top: -16px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.ajax-loading-overlay.content-loaded {
    opacity: 0;
    transition: opacity var(--transition-base);
}

.ajax-loading-overlay.almost-loaded {
    opacity: 0.5;
}

.ajax-loading-overlay.ajax-overlay-body {
    position: fixed;
    border-radius: 0;
}

.ajax-loading-overlay.ajax-overlay-body > .ajax-loading-icon {
    margin-left: -32px;
    margin-top: -32px;
}

/* ================================
   ACE Icons
   ================================ */
.ace-icon {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

:root {
  /* ================================
     Material Design 3 (Material You) - 2025
     Based on Google's M3 Design System
     ================================ */
  
  /* Material Design 3 Color Modes */
  color-scheme: light dark;
  
  /* Material 3 Primary Color Palette - Project State Blue System */
  --md-primary-0: #404040;
  --md-primary-10: #001d3d;
  --md-primary-20: #003566;
  --md-primary-25: #004080;
  --md-primary-30: #1e4a8c;
  --md-primary-35: #2563eb;
  --md-primary-40: #3b82f6;
  --md-primary-50: #60a5fa;
  --md-primary-60: #93c5fd;
  --md-primary-70: #bfdbfe;
  --md-primary-80: #dbeafe;
  --md-primary-90: #eff6ff;
  --md-primary-95: #f0f9ff;
  --md-primary-98: #f8fafc;
  --md-primary-99: #fcfcfd;
  --md-primary-100: #ffffff;

  /* Material 3 Secondary Color Palette - Standard Blue-Gray System */
  --md-secondary-10: #1d192b;
  --md-secondary-20: #332d41;
  --md-secondary-30: #4a4458;
  --md-secondary-40: #625b71;
  --md-secondary-50: #7a7289;
  --md-secondary-60: #958da5;
  --md-secondary-70: #b0a7c0;
  --md-secondary-80: #ccc2dc;
  --md-secondary-90: #e8def8;
  --md-secondary-95: #f6edff;
  --md-secondary-99: #fffbfe;

  /* Material 3 Tertiary Color Palette - Standard Pink System */
  --md-tertiary-10: #31111d;
  --md-tertiary-20: #492532;
  --md-tertiary-30: #633b48;
  --md-tertiary-40: #7d5260;
  --md-tertiary-50: #986977;
  --md-tertiary-60: #b58392;
  --md-tertiary-70: #d29dac;
  --md-tertiary-80: #efb8c8;
  --md-tertiary-90: #ffd8e4;
  --md-tertiary-95: #ffecf1;
  --md-tertiary-99: #fffbfa;

  /* Material 3 Error Colors - Project State Red System */
  --md-error-10: #2d1b1b;
  --md-error-20: #7f1d1d;
  --md-error-30: #991b1b;
  --md-error-40: #dc4c35;
  --md-error-50: #ef4444;
  --md-error-60: #f87171;
  --md-error-70: #fca5a5;
  --md-error-80: #fecaca;
  --md-error-90: #fee2e2;
  --md-error-95: #fef2f2;
  --md-error-99: #fffbfb;

  /* Material 3 Success Colors - Standard Green System */
  --md-success-10: #002114;
  --md-success-20: #003919;
  --md-success-30: #00531f;
  --md-success-40: #146c2e;
  --md-success-50: #2e7d32;
  --md-success-60: #4caf50;
  --md-success-70: #6fbf73;
  --md-success-80: #81c784;
  --md-success-90: #a5d6a7;
  --md-success-95: #e8f5e8;
  --md-success-99: #f1f8e9;

  /* Material 3 Warning Colors - Project State Yellow System */
  --md-warning-10: #451a03;
  --md-warning-20: #78350f;
  --md-warning-30: #92400e;
  --md-warning-40: #b68929;
  --md-warning-50: #d97706;
  --md-warning-60: #f59e0b;
  --md-warning-70: #fbbf24;
  --md-warning-80: #fcd34d;
  --md-warning-90: #fde68a;
  --md-warning-95: #fef3c7;
  --md-warning-99: #fffbeb;

  /* Material 3 Neutral Color Palette */
  --md-neutral-0: #404040;
  --md-neutral-4: #0f0d13;
  --md-neutral-6: #141218;
  --md-neutral-10: #1d1b20;
  --md-neutral-12: #211f26;
  --md-neutral-17: #2b2930;
  --md-neutral-20: #322f35;
  --md-neutral-22: #373339;
  --md-neutral-24: #3b383e;
  --md-neutral-25: #3e3a41;
  --md-neutral-30: #484649;
  --md-neutral-35: #544f53;
  --md-neutral-40: #605d62;
  --md-neutral-50: #787579;
  --md-neutral-60: #918e92;
  --md-neutral-70: #aca9ac;
  --md-neutral-80: #c7c5ca;
  --md-neutral-87: #ddd8dd;
  --md-neutral-90: #e6e0e9;
  --md-neutral-92: #ebe5ea;
  --md-neutral-94: #f1ebf0;
  --md-neutral-95: #f4eff4;
  --md-neutral-96: #f7f2fa;
  --md-neutral-98: #fcf7fc;
  --md-neutral-99: #fffbfe;
  --md-neutral-100: #ffffff;

  /* Material 3 Neutral Variant */
  --md-neutral-variant-10: #1d1a22;
  --md-neutral-variant-20: #322f37;
  --md-neutral-variant-30: #49454f;
  --md-neutral-variant-40: #605d66;
  --md-neutral-variant-50: #79747e;
  --md-neutral-variant-60: #938f99;
  --md-neutral-variant-70: #aea9b4;
  --md-neutral-variant-80: #cac4d0;
  --md-neutral-variant-90: #e7e0ec;
  --md-neutral-variant-95: #f5eff7;
  --md-neutral-variant-99: #fffbfe;

  /* Material 3 Semantic Token Mapping */
  --color-primary: var(--md-primary-40);
  --color-primary-50: var(--md-primary-50);
  --color-primary-100: var(--md-primary-95);
  --color-primary-200: var(--md-primary-90);
  --color-primary-300: var(--md-primary-80);
  --color-primary-400: var(--md-primary-70);
  --color-primary-500: var(--md-primary-40);
  --color-primary-600: var(--md-primary-30);
  --color-primary-700: var(--md-primary-20);
  --color-primary-800: var(--md-primary-10);
  --color-primary-900: var(--md-primary-10);
  
  /* Error/Danger Colors */
  --color-error: var(--md-error-40);
  --color-error-50: var(--md-error-95);
  --color-error-100: var(--md-error-90);
  --color-error-200: var(--md-error-80);
  --color-error-300: var(--md-error-70);
  --color-error-400: var(--md-error-60);
  --color-error-500: var(--md-error-40);
  --color-error-600: var(--md-error-30);
  --color-error-700: var(--md-error-20);
  --color-error-800: var(--md-error-10);
  --color-error-900: var(--md-error-10);
  
  /* Success Colors */
  --color-success: var(--md-success-40);
  --color-success-50: var(--md-success-95);
  --color-success-100: var(--md-success-90);
  --color-success-200: var(--md-success-80);
  --color-success-300: var(--md-success-70);
  --color-success-400: var(--md-success-60);
  --color-success-500: var(--md-success-40);
  --color-success-600: var(--md-success-30);
  --color-success-700: var(--md-success-20);
  --color-success-800: var(--md-success-10);
  --color-success-900: var(--md-success-10);
  
  /* Warning Colors */
  --color-warning: var(--md-warning-50);
  --color-warning-50: var(--md-warning-95);
  --color-warning-100: var(--md-warning-90);
  --color-warning-200: var(--md-warning-80);
  --color-warning-300: var(--md-warning-70);
  --color-warning-400: var(--md-warning-60);
  --color-warning-500: var(--md-warning-50);
  --color-warning-600: var(--md-warning-40);
  --color-warning-700: var(--md-warning-30);
  --color-warning-800: var(--md-warning-20);
  --color-warning-900: var(--md-warning-10);
  
  /* Info Colors (using secondary) */
  --color-info: var(--md-secondary-40);
  --color-info-50: var(--md-secondary-95);
  --color-info-100: var(--md-secondary-90);
  --color-info-200: var(--md-secondary-80);
  --color-info-300: var(--md-secondary-70);
  --color-info-400: var(--md-secondary-60);
  --color-info-500: var(--md-secondary-40);
  --color-info-600: var(--md-secondary-30);
  --color-info-700: var(--md-secondary-20);
  --color-info-800: var(--md-secondary-10);
  --color-info-900: var(--md-secondary-10);
  
  /* Gray Colors (using neutral) */
  --color-gray-50: var(--md-neutral-96);
  --color-gray-100: var(--md-neutral-94);
  --color-gray-200: var(--md-neutral-90);
  --color-gray-300: var(--md-neutral-80);
  --color-gray-400: var(--md-neutral-70);
  --color-gray-500: var(--md-neutral-60);
  --color-gray-600: var(--md-neutral-50);
  --color-gray-700: var(--md-neutral-40);
  --color-gray-800: var(--md-neutral-30);
  --color-gray-900: var(--md-neutral-20);
  
  /* White variations */
  --color-white: var(--md-neutral-100);

  /* Material 3 Surface Colors */
  --md-surface: var(--md-neutral-99);
  --md-surface-dim: var(--md-neutral-87);
  --md-surface-bright: var(--md-neutral-98);
  --md-surface-container-lowest: var(--md-neutral-100);
  --md-surface-container-low: var(--md-neutral-96);
  --md-surface-container: var(--md-neutral-94);
  --md-surface-container-high: var(--md-neutral-92);
  --md-surface-container-highest: var(--md-neutral-90);
  --md-surface-variant: var(--md-neutral-variant-90);

  /* Material 3 Text Colors */
  --md-on-surface: var(--md-neutral-10);
  --md-on-surface-variant: var(--md-neutral-variant-30);
  --md-on-primary: var(--md-primary-100);
  --md-on-primary-container: var(--md-primary-10);
  --md-outline: var(--md-neutral-variant-50);
  --md-outline-variant: var(--md-neutral-variant-80);

  /* Design Token Mapping for Components */
  --color-text: var(--md-on-surface);
  --color-text-secondary: var(--md-on-surface-variant);
  --color-text-muted: var(--md-neutral-variant-40);
  --color-text-subtle: var(--md-neutral-variant-50);
  --color-text-disabled: var(--md-neutral-variant-60);
  --color-text-inverse: var(--md-on-primary);

  /* Background Colors */
  --color-bg: var(--md-surface);
  --color-bg-secondary: var(--md-surface-container-low);
  --color-bg-muted: var(--md-surface-container);
  --color-bg-subtle: var(--md-surface-container-high);
  --color-bg-overlay: rgba(29, 27, 32, 0.7);
  --color-bg-glass: rgba(255, 251, 254, 0.8);

  /* Border Colors */
  --color-border: var(--md-outline-variant);
  --color-border-subtle: var(--md-surface-container-high);
  --color-border-strong: var(--md-outline);
  --color-border-interactive: var(--md-outline);
  
  /* Focus Ring */
  --color-focus: var(--md-primary-40);
  --focus-ring: 0 0 0 3px rgba(103, 80, 164, 0.12);

  /* Material 3 Elevation System */
  --md-elevation-0: none;
  --md-elevation-1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
  --md-elevation-2: 0px 1px 2px 0px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
  --md-elevation-3: 0px 1px 3px 0px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
  --md-elevation-4: 0px 2px 3px 0px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
  --md-elevation-5: 0px 4px 4px 0px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);

  /* Modern Border Radius - Material 3 */
  --border-radius-none: 0px;
  --border-radius-xs: 4px;
  --border-radius-sm: 8px;
  --border-radius: 12px;
  --border-radius-md: 16px;
  --border-radius-lg: 20px;
  --border-radius-xl: 28px;
  --border-radius-2xl: 32px;
  --border-radius-3xl: 40px;
  --border-radius-full: 9999px;

  /* Material 3 Typography Scale */
  --md-display-large: 57px;
  --md-display-medium: 45px;
  --md-display-small: 36px;
  --md-headline-large: 32px;
  --md-headline-medium: 28px;
  --md-headline-small: 24px;
  --md-title-large: 22px;
  --md-title-medium: 16px;
  --md-title-small: 14px;
  --md-label-large: 14px;
  --md-label-medium: 12px;
  --md-label-small: 11px;
  --md-body-large: 16px;
  --md-body-medium: 14px;
  --md-body-small: 12px;

  /* Typography Scale Mapping */
  --font-size-xs: var(--md-label-small);
  --font-size-sm: var(--md-body-small);
  --font-size-base: var(--md-body-medium);
  --font-size-lg: var(--md-body-large);
  --font-size-xl: var(--md-title-large);
  --font-size-2xl: var(--md-headline-small);
  --font-size-3xl: var(--md-headline-medium);
  --font-size-4xl: var(--md-headline-large);

  /* Material 3 Spacing */
  --space-px: 1px;
  --space-0: 0;
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-7: 28px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;
  --space-24: 96px;

  /* Material 3 Transitions */
  --transition-emphasized: cubic-bezier(0.2, 0, 0, 1);
  --transition-standard: cubic-bezier(0.2, 0, 0, 1);
  --transition-emphasized-decelerate: cubic-bezier(0.05, 0.7, 0.1, 1);
  --transition-emphasized-accelerate: cubic-bezier(0.3, 0, 0.8, 0.15);
  
  --transition-fast: 200ms var(--transition-standard);
  --transition-base: 300ms var(--transition-standard);
  --transition-slow: 500ms var(--transition-emphasized);

  /* Material 3 Z-Index */
  --z-tooltip: 2000;
  --z-modal: 1300;
  --z-modal-backdrop: 1200;
  --z-popover: 1200;
  --z-dropdown: 1000;
  --z-sticky: 100;
  --z-fixed: 100;

  /* Project State Colors - Default Theme (Blue) */
  --link-color: #3b82f6;
  --hover-color: #2563eb;
  --active-color: #1d4ed8;
  --sidebar-bg: #dbeafe;
  --nav-bg: #3b82f6;
  --nav-active: #1d4ed8;
  --nav-hover: #2563eb;

  /* Legacy compatibility */
  --shadow-xs: none;
  --shadow-sm: none;
  --shadow-base: none;
  --shadow-md: none;
  --shadow-lg: none;
  --shadow-xl: none;
  --shadow-2xl: none;
  --shadow-inner: none;
  --shadow-focus: var(--focus-ring);
}

/* ================================
   Modern Base Reset and Utilities (2025)
   ================================ */

/* Enhanced reset for modern consistency */
*,
*::before,
*::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: var(--color-border);
}

/* Focus management for accessibility */
:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

:focus-visible {
  box-shadow: var(--shadow-focus);
}

/* Modern scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-muted);
  border-radius: var(--border-radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--border-radius-full);
  border: 2px solid var(--color-bg-muted);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* Modern selection styling */
::selection {
  background-color: rgba(99, 102, 241, 0.15);
  color: var(--color-text);
}

/* Enhanced reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Modern container queries support */
@container (min-width: 640px) {
  .container-sm\:hidden {
    display: none;
  }
}

/* Glass morphism utility classes */
.glass {
  background: var(--color-bg-glass);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Modern elevation system */
.elevation-0 { box-shadow: none; }
.elevation-1 { box-shadow: var(--shadow-xs); }
.elevation-2 { box-shadow: var(--shadow-sm); }
.elevation-3 { box-shadow: var(--shadow-base); }
.elevation-4 { box-shadow: var(--shadow-md); }
.elevation-5 { box-shadow: var(--shadow-lg); }
.elevation-6 { box-shadow: var(--shadow-xl); }
.elevation-7 { box-shadow: var(--shadow-2xl); }

/* Modern spacing utilities */
.space-y-1 > * + * { margin-top: var(--space-1); }
.space-y-2 > * + * { margin-top: var(--space-2); }
.space-y-3 > * + * { margin-top: var(--space-3); }
.space-y-4 > * + * { margin-top: var(--space-4); }
.space-y-6 > * + * { margin-top: var(--space-6); }
.space-y-8 > * + * { margin-top: var(--space-8); }

.space-x-1 > * + * { margin-left: var(--space-1); }
.space-x-2 > * + * { margin-left: var(--space-2); }
.space-x-3 > * + * { margin-left: var(--space-3); }
.space-x-4 > * + * { margin-left: var(--space-4); }
.space-x-6 > * + * { margin-left: var(--space-6); }
.space-x-8 > * + * { margin-left: var(--space-8); }

/* Modern responsive design utilities */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Modern animation classes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn var(--duration-300) var(--ease-out);
}

.animate-slide-up {
  animation: slideUp var(--duration-300) var(--ease-out);
}

.animate-slide-down {
  animation: slideDown var(--duration-300) var(--ease-out);
}

.animate-scale-in {
  animation: scaleIn var(--duration-200) var(--ease-out);
}

/* Modern typography utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Modern layout utilities */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
}

.grid-auto-fill {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--space-4);
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* ================================
   DUPLICATION CLEANUP COMPLETE (July 2025)
   
   BEFORE: 801 lines with duplicate :root blocks
   AFTER: 758 lines with single comprehensive :root
   EFFICIENCY GAIN: 43 lines removed
   
   Removed duplicates of:
   - First incomplete :root block (missing full Material 3 system)
   - Duplicate Material 3 color definitions
   - Duplicate spacing variables
   - Conflicting project state color definitions
   
   Kept comprehensive Material 3 system with:
   - Complete color palettes (primary, secondary, tertiary, error, success, warning)
   - Full spacing and typography scales
   - Semantic color mappings
   - Project state theming support
   - Modern utility classes
   ================================ */

