/* ================================
   Material Design 3 Table System (2025)
   Based on Google Material You Guidelines
   ================================ */

/* ================================
   PROJECT STATE THEMED TABLES
   ================================ */
table {
    width: 100% !important;
    border-collapse: collapse !important;
    font-family: var(--font-family) !important;
}

table th {
    background-color: var(--link-color) !important;
    color: white !important;
    padding: var(--space-3) var(--space-2) !important;
    text-align: left !important;
    font-weight: 600 !important;
    border: none !important;
}

table td {
    padding: var(--space-2) !important;
    border-bottom: 1px solid var(--md-neutral-90) !important;
    vertical-align: top !important;
}

table tr:nth-child(even) {
    background-color: var(--md-neutral-95) !important;
}

table tr:hover {
    background-color: var(--sidebar-bg) !important;
}

/* ================================
   LEGACY TABLE STYLES (Below)
   ================================ */

/* Base table styling with Material 3 principles */
.table {
    width: 100%;
    max-width: 100%;
    min-width: 100%;
    margin-bottom: var(--space-6);
    background-color: var(--md-surface-container-lowest);
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0;
    overflow: hidden;
    border: 1px solid var(--md-outline-variant);
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: var(--space-4);
    box-shadow: none;
}

.table-responsive .table {
    margin-bottom: 0;
    box-shadow: none;
    border-radius: 0;
    border: none;
}

.table th,
.table td {
    padding: var(--space-4) var(--space-5);
    vertical-align: middle;
    border-bottom: 1px solid var(--md-outline-variant);
    font-size: var(--md-body-small);
    line-height: 20px;
    transition: var(--transition-fast);
    white-space: normal;
}

/* Material 3 table headers with purple theme */
.table th {
    background-color: var(--md-primary-90) !important;
    font-weight: 500 !important;
    color: var(--md-primary-10) !important;
    text-align: left;
    vertical-align: bottom;
    border-bottom: 2px solid var(--md-primary-40) !important;
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    font-size: var(--md-label-large) !important;
}

.table td {
    color: var(--md-on-surface);
}

/* Remove border from last row */
.table tbody tr:last-child td {
    border-bottom: none;
}

/* Material 3 table variants and interactions */
.table-bordered {
    border: 1px solid var(--md-outline-variant);
    border-radius: 0;
    box-shadow: none;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--md-outline-variant);
}

.table-hover tbody tr {
    transition: var(--transition-fast);
    transition-property: background-color, box-shadow;
}

.table-hover > tbody > tr:hover {
    background-color: var(--md-surface-container-hover);
    color: var(--md-on-surface);
    box-shadow: none;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: var(--md-surface-container-lowest);
    transition-property: background-color;
}

/* Material 3 table sizes */
.table-sm th,
.table-sm td {
    padding: var(--space-2) var(--space-3);
    font-size: var(--md-label-medium);
}

.table-lg th,
.table-lg td {
    padding: var(--space-5) var(--space-6);
    font-size: var(--md-body-medium);
}

/* Material 3 table row states */
.table tbody tr.table-active {
    background-color: var(--md-primary-90);
    border-color: var(--md-primary-70);
}

.table tbody tr.table-success {
    background-color: var(--md-success-95);
    border-color: var(--md-success-80);
}

.table tbody tr.table-warning {
    background-color: var(--md-warning-95);
    border-color: var(--md-warning-80);
}

.table tbody tr.table-danger,
.table tbody tr.table-error {
    background-color: var(--md-error-95);
    border-color: var(--md-error-80);
}

.table tbody tr.table-info {
    background-color: var(--md-secondary-95);
    border-color: var(--md-secondary-80);
}

/* styles from the modern1 styles */
.table > thead > tr {
  background: none;
  background-image: none;
}

/* Table action buttons with Material 3 styling */
.table .btn {
    margin: 0 var(--space-1);
    padding: var(--space-1) var(--space-2);
    font-size: var(--md-label-small);
    min-height: 28px;
}

.table .btn:first-child {
    margin-left: 0;
}

.table .btn:last-child {
    margin-right: 0;
}

/* Material 3 table sorting indicators */
.table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    padding-right: var(--space-8);
    border-radius: 0;
    transition: var(--transition-fast);
}

.table th.sortable:hover {
    background-color: var(--md-secondary-90);
}

.table th.sortable::after {
    content: "↕";
    position: absolute;
    right: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.5;
    font-size: var(--md-label-small);
    color: var(--md-on-surface-variant);
}

.table th.sortable.sort-asc::after {
    content: "↑";
    opacity: 1;
    color: var(--md-primary-40);
}

.table th.sortable.sort-desc::after {
    content: "↓";
    opacity: 1;
    color: var(--md-primary-40);
}

/* Material 3 table pagination */
.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--space-4);
    padding: var(--space-4);
    background-color: var(--md-surface-container-low);
    border-radius: 0;
    border: 1px solid var(--md-outline-variant);
}

.table-pagination .pagination-info {
    font-size: var(--md-body-small);
    color: var(--md-on-surface-variant);
}

.table-pagination .pagination-controls {
    display: flex;
    gap: var(--space-2);
}

/* Responsive table improvements */
@media (max-width: 768px) {
    .table th,
    .table td {
        padding: var(--space-3) var(--space-4);
        font-size: var(--md-label-small);
    }
    
    .table-pagination {
        flex-direction: column;
        gap: var(--space-3);
        text-align: center;
    }
}

/* Table filters */
.table-filters {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-4);
    padding: var(--space-4);
    background-color: var(--md-surface-container-low);
    border-radius: 0;
    border: 1px solid var(--md-outline-variant);
}

.table-search {
    flex: 1;
    max-width: 300px;
}

/* Modern loading state */
.table-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 32px;
    height: 32px;
    margin: -16px 0 0 -16px;
    border: 3px solid var(--color-gray-200);
    border-top-color: var(--color-primary-500);
    border-radius: 0;
    animation: spin var(--duration-1000) linear infinite;
    z-index: var(--z-10);
}

/* Empty state */
.table-empty {
    text-align: center;
    padding: var(--space-12) var(--space-6);
    color: var(--color-text-muted);
}

.table-empty-icon {
    font-size: var(--font-size-4xl);
    color: var(--color-text-subtle);
    margin-bottom: var(--space-4);
}

.table-empty-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-secondary);
    margin-bottom: var(--space-2);
}

.table-empty-description {
    font-size: var(--font-size-sm);
    color: var(--color-text-muted);
}

/* Responsive table adjustments */
@media (max-width: 768px) {
    .table th,
    .table td {
        padding: var(--space-2) var(--space-3);
        font-size: var(--font-size-xs);
    }
    
    .table-filters {
        flex-direction: column;
        gap: var(--space-2);
    }
    
    .table-pagination {
        flex-direction: column;
        gap: var(--space-3);
        text-align: center;
    }
    
    /* Mobile-friendly table layout */
    .table-mobile-stack {
        display: block;
    }
    
    .table-mobile-stack thead {
        display: none;
    }
    
    .table-mobile-stack tbody,
    .table-mobile-stack tr,
    .table-mobile-stack td {
        display: block;
        width: 100%;
    }
    
    .table-mobile-stack tr {
        border: 1px solid var(--color-border);
        border-radius: 0;
        margin-bottom: var(--space-4);
        padding: var(--space-4);
        background-color: var(--color-white);
        box-shadow: var(--shadow-sm);
    }
    
    .table-mobile-stack td {
        border: none;
        padding: var(--space-2) 0;
        position: relative;
        padding-left: 35%;
    }
    
    .table-mobile-stack td::before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        top: var(--space-2);
        font-weight: var(--font-weight-semibold);
        color: var(--color-text-secondary);
        width: 30%;
    }
}

/* ACE framework specific table styles */
.widget-box .table {
    margin-bottom: 0;
    border-radius: 0;
    box-shadow: none;
}

.widget-box .widget-body .table th:first-child {
    border-top-left-radius: 0;
}

.widget-box .widget-body .table th:last-child {
    border-top-right-radius: 0;
}

/* ================================
   Material Design 3 Pagination System (2025)
   Based on Google Material You Guidelines
   ================================ */

/* Override default pagination colors to use Material 3 purple theme */
.pagination > li > a,
.pager > li > a {
    border-width: 1px;
    border-color: var(--md-outline-variant);
    border-radius: var(--border-radius-md) !important;
    color: var(--md-primary-40);
    background-color: var(--md-surface-container-lowest);
    margin: 0 var(--space-1) 0 0;
    position: relative;
    z-index: auto;
    padding: var(--space-2) var(--space-3);
    font-size: var(--md-body-small);
    transition: var(--transition-fast);
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination > li > span,
.pager > li > span {
    border-width: 1px;
    border-color: var(--md-outline-variant);
    border-radius: 0 !important;
    color: var(--md-on-surface-variant);
    background-color: var(--md-surface-container-lowest);
    padding: var(--space-2) var(--space-3);
    font-size: var(--md-body-small);
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination > li > a:hover,
.pager > li > a:hover {
    color: var(--md-primary-30);
    background-color: var(--md-primary-95);
    border-color: var(--md-primary-80);
    z-index: 1;
    box-shadow: var(--md-elevation-1);
}

.pagination > li > a:focus,
.pager > li > a:focus {
    background-color: var(--md-primary-95);
    box-shadow: 0 0 0 2px rgba(103, 80, 164, 0.12);
    outline: none;
}

.pagination > li.disabled > a,
.pager > li.disabled > a,
.pagination > li.disabled > a:hover,
.pager > li.disabled > a:hover {
    background-color: var(--md-surface-container-low);
    border-color: var(--md-outline-variant);
    color: var(--md-on-surface);
    opacity: 0.38;
    cursor: not-allowed;
    z-index: auto;
}

.pagination > li.active > a,
.pagination > li.active > a:hover,
.pagination > li.active > a:focus {
    background-color: var(--md-primary-40);
    border-color: var(--md-primary-40);
    color: var(--md-on-primary);
    box-shadow: var(--md-elevation-1);
    z-index: 2;
}

/* Material 3 pagination sizes */
.pagination-lg > li > a,
.pagination-lg > li > span {
    padding: var(--space-3) var(--space-4);
    font-size: var(--md-body-medium);
    min-height: 48px;
    border-radius: 0 !important;
}

.pagination-sm > li > a,
.pagination-sm > li > span {
    padding: var(--space-1) var(--space-2);
    font-size: var(--md-label-small);
    min-height: 32px;
    border-radius: 0 !important;
}

/* Material 3 pagination container */
.pagination {
    margin: var(--space-4) 0;
    display: flex;
    gap: var(--space-1);
}

.pagination > li {
    margin: 0;
}

/* Message footer pagination styling */
.message-footer .pagination > li > a,
.message-footer .pagination > li > span {
    color: var(--md-primary-40);
    background-color: var(--md-surface-container-lowest);
    border-color: var(--md-outline-variant);
}

.message-footer .pagination > li > a:hover {
    background-color: var(--md-primary-95);
    color: var(--md-primary-30);
}

.message-footer-style2 .pagination > li > a,
.message-footer-style2 .pagination > li > span {
    color: var(--md-primary-40);
    background-color: transparent;
    border: 1px solid var(--md-outline-variant);
}

/* ================================
   LEGACY OVERRIDES - Colors from default.css and marevalcore.css
   Converted to Material 3 Design System
   ================================ */

/* Category cells (from default.css lines 10-18) */
td.category, 
th.category,
td.category label, 
th.category label {
    background-color: var(--md-primary-95) !important;
    color: var(--md-primary-10) !important;
    font-weight: 600 !important;
    vertical-align: top !important;
    border-radius: 0 !important;
}

td.category .small, 
th.category .small {
    font-weight: 400 !important;
    color: var(--md-primary-20) !important;
}

/* Filter table category colors (from default.css line 132) */
table.filters td.category {
    color: var(--md-primary-40) !important;
    background-color: var(--md-primary-95) !important;
}

/* MarEval Core overrides (from marevalcore.css) */
table.filters td.category.mar-label-color-default {
    color: var(--md-on-surface) !important;
    background-color: var(--md-surface-container-low) !important;
}

/* Spacer rows (from default.css line 25) */
tr.spacer {
    background-color: var(--md-surface) !important;
    color: var(--md-on-surface) !important;
    height: 5px !important;
}

/* Sticky separators and test language headers (from default.css line 43) */
.sticky-separator,
.test-langs th {
    background-color: var(--md-surface-container-high) !important;
    color: var(--md-on-surface) !important;
}

/* Bug note styling (from default.css line 51) */
tr.bugnote .bugnote-note {
    background-color: var(--md-surface-container-low) !important;
    color: var(--md-on-surface) !important;
    width: 75% !important;
    vertical-align: top !important;
    border-radius: 0 !important;
}

/* Private bug notes (from default.css line 53) */
.bugnote-private {
    background-color: var(--md-warning-95) !important;
    color: var(--md-warning-10) !important;
    border: 1px solid var(--md-warning-70) !important;
}

/* Configuration management colors (from default.css lines 105-106) */
.color-global {
    background-color: var(--md-primary-90) !important;
    color: var(--md-primary-10) !important;
    border: 1px solid var(--md-primary-70) !important;
}

.color-project {
    background-color: var(--md-success-90) !important;
    color: var(--md-success-10) !important;
    border: 1px solid var(--md-success-70) !important;
}

/* Due date and status indicators (from default.css lines 108-110) */
td.due-0, 
td.overdue {
    background-color: var(--md-error-40) !important;
    color: var(--md-on-error) !important;
    font-weight: 600 !important;
    border-radius: 0 !important;
}

td.due-1 {
    background-color: var(--md-warning-50) !important;
    color: var(--md-warning-10) !important;
    font-weight: 600 !important;
    border-radius: 0 !important;
}

td.due-2 {
    background-color: var(--md-success-40) !important;
    color: var(--md-on-success) !important;
    font-weight: 600 !important;
    border-radius: 0 !important;
}

td.print-overdue {
    font-weight: 600 !important;
    color: var(--md-error-40) !important;
}

/* Additional status and state indicators */
span.dependency_dated {
    color: var(--md-error-60) !important;
}

span.dependency_met {
    color: var(--md-success-40) !important;
}

span.dependency_unmet {
    color: var(--md-error-40) !important;
}

span.dependency_upgrade {
    color: var(--md-warning-50) !important;
}

/* Small caption styling */
.small-caption {
    border: 1px solid var(--md-outline-variant) !important;
    font-size: var(--md-label-small) !important;
    padding: var(--space-1) var(--space-2) !important;
    background-color: var(--md-surface-container-lowest) !important;
    color: var(--md-on-surface) !important;
    border-radius: 0 !important;
}

/* Required field indicator */
span.required {
    font-size: var(--md-label-small) !important;
    color: var(--md-error-40) !important;
    font-weight: 600 !important;
}

/* Bug list specific styling */
#buglist td, 
#buglist th {
    text-align: center !important;
    background-color: var(--md-surface-container-lowest) !important;
    color: var(--md-on-surface) !important;
}

#buglist .column-summary,
#buglist .column-description,
#buglist .column-notes,
#buglist .column-steps-to-reproduce,
#buglist .column-additional-information,
#buglist .cftype-string,
#buglist .cftype-textarea {
    text-align: left !important;
}

/* Issue status styling */
.issue-status {
    border-bottom: 1px dotted var(--md-outline) !important;
    color: var(--md-primary-40) !important;
}

/* Resolved issues styling */
.resolved {
    text-decoration: line-through !important;
    color: var(--md-on-surface-variant) !important;
    opacity: 0.7 !important;
}

/* List.js table sorting indicators */
.listjs-table .sort:after {
    display: inline-block !important;
    width: 0 !important;
    height: 0 !important;
    border-left: 5px solid transparent !important;
    border-right: 5px solid transparent !important;
    border-bottom: 5px solid transparent !important;
    content: "" !important;
    position: relative !important;
    top: -10px !important;
    right: -5px !important;
}

.listjs-table .sort.desc:after {
    width: 0 !important;
    height: 0 !important;
    border-left: 5px solid transparent !important;
    border-right: 5px solid transparent !important;
    border-top: 5px solid var(--md-on-surface) !important;
    content: "" !important;
    position: relative !important;
    top: 4px !important;
    right: -5px !important;
}

.listjs-table .sort.asc:after {
    width: 0 !important;
    height: 0 !important;
    border-left: 5px solid transparent !important;
    border-right: 5px solid transparent !important;
    border-bottom: 5px solid var(--md-on-surface) !important;
    content: "" !important;
    position: relative !important;
    top: -4px !important;
    right: -5px !important;
}

.listjs-table .sort:hover {
    text-decoration: underline !important;
    color: var(--md-primary-40) !important;
}