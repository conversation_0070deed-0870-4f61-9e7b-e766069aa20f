/* ================================
   Material Design 3 Header System (2025)
   Based on Google Material You Guidelines
   ================================ */
.header {
  margin-top: var(--space-4);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  line-height: 1.5;
  border-bottom: 1px solid var(--md-outline-variant);
}
.header h1 {
  font-size: var(--md-headline-medium);
  font-weight: 400;
  margin: 0 0 var(--space-2);
  color: var(--md-primary-40);
  letter-spacing: 0px;
}
.header > .ace-icon {
  margin-right: var(--space-2);
  color: var(--md-primary-40);
}

/* ================================
   Material Design 3 Navbar System (2025)
   ================================ */
.navbar {
    margin: 0;
    padding-left: 0;
    padding-right: 0;
    border-width: 0;
    border-radius: 0;
    box-shadow: var(--md-elevation-2);
    min-height: 64px;
    background: var(--md-primary-40);
}
.navbar .navbar-text,
.navbar .navbar-link {
    color: var(--md-on-primary);
}
.navbar .navbar-brand {
    color: var(--md-on-primary) !important;
    font-size: var(--md-headline-small) !important;
    font-weight: 400;
    text-shadow: none;
    padding-top: var(--space-3);
    padding-bottom: var(--space-3);
    height: auto;
}
.navbar .navbar-brand:hover,
.navbar .navbar-brand:focus {
    color: var(--md-on-primary);
}

@media only screen and (min-width: 992px) {
    .navbar-container {
        padding-left: var(--space-3);
        padding-right: var(--space-3);
    }
}
.navbar-container.container {
    padding-left: 0;
    padding-right: 0;
}

/* ================================
   Material Design 3 Navigation Styles
   ================================ */
.ace-nav {
    height: 100%;
    margin: 0 !important;
}
.ace-nav > li {
    line-height: 45px;
    height: 64px;
    border-left: 1px solid rgba(255, 255, 255, 0.12);
    padding: 0;
    position: relative;
    float: left;
}
.ace-nav > li:first-child {
    border-left-width: 0;
}
.ace-nav > li > a {
    background-color: var(--md-primary-30);
    color: var(--md-on-primary);
    display: block;
    line-height: inherit;
    text-align: center;
    height: 100%;
    width: auto;
    min-width: 64px;
    padding: 0 var(--space-3);
    position: relative;
    transition: var(--transition-fast);
}

.ace-nav > li > a:hover {
    background-color: var(--md-primary-20);
}

.ace-nav > li > a > .ace-icon {
    display: inline-block;
    font-size: 18px;
    color: var(--md-on-primary);
    text-align: center;
    width: 20px;
}
.ace-nav > li > a > .badge {
    position: relative;
    top: -4px;
    left: 2px;
    padding-right: 5px;
    padding-left: 5px;
}
.ace-nav > li > a:hover,
.ace-nav > li > a:focus,
.ace-nav > li.open > a {
    background-color: #2c5976;
    color: #FFF;
}

/* ACE Nav Color Variants */
.ace-nav > li.blue > a { background-color: #2E6589; }
.ace-nav > li.blue > a:hover,
.ace-nav > li.blue > a:focus,
.ace-nav > li.open.blue > a { background-color: #2c5976; }

.ace-nav > li.grey > a { background-color: #555; }
.ace-nav > li.grey > a:hover,
.ace-nav > li.grey > a:focus,
.ace-nav > li.open.grey > a { background-color: #4b4b4b; }

.ace-nav > li.purple > a { background-color: #892E65; }
.ace-nav > li.purple > a:hover,
.ace-nav > li.purple > a:focus,
.ace-nav > li.open.purple > a { background-color: #762c59; }

.ace-nav > li.green > a { background-color: #2E8965; }
.ace-nav > li.green > a:hover,
.ace-nav > li.green > a:focus,
.ace-nav > li.open.green > a { background-color: #2c7659; }

.ace-nav > li.light-blue > a { background-color: #62A8D1; }
.ace-nav > li.light-blue > a:hover,
.ace-nav > li.light-blue > a:focus,
.ace-nav > li.open.light-blue > a { background-color: #579ec8; }

.ace-nav > li.light-blue2 > a { background-color: #42A8E1; }
.ace-nav > li.light-blue2 > a:hover,
.ace-nav > li.light-blue2 > a:focus,
.ace-nav > li.open.light-blue2 > a { background-color: #359fd9; }

.ace-nav > li.red > a { background-color: #B74635; }
.ace-nav > li.red > a:hover,
.ace-nav > li.red > a:focus,
.ace-nav > li.open.red > a { background-color: #a34335; }

.ace-nav > li.light-green > a { background-color: #9ABC32; }
.ace-nav > li.light-green > a:hover,
.ace-nav > li.light-green > a:focus,
.ace-nav > li.open.light-green > a { background-color: #8ba832; }

.ace-nav > li.light-purple > a { background-color: #CB6FD7; }
.ace-nav > li.light-purple > a:hover,
.ace-nav > li.light-purple > a:focus,
.ace-nav > li.open.light-purple > a { background-color: #c263ce; }

.ace-nav > li.light-orange > a { background-color: #F79263; }
.ace-nav > li.light-orange > a:hover,
.ace-nav > li.light-orange > a:focus,
.ace-nav > li.open.light-orange > a { background-color: #f28653; }

.ace-nav > li.dark > a { background-color: #404040; }
.ace-nav > li.dark > a:hover,
.ace-nav > li.dark > a:focus,
.ace-nav > li.open.dark > a { background-color: #363636; }

.ace-nav > li.transparent > a { background-color: transparent; }
.ace-nav > li.transparent > a:hover,
.ace-nav > li.transparent > a:focus,
.ace-nav > li.open.transparent > a { background-color: rgba(0, 0, 0, 0.1); }

/* ACE Nav Utility Classes */
.ace-nav > li.margin-4 { margin-left: 4px; }
.ace-nav > li.margin-3 { margin-left: 3px; }
.ace-nav > li.margin-2 { margin-left: 2px; }
.ace-nav > li.margin-1 { margin-left: 1px; }
.ace-nav > li.no-border { border-width: 0 !important; }
.ace-nav > li .dropdown-menu { z-index: 1031; }

/* User Photo and Menu */
.ace-nav .nav-user-photo {
    margin: -4px 8px 0 0;
    border-radius: 100%;
    border: 2px solid #FFF;
    max-width: 40px;
}
.ace-nav li.dropdown-footer a .ace-icon {
    display: inline-block;
    width: 1.25em;
    text-align: center;
}

.user-menu > li > a {
    padding: 4px 12px;
}
.user-menu > li > a > .ace-icon {
    margin-right: 6px;
    font-size: 120%;
}

.user-info {
    max-width: 100px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    vertical-align: top;
    line-height: 15px;
    position: relative;
    top: 6px;
}
.user-info small {
    display: block;
}

/* ================================
   Navbar Forms
   ================================ */
.navbar-form.form-search {
    margin-top: 9px;
    margin-bottom: 0;
}
.navbar-form.form-search .form-group {
    margin-bottom: 0;
}
.navbar-form.form-search input[type=text] {
    width: 100px;
}

/* ================================
   Navbar Navigation
   ================================ */
.navbar .navbar-nav > li {
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-width: 0 1px 0 0;
}
.navbar .navbar-nav > li:first-child {
    border-width: 0 1px 0 1px;
}
.navbar .navbar-nav > li > a {
    padding-top: 12px;
    padding-bottom: 11px;
    height: 45px;
    background-color: transparent;
    font-size: 13px;
    color: #FFF;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.25);
}
.navbar .navbar-nav > li > a:hover,
.navbar .navbar-nav > li > a:focus,
.navbar .navbar-nav > li.open > a {
    background-color: rgba(0, 0, 0, 0.1) !important;
    color: #FFF !important;
}
.navbar .navbar-nav > li > .dropdown-menu > li > a {
    line-height: 2;
}
.navbar-nav .dropdown-menu > li > a > .ace-icon {
    display: inline-block;
    width: 20px;
}
.navbar-nav > li > .dropdown-menu {
    z-index: 1033;
}

/* Responsive navbar styles */
@media only screen and (max-width: 991px) {
    .navbar.navbar-collapse {
        max-height: none;
    }
    .navbar .navbar-header,
    .navbar .navbar-collapse {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
    .navbar.navbar-collapse .navbar-header {
        float: none !important;
        min-height: 45px;
    }
    .navbar.navbar-collapse .navbar-buttons {
        min-height: 0;
        padding-left: 0;
        padding-right: 0;
        border: 1px solid rgba(255, 255, 255, 0.4);
        border-width: 1px 0 0;
        width: 100%;
    }
    .navbar.navbar-collapse .navbar-buttons.in {
        overflow: visible !important;
    }
    .navbar.navbar-collapse .navbar-buttons > .ace-nav {
        display: block;
        float: none !important;
        text-align: center;
        background-color: transparent !important;
        border-width: 0;
        letter-spacing: -3px;
    }
    .navbar.navbar-collapse .navbar-buttons > .ace-nav > li {
        display: inline-block;
        float: none !important;
        text-align: left;
        letter-spacing: normal;
    }
    .navbar.navbar-collapse .navbar-buttons > .ace-nav > li:first-child {
        border-left: 1px solid rgba(255, 255, 255, 0.4);
    }
    .navbar.navbar-collapse .navbar-buttons > .ace-nav > li:last-child {
        border-right: 1px solid rgba(255, 255, 255, 0.4);
    }
}

@media only screen and (max-width: 479px) {
    .navbar:not(.navbar-collapse) .navbar-header {
        float: none !important;
        display: block;
    }
    .navbar:not(.navbar-collapse) .ace-nav {
        display: block;
        float: none !important;
        text-align: center;
        background-color: rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-width: 1px 0 0;
        letter-spacing: -3px;
    }
    .navbar:not(.navbar-collapse) .ace-nav > li {
        display: inline-block;
        float: none !important;
        text-align: left;
        letter-spacing: normal;
    }
    .navbar:not(.navbar-collapse) .ace-nav > li:first-child {
        border-left: 1px solid rgba(255, 255, 255, 0.4);
    }
    .navbar:not(.navbar-collapse) .ace-nav > li:last-child {
        border-right: 1px solid rgba(255, 255, 255, 0.4);
    }
}

@media only screen and (max-width: 319px) {
    .ace-nav > li > a {
        padding: 0 5px !important;
    }
}

/* ================================
   Footer Style (from ace.css)
   ================================ */
.footer {
  padding: 16px 20px;
  text-align: center;
  background-color: #fafafa;
  border-top: 1px solid rgba(0,0,0,0.08);
  font-size: 0.9rem;
  color: #666;
}
.footer .footer-inner {
  max-width: 1200px;
  margin: auto;
}

div.footer {
    & div.footer-inner {
        div.footer-content {
            height: 120px;
            /* margin-top: 16px; */
            left: -12px;
            right: -30px;
            bottom: 0px;
            padding: 8px 8px;
            line-height: 36px;
            border-top: none;
            background: var(--md-primary-0);
            color: white;
            border-top-right-radius: var(--border-radius-xl);
        }
    }
}

/*********************************************/
/* Footer styles responsive                  */
/* *******************************************/

@media (min-width: 1200px) {
    .sidebar.compact ~ .footer .footer-inner {
        /* left: 125px; */
        left: 240px !important;
    }
}
@media (min-width: 992px) {
    .sidebar.compact ~ .footer .footer-inner {
        /* left: 125px; */
        left: 240px !important;
    }
}

@media (min-width: 1200px) {
    .sidebar.menu-min ~ .footer .footer-inner {
        /* left: 43px; */
        left: 90px !important;
    }
}
@media (min-width: 992px) {
    .sidebar.menu-min ~ .footer .footer-inner {
        /* left: 43px; */
        left: 90px !important;
    }
}