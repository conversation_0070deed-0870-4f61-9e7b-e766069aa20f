<?php
# MantisBT - A PHP based bugtracking system

# MantisBT is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 2 of the License, or
# (at your option) any later version.
#
# MantisBT is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with MantisBT.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Web Service Checks
 * @package MantisBT
 * @copyright Copyright (C) 2000 - 2002  Kenzaburo Ito - <EMAIL>
 * @copyright Copyright (C) 2002 - 2013  MantisBT Team - <EMAIL>
 * @link http://www.mantisbt.org
 *
 * @uses check_api.php
 * @uses config_api.php
 * @uses constant_inc.php
 */

if( !defined( 'CHECK_WEBSERVICE_INC_ALLOW' ) ) {
	return;
}

# MantisBT Check API
require_once( 'check_api.php' );
require_api( 'config_api.php' );
require_api( 'constant_inc.php' );

check_print_section_header_row( 'Webservice' );

check_print_test_warn_row(
	'SOAP Extension Enabled',
	extension_loaded( 'soap' ),
	array( false => 'Enable the PHP SOAP extension.' )
);

