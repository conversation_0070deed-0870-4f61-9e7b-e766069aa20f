<?php
/** MantisBT - a php based bugtracking system
 *
 * Copyright (C) 2000 - 2002  Kenzaburo Ito - <EMAIL>
 * Copyright (C) 2002 - 2016  MantisBT Team - <EMAIL>
 *
 * MantisBT is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * MantisBT is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with MantisBT.  If not, see <http://www.gnu.org/licenses/>.
 *
 * **********************************************************************
 * ** This file contains translations stored in translatewiki.net.     **
 * ** See https://translatewiki.net/wiki/Project:About for information **
 * ** on copyright/license for translatewiki.net translations.         **
 * **********************************************************************
 * **                                                                  **
 * **                      DO NOT UPDATE MANUALLY                      **
 * **                                                                  **
 * ** To improve a translation please visit https://translatewiki.net  **
 * ** Detailed instructions on how to create or update translations at **
 * ** http://www.mantisbt.org/wiki/doku.php/mantisbt:translationshowto **
 * **********************************************************************
 */
/** Portuguese (português)
 * 
 * See the qqq 'language' for message documentation incl. usage of parameters
 * To improve a translation please visit https://translatewiki.net
 *
 * @ingroup Language
 * @file
 *
 * <AUTHOR> in Wonderland
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR> Abreu
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR> alfalb
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */

$s_actiongroup_menu_copy = 'Copiar';
$s_actiongroup_menu_assign = 'Atribuir a';
$s_actiongroup_menu_resolve = 'Resolver';
$s_actiongroup_menu_update_priority = 'Atualizar Prioridade';
$s_actiongroup_menu_update_status = 'Atualizar Estado';
$s_actiongroup_menu_update_severity = 'Atualizar a Gravidade';
$s_actiongroup_menu_update_view_status = 'Atualizar a Visibilidade';
$s_actiongroup_menu_update_category = 'Atualizar a categoria';
$s_actiongroup_menu_set_sticky = 'Marcar/Desmarcar como Fixo';
$s_actiongroup_menu_update_field = 'Atualizar %1$s';
$s_actiongroup_menu_update_product_version = 'Atualizar a versão do produto';
$s_actiongroup_menu_update_target_version = 'Atualizar a versão de destino';
$s_actiongroup_menu_update_fixed_in_version = 'Atualizar Resolvido na Versão';
$s_actiongroup_menu_update_product_build = 'Atualizar compilação do produto';
$s_actiongroup_menu_update_due_date = 'Atualizar prazo';
$s_actiongroup_menu_add_note = 'Adicionar Nota';
$s_actiongroup_menu_attach_tags = 'Anexar Etiquetas';
$s_actiongroup_bugs = 'Problemas selecionados';
$s_actiongroup_error_issue_is_readonly = 'O problema está limitado só a leitura.';
$s_all_projects = 'Todos os projetos';
$s_move_bugs = 'Mover Problemas';
$s_operation_successful = 'Operação bem-sucedida.';
$s_operation_warnings = 'Operação concluída com avisos.';
$s_operation_failed = 'A operação não foi concluída com sucesso.';
$s_date_order = 'Ordenar por data';
$s_print_all_bug_page_link = 'Imprimir Relatórios';
$s_csv_export = 'Exportar para CSV';
$s_excel_export = 'Exportar para Excel';
$s_login_anonymously = 'Entrar Anonimamente';
$s_anonymous = 'Anónimo';
$s_jump_to_bugnotes = 'Ver Notas';
$s_jump_to_history = 'Ir para o histórico';
$s_display_history = 'Mostrar o histórico';
$s_public_project_msg = 'Este projeto é público. Todos os utilizadores têm acesso.';
$s_private_project_msg = 'Este projeto é privado. Apenas os administradores e os utilizadores adicionados manualmente têm acesso.';
$s_access_level_project = 'Nível de acesso do projeto';
$s_view_submitted_bug_link = 'Ver Problema Submetido %1$s';
$s_assigned_projects = 'Projectos Atribuídos';
$s_assigned_projects_label = 'Projetos atribuídos';
$s_unassigned_projects_label = 'Projetos não atribuídos';
$s_copy_users = 'Copiar Utilizadores';
$s_copy_categories_from = 'Copiar Categorias de';
$s_copy_categories_to = 'Copiar Categorias para';
$s_copy_versions_from = 'Copiar Versões de';
$s_copy_versions_to = 'Copiar Versões para';
$s_copy_users_from = 'Copiar Utilizadores de';
$s_copy_users_to = 'Copiar Utilizadores para';
$s_bug_history = 'Histórico do Problema';
$s_field = 'Campo';
$s_old_value = 'Valor Antigo';
$s_new_value = 'Novo Valor';
$s_date_modified = 'Data da Modificação';
$s_bugnote = 'Nota';
$s_bugnote_view_state = 'Visibilidade da Nota';
$s_bug_monitor = 'Problema Acompanhado';
$s_bug_end_monitor = 'Deixar de acompanhar problema';
$s_announcement = 'Anúncio';
$s_stays_on_top = 'Mantém-se no Topo';
$s_bugnote_link_title = 'Ligação direta para nota';
$s_delete_bugnote_button = 'Apagar Nota';
$s_delete_bugnote_sure_msg = 'Tem a certeza que quer apagar esta nota?';
$s_bug_relationships = 'Relacionamentos';
$s_empty_password_sure_msg = 'Este utilizador tem uma password vazia. Tem a certeza que é isso o pretendido?';
$s_empty_password_button = 'Usar Password Vazia';
$s_reauthenticate_title = 'Autenticar';
$s_reauthenticate_message = 'Está a aceder a uma página segura, e a sua sessão segura expirou. Por favor, autentique-se para continuar.';
$s_no_category = '(Sem Categoria)';
$s_global_categories = 'Categorias Globais';
$s_inherit = 'Herdar Categorias';
$s_inherit_global = 'Herdar Categorias Globais';
$s_inherit_parent = 'Herdar Categorias-Mãe';
$s_update_subproject_inheritance = 'Atualizar herança do subprojeto';
$s_duplicate_of = 'duplicado de';
$s_has_duplicate = 'tem duplicado';
$s_related_to = 'relacionado com';
$s_dependant_on = 'pai de';
$s_blocks = 'filho de';
$s_new_bug = 'Novo Problema';
$s_bugnote_added = 'Nota Acrescentada';
$s_bugnote_edited = 'Nota Alterada';
$s_bugnote_deleted = 'Nota Apagada';
$s_summary_updated = 'Resumo atualizado';
$s_description_updated = 'Descrição atualizada';
$s_additional_information_updated = 'Informação adicional atualizada';
$s_steps_to_reproduce_updated = 'Passos para reprodução atualizados';
$s_file_added = 'Ficheiro Adicionado';
$s_file_deleted = 'Ficheiro Apagado';
$s_bug_deleted = 'Problema Apagado';
$s_make_private = 'Tornar Privado';
$s_make_public = 'Tornar Público';
$s_create_new_project_link = 'Criar um novo projeto';
$s_opensearch_id_short = '%s Id';
$s_opensearch_id_description = '%s: pesquisa por Id de incidência';
$s_opensearch_text_short = '%s Texto';
$s_opensearch_text_description = '%s: pesquisa de texto completo';
$s_select_option = '(selecionar)';
$s_bug_actiongroup_access = 'Não tem as permissões apropriadas para executar essa ação.';
$s_bug_actiongroup_status = 'O estado deste problema não pode ser alterado para o pretendido';
$s_bug_actiongroup_category = 'Este problema não pode ser alterado para a categoria pretendida';
$s_bug_actiongroup_handler = 'O utilizador solicitado não tem permissão para lidar com este problema';
$s_bug_actiongroup_version = 'A versão solicitada não existe no projeto que contém este problema';
$s_close_bugs_conf_msg = 'Tem a certeza que quer fechar estes problemas?';
$s_delete_bugs_conf_msg = 'Tem a certeza que quer apagar estes problemas?';
$s_move_bugs_conf_msg = 'Mover problemas para';
$s_copy_bugs_conf_msg = 'Copiar problemas para';
$s_assign_bugs_conf_msg = 'Atribuir problemas a';
$s_resolve_bugs_conf_msg = 'Escolher resolução dos problemas';
$s_priority_bugs_conf_msg = 'Escolher prioridade dos problemas';
$s_status_bugs_conf_msg = 'Escolher estado dos problemas';
$s_view_status_bugs_conf_msg = 'Escolher o estado de visibilidade dos problemas';
$s_category_bugs_conf_msg = 'Escolher a categoria dos problemas';
$s_set_sticky_bugs_conf_msg = 'Tem a certeza que quer marcar/desmarcar estes problemas como fixos?';
$s_product_version_bugs_conf_msg = 'Atualizar versão do produto para';
$s_fixed_in_version_bugs_conf_msg = 'Atualizar Resolvido na Versão para';
$s_target_version_bugs_conf_msg = 'Atualizar Versão de destino para';
$s_due_date_bugs_conf_msg = 'Atualizar prazo para';
$s_close_group_bugs_button = 'Fechar problemas';
$s_delete_group_bugs_button = 'Apagar os problemas';
$s_move_group_bugs_button = 'Mover Problemas';
$s_copy_group_bugs_button = 'Copiar problemas';
$s_assign_group_bugs_button = 'Atribuir Problemas';
$s_resolve_group_bugs_button = 'Resolver Problemas';
$s_priority_group_bugs_button = 'Atualizar Prioridade';
$s_status_group_bugs_button = 'Atualizar estado';
$s_category_group_bugs_button = 'Atualizar categoria';
$s_view_status_group_bugs_button = 'Atualizar o estado de privacidade';
$s_set_sticky_group_bugs_button = 'Marcar/Desmarcar como Fixo';
$s_product_version_group_bugs_button = 'Atualizar versão do produto';
$s_fixed_in_version_group_bugs_button = 'Atualizar Resolvido na Versão';
$s_target_version_group_bugs_button = 'Atualizar Versão de Destino';
$s_due_date_group_bugs_button = 'Atualizar prazo';
$s_update_severity_title = 'Atualizar gravidade';
$s_update_severity_msg = 'Escolha a gravidade do problema';
$s_update_severity_button = 'Atualizar Gravidade';
$s_hide_button = 'Mostrar apenas os selecionados';
$s_printing_preferences_title = 'Escolher campos a imprimir';
$s_printing_options_link = 'Opções de Impressão';
$s_bugnote_title = 'Handler da nota';
$s_bugnote_date = 'Data da nota';
$s_bugnote_description = 'Descrição da nota';
$s_error_no_proceed = 'Por favor, use o botão "Voltar" no seu browser para retornar à página anterior. Pode corrigir quaisquer problemas identificados neste erro ou escolher uma outra ação. Pode também clicar numa opção na barra de menus para ir diretamente para uma nova secção.';
$s_login_error = 'A sua conta pode estar desativada ou bloqueada, ou a combinação nome de utilizador/palavra-chave que você digitou está incorreta.';
$s_login_cookies_disabled = 'O seu browser não consegue lidar com cookies, ou recusa-se a fazê-lo.';
$s_logged_in_as = 'Identificado como';
$s_prefix_for_deleted_users = 'utilizador';
$s_administrator = 'administrador';
$s_myself = 'Eu próprio';
$s_default_access_level = 'Nível de acesso padrão';
$s_issue_status_percentage = 'Percentagem de estado do problema';
$s_access_levels_enum_string = '10:espectador,25:repórter,40:actualizador,55:programador,70:gestor,90:administrador';
$s_no_access = 'sem acesso';
$s_project_status_enum_string = '10:desenvolvimento,30:produção,50:estável,70:obsoleto';
$s_project_view_state_enum_string = '10:público,50:privado';
$s_view_state_enum_string = '10:público,50:privado';
$s_priority_enum_string = '10:nenhuma,20:baixa,30:normal,40:alta,50:urgente,60:imediato';
$s_severity_enum_string = '10:funcionalidade,20:trivial,30:texto,40:ajuste,50:pequena,60:grande,70:crash,80:bloqueio';
$s_reproducibility_enum_string = '10:sempre,30:algumas Vezes,50:esporadicamente,70:não tentado,90:impossível de reproduzir,100:N/A';
$s_status_enum_string = '10:Novo,20:Resposta,30:Reconhecido,40:Confirmado,50:Atribuído,80:Resolvido,90:Encerrado';
$s_resolution_enum_string = '10:Aberto,20:Resolvido,30:Reaberto,40:Impossível de Reproduzir,50:Não Resolúvel,60:Duplicado,70:Não são necessárias alterações,80:Suspenso,90:Não Será Resolvido';
$s_projection_enum_string = '10:Nada,30:Ajuste Mínimo,50:Pequenas Correcções,70:Grandes Alterações,90:Redesenho';
$s_eta_enum_string = '10:Nada,20:< 1 dia,30:2-3 dias,40:< 1 semana,50:< 1 mês,60:> 1 mês';
$s_sponsorship_enum_string = '0:Não remunerado,1:Pedido,2:Pago';
$s_new_account_subject = 'Registo de conta';
$s_new_account_greeting = 'Obrigado por se registar. Tem uma conta com o nome de utilizador "%1$s". Para completar o seu registo, visite o seguinte URL (certifique-se que o introduz como uma única linha) e defina a sua própria palavra-chave de acesso:';
$s_new_account_greeting_admincreated = 'O utilizador %1$s criou uma conta para si com o nome de utilizador "%2$s". De modo a completar o seu registo, visite o URL seguinte (certifique-se que é inserido como uma linha única) e defina a sua palavra-chave:';
$s_new_account_username = 'Nome de utilizador:';
$s_new_account_message = 'Caso você não tenha pedido nenhum registo, ignore esta mensagem e nada irá acontecer.';
$s_new_account_do_not_reply = 'Não responda a esta mensagem';
$s_new_account_email = 'Email:';
$s_new_account_IP = 'Endereço IP remoto:';
$s_new_account_signup_msg = 'A seguinte conta foi criada:';
$s_reset_request_msg = 'Alguém (provavelmente você) pediu uma alteração de palavra-chave através da verificação de email. Se não foi você, ignore esta mensagem e nada irá acontecer.

Caso você tenha solicitado esta verificação, visite o seguinte URL para alterar a sua palavra-chave:';
$s_reset_request_admin_msg = 'A sua palavra-pase foi redefinida. Por favor, visite o URL seguinte para definir uma nova:';
$s_reset_request_in_progress_msg = 'Se você forneceu o nome de utilizador e endereço de email corretos para a sua conta, teremos a esta altura enviado uma mensagem de confirmação para esse endereço de email. Assim que a mensagem for recebida, siga as instruções fornecidas para alterar a palavra-chave da sua conta.';
$s_email_notification_title_for_status_bug_new = 'O problema seguinte passou para o estado NOVO (novamente)';
$s_email_notification_title_for_status_bug_feedback = 'O problema seguinte requer o seu COMENTÁRIO.';
$s_email_notification_title_for_status_bug_acknowledged = 'O problema seguinte foi RECONHECIDO.';
$s_email_notification_title_for_status_bug_confirmed = 'O problema seguinte foi CONFIRMADO.';
$s_email_notification_title_for_status_bug_assigned = 'O problema seguinte foi ATRIBUÍDO.';
$s_email_notification_title_for_status_bug_resolved = 'O problema seguinte foi RESOLVIDO.';
$s_email_notification_title_for_status_bug_closed = 'O problema seguinte foi FECHADO';
$s_email_notification_title_for_action_bug_submitted = 'O problema seguinte foi CRIADO.';
$s_email_notification_title_for_action_bug_assigned = 'O problema seguinte foi ATRIBUÍDO.';
$s_email_notification_title_for_action_bug_unassigned = 'A seguinte incidência foi APAGADA.';
$s_email_notification_title_for_action_bug_reopened = 'O problema seguinte foi REABERTO.';
$s_email_notification_title_for_action_bug_deleted = 'O problema seguinte foi APAGADO.';
$s_email_notification_title_for_action_bug_updated = 'O problema seguinte foi ATUALIZADO.';
$s_email_notification_title_for_action_sponsorship_added = 'O problema seguinte foi PATROCINADO.';
$s_email_notification_title_for_action_sponsorship_updated = 'Um patrocínio relativo ao problema seguinte foi modificado.';
$s_email_notification_title_for_action_sponsorship_deleted = 'Um patrocínio relativo ao problema seguinte foi retirado.';
$s_email_notification_title_for_action_bugnote_submitted = 'Uma NOTA foi adicionada a este problema.';
$s_email_notification_title_for_action_duplicate_of_relationship_added = 'O problema seguinte foi marcado como DUPLICADO DO problema %1$s.';
$s_email_notification_title_for_action_has_duplicate_relationship_added = 'O problema %1$s foi definido como DUPLICADO DO problema seguinte.';
$s_email_notification_title_for_action_related_to_relationship_added = 'O problema seguinte foi marcado como RELACIONADO COM o problema %1$s.';
$s_email_notification_title_for_action_dependant_on_relationship_added = 'O problema seguinte foi marcado como PAI DO problema %1$s.';
$s_email_notification_title_for_action_blocks_relationship_added = 'O problema seguinte foi marcado como FILHO DO problema %1$s.';
$s_email_notification_title_for_action_duplicate_of_relationship_deleted = 'O problema seguinte foi DESmarcado como DUPLICADO DO problema %1$s.';
$s_email_notification_title_for_action_has_duplicate_relationship_deleted = 'O problema %1$s foi removido como DUPLICADO DO problema seguinte.';
$s_email_notification_title_for_action_related_to_relationship_deleted = 'O problema seguinte foi marcado como NÃO RELACIONADO COM O problema %1$s.';
$s_email_notification_title_for_action_dependant_on_relationship_deleted = 'O problema seguinte foi removido como PAI DO problema %1$s.';
$s_email_notification_title_for_action_blocks_relationship_deleted = 'O problema seguinte foi removido como FILHO DO problema %1$s.';
$s_email_notification_title_for_action_relationship_child_resolved = 'O problema RELACIONADO %1$s foi RESOLVIDO.';
$s_email_notification_title_for_action_relationship_child_closed = 'O problema RELACIONADO %1$s foi FECHADO.';
$s_email_notification_title_for_action_related_issue_deleted = 'A incidência RELACIONADA %1$s foi ELIMINADA.';
$s_email_notification_title_for_action_monitor = 'O problema %1$s é agora acompanhado pelo utilizador %2$s.';
$s_email_reporter = 'Relatado por';
$s_email_handler = 'Atribuído a';
$s_email_project = 'Projeto';
$s_email_bug = 'ID de Problema';
$s_email_category = 'Categoria';
$s_email_reproducibility = 'Frequência';
$s_email_severity = 'Gravidade';
$s_email_priority = 'Prioridade';
$s_email_status = 'Estado';
$s_email_resolution = 'Solução';
$s_email_duplicate = 'Duplicado';
$s_email_fixed_in_version = 'Corrigido na Versão';
$s_email_target_version = 'Versão de Destino';
$s_email_date_submitted = 'Data de Abertura';
$s_email_last_modified = 'Última modificação';
$s_email_summary = 'Resumo';
$s_email_description = 'Descrição';
$s_email_additional_information = 'Informações Adicionais';
$s_email_steps_to_reproduce = 'Passos para Reproduzir';
$s_email_tag = 'Etiquetas';
$s_email_due_date = 'Data limite';
$s_account_protected_msg = 'Conta protegida. Impossível alterar as configurações...';
$s_account_removed_msg = 'A sua conta foi removida...';
$s_confirm_delete_msg = 'Tem a certeza que quer remover a sua conta?';
$s_delete_account_button = 'Remover Conta';
$s_manage_profiles_link = 'Perfis';
$s_change_preferences_link = 'Preferências';
$s_edit_account_title = 'Editar Conta';
$s_username = 'Nome de utilizador';
$s_username_label = 'Nome de utilizador';
$s_realname = 'Nome Real';
$s_realname_label = 'Nome verdadeiro';
$s_email = 'Email';
$s_email_label = 'Correio eletrónico';
$s_password = 'Palavra-chave';
$s_new_password = 'Nova palavra-passe';
$s_no_password_change = 'A palavra-chave é controlada por outro sistema, portanto não pode ser editada aqui.';
$s_confirm_password = 'Confirmar a palavra-chave';
$s_current_password = 'Palavra-chave atual';
$s_access_level = 'Nível de Acesso';
$s_access_level_label = 'Nível de acesso';
$s_update_user_button = 'Atualizar utilizador';
$s_verify_warning = 'As informações da sua conta foram verificadas.';
$s_verify_change_password = 'Tem que definir uma palavra-chave aqui para que possa entrar novamente.';
$s_api_tokens_link = 'Chaves API';
$s_api_token_create_form_title = 'Criar chave API';
$s_api_token_create_button = 'Criar chave API';
$s_api_token_name = 'Nome da chave';
$s_api_token_disclose_message = 'A chave que deve ser utilizada ao aceder a API.';
$s_api_token_displayed_once = 'Tenha em atenção que esta chave só será mostrada uma vez.';
$s_api_tokens_title = 'Chaves API';
$s_api_token_revoke_button = 'Revogar';
$s_api_token_never_used = 'Nunca utilizado';
$s_api_token_revoked = 'A chave API "%s" foi revogada.';
$s_last_used = 'Última utilização';
$s_default_account_preferences_title = 'Preferências da Conta';
$s_default_project = 'Projeto padrão';
$s_refresh_delay = 'Atraso de atualização';
$s_minutes = 'minutos';
$s_redirect_delay = 'Atraso de redirecionamento';
$s_seconds = 'segundos';
$s_with_minimum_severity = 'Com gravidade mínima de';
$s_bugnote_order = 'Ordenação de classificação das atividades';
$s_bugnote_order_asc = 'Ascendente';
$s_bugnote_order_desc = 'Descendente';
$s_email_on_new = 'Receber email quando for criado';
$s_email_on_assigned = 'Receber email na mudança de handler';
$s_email_on_feedback = 'Receber email na criação de comentários';
$s_email_on_resolved = 'Receber email quando for resolvido';
$s_email_on_closed = 'Receber email quando for encerrado';
$s_email_on_reopened = 'Receber email quando for reaberto';
$s_email_on_bugnote_added = 'Receber email quando uma Nota for adicionada';
$s_email_on_status_change = 'Receber email quando houver troca de estado';
$s_email_on_priority_change = 'Receber email quando houver mudança de prioridade';
$s_email_bugnote_limit = 'Limite de notas para e-mail';
$s_email_full_issue_details = 'Enviar por correio eletrónico todos os detalhes da incidência';
$s_language = 'Língua';
$s_font_family = 'Família de fontes';
$s_update_prefs_button = 'Atualizar preferências';
$s_reset_prefs_button = 'Cancelar alterações das preferências';
$s_timezone = 'Fuso horário';
$s_prefs_reset_msg = 'As alterações às preferências foram canceladas...';
$s_prefs_updated_msg = 'As preferências foram atualizadas...';
$s_select_profile = 'Selecionar Perfil';
$s_edit_profile = 'Editar perfil';
$s_delete_profile = 'Remover Perfil';
$s_my_sponsorship = 'Os meus patrocínios';
$s_update_sponsorship_button = 'Alterar estado de pagamento';
$s_no_sponsored = 'Não foram encontrados problemas patrocinados atribuídos a si.';
$s_own_sponsored = 'Problemas que patrocinou:';
$s_issues_handled = 'Problemas patrocinados que lhe foram atribuídos:';
$s_no_own_sponsored = 'Não patrocinou qualquer problema.';
$s_sponsor = 'Patrocinador';
$s_sponsor_verb = 'Patrocinar';
$s_amount = 'Quantia';
$s_total_owing = 'Total devido';
$s_total_paid = 'Total pago';
$s_sponsor_hide = 'Esconder Resolvidos e Pagos';
$s_sponsor_show = 'Mostrar todos';
$s_payment_updated = 'Informação de pagamento atualizada.';
$s_account_updated_msg = 'A sua conta foi atualizada com sucesso...';
$s_email_updated = 'Endereço de Email Alterado com Sucesso';
$s_realname_updated = 'Nome real atualizado com sucesso';
$s_password_updated = 'Palavra-chave alterada com sucesso';
$s_multiple_projects = 'Os problemas selecionados pertencem a projetos diferentes. Os parâmetros abaixo refletem as configurações para todos os projectos. Se isto estiver incorreto, por favor, tente novamente as alterações com um menor número de problemas selecionados.';
$s_new_bug_title = 'Novo problema';
$s_feedback_bug_title = 'Pedir comentários ao problema';
$s_acknowledged_bug_title = 'Reconhecer problema';
$s_confirmed_bug_title = 'Confirmar problema';
$s_assigned_bug_title = 'Atribuir problema';
$s_new_bug_button = 'Novo problema';
$s_feedback_bug_button = 'Pedir comentários';
$s_acknowledged_bug_button = 'Reconhecer problema';
$s_confirmed_bug_button = 'Confirmar problema';
$s_assigned_bug_button = 'Atribuir problema';
$s_bug_close_msg = 'O problema foi encerrado...';
$s_close_immediately = 'Fechar imediatamente:';
$s_closed_bug_title = 'Fechar problema';
$s_bug_deleted_msg = 'O problema foi removido...';
$s_delete_bug_sure_msg = 'Você tem certeza que deseja apagar este problema?';
$s_monitor_bug_button = 'Acompanhar';
$s_unmonitor_bug_button = 'Terminar acompanhamento';
$s_upload_file = 'Enviar Ficheiro';
$s_upload_files = 'Carregar Ficheiros';
$s_select_file = 'Selecionar Ficheiro';
$s_select_files = 'Selecionar Ficheiros';
$s_upload_file_button = 'Enviar Ficheiro';
$s_upload_files_button = 'Carregar Ficheiros';
$s_max_file_size_info = 'Tamanho máximo: %1$s %2$s';
$s_bug_reopened_msg = 'O problema foi reaberto...';
$s_reopen_add_bugnote_title = 'Adicione uma nota com o motivo da reabertura do problema';
$s_bugnote_add_reopen_button = 'Adicionar Nota e Reabrir o Problema';
$s_resolved_bug_title = 'Resolver o problema';
$s_resolved_bug_button = 'Resolver o problema';
$s_bug_resolved_msg = 'O problema foi resolvido. Escreva uma nota...';
$s_resolve_add_bugnote_title = 'Adicione uma nota com o motivo da resolução do problema';
$s_bugnote_add_resolve_button = 'Adicionar Nota';
$s_from = 'De';
$s_to = 'Para';
$s_sent_you_this_reminder_about = 'enviou-lhe este aviso acerca de';
$s_bug_reminder = 'Enviar um recordatório';
$s_reminder_sent_to = 'Aviso enviado a';
$s_reminder_sent_none = 'Nenhum lembrete pôde ser mandado';
$s_reminder_list_truncated = 'lista de destinatários truncada';
$s_bug_send_button = 'Enviar';
$s_reminder = 'Aviso';
$s_reminder_mentions = 'Agora, pode mencionar os utilizadores para ativar as notificações para eles, em vez de utilizar a funcionalidade de recordatórios. Por exemplo, os outros podem mencioná-lo ao escrever %1s nas edições e notas e receberá uma notificação por correio eletrónico.';
$s_reminder_explain = 'Esta nota será enviada aos destinatários indicados solicitando comentários a este problema.';
$s_reminder_monitor = 'Estes destinatários iniciarão também o acompanhamento do problema. Podem terminar este acompanhamento através do botão "Desacompanhar".';
$s_reminder_store = 'Esta nota será armazenada com o problema.';
$s_mentioned_you = 'mencionou-te em:';
$s_mentioned_in = 'Mencionado em %1$s';
$s_confirm_sponsorship = 'Confirme, por favor, que pretende patrocinar o problema %1$s para %2$s.';
$s_stick_bug_button = 'Colar';
$s_unstick_bug_button = 'Descolar';
$s_bug_updated_msg = 'O problema foi atualizado com sucesso...';
$s_back_to_bug_link = 'Voltar para o problema';
$s_update_simple_link = 'Atualização simples';
$s_updating_bug_advanced_title = 'A atualizar informações do problema';
$s_id = 'ID';
$s_category = 'Categoria';
$s_severity = 'Gravidade';
$s_reproducibility = 'Frequência';
$s_date_submitted = 'Data do Envio';
$s_last_update = 'Última atualização';
$s_reporter = 'Repórter';
$s_assigned_to = 'Atribuído a';
$s_priority = 'Prioridade';
$s_resolution = 'Solução';
$s_status = 'Estado';
$s_duplicate_id = 'ID Duplicado';
$s_os = 'Sistema Operativo';
$s_platform = 'Computador (CPU, RAM, disco, etc)';
$s_os_build = 'Versão do Sist. Operativo';
$s_projection = 'Projeção';
$s_eta = 'Estimado';
$s_product_version = 'Versão do Produto';
$s_build = 'Compilação';
$s_fixed_in_version = 'Corrigido na versão';
$s_target_version = 'Versão de destino';
$s_votes = 'Votos';
$s_summary = 'Resumo';
$s_synthesis = 'Síntese';
$s_description = 'Descrição';
$s_steps_to_reproduce = 'Passos Para Reprodução';
$s_update_information_button = 'Atualizar informação';
$s_sticky_issue = 'Problema colado';
$s_profile = 'Perfil';
$s_updating_bug_simple_title = 'A atualizar informação do problema';
$s_view_revisions = 'Ver revisões';
$s_view_num_revisions = 'Ver %1$d revisões';
$s_revision = 'Revisão';
$s_revision_by = '%1$s por %2$s';
$s_revision_drop = 'Apagar';
$s_bug_revision_dropped_history = 'Revisão do problema apagada';
$s_bugnote_revision_dropped_history = 'Revisão da nota apagada';
$s_all_revisions = 'Todas as revisões';
$s_back_to_issue = 'Voltar ao problema';
$s_confirm_revision_drop = 'Tem a certeza que quer apagar esta revisão do problema?';
$s_activities_title = 'Atividades';
$s_bugnote_attached_files = 'Ficheiros anexados:';
$s_bugnote_deleted_msg = 'Nota foi apagada com sucesso...';
$s_bug_notes_title = 'Notas';
$s_edit_bugnote_title = 'Editar Nota';
$s_no_bugnotes_msg = 'Não existem notas anexadas a este problema';
$s_add_bugnote_title = 'Adicionar nota';
$s_add_bugnote_button = 'Adicionar nota';
$s_closed_bug_button = 'Fechar problema';
$s_bugnote_updated_msg = 'A nota referente ao problema foi atualizada com sucesso...';
$s_last_edited = 'Última edição:';
$s_hide_content = 'Esconder conteúdo';
$s_show_content = 'Mostrar conteúdo';
$s_webmaster_contact_information = 'Contacte o <a href="mailto:%1$s" title="Contacte por correio eletrónico o responsável pela manutenção do sítio.">administrador</a> para obter ajuda';
$s_total_queries_executed = 'Número total de consultas executadas: %1$d';
$s_unique_queries_executed = 'Consultas únicas executadas: %1$d';
$s_total_query_execution_time = 'Tempo total da execução de consulta: %1$s segundos';
$s_page_execution_time = 'Tempo de execução da página: %1$s segundos';
$s_memory_usage = 'Uso da memória: %1$s KiB';
$s_log_page_number = 'Número';
$s_log_page_time = 'Tempo de execução';
$s_log_page_caller = 'Invocador';
$s_log_page_event = 'Acontecimento';
$s_please_report = 'Por favor, relate isto ao %1$s';
$s_warning_plain_password_authentication = '<strong>Aviso:</strong> Autenticação com palavras-chave em claro está a ser usada. Os administradores ficarão a conhecer a sua palavra-chave.';
$s_warning_default_administrator_account_present = '<strong>Aviso:</strong> Deve desativar a conta predefinida de \'administrador\' ou alterar a respetiva palavra-chave.';
$s_warning_admin_directory_present = '<strong>Aviso:</strong> O diretório "admin" deve ser removido, ou ter o seu acesso restringido.';
$s_warning_change_setting = '<strong>Aviso:</strong> "%1$s" não está definido ao seu valor por omissão (%2$s).';
$s_warning_security_hazard = 'Este é um risco de segurança em potencial, pois pode expor informações confidenciais.';
$s_warning_integrity_hazard = 'Isto fará com que o MantisBT continue quando houver erros e pode gerar problemas de integridade do sistema ou dos dados.';
$s_warning_debug_email = '<strong>Aviso:</strong> "<code>debug_email</code>" não está definido para <code>OFF</code>, todas as notificações por correio eletrónico serão enviadas para "%1$s".';
$s_error_database_no_schema_version = '<strong>Erro:</strong> A sua versão do modelo da base de dados é 0. Isto pode ocorrer porque a base de dados está corrompida ou o valor de "database_version" não pode ser recuperado da tabela de configuração, ou a estrutura da base de dados pertence a uma versão desatualizada do MantisBT (anterior à 1.0.0).';
$s_error_database_version_out_of_date_2 = '<strong>Aviso:</strong> A estrutura da base de dados pode estar desatualizada. Por favor, atualize <a href="admin/install.php">aqui</a> antes de entrar.';
$s_error_code_version_out_of_date = '<strong>Aviso:</strong> A estrutura da base de dados é mais recente do que o código instalado. Por favor, atualize o código.';
$s_login_page_info = 'Bem-vindo ao Sistema de Acompanhamento de Problemas';
$s_login_title = 'Entrar';
$s_save_login = 'Manter-me autenticado';
$s_secure_session = 'Sessão segura';
$s_secure_session_long = 'Permitir a sessão apenas deste endereço IP.';
$s_choose_project = 'Escolher projeto';
$s_signup_link = 'Pedir uma nova conta';
$s_lost_password_link = 'Perdeu a sua palavra-chave?';
$s_username_or_email = 'Nome de utilizador(a) ou endereço de correio eletrónico';
$s_enter_password = 'Introduza a palavra-passe para \'%s\'';
$s_select_project_button = 'Selecionar projeto';
$s_lost_password_title = 'Reposição da palavra-chave';
$s_lost_password_done_title = 'Mensagem de palavra-chave enviada';
$s_lost_password_subject = 'Reposição da palavra-chave';
$s_lost_password_info = 'Para repor a sua palavra-chave perdida, forneça o nome e  endereço de e-mail da sua conta.<br /><br />Se os dados corresponderem a uma conta válida, ser-lhe-á enviado um URL especial, via e-mail, que contém um código de validação para a sua conta. Siga o link fornecido para alterar a sua palavra-passe.';
$s_lost_password_confirm_hash_OK = 'A sua confirmação foi aceite. Por favor, atualize a sua palavra-passe.';
$s_open_and_assigned_to_me_label = 'Erros abertos que tenho atribuído:';
$s_open_and_reported_to_me_label = 'Erros abertos dos que informei:';
$s_newer_news_link = 'Notícias Recentes';
$s_older_news_link = 'Notícias Antigas';
$s_archives = 'Arquivos';
$s_rss = 'RSS';
$s_site_information = 'Informação do site';
$s_mantis_version = 'Versão do MantisBT';
$s_schema_version = 'Versão do Schema';
$s_site_path = 'Caminho do site';
$s_core_path = 'Caminho de base';
$s_plugin_path = 'Caminho de plugins';
$s_created_user_part1 = 'Criado o utilizador';
$s_created_user_part2 = 'com o nível de acesso';
$s_create_new_account_title = 'Criar Nova Conta';
$s_verify_password = 'Verificar palavra-chave';
$s_enabled = 'Ativado';
$s_enabled_label = 'Ativado';
$s_protected = 'Protegido';
$s_protected_label = 'Protegido';
$s_create_user_button = 'Criar Utilizador';
$s_hide_disabled = 'Esconder desativados';
$s_filter_button = 'Aplicar Filtro';
$s_default_filter = 'Filtro padrão';
$s_create_filter_link = 'Criar Permalink';
$s_create_short_link = 'Criar link curto';
$s_filter_permalink = 'Em seguida está uma ligação permanente para o filtro configurado atualmente:';
$s_manage_users_link = 'Gerir Utilizadores';
$s_manage_projects_link = 'Gerir Projectos';
$s_manage_custom_field_link = 'Gerir campos personalizados';
$s_manage_global_profiles_link = 'Gerir perfis globais';
$s_manage_plugin_link = 'Gerir plugins';
$s_permissions_summary_report = 'Relatório de permissões';
$s_manage_config_link = 'Gerir Configuração';
$s_manage_threshold_config = 'Limiares de workflow';
$s_manage_email_config = 'Notificações de e-mail';
$s_manage_workflow_config = 'Transições de Workflow';
$s_manage_workflow_graph = 'Gráfico do fluxo de trabalho';
$s_manage_tags_link = 'Gerir etiquetas';
$s_create_new_account_link = 'Criar Nova Conta';
$s_projects_link = 'Projetos';
$s_documentation_link = 'Documentação';
$s_new_accounts_title = 'Novas Contas';
$s_1_week_title = '1 Semana';
$s_never_logged_in_title = 'Nunca Entrou';
$s_prune_accounts = 'Cortar Contas';
$s_hide_inactive = 'Esconder Inactivas';
$s_show_disabled = 'Mostrar desativados';
$s_manage_accounts_title = 'Gestor de Contas';
$s_date_created = 'Data de Criação';
$s_last_visit = 'Última Visita';
$s_last_visit_label = 'Última visita:';
$s_edit_user_link = 'Editar Utilizador';
$s_search_user_hint = 'Nome de utilizador, nome real ou email';
$s_separate_list_items_by = '(separar os elementos da lista por "%1$s")';
$s_config_all_projects = 'Nota: Estas configurações afetam todos os projetos, a não ser que sejam substituídas ao nível do projeto.';
$s_config_project = 'Nota: Estas configurações afetam apenas o projeto %1$s.';
$s_colour_coding = 'Na tabela seguinte, aplica-se o seguinte código de cores:';
$s_colour_project = 'Configuração do projeto sobrepõe-se a outras.';
$s_colour_global = 'Todas as configurações de projeto sobrepõem-se às configurações padrão.';
$s_issue_reporter = 'Utilizador que relatou o problema';
$s_issue_handler = 'Utilizador que está a tratar do problema';
$s_users_added_bugnote = 'Utilizadores que acrescentaram notas ao problema';
$s_category_assigned_to = 'Proprietário da categoria';
$s_email_notify_users = 'Qualquer utilizador com nível de acesso';
$s_change_configuration = 'Atualizar configuração';
$s_message = 'Mensagem';
$s_default_notify = 'Definir flags de notificação padrão para';
$s_action_notify = 'A definir marcadores de notificação de ação para';
$s_notify_defaults_change_access = 'Quem pode alterar as notificações predefinidas';
$s_notify_actions_change_access = 'Quem pode alterar notificações:';
$s_revert_to_system = 'Apagar todas as definições de projeto';
$s_revert_to_all_project = 'Apagar configurações específicas do projeto';
$s_non_existent = 'inexistente';
$s_current_status = 'Estado atual';
$s_next_status = 'Próximo estado';
$s_workflow = 'Workflow';
$s_workflow_thresholds = 'Limites que afectam o workflow';
$s_threshold = 'Limite';
$s_status_level = 'Estado';
$s_alter_level = 'Quem pode alterar este valor';
$s_validation = 'Validação de Workflow';
$s_comment = 'Comnetário de validação';
$s_superfluous = 'Um arco do estado para si próprio está implícito e não precisa de ser indicado explicitamente';
$s_unreachable = 'Não pode mover um problema para este estado';
$s_no_exit = 'Não pode mover um problema deste estado';
$s_access_levels = 'Níveis de acesso';
$s_access_change = 'Nível de acesso mínimo para mudar para este estado';
$s_desc_bug_submit_status = 'Estado para o qual um novo problema fica definido';
$s_desc_bug_reopen_status = 'Estado para o qual os problemas reabertos são definidos';
$s_desc_bug_resolved_status_threshold = 'Estado em que um problema é considerado resolvido';
$s_desc_bug_closed_status_threshold = 'Estado no qual um problema é considerado fechado';
$s_workflow_change_access_label = 'Quem pode alterar o fluxo de trabalho:';
$s_access_change_access_label = 'Quem pode alterar os níveis de acesso:';
$s_default_not_in_flow = 'O estado predefinido de %1$s não está selecionado no próximo estado de %2$s. Será ignorado.';
$s_allowed_access_levels = 'Permitido para qualquer utilizador com nível de acesso';
$s_assign_issue = 'Atribuir um problema';
$s_allow_reporter_close = 'Permitir que o repórter feche o problema';
$s_allow_reporter_reopen = 'Permitir que o repórter reabra o problema';
$s_set_status_assigned = 'Definir estado ao atribuir Handler';
$s_edit_others_bugnotes = 'Editar as notas dos outros';
$s_edit_own_bugnotes = 'Editar as próprias notas';
$s_delete_others_bugnotes = 'Apagar as notas dos outros';
$s_delete_own_bugnotes = 'Apagar as próprias notas';
$s_change_view_state_own_bugnotes = 'Alterar o estado de visibilidade das minhas próprias notas';
$s_limit_access = 'Limitar o acesso do repórter aos seus próprios problemas';
$s_submit_status = 'Estado no qual um novo problema é colocado';
$s_assigned_status = 'Estado para o qual se definem os problemas autoatribuídos';
$s_resolved_status = 'Estado no qual um problema é considerado resolvido';
$s_readonly_status = 'Estado no qual um problema se torna apenas de leitura';
$s_reopen_status = 'Estado para o qual se define um problema reaberto';
$s_reopen_resolution = 'Resolução atribuída a um problema reaberto';
$s_limit_view_unless_threshold_option = 'Ver incidentes de outros utentes (se não estabelecido, o acesso será limitado aos incidentes reportados, alocados ou monitorizados pelo utente)';
$s_config_delete_sure = 'Tem a certeza que quer apagar as configurações para:';
$s_delete_config_button = 'Apagar configurações';
$s_configuration_report = 'Relatório de configuração';
$s_database_configuration = 'Configuração da base de dados';
$s_configuration_option = 'Opção de configuração';
$s_configuration_option_type = 'Tipo';
$s_configuration_option_value = 'Valor';
$s_all_users = 'Todos os utilizadores';
$s_set_configuration_option = 'Definir opção de configuração';
$s_delete_config_sure_msg = 'Tem a certeza que quer apagar esta opção de configuração';
$s_configuration_corrupted = 'A configuração na base de dados está corrompida.';
$s_set_configuration_option_action_create = 'Criar uma opção de configuração';
$s_set_configuration_option_action_edit = 'Editar uma opção de configuração';
$s_set_configuration_option_action_clone = 'Duplicar uma opção de configuração';
$s_set_configuration_option_action_view = 'Ver opção de configuração';
$s_show_all_complex = 'Mostrar valor para todas as opções "%1$s"';
$s_plugin = 'Plugin';
$s_plugins_installed = 'Plugins instalados';
$s_plugins_available = 'Plugins disponíveis';
$s_plugin_description = 'Descrição';
$s_plugin_author = 'Autor: %1$s';
$s_plugin_url = 'Website:';
$s_plugin_depends = 'Dependências';
$s_plugin_no_depends = 'Sem dependências';
$s_plugin_priority = 'Prioridade';
$s_plugin_protected = 'Protegido';
$s_plugin_actions = 'Ações';
$s_plugin_install = 'Instalar';
$s_plugin_upgrade = 'Atualizar';
$s_plugin_uninstall = 'Desinstalar';
$s_plugin_uninstall_message = 'Tem a certeza que quer desinstalar o plugin "%1$s"?';
$s_plugin_key_label = 'Chave:';
$s_plugin_key_met = 'plugin pronto';
$s_plugin_key_unmet = 'dependências não satisfeitas';
$s_plugin_key_dated = 'dependências desatualizadas';
$s_plugin_key_upgrade = 'atualização necessária';
$s_project_added_msg = 'O projeto foi adicionado com sucesso...';
$s_category_added_msg = 'A Categoria foi adicionada com sucesso...';
$s_category_deleted_msg = 'A Categoria foi apagada com sucesso...';
$s_category_delete_confirm_msg = 'Tem a certeza de que deseja apagar a categoria "%1$s"?';
$s_delete_category_button = 'Remover Categoria';
$s_edit_project_category_title = 'Editar categoria do projeto';
$s_update_category_button = 'Atualizar categoria';
$s_category_updated_msg = 'A categoria foi atualizada com sucesso...';
$s_create_first_project = 'Criar um projeto para poder registar as incidências.';
$s_add_subproject_title = 'Adicionar subprojeto';
$s_project_deleted_msg = 'O projeto foi removido com sucesso...';
$s_project_delete_msg = 'Você tem certeza que deseja apagar este projeto e todos os relatos de casos anexados?';
$s_project_delete_button = 'Apagar projeto';
$s_edit_project_title = 'Editar projeto';
$s_project_name = 'Nome do projeto';
$s_project_name_label = 'Nome do projeto:';
$s_view_status = 'Visualização';
$s_public = 'Público';
$s_private = 'Privado';
$s_update_project_button = 'Atualizar projeto';
$s_delete_project_button = 'Apagar projeto';
$s_copy_from = 'Copiar de';
$s_copy_to = 'Copiar para';
$s_categories_and_version_title = 'Categorias e Versões';
$s_categories = 'Categorias';
$s_add_category_button = 'Adicionar Categoria';
$s_add_and_edit_category_button = 'Adicionar e editar uma categoria';
$s_versions = 'Versão';
$s_add_version_button = 'Adicionar Versão';
$s_add_and_edit_version_button = 'Adicionar e editar versão';
$s_actions = 'Ações';
$s_version = 'Versão';
$s_version_label = 'Versão:';
$s_timestamp = 'Timestamp';
$s_subprojects = 'Subprojectos';
$s_add_subproject = 'Adicionar como subprojecto';
$s_create_new_subproject_link = 'Criar novo subprojecto';
$s_unlink_link = 'Desligar';
$s_show_global_users = 'Mostrar utilizadores com acesso global';
$s_hide_global_users = 'Esconder utilizadores com acesso global';
$s_review_changes = 'Rever alterações';
$s_review_changes_confirmation = 'Tem a certeza que quer aplicar as seguintes alterações?';
$s_review_changes_empty = 'Não há nenhuma alteração selecionada';
$s_add_project_title = 'Adicionar projeto';
$s_upload_file_path = 'Caminho do Ficheiro para Upload';
$s_add_project_button = 'Adicionar projeto';
$s_projects_title = 'Projetos';
$s_projects_title_label = 'Projetos';
$s_name = 'Nome';
$s_project_updated_msg = 'O projeto foi atualizado com sucesso...';
$s_version_added_msg = 'Versão atualizada com sucesso...';
$s_version_deleted_msg = 'Versão apagada com sucesso...';
$s_version_delete_sure = 'Tem certeza que deseja Remover esta versão?';
$s_delete_version_button = 'Remover Versão';
$s_edit_project_version_title = 'Editar versão do projeto';
$s_update_version_button = 'Atualizar versão';
$s_released = 'Lançado';
$s_not_released = 'Ainda não lançado';
$s_scheduled_release = 'Agendado para lançamento';
$s_obsolete = 'Obsoleto';
$s_version_updated_msg = 'Versão atualizada com sucesso...';
$s_account_delete_protected_msg = 'Conta Protegida. Não é possível apagá-la.';
$s_account_deleted_msg = 'Conta Apagada...';
$s_delete_account_sure_msg = 'Tem certeza que deseja Remover esta conta?';
$s_notify_user = 'Notificar ao utilizador sobre esta mudança';
$s_accounts_pruned_msg = 'Todas as contas não utilizadas durante o período de 1 semana foram removidas';
$s_prune_accounts_button = 'Limpar';
$s_confirm_account_pruning = 'Tem a certeza que quer apagar as contas antigas que nunca tenham entrado?';
$s_edit_user_title = 'Editar Utilizador';
$s_account_unlock_button = 'Desbloquear conta';
$s_reset_password_button = 'Repor Palavra-Chave';
$s_delete_user_button = 'Apagar utilizador';
$s_impersonate_user_button = 'Representar utilizador';
$s_reset_password_msg = 'Repor Palavra-Chave envia o URL de confirmação via e-mail';
$s_reset_password_msg2 = 'Repor Palavra-chave configura uma senha em branco.';
$s_show_all_users = 'Todos';
$s_users_unused = 'Não usado';
$s_users_new = 'Novo';
$s_account_reset_msg = 'Um pedido de confirmação foi enviado para o email do utilizador. Através deste pedido, o utilizador poderá modificar a sua palavra-passe.';
$s_account_reset_msg2 = 'Palavra-chave da conta definida para vazia...';
$s_account_unlock_msg = 'A conta foi desbloqueada.';
$s_manage_user_updated_msg = 'Conta atualizada com sucesso...';
$s_email_user_updated_subject = 'Conta atualizada';
$s_email_user_updated_msg = 'A sua conta foi atualizada por um administrador. Segue-se uma lista destas alterações. Pode atualizar os detalhes e preferências da sua conta em qualquer altura através do URL:';
$s_main_link = 'Principal';
$s_view_bugs_link = 'ver problemas';
$s_report_bug_link = 'Relatar Problema';
$s_changelog_link = 'Registo de alterações';
$s_roadmap_link = 'Roadmap';
$s_summary_link = 'Resumo';
$s_account_link = 'Conta Pessoal';
$s_users_link = 'Utilizadores';
$s_manage_link = 'Gerir';
$s_edit_news_link = 'Editar Notícias';
$s_docs_link = 'Docs.';
$s_logout_link = 'Sair';
$s_my_view_link = 'Minha vista';
$s_invite_users = 'Convidar utilizadores';
$s_my_view_title_unassigned = 'Não atribuído';
$s_my_view_title_recent_mod = 'Recentemente modificado';
$s_my_view_title_reported = 'Relatado por mim';
$s_my_view_title_assigned = 'Atribuído a mim (não resolvido)';
$s_my_view_title_resolved = 'Resolvido';
$s_my_view_title_monitored = 'Acompanhado por mim';
$s_my_view_title_feedback = 'À espera de um comentário meu';
$s_my_view_title_verify = 'À espera de confirmação de resolução por mim';
$s_my_view_title_my_comments = 'Problemas que eu comentei';
$s_news_added_msg = 'Notícia adicionada...';
$s_news_deleted_msg = 'Notícia apagada...';
$s_delete_news_sure_msg = 'Tem certeza que deseja remover esta notícia?';
$s_delete_news_item_button = 'Remover Notícia';
$s_edit_news_title = 'Editar Notícias';
$s_headline = 'Título';
$s_body = 'Corpo';
$s_update_news_button = 'Atualizar notícias';
$s_add_news_title = 'Adicionar Notícias';
$s_post_to = 'Publicar em';
$s_post_news_button = 'Publicar Notícia';
$s_edit_or_delete_news_title = 'Editar ou remover Notícia';
$s_edit_post = 'Editar publicação';
$s_delete_post = 'Remover publicação';
$s_select_post = 'Selecionar publicação';
$s_news_updated_msg = 'Notícia atualizada...';
$s_back_link = 'Voltar';
$s_file_uploaded_msg = 'Carregamento do ficheiro com sucesso.';
$s_upload_file_title = 'Carregar ficheiro';
$s_title = 'Título';
$s_project_file_deleted_msg = 'Ficheiro do projeto apagado.';
$s_confirm_file_delete_msg = 'Tem certeza que deseja remover este ficheiro ?';
$s_filename = 'Nome do Ficheiro';
$s_filename_label = 'Nome do ficheiro:';
$s_file_update_button = 'Atualizar ficheiro';
$s_file_delete_button = 'Remover Ficheiro';
$s_project_documentation_title = 'Documentação do projeto';
$s_user_documentation = 'Documentação do Utilizador';
$s_project_documentation = 'Documentação do projeto';
$s_add_file = 'Adicionar Ficheiro';
$s_project_document_updated = 'Projeto atualizado com sucesso';
$s_project_user_added_msg = 'Utilizador adicionado ao projeto com sucesso.';
$s_project_removed_user_msg = 'Utilizador removido do projeto com sucesso.';
$s_remove_user_sure_msg = 'Tem Certeza que Deseja Remover o Utilizador ?';
$s_remove_user_button = 'Remover utilizador';
$s_remove_all_users_sure_msg = 'Tem a certeza que deseja remover todos os utilizadores deste projeto?';
$s_remove_all_users_button = 'Remover todos os utilizadores';
$s_add_user_title = 'Adicionar utilizador ao projeto';
$s_add_user_button = 'Adicionar Utilizador';
$s_project_selection_title = 'Selecionar projeto';
$s_remove_link = 'Remover';
$s_remove_all_link = 'Remover tudo';
$s_remove_project_user_title = 'Remover do projeto o acesso do utilizador';
$s_modify_project_user_title = 'Modificar o acesso do utilizador ao projeto';
$s_updated_user_msg = 'Utilizador atualizado com sucesso.';
$s_must_enter_category = 'Você deve selecionar uma categoria.';
$s_must_enter_severity = 'Deve seleccionar uma Gravidade';
$s_must_enter_reproducibility = 'Deve seleccionar uma Frequência';
$s_must_enter_summary = 'Deve introduzir um resumo';
$s_must_enter_description = 'Deve escrever uma Descrição';
$s_report_more_bugs = 'Relatar mais problemas';
$s_submission_thanks_msg = 'Obrigado pelo seu relatório';
$s_simple_report_link = 'Relatório Simples';
$s_enter_report_details_title = 'Adicionar detalhes da incidência';
$s_required = 'requerido';
$s_select_category = 'Selecionar categoria';
$s_select_reproducibility = 'Seleccionar Frequência';
$s_select_severity = 'Seleccionar Gravidade';
$s_or_fill_in = 'Ou preencha';
$s_assign_to = 'Atribuído a';
$s_additional_information = 'Informações adicionais';
$s_submit_report_button = 'Enviar incidência';
$s_check_report_more_bugs = 'marcar para relatar mais problemas';
$s_report_stay = 'Continuar no Relatório ?';
$s_selected_project = 'Projeto selecionado';
$s_valid_project_msg = 'Você deve escolher um projeto válido.';
$s_signup_done_title = 'Registo de conta processada.';
$s_password_emailed_msg = 'Parabéns. Registou-se com sucesso. Vai-lhe ser enviado agora um email de confirmação para verificar o seu endereço de email. Ao visitar o link que irá no e-mail irá ativar a sua conta.';
$s_no_reponse_msg = 'Terá sete dias para completar o processo de confirmação da conta; se não conseguir completar este processo neste período, a conta recém-registada pode ser descartada.';
$s_signup_captcha_request_label = 'Introduza o código como aparece no quadro à direita:';
$s_signup_captcha_refresh = 'Gerar um novo código';
$s_signup_info = 'Ao completar este formulário e verificar as suas respostas, ser-lhe-á enviada uma mensagem de confirmação por correio eletrónico, para o endereço que indicou.<br />Através deste correio eletrónico, será possível ativar a sua conta. Se não ativar a conta no prazo de sete dias, será apagada.<br />Deve indicar um endereço de correio eletrónico válido para que possa receber a mensagem de confirmação.';
$s_signup_title = 'Inscrição';
$s_signup_button = 'Inscrição';
$s_no_password_request = 'A sua palavra-chave é gerida por outro sistema. Contacte o seu administrador de sistema.';
$s_edit_site_settings_title = 'Editar Configurações do Site';
$s_save_settings_button = 'Gravar Configurações';
$s_site_settings_title = 'Configurações do Site';
$s_system_info_link = 'Informações do Sistema';
$s_site_settings_link = 'Configurações do Site';
$s_site_settings_updated_msg = 'As configurações do site foram atualizadas';
$s_summary_title = 'Resumo';
$s_summary_advanced_link = 'Resumo Avançado';
$s_by_project = 'por projeto';
$s_by_status = 'por Estado';
$s_by_date = 'por Data de Entrada (nº de dias)';
$s_by_severity = 'por Importância';
$s_by_resolution = 'por Resolução';
$s_by_category = 'por Categoria';
$s_by_priority = 'por Prioridade';
$s_by_developer = 'por Gestor';
$s_by_reporter = 'por Relator';
$s_reporter_by_resolution = 'Repórter por Resolução';
$s_reporter_effectiveness = 'Eficácia do repórter';
$s_developer_by_resolution = 'Programador por Resolução';
$s_percentage_fixed = '% Resolvida';
$s_percentage_errors = '% Falso';
$s_errors = 'Falso';
$s_opened = 'Aberto';
$s_resolved = 'Resolvido';
$s_total = 'Total';
$s_balance = 'Balanço';
$s_most_active = 'Mais ativos';
$s_score = 'Pontuação';
$s_days = 'Dias';
$s_time_stats = 'Estatísticas de tempo para problemas resolvidos (dias)';
$s_longest_open_bug = 'Problema aberto há mais tempo';
$s_longest_open = 'Aberto há mais tempo';
$s_average_time = 'Tempo médio';
$s_total_time = 'Tempo Total';
$s_developer_stats = 'Estatísticas dos programadores';
$s_reporter_stats = 'Estatísticas dos Relatores';
$s_orct = '(aberto/resolvido/fechado/total)';
$s_summary_header = 'aberta/resolvida/fechada/total/proporção resolvida/proporção';
$s_summary_notice_filter_is_applied = 'Foi aplicado um filtro';
$s_any = 'Qualquer';
$s_all = 'todos';
$s_show = 'Mostrar';
$s_changed = 'Assinalar mudança (horas)';
$s_viewing_bugs_title = 'Ver problemas';
$s_updated = 'Atualizado';
$s_sticky = 'Mostrar problemas colados';
$s_sort = 'Ordenar por';
$s_issue_id = 'Problema #';
$s_recently_visited = 'Recentemente visitado';
$s_note_user_id = 'Nota por';
$s_filter_match_type = 'Tipo de correspondência';
$s_filter_match_all = 'Todas as condições';
$s_filter_match_any = 'Qualquer condição';
$s_none = 'Nenhum';
$s_current = 'atual';
$s_search = 'Pesquisar';
$s_view_prev_link = 'Ver anterior';
$s_view_next_link = 'Ver seguinte';
$s_prev = 'Anterior';
$s_next = 'Seguinte';
$s_first = 'Primeiro';
$s_last = 'Último';
$s_start_date_label = 'Data de início:';
$s_end_date_label = 'Data de término:';
$s_use_date_filters = 'Filtrar por data de envio';
$s_use_last_updated_date_filters = 'Filtrar por data da última atualização';
$s_yes = 'Sim';
$s_no = 'Não';
$s_open_filters = 'Mudar filtro';
$s_or_unassigned = 'Ou não atribuído';
$s_ok = 'OK';
$s_select_all = 'Selecionar tudo';
$s_use_query = 'Use Filtro';
$s_delete_query = 'Apagar filtro';
$s_query_deleted = 'O filtro "%s" foi apagado';
$s_save_query = 'Guardar filtro atual';
$s_reset_query = 'Repor filtro';
$s_query_name = 'Nome do filtro';
$s_query_name_label = 'Nome do filtro:';
$s_query_exists = 'Aparentemente já existe este filtro em particular.';
$s_query_dupe_name = 'Outro filtro já tem este nome. Por favor, escolha um nome diferente.';
$s_query_blank_name = 'Não pode armazenar um filtro sem nome. Por favor, dê um nome a este filtro antes do guardar.';
$s_query_name_too_long = 'Não pode guardar um filtro com um nome maior de 64 caracteres. Por favor, dê um nome mais curto ao filtro.';
$s_query_store_error = 'Houve um erro ao guardar este filtro.';
$s_open_queries = 'Gerir filtros';
$s_query_delete_msg = 'Tem a certeza que quer apagar este filtro?';
$s_edit_filter = 'Editar filtro';
$s_owner = 'Proprietário';
$s_update_filter = 'Atualizar filtro';
$s_current_project = 'Projeto atual';
$s_stored_project = 'Projeto armazenado';
$s_available_filter_for_project = 'Filtros disponíveis para o projeto';
$s_manage_filter_page_title = 'Gerir filtros';
$s_manage_filter_edit_page_title = 'Editar filtro';
$s_apply_filter_button = 'Aplicar';
$s_temporary_filter = 'Filtro temporário';
$s_set_as_persistent_filter = 'Definir como filtro persistente';
$s_view_simple_link = 'Visualização Simples';
$s_product_build = 'Compilação do Produto';
$s_bug_assign_to_button = 'Atribuir a:';
$s_bug_status_to_button = 'Mudar estado para:';
$s_reopen_bug_button = 'Reabrir';
$s_attached_files = 'Ficheiros anexos';
$s_publish = 'Publicar';
$s_browser_does_not_support_audio = 'O seu browser não suporta a etiqueta de áudio.';
$s_browser_does_not_support_video = 'O seu browser não suporta a etiqueta de vídeo.';
$s_bug_view_title = 'Ver detalhes do problema';
$s_no_users_monitoring_bug = 'Não há utilizadores a acompanhar este problema';
$s_users_monitoring_bug = 'Utilizadores a acompanhar este problema';
$s_monitoring_user_list = 'Lista de Utilizadores';
$s_no_users_sponsoring_bug = 'Não há utilizadores a patrocinar este problema.';
$s_users_sponsoring_bug = 'Utilizadores a patrocinar este problema';
$s_sponsors_list = 'Lista de patrocinadores';
$s_total_sponsorship_amount = 'Patrocínio total = %1$s';
$s_add_custom_field_button = 'Novo campo personalizado';
$s_delete_custom_field_button = 'Apagar campo personalizado';
$s_delete_custom_field_everywhere = 'Apagar campo personalizado em todo o lado';
$s_update_custom_field_button = 'Atualizar campo personalizado';
$s_add_existing_custom_field = 'Adicionar este campo personalizado';
$s_edit_custom_field_title = 'Editar campo personalizado';
$s_custom_field = 'Campo';
$s_custom_field_label = 'Campo:';
$s_custom_fields_setup = 'Campos personalizados';
$s_custom_field_name = 'Nome';
$s_custom_field_project_count = 'Número de projetos';
$s_custom_field_type = 'Tipo';
$s_custom_field_possible_values = 'Valores Possíveis';
$s_custom_field_default_value = 'Valor predefinido';
$s_custom_field_valid_regexp = 'Expressão regular';
$s_custom_field_access_level_r = 'Acesso de Leitura';
$s_custom_field_access_level_rw = 'Acesso de Escrita';
$s_custom_field_length_min = 'Comprimento Mínimo';
$s_custom_field_length_max = 'Comprimento Máximo';
$s_custom_field_filter_by = 'Acrescentar ao filtro';
$s_custom_field_display_report = 'Mostar ao relatar problemas';
$s_custom_field_display_update = 'Mostrar ao atualizar problemas';
$s_custom_field_display_resolved = 'Mostrar ao resolver problemas';
$s_custom_field_display_closed = 'Mostrar ao fechar problemas';
$s_custom_field_require_report = 'Necessário para o relatório';
$s_custom_field_require_update = 'Necessário ao atualizar';
$s_custom_field_require_resolved = 'Necessário ao resolver';
$s_custom_field_require_closed = 'Necessário ao fechar';
$s_link_custom_field_to_project_title = 'Ligar campo personalizado aos projetos';
$s_link_custom_field_to_project_button = 'Ligar campo personalizado';
$s_linked_projects_label = 'Projetos ligados';
$s_custom_field_sequence = 'Sequência';
$s_custom_field_sequence_label = 'Sequência';
$s_custom_field_type_enum_string = '0:Segmento de texto,1:Numérico,2:Vírgula flutuante,3:Enumeração,4:Correio eletrónico,5:Caixa de seleção,6:Lista,7:Lista de seleção múltipla,8:Data,9:Rádio,10:Área de texto';
$s_confirm_used_custom_field_deletion = 'Este campo está atualmente ligado a pelo menos um projeto. Se continuar, todos os valores para este campo serão permanentemente apagados. Esta ação não pode ser desfeita. Se não quer apagar este campo, use o botão \'Voltar\' no seu browser. Para avançar, clique no botão seguinte';
$s_confirm_custom_field_deletion = 'Tem a certeza que quer apagar este campo personalizado e todos os valores associados?';
$s_field_delete_button = 'Apagar campo';
$s_confirm_custom_field_unlinking = 'Tem a certeza que quer desvincular este campo personalizado do projeto? Os valores não serão apagados, a não ser que o campo personalizado seja ele próprio eliminado.';
$s_field_remove_button = 'Remover campo';
$s_hide_status = 'Esconder estado';
$s_filter_closed = 'Encerrado(s)';
$s_filter_resolved = 'Resolvido(s)';
$s_hide_closed = 'Ocultar fechados';
$s_hide_resolved = 'Ocultar resolvidos';
$s_and_above = 'E acima';
$s_advanced_filters = 'Filtros avançados';
$s_simple_filters = 'Filtros simples';
$s_monitored_by = 'Acompanhado por';
$s_attachments = 'anexo(s)';
$s_bytes = 'bytes';
$s_kib = 'KiB';
$s_attachment_missing = 'Anexo em falta';
$s_attachment_count = 'Contagem de anexos';
$s_view_attachments_for_issue = 'Ver %1$d anexo(s) do problema #%2$d';
$s_warning_update_custom_field_type = '<strong>Aviso:</strong> O campo personalizado "%1$s" já possui valores armazenados. Alterar o tipo do campo pode causar erros e comportamentos estranhos, se os valores existentes não forem coerentes com o novo tipo do campo. Para continuar, clique no botão abaixo';
$s_phpmailer_language = 'pt';
$s_sponsors = '%1$d patrocinador(es)';
$s_sponsorship_added = 'Patrocínio acrescentado';
$s_sponsorship_updated = 'Patrocínio atualizado';
$s_sponsorship_deleted = 'Patrocínio apagado';
$s_sponsorship_paid = 'Patrocínio pago';
$s_sponsorship_more_info = 'Mais informação sobre patrocínio';
$s_sponsorship_total = 'Patrocínio total';
$s_changelog = 'Registo de alterações';
$s_changelog_empty = 'Não está disponível informação sobre o registo de alterações';
$s_changelog_empty_manager = 'Não há informações disponíveis do registo de mudanças. As incidências são incluídas quando os projetos têm versões e as incidências são solucionadas com a atribuição "corrigida na versão".';
$s_roadmap = 'Roadmap';
$s_resolved_progress = '%1$d de %2$d problema(s) resolvido(s). Progresso (%3$d%%).';
$s_roadmap_empty = 'Não há informações disponíveis do mapa das estradas';
$s_roadmap_empty_manager = 'Não há informações disponíveis do mapa das estradas. As incidências são incluídas quando os projetos têm versões e as incidências definem a "versão de destino".';
$s_http_auth_realm = 'MantisBT Login';
$s_bug = 'problema';
$s_bugs = 'problemas';
$s_add_new_relationship = 'Novo relacionamento';
$s_this_bug = 'Problema atual';
$s_relationship_added = 'Relacionamento acrescentado';
$s_relationship_deleted = 'Relacionamento apagado';
$s_no_relationship = 'sem relacionamento';
$s_relationship_replaced = 'Relacionamento substituído';
$s_replace_relationship_button = 'Trocar';
$s_relationship_with_parent = 'Relacionamento com o problema pai';
$s_delete_relationship_sure_msg = 'Tem a certeza que quer remover este relacionamento?';
$s_relationship_warning_blocking_bugs_not_resolved = 'Nem todos os filhos deste problema estão já resolvidos ou fechados.';
$s_relationship_warning_blocking_bugs_not_resolved_2 = '<strong>Atenção:</strong> Nem todos os filhos deste problema estão já resolvidos ou fechados.<br />Antes de <strong>resolver/fechar</strong> um problema pai, todos os problemas filhos devem ser resolvidos ou fechados.';
$s_create_child_bug_button = 'Clonar';
$s_bug_cloned_to = 'Problema clonado';
$s_bug_created_from = 'Problema gerado a partir de';
$s_copy_from_parent = 'Copiar os dados adicionais do problema principal';
$s_copy_notes_from_parent = 'Copiar as notas da incidência';
$s_copy_attachments_from_parent = 'Copiar anexos';
$s_with = 'com';
$s_relation_graph = 'Gráfico de Relacionamentos';
$s_dependency_graph = 'Gráfico de dependências';
$s_vertical = 'Vertical';
$s_horizontal = 'Horizontal';
$s_view_issue = 'Ver problema';
$s_show_summary = 'Mostrar Resumo';
$s_hide_summary = 'Ocultar Resumo';
$s_perm_rpt_capability = 'Capacidade';
$s_view = 'Ver';
$s_issues = 'Problemas';
$s_report_issue = 'Relatar um problema';
$s_update_issue = 'Atualizar um problema';
$s_monitor_issue = 'Acompanhar um problema';
$s_handle_issue = 'Tratar de um problema';
$s_move_issue = 'Mover um problema';
$s_delete_issue = 'Apagar um problema';
$s_reopen_issue = 'Reabrir um problema';
$s_view_private_issues = 'Ver problemas privados';
$s_update_readonly_issues = 'Atualizar problemas só de leitura';
$s_update_issue_status = 'Atualizar estado do problema';
$s_set_view_status = 'Definir o estado de visibilidade ao informar sobre um novo problema ou nota';
$s_update_view_status = 'Alterar o estado de visibilidade de um problema ou nota existente';
$s_show_list_of_users_monitoring_issue = 'Mostrar lista de utilizadores a acompanhar um problema';
$s_add_users_monitoring_issue = 'Adicionar monitores a um problema';
$s_remove_users_monitoring_issue = 'Remover monitores de um problema';
$s_notes = 'Notas';
$s_add_notes = 'Acrescentar notas';
$s_view_private_notes = 'Ver as notas privadas dos outros';
$s_news = 'Notícias';
$s_view_private_news = 'Ver notícias privadas';
$s_manage_news = 'Gerir notícias';
$s_view_list_of_attachments = 'Ver lista de anexos';
$s_download_attachments = 'Fazer download de anexos';
$s_delete_attachments = 'Apagar anexos';
$s_delete_attachment_sure_msg = 'Tem a certeza que quer apagar este anexo?';
$s_upload_issue_attachments = 'Fazer o upload de anexos do problema';
$s_filters = 'Filtros';
$s_save_filters = 'Gravar filtros';
$s_save_filters_as_shared = 'Gravar como filtros partilhados';
$s_use_saved_filters = 'Usar filtros gravados';
$s_create_project = 'Criar projeto';
$s_delete_project = 'Apagar projeto';
$s_manage_project = 'Gerenciar projeto';
$s_manage_user_access_to_project = 'Gerir o acesso de utilizadores a um projecto';
$s_automatically_included_in_private_projects = 'Incluído automaticamente em projectos privados';
$s_project_documents = 'Documentos do projeto';
$s_view_project_documents = 'Ver documentos de projeto';
$s_upload_project_documents = 'Fazer o upload dos documentos do projecto';
$s_link_custom_fields_to_projects = 'Ligar campos personalizados a projectos';
$s_sponsorships = 'Patrocínios';
$s_view_sponsorship_details = 'Ver detalhes do patrocínio';
$s_view_sponsorship_total = 'Ver patrocínio total';
$s_sponsor_issue = 'Patrocinar problema';
$s_assign_sponsored_issue = 'Atribuir problema patrocinado';
$s_handle_sponsored_issue = 'Tratar de problema patrocinado';
$s_others = 'Outros';
$s_see_email_addresses_of_other_users = 'Ver os endereços de email dos outros utilizadores';
$s_send_reminders = 'Enviar lembretes';
$s_receive_reminders = 'Receber lembretes';
$s_add_profiles = 'Adicionar perfis';
$s_notify_of_new_user_created = 'Notifique quando for criado um novo utilizador';
$s_email_notification = 'Notificação por email';
$s_status_changed_to = 'Estado muda para';
$s_email_on_deleted = 'Email quando for apagado';
$s_email_on_sponsorship_changed = 'Email ao mudar de patrocínio';
$s_email_on_relationship_changed = 'Email quando houver alterações de relacionamento';
$s_email_on_updated = 'Correio eletrónico sobre as atualizações';
$s_email_on_monitor = 'Enviar e-mail quando o utilizador iniciar a monitorização';
$s_view_tags = 'Ver etiquetas anexadas a um erro (bug)';
$s_attach_tags = 'Anexar etiquetas a um erro (bug)';
$s_detach_tags = 'Desanexar etiquetas de um erro (bug)';
$s_detach_own_tags = 'Desanexar etiquetas anexadas pelo mesmo utilizador';
$s_create_new_tags = 'Criar etiquetas novas';
$s_edit_tags = 'Editar nomes e descrições de etiquetas';
$s_edit_own_tags = 'Editar etiquetas criadas pelo mesmo utilizador';
$s_loading = 'A carregar…';
$s_between_date = 'Entre';
$s_on_or_before_date = 'Em ou antes de';
$s_before_date = 'Antes de';
$s_after_date = 'Depois';
$s_on_or_after_date = 'Em ou Depois de';
$s_from_date = 'De';
$s_to_date = 'Para';
$s_on_date = 'Em';
$s_wiki = 'Wiki';
$s_tags = 'Etiquetas';
$s_tag_details = 'Detalhes da etiqueta: %1$s';
$s_tag_id = 'ID da etiqueta';
$s_tag_name = 'Nome';
$s_tag_creator = 'Criador';
$s_tag_created = 'Data de Criação';
$s_tag_updated = 'Última atualização';
$s_tag_description = 'Descrição da etiqueta';
$s_tag_statistics = 'Estatísticas de utilização';
$s_tag_update = 'Atualizar etiqueta: %1$s';
$s_tag_update_return = 'Voltar à etiqueta';
$s_tag_update_button = 'Atualizar etiqueta';
$s_tag_delete_button = 'Apagar etiqueta';
$s_tag_delete_message = 'Tem a certeza que quer apagar esta etiqueta?';
$s_tag_existing = 'Etiquetas existentes';
$s_tag_none_attached = 'Sem etiquetas anexadas.';
$s_tag_attach = 'Anexar';
$s_tag_attach_long = 'Anexar etiquetas';
$s_tag_attach_failed = 'Falhou a anexação da etiqueta';
$s_tag_detach = 'Desanexar "%1$s"';
$s_tag_separate_by = '(Separar por "%1$s")';
$s_tag_invalid_name = 'Nome de etiqueta inválido';
$s_tag_create_denied = 'Criação de permissão negada.';
$s_tag_attach_denied = 'Pedido de anexação negado.';
$s_tag_filter_default = 'Problemas anexos (%1$s)';
$s_tag_history_attached = 'Etiqueta anexada';
$s_tag_history_detached = 'Etiqueta desanexada';
$s_tag_history_renamed = 'Nome da Etiqueta Alterado';
$s_tag_related = 'Etiquetas relacionadas';
$s_tag_related_issues = 'Problemas partilhados (%1$s)';
$s_tag_stats_attached = 'Problemas anexos: %1$s';
$s_tag_create = 'Criar etiqueta';
$s_show_all_tags = 'Todas';
$s_time_tracking_billing_link = 'Acompanhamento de Tempo';
$s_time_tracking = 'Acompanhamento de tempo';
$s_time_tracking_time_spent = 'Tempo gasto:';
$s_time_tracking_get_info_button = 'Obter informação de acompanhamento de tempo';
$s_time_tracking_cost_per_hour = 'Custo / Hora';
$s_time_tracking_cost_per_hour_label = 'Custo / Hora:';
$s_time_tracking_cost = 'Custo';
$s_time_tracking_cost_label = 'Custo:';
$s_total_time_for_issue = 'Tempo total para o problema = %1$s';
$s_time_tracking_stopwatch_start = 'Iniciar';
$s_time_tracking_stopwatch_stop = 'Parar';
$s_time_tracking_stopwatch_reset = 'Reset';
$s_access_denied = 'Acesso negado';
$s_manage_columns_config = 'Gerir colunas';
$s_all_columns_title = 'Todas as colunas disponíveis';
$s_csv_columns_title = 'Colunas CSV';
$s_view_issues_columns_title = 'Ver colunas de problemas';
$s_print_issues_columns_title = 'Imprimir colunas de problemas';
$s_excel_columns_title = 'Colunas de Excel';
$s_update_columns_as_global_default = 'Atualizar colunas como padrão global para todos os projetos';
$s_update_columns_for_current_project = 'Atualizar colunas para projeto atual';
$s_update_columns_as_my_default = 'Atualizar colunas como padrão para todos os projetos';
$s_reset_columns_configuration = 'Repor as configurações de colunas';
$s_copy_columns_from = 'Copiar colunas de';
$s_copy_columns_to = 'Copia colunas para';
$s_due_date = 'Data de vencimento';
$s_overdue = 'Vencido';
$s_overdue_since = 'Atrasado desde %1$s';
$s_overdue_one_day = 'Menos de um dia de atraso.';
$s_overdue_days = 'Com %1$d dias de atraso.';
$s_view_account_title = 'Informação do utilizador';
$s_manage_user = 'Gerir utilizador';
$s_install_information = 'Informação da instalação MantisBT';
$s_database_information = 'Informação da base de dados do MantisBT';
$s_path_information = 'Informação do caminho MantisBT';
$s_mantisbt_database_statistics = 'Estatísticas da base de dados do MantisBT';
$s_php_version = 'Versão do PHP';
$s_adodb_version = 'Versão do ADOdb';
$s_database_driver = 'Controlador da base de dados';
$s_database_version_description = 'Versão da base de dados, descrição';
$s_month_january = 'Janeiro';
$s_month_february = 'Fevereiro';
$s_month_march = 'Março';
$s_month_april = 'Abril';
$s_month_may = 'Maio';
$s_month_june = 'Junho';
$s_month_july = 'Julho';
$s_month_august = 'Agosto';
$s_month_september = 'Setembro';
$s_month_october = 'Outubro';
$s_month_november = 'Novembro';
$s_month_december = 'Dezembro';
$s_timeline_issue_created = '<span class="username">%1$s</span> criou a incidência <span class="issue_id">%2$s</span>';
$s_timeline_issue_file_added = '<span class="username">%1$s</span> anexou o ficheiro <em>%3$s</em> na incidência <span class="issue_id">%2$s</span>';
$s_timeline_issue_file_deleted = '<span class="username">%1$s</span> removeu o ficheiro <em>%3$s</em> da incidência <span class="issue_id">%2$s</span>';
$s_timeline_issue_note_created = '<span class="username">%1$s</span> comentou na incidência <span class="issue_id">%2$s</span>';
$s_timeline_issue_monitor = '<span class="username">%1$s</span> está a monitorizar a incidência <span class="issue_id">%2$s</span>';
$s_timeline_issue_unmonitor = '<span class="username">%1$s</span> deixou de monitorizar a incidência <span class="issue_id">%2$s</span>';
$s_timeline_issue_tagged = '<span class="username">%1$s</span> etiquetou a incidência <span class="issue_id">%2$s</span> com <span class="tag_name">%3$s</span>';
$s_timeline_issue_untagged = '<span class="username">%1$s</span> removeu a etiqueta <span class="tag_name">%3$s</span> da incidência <span class="issue_id">%2$s</span>';
$s_timeline_issue_resolved = '<span class="username">%1$s</span> solucionou a incidência <span class="issue_id">%2$s</span>';
$s_timeline_issue_closed = '<span class="username">%1$s</span> fechou a incidência <span class="issue_id">%2$s</span>';
$s_timeline_issue_reopened = '<span class="username">%1$s</span> reabriu a incidência <span class="issue_id">%2$s</span>';
$s_timeline_issue_assigned = '<span class="username">%1$s</span> atribuiu a incidência <span class="issue_id">%2$s</span> a <span class="username">%3$s</span>';
$s_timeline_issue_assigned_to_self = '<span class="username">%1$s</span> escolheu a incidência <span class="issue_id">%2$s</span>';
$s_timeline_issue_unassigned = '<span class="username">%1$s</span> deixou de atribuir a incidência <span class="issue_id">%2$s</span>';
$s_timeline_no_activity = 'Não há atividades no intervalo de tempo.';
$s_timeline_title = 'Linha do tempo';
$s_timeline_more = 'Mais acontecimentos...';
$s_missing_error_string = 'Está a faltar o segmento de texto: %1$s';
$MANTIS_ERROR[ERROR_GENERIC] = 'Ocorreu um erro durante a execução desta ação. Pode querer comunicar este erro ao seu administrador local';
$MANTIS_ERROR[ERROR_SQL] = 'Erro de SQL detectado';
$MANTIS_ERROR[ERROR_REPORT] = 'Houve um erro no seu relatório';
$MANTIS_ERROR[ERROR_NO_FILE_SPECIFIED] = 'Nenhum ficheiro especificado';
$MANTIS_ERROR[ERROR_FILE_DISALLOWED] = 'O tipo de ficheiro não é permitido.';
$MANTIS_ERROR[ERROR_NO_DIRECTORY] = 'A pasta não existe. Por favor verifique as definições do projeto.';
$MANTIS_ERROR[ERROR_DUPLICATE_PROJECT] = 'Um projeto com este nome já existe.';
$MANTIS_ERROR[ERROR_EMPTY_FIELD] = 'O campo necessário "%1$s" estava vazio. Por favor, verifique novamente as entradas.';
$MANTIS_ERROR[ERROR_INVALID_FIELD_VALUE] = 'Valor inválido de \'%1$s\'';
$MANTIS_ERROR[ERROR_PROTECTED_ACCOUNT] = 'Esta conta está protegida. Você não tem permissão para realizar esta operação até que a proteção seja removida.';
$MANTIS_ERROR[ERROR_ACCESS_DENIED] = 'Acesso Negado.';
$MANTIS_ERROR[ERROR_UPLOAD_FAILURE] = 'Carregamento de ficheiro mal-sucedido. Ficheiro não é legível pelo MantisBT. Por favor verifique as definições do projeto.';
$MANTIS_ERROR[ERROR_FILE_TOO_BIG] = 'Carregamento de ficheiro mal-sucedido. Isto foi provavelmente causado pelo tamanho do ficheiro ser superior ao limite permitido por esta instalação do PHP.';
$MANTIS_ERROR[ERROR_GPC_VAR_NOT_FOUND] = 'Um parâmetro obrigatório para esta página (%1$s) não foi encontrado.';
$MANTIS_ERROR[ERROR_USER_NAME_NOT_UNIQUE] = 'Esse nome de utilizador já está a ser usado. Por favor volte atrás e escolha outro.';
$MANTIS_ERROR[ERROR_USER_EMAIL_NOT_UNIQUE] = 'Este correio eletrónico já está a ser utilizado. Por favor, clique em voltar e escolha outro.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_NOT_FOUND] = 'Opção de configuração "%1$s" não encontrada.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_CANT_BE_SET_IN_DB] = 'Opção de configuração "%1$s" não pode ser definida na base de dados. Deve ser definida em config_inc.php.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_BAD_SYNTAX] = 'Não foi possível definir a opção de configuração "%1$s": %2$s';
$MANTIS_ERROR[ERROR_LANG_STRING_NOT_FOUND] = 'Cadeia de caracteres "%1$s" não encontrada.';
$MANTIS_ERROR[ERROR_BUGNOTE_NOT_FOUND] = 'Nota não encontrada.';
$MANTIS_ERROR[ERROR_DB_FIELD_NOT_FOUND] = 'Campo de base de dados "%1$s" não encontrado.';
$MANTIS_ERROR[ERROR_HANDLER_ACCESS_TOO_LOW] = 'Handler de problemas não tem privilégios suficientes para tratar do problema neste estado.';
$MANTIS_ERROR[ERROR_PROJECT_HIERARCHY_DISABLED] = 'A hierarquia de projetos (subprojetos) está desativada.';
$MANTIS_ERROR[ERROR_PROJECT_NOT_FOUND] = 'O projeto "%1$s" não foi encontrado.';
$MANTIS_ERROR[ERROR_PROJECT_NAME_NOT_UNIQUE] = 'Um projeto com esse nome já existe. Por favor, volte atrás e introduza um nome diferente.';
$MANTIS_ERROR[ERROR_PROJECT_NAME_INVALID] = 'O nome de projeto especificado é inválido. Nomes de projeto não podem estar em branco.';
$MANTIS_ERROR[ERROR_PROJECT_RECURSIVE_HIERARCHY] = 'Essa operação iria criar um ciclo na hierarquia de subprojectos.';
$MANTIS_ERROR[ERROR_PROJECT_SUBPROJECT_DUPLICATE] = 'O projeto "%1$s" já é um subprojeto de "%2$s".';
$MANTIS_ERROR[ERROR_PROJECT_SUBPROJECT_NOT_FOUND] = 'O projeto "%1$s" não é um subprojeto de "%2$s".';
$MANTIS_ERROR[ERROR_USER_BY_NAME_NOT_FOUND] = 'Utilizador com o nome "%1$s" não encontrado.';
$MANTIS_ERROR[ERROR_USER_BY_ID_NOT_FOUND] = 'Utilizador com o id "%1$d" não encontrado.';
$MANTIS_ERROR[ERROR_USER_BY_EMAIL_NOT_FOUND] = 'Não foi encontrado o utilizador com o correio eletrónico "%1$s".';
$MANTIS_ERROR[ERROR_USER_BY_REALNAME_NOT_FOUND] = 'Não foi encontrado o utilizador com o nome real "%1$s".';
$MANTIS_ERROR[ERROR_AUTH_INVALID_COOKIE] = 'A informação de login armazenada pelo seu navegador é inválida. Talvez a sua conta tenha sido apagada?';
$MANTIS_ERROR[ERROR_USER_PREFS_NOT_FOUND] = 'As preferências deste utilizador não puderam ser encontradas.';
$MANTIS_ERROR[ERROR_NEWS_NOT_FOUND] = 'Não foi encontrado item de notícias.';
$MANTIS_ERROR[ERROR_USER_CREATE_PASSWORD_MISMATCH] = 'Palavra-chave não corresponde à verificação.';
$MANTIS_ERROR[ERROR_USER_CURRENT_PASSWORD_MISMATCH] = 'A palavra-passe atual está incorreta.';
$MANTIS_ERROR[ERROR_GPC_ARRAY_EXPECTED] = 'Era esperado um array mas foi recebida uma cadeia de caracteres para %1$s.';
$MANTIS_ERROR[ERROR_GPC_ARRAY_UNEXPECTED] = 'Era esperada uma cadeia de caracteres mas foi recebido um array para %1$s.';
$MANTIS_ERROR[ERROR_GPC_NOT_NUMBER] = 'Um número era esperado para %1$s.';
$MANTIS_ERROR[ERROR_BUG_NOT_FOUND] = 'Problema %1$d não encontrado.';
$MANTIS_ERROR[ERROR_FILTER_NOT_FOUND] = 'Filtro %1$s não encontrado.';
$MANTIS_ERROR[ERROR_EMAIL_INVALID] = 'Endereço de email inválido.';
$MANTIS_ERROR[ERROR_EMAIL_DISPOSABLE] = 'Não é permitido o uso de endereços de e-mail descartáveis.';
$MANTIS_ERROR[ERROR_USER_PROFILE_NOT_FOUND] = 'Perfil não encontrado.';
$MANTIS_ERROR[ERROR_FILE_NOT_ALLOWED] = 'Tipo de ficheiro não permitido para uploads.';
$MANTIS_ERROR[ERROR_FILE_DUPLICATE] = 'Este é um ficheiro duplicado. Por favor, apague o ficheiro antes.';
$MANTIS_ERROR[ERROR_FILE_INVALID_UPLOAD_PATH] = 'Localização inválida. O diretório não existe ou não tem as permissões necessárias.';
$MANTIS_ERROR[ERROR_FILE_NO_UPLOAD_FAILURE] = 'Nenhum ficheiro foi carregado. Por favor, volte atrás e escolha um ficheiro antes de premir o botão Carregar.';
$MANTIS_ERROR[ERROR_FILE_MOVE_FAILED] = 'Não foi possível mover para o destino final o ficheiro enviado. O diretório não existe ou não tem as permissões necessárias.';
$MANTIS_ERROR[ERROR_FILE_NOT_FOUND] = 'Não foi encontrado nenhum anexo com o identificador "%1$d".';
$MANTIS_ERROR[ERROR_BUG_DUPLICATE_SELF] = 'Não pode marcar um problema como duplicado dele próprio.';
$MANTIS_ERROR[ERROR_BUG_REVISION_NOT_FOUND] = 'Revisão do problema não encontrada.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_NOT_FOUND] = 'Campo personalizado não encontrado.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_NAME_NOT_UNIQUE] = 'Este é um nome duplicado.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_IN_USE] = 'Pelo menos um projeto ainda utiliza este campo.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_INVALID_VALUE] = 'Valor inválido para o campo "%1$s".';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_INVALID_DEFINITION] = 'Definição de campo personalizado inválida.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_INVALID_PROPERTY] = 'Propriedade de campo personalizado inválido ( %1$s ).';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_NOT_LINKED_TO_PROJECT] = 'O campo personalizado "%1$s" (identificador %2$s) não está vinculado ao projeto ativo.';
$MANTIS_ERROR[ERROR_LDAP_AUTH_FAILED] = 'Falha na Autenticação LDAP.';
$MANTIS_ERROR[ERROR_LDAP_SERVER_CONNECT_FAILED] = 'Falha na ligação ao servidor LDAP.';
$MANTIS_ERROR[ERROR_LDAP_UPDATE_FAILED] = 'A atualização do Registo LDAP falhou.';
$MANTIS_ERROR[ERROR_LDAP_USER_NOT_FOUND] = 'Registo de Utilizador LDAP Não Encontrado.';
$MANTIS_ERROR[ERROR_DB_CONNECT_FAILED] = 'Ligação à base de dados falhou. O erro recebido da base de dados foi #%1$d: %2$s.';
$MANTIS_ERROR[ERROR_DB_QUERY_FAILED] = 'Query à base de dados falhou. Erro recebido da base de dados foi #%1$d: %2$s para a query: %3$s.';
$MANTIS_ERROR[ERROR_DB_SELECT_FAILED] = 'Seleção da base de dados falhou. Erro recebido da base de dados foi #%1$d: %2$s.';
$MANTIS_ERROR[ERROR_DB_IDENTIFIER_TOO_LONG] = 'O identificador da base de dados "%1$s" é demasiado longo. Tente reduzir o tamanho de g_db_table_prefix/suffix';
$MANTIS_ERROR[ERROR_CATEGORY_DUPLICATE] = 'Já existe uma categoria com esse nome.';
$MANTIS_ERROR[ERROR_NO_COPY_ACTION] = 'Nenhuma ação de cópia foi especificada.';
$MANTIS_ERROR[ERROR_CATEGORY_NOT_FOUND] = 'Categoria não encontrada.';
$MANTIS_ERROR[ERROR_CATEGORY_NOT_FOUND_FOR_PROJECT] = 'Categoria "%1$s" não encontrada para o projeto "%2$s".';
$MANTIS_ERROR[ERROR_CATEGORY_CANNOT_DELETE_DEFAULT] = 'Esta categoria não pode ser eliminada, porque está definida como "categoria padrão para movimentos".';
$MANTIS_ERROR[ERROR_CATEGORY_CANNOT_DELETE_HAS_ISSUES] = 'A categoria "%1$s" não pode ser apagada, porque está associada com uma ou mais incidências.';
$MANTIS_ERROR[ERROR_VERSION_DUPLICATE] = 'Já existe uma versão com esse nome.';
$MANTIS_ERROR[ERROR_VERSION_NOT_FOUND] = 'Versão "%1$s" não encontrada.';
$MANTIS_ERROR[ERROR_USER_NAME_INVALID] = 'O nome de utilizador é inválido. Nomes de utilizador só podem conter letras, números, espaços, hífenes (-), pontos (.), sinais de mais (+) e sublinhados (_).';
$MANTIS_ERROR[ERROR_USER_REAL_NAME_INVALID] = 'O nome verdadeiro do utilizador é inválido.';
$MANTIS_ERROR[ERROR_USER_DOES_NOT_HAVE_REQ_ACCESS] = 'O utilizador não tem o nível de acesso exigido.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_NOT_ENABLED] = 'Apoio de patrocínio não ativado.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_NOT_FOUND] = 'Patrocínio %1$d não encontrado.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_AMOUNT_TOO_LOW] = 'Patrocínio (%1$s) é inferior à quantidade mínima (%2$s).';
$MANTIS_ERROR[ERROR_SPONSORSHIP_HANDLER_ACCESS_LEVEL_TOO_LOW] = 'Handler não tem o nível de acesso necessário para tratar de problemas patrocinados.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_ASSIGNER_ACCESS_LEVEL_TOO_LOW] = 'Acesso negado: Atribuir patrocínio requer um nível de acesso mais alto.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_SPONSOR_NO_EMAIL] = 'O patrocinador não indicou um endereço de email. Por favor, atualize o seu perfil.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_INVALID] = 'Opção de configuração "%1$s" tem o valor inválido "%2$s".';
$MANTIS_ERROR[ERROR_BUG_READ_ONLY_ACTION_DENIED] = 'Não pode realizar esta ação porque o problema "%1$d" é só de leitura.';
$MANTIS_ERROR[ERROR_BUG_RESOLVE_DEPENDANTS_BLOCKING] = 'Esta incidência não pode ser solucionada até que todas as incidências dependentes sejam solucionadas. Se não vê nenhuma incidência dependente, peça ao administrador do seu sistema que lhe dê acesso ao projeto.';
$MANTIS_ERROR[ERROR_RELATIONSHIP_NOT_FOUND] = 'Relacionamento não encontrado.';
$MANTIS_ERROR[ERROR_RELATIONSHIP_ACCESS_LEVEL_TO_DEST_BUG_TOO_LOW] = 'Acesso negado: O problema %1$d requer nível de acesso mais alto.';
$MANTIS_ERROR[ERROR_RELATIONSHIP_SAME_BUG] = 'Um problema não pode estar relacionado consigo próprio.';
$MANTIS_ERROR[ERROR_SIGNUP_NOT_MATCHING_CAPTCHA] = 'Hash de confirmação não corresponde. Por favor, tente novamente.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_NOT_ENABLED] = 'A funcionalidade "perdeu a sua palavra-chave" não está disponível.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_NO_EMAIL_SPECIFIED] = 'Tem que indicar um endereço de email para poder redefinir a palavra-chave.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_NOT_MATCHING_DATA] = 'A informação fornecida não corresponde a qualquer conta registada!';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_CONFIRM_HASH_INVALID] = 'O URL de confirmação é inválido ou já foi utilizado. Por favor registe-se novamente.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_MAX_IN_PROGRESS_ATTEMPTS_REACHED] = 'Foi atingido o número máximo de pedidos pendentes. Por favor, contacte o administrador do sistema.';
$MANTIS_ERROR[ERROR_USER_CHANGE_LAST_ADMIN] = 'Não pode alterar ou despromover a última conta de administrador. Para executar a ação pedida, é necessário criar primeiro outra conta de administrador.';
$MANTIS_ERROR[ERROR_PAGE_REDIRECTION] = 'Erro de redirecionamento da página, certifique-se que não haja espaços fora do bloco PHP (&lt;?php ?&gt;) nos ficheiros config_inc.php ou custom_*.php.';
$MANTIS_ERROR[ERROR_TAG_NOT_FOUND] = 'Não foi possível encontrar a etiqueta "%1$s".';
$MANTIS_ERROR[ERROR_TAG_DUPLICATE] = 'Já existe uma etiqueta com o nome "%1$s".';
$MANTIS_ERROR[ERROR_TAG_NAME_INVALID] = 'O nome de etiqueta "%1$s" é inválido.';
$MANTIS_ERROR[ERROR_TAG_NOT_ATTACHED] = 'Essa etiqueta não está associada a esse problema.';
$MANTIS_ERROR[ERROR_TAG_ALREADY_ATTACHED] = 'Essa etiqueta já está associada a esse problema.';
$MANTIS_ERROR[ERROR_TOKEN_NOT_FOUND] = 'Não foi possível encontrar o token.';
$MANTIS_ERROR[ERROR_EVENT_UNDECLARED] = 'Evento "%1$s" ainda não foi declarado.';
$MANTIS_ERROR[ERROR_PLUGIN_NOT_REGISTERED] = 'O plugin "%1$s" não está registado.';
$MANTIS_ERROR[ERROR_PLUGIN_NOT_LOADED] = 'O plugin "%1$s" não foi carregado ainda, certifique-se se as dependências já foram cumpridas.';
$MANTIS_ERROR[ERROR_PLUGIN_ALREADY_INSTALLED] = 'O plugin "%1$s" já está instalado.';
$MANTIS_ERROR[ERROR_PLUGIN_CLASS_NOT_FOUND] = 'A classe "%2$s" não foi definida no plugin "%1$s".';
$MANTIS_ERROR[ERROR_PLUGIN_PAGE_NOT_FOUND] = 'A página "%2$s" não existe no Plugin "%1$s".';
$MANTIS_ERROR[ERROR_PLUGIN_FILE_NOT_FOUND] = 'O ficheiro "%2$s" não existe no plugin "%1$s".';
$MANTIS_ERROR[ERROR_PLUGIN_INSTALL_FAILED] = 'Instalação do plugin falhada:  %1$s .';
$MANTIS_ERROR[ERROR_PLUGIN_UPGRADE_FAILED] = 'A atualização do schema do plugin falhou no bloco #%1$s.';
$MANTIS_ERROR[ERROR_PLUGIN_UPGRADE_NEEDED] = 'O plugin "%1$s" precisa de ser atualizado antes de você poder aceder a esta página.';
$MANTIS_ERROR[ERROR_PLUGIN_INVALID_PAGE] = 'O formato especificado da página de plugin "%1$s" não é válido.
Deve corresponder com o formato "Plugin[/path/to]/page".';
$MANTIS_ERROR[ERROR_PLUGIN_INVALID_FILE] = 'O formato especificado do ficheiro para o plugin "%1$s" não é válido. Deve corresponder com o formato "Plugin[/path/to]/file[.ext]".';
$MANTIS_ERROR[ERROR_PLUGIN_GENERIC] = 'Houve um erro desconhecido "%1$s" durante a execução do plugin "%2$s".';
$MANTIS_ERROR[ERROR_COLUMNS_DUPLICATE] = 'Campo "%1$s" contém coluna duplicada "%2$s".';
$MANTIS_ERROR[ERROR_COLUMNS_INVALID] = 'Campo "%1$s" contém campo inválido "%2$s".';
$MANTIS_ERROR[ERROR_SESSION_VAR_NOT_FOUND] = 'Variável de sessão "%1$s" não encontrada.';
$MANTIS_ERROR[ERROR_SESSION_NOT_VALID] = 'A sua sessão tornou-se inválida.';
$MANTIS_ERROR[ERROR_FORM_TOKEN_INVALID] = 'Token de segurança de formulário inválido. Isto pode ser causado por um timeout da sessão ou pelo envio acidental do formulário em duplicado.';
$MANTIS_ERROR[ERROR_CRYPTO_MASTER_SALT_INVALID] = 'Por razões de segurança, o MantisBT não funcionará quando o $g_crypto_master_salt não for especificado corretamente em config_inc.php ou for menor que 16 caracteres.';
$MANTIS_ERROR[ERROR_INVALID_REQUEST_METHOD] = 'Esta página não pode ser acedida por este método.';
$MANTIS_ERROR[ERROR_INVALID_SORT_FIELD] = 'Campo de ordenação inválido.';
$MANTIS_ERROR[ERROR_INVALID_DATE_FORMAT] = 'Formato de data inválido.';
$MANTIS_ERROR[ERROR_INVALID_RESOLUTION] = 'A resolução "%1$s" não é permitida para o estado "%2$s".';
$MANTIS_ERROR[ERROR_UPDATING_TIMEZONE] = 'Não é possível atualizar o fuso horário.';
$MANTIS_ERROR[ERROR_DEPRECATED_SUPERSEDED] = 'Funcionalidade obsoleta: "%1$s", utilize "%2$s" no seu lugar.';
$MANTIS_ERROR[ERROR_DISPLAY_USER_ERROR_INLINE] = 'Aviso: O sistema está configurado para mostrar os erros do MantisBT (E_USER_ERROR) na linha. A execução do programa continuará; isto pode causar problemas de integridade do sistema ou dos dados.';
$MANTIS_ERROR[ERROR_TYPE_MISMATCH] = 'Os tipos de dados não coincidem. Ative as mensagens de erros detalhadas para mais informações.';
$MANTIS_ERROR[ERROR_BUG_CONFLICTING_EDIT] = 'Essa incidência foi atualizada por outro utilizador. Por favor, retorne à incidência e envie novamente as suas alterações.';
$MANTIS_ERROR[ERROR_SPAM_SUSPECTED] = 'Atingiu o limite permitido de atividade de %d acontecimentos nos últimos %d segundos; as suas ações foram bloqueadas para evitar spam, por favor tente novamente mais tarde.';
$MANTIS_ERROR[ERROR_FIELD_TOO_LONG] = 'O campo "%1$s" deve ser menor ou igual a %2$d caracteres.';
$MANTIS_ERROR[ERROR_API_TOKEN_NAME_NOT_UNIQUE] = 'O nome da chave API "%s" já está a ser utilizado. Por favor, volte e selecione outro.';
$MANTIS_ERROR[ERROR_LOGFILE_NOT_WRITABLE] = 'O ficheiro especificado em $g_log_destination "%s" não pode ser escrito.';
$s_dropzone_default_message = 'Anexe ficheiros arrastando e largando-os, selecionando-os ou inserindo-os.';
$s_dropzone_fallback_message = 'O seu navegador não permite carregar ficheiros, com a opção de arrastar e largar.';
$s_dropzone_file_too_big = 'O ficheiro é demasiado grande ({{filesize}}MiB). Tamanho máximo: {{maxFilesize}}MiB.';
$s_dropzone_invalid_file_type = 'Não pode carregar ficheiros deste tipo.';
$s_dropzone_response_error = 'O servidor respondeu com o código {{statusCode}}.';
$s_dropzone_cancel_upload = 'Cancelar carregamento';
$s_dropzone_cancel_upload_confirmation = 'Tem a certeza de que deseja cancelar este carregamento?';
$s_dropzone_remove_file = 'Eliminar ficheiro';
$s_dropzone_max_files_exceeded = 'Não pode carregar mais ficheiros.';
$s_dropzone_not_supported = 'O Dropzone.js não suporta navegadores antigos!';
$s_dropzone_multiple_files_too_big = 'Os seguintes ficheiros:{{files}}Excedem o tamanho máximo de ficheiro permitido ({{maxFilesize}} MiB)';
$s_save = 'Gravar';
$s_reset = 'Reiniciar';
$s_persist = 'Persistir';
$s_load = 'Carregar';
$s_apply_changes = 'Aplicar alterações';
$s_undo = 'Desfazer';
$s_edit = 'Editar';
$s_update = 'Atualizar';
$s_delete = 'Apagar';
$s_make_default = 'Tornar Padrão';
$s_print = 'Imprimir';
$s_jump = 'Ir Para';
$s_change = 'Alteração';
$s_go_back = 'Voltar';
$s_proceed = 'Prosseguir';
$s_move = 'Mover';
$s_close = 'Fechar';
$s_add = 'Adicionar';
$s_login = 'Entrar';
