<?php
/** MantisBT - a php based bugtracking system
 *
 * Copyright (C) 2000 - 2002  Kenzaburo Ito - <EMAIL>
 * Copyright (C) 2002 - 2016  MantisBT Team - <EMAIL>
 *
 * MantisBT is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * MantisBT is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with MantisBT.  If not, see <http://www.gnu.org/licenses/>.
 *
 * **********************************************************************
 * ** This file contains translations stored in translatewiki.net.     **
 * ** See https://translatewiki.net/wiki/Project:About for information **
 * ** on copyright/license for translatewiki.net translations.         **
 * **********************************************************************
 * **                                                                  **
 * **                      DO NOT UPDATE MANUALLY                      **
 * **                                                                  **
 * ** To improve a translation please visit https://translatewiki.net  **
 * ** Detailed instructions on how to create or update translations at **
 * ** http://www.mantisbt.org/wiki/doku.php/mantisbt:translationshowto **
 * **********************************************************************
 */
/** French (français)
 * 
 * See the qqq 'language' for message documentation incl. usage of parameters
 * To improve a translation please visit https://translatewiki.net
 *
 * @ingroup Language
 * @file
 *
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR> 113
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR> p
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */

$s_directionality = 'ltr';
$s_actiongroup_menu_copy = 'Copier';
$s_actiongroup_menu_assign = 'Affecter';
$s_actiongroup_menu_resolve = 'Résoudre';
$s_actiongroup_menu_update_priority = 'Mettre à jour la priorité';
$s_actiongroup_menu_update_status = 'Mettre à jour le statut';
$s_actiongroup_menu_update_severity = 'Mettre à jour l’impact';
$s_actiongroup_menu_update_view_status = 'Mettre à jour l’état d’affichage';
$s_actiongroup_menu_update_category = 'Mettre à jour la catégorie';
$s_actiongroup_menu_set_sticky = 'Activer / désactiver l’épinglage';
$s_actiongroup_menu_update_field = 'Mettre à jour %1$s';
$s_actiongroup_menu_update_product_version = 'Mettre à jour la version du produit';
$s_actiongroup_menu_update_target_version = 'Mettre à jour la version ciblée';
$s_actiongroup_menu_update_fixed_in_version = 'Mettre à jour la version résolue';
$s_actiongroup_menu_update_product_build = 'Mettre à jour la compilation';
$s_actiongroup_menu_update_due_date = 'Mettre à jour la date d’échéance';
$s_actiongroup_menu_add_note = 'Ajouter une note';
$s_actiongroup_menu_attach_tags = 'Ajouter des balises';
$s_actiongroup_bugs = 'Anomalies sélectionnées';
$s_actiongroup_error_issue_is_readonly = 'L’anomalie est en lecture seule.';
$s_all_projects = 'Tous les projets';
$s_move_bugs = 'Déplacer les anomalies';
$s_operation_successful = 'Opération réussie.';
$s_operation_warnings = 'Opération terminée avec des avertissements.';
$s_operation_failed = 'L’opération ne s’est pas bien terminée.';
$s_date_order = 'Tri par date';
$s_print_all_bug_page_link = 'Imprimer des rapports';
$s_csv_export = 'Export CSV';
$s_excel_export = 'Export Excel';
$s_login_anonymously = 'Connexion anonyme';
$s_anonymous = 'Anonyme';
$s_jump_to_bugnotes = 'Aller aux notes';
$s_jump_to_history = 'Sauter à l’historique';
$s_display_history = 'Afficher l’historique';
$s_public_project_msg = 'Ce projet est public. Tous les utilisateurs y ont accès.';
$s_private_project_msg = 'Ce projet est privé. Seuls les administrateurs et les utilisateurs ajoutés manuellement y ont accès.';
$s_access_level_project = 'Niveau d’accès du projet';
$s_view_submitted_bug_link = 'Voir l’anomalie soumise %1$s';
$s_assigned_projects = 'Projets assignés';
$s_assigned_projects_label = 'Projets assignés';
$s_unassigned_projects_label = 'Projets non assignés';
$s_copy_users = 'Copier les utilisateurs';
$s_copy_categories_from = 'Copier les catégories depuis';
$s_copy_categories_to = 'Copier les catégories vers';
$s_copy_categories_exclude_inherited = 'Exclure les catégories héritées';
$s_copy_versions_from = 'Copier les versions depuis';
$s_copy_versions_to = 'Copier les versions vers';
$s_copy_users_from = 'Copier les utilisateurs depuis';
$s_copy_users_to = 'Copier les utilisateurs vers';
$s_bug_history = 'Historique de l’anomalie';
$s_field = 'Champ';
$s_old_value = 'Ancienne valeur';
$s_new_value = 'Nouvelle valeur';
$s_date_modified = 'Date de modification';
$s_bugnote = 'Note';
$s_bugnote_view_state = 'Voir l’état de la note';
$s_bug_monitor = 'Anomalie surveillée';
$s_bug_end_monitor = 'Fin de surveillance de l’anomalie';
$s_announcement = 'Annonce';
$s_stays_on_top = 'Garder au dessus';
$s_bugnote_link_title = 'Lien direct vers la note';
$s_delete_bugnote_button = 'Supprimer la note';
$s_delete_bugnote_sure_msg = 'Voulez-vous vraiment supprimer cette note ?';
$s_bug_relationships = 'Relations';
$s_empty_password_sure_msg = 'L’utilisateur a un mot de passe vide. Est-ce bien ce que vous voulez ?';
$s_empty_password_button = 'Utiliser un mot de passe vide';
$s_reauthenticate_title = 'Identification';
$s_reauthenticate_message = 'Vous visitez une page sécurisée et votre session sécurisée est expirée. Veuillez vous identifier à nouveau pour poursuivre.';
$s_no_category = '(Aucune catégorie)';
$s_global_categories = 'Catégories globales';
$s_inherit = 'Hériter des catégories';
$s_inherit_global = 'Hériter des catégories globales';
$s_inherit_parent = 'Hériter des catégories parentes';
$s_update_subproject_inheritance = 'Mettre à jour l’héritage du sous-projet';
$s_duplicate_of = 'doublon de';
$s_has_duplicate = 'a pour doublon';
$s_related_to = 'relatif à';
$s_dependant_on = 'parent de';
$s_blocks = 'enfant de';
$s_new_bug = 'Nouvelle anomalie';
$s_bugnote_added = 'Note ajoutée';
$s_bugnote_edited = 'Note modifiée';
$s_bugnote_deleted = 'Note supprimée';
$s_summary_updated = 'Résumé mis à jour';
$s_description_updated = 'Description mise à jour';
$s_additional_information_updated = 'Information complémentaire mise à jour';
$s_steps_to_reproduce_updated = 'Étapes pour reproduire mises à jour';
$s_file_added = 'Fichier ajouté';
$s_file_deleted = 'Fichier supprimé';
$s_bug_deleted = 'Anomalie supprimée';
$s_make_private = 'Rendre privé';
$s_make_public = 'Rendre public';
$s_create_new_project_link = 'Créer un nouveau projet';
$s_opensearch_id_short = 'Identifiant %s';
$s_opensearch_id_description = '%s : recherche par identifiant de problème';
$s_opensearch_text_short = '%s texte';
$s_opensearch_text_description = '%s : recherche en texte intégral';
$s_select_option = '(choisir)';
$s_bug_actiongroup_access = 'Vous n’aviez pas les permissions appropriées pour effectuer cette action.';
$s_bug_actiongroup_status = 'Ce bogue ne peut pas être mis au statut demandé';
$s_bug_actiongroup_category = 'Cette anomalie ne peut pas être changée pour la catégorie demandée';
$s_bug_actiongroup_handler = 'L’utilisateur demandé n’est pas autorisé à traiter cette question';
$s_bug_actiongroup_version = 'La version demandée n’existe pas dans le projet associé à cette anomalie';
$s_close_bugs_conf_msg = 'Voulez-vous vraiment fermer ces anomalies ?';
$s_delete_bugs_conf_msg = 'Voulez-vous vraiment supprimer ces anomalies ?';
$s_move_bugs_conf_msg = 'Déplacer les anomalies vers';
$s_copy_bugs_conf_msg = 'Copier les anomalies vers';
$s_assign_bugs_conf_msg = 'Affecter les problèmes à';
$s_resolve_bugs_conf_msg = 'Choisir la résolution des anomalies';
$s_priority_bugs_conf_msg = 'Choisir la priorité des anomalies';
$s_status_bugs_conf_msg = 'Choisir le statut des anomalies';
$s_view_status_bugs_conf_msg = 'Choisir l’état d’affichage des anomalies';
$s_category_bugs_conf_msg = 'Choisir la catégorie des anomalies';
$s_set_sticky_bugs_conf_msg = 'Voulez-vous vraiment épingler / désépingler ces anomalies ?';
$s_product_version_bugs_conf_msg = 'Mettre à jour la version du produit à';
$s_fixed_in_version_bugs_conf_msg = 'Mettre à jour la version de résolution à';
$s_target_version_bugs_conf_msg = 'Mettre à jour la version ciblée à';
$s_due_date_bugs_conf_msg = 'Mettre à jour la date d’échéance à';
$s_close_group_bugs_button = 'Fermer les anomalies';
$s_delete_group_bugs_button = 'Supprimer les anomalies';
$s_move_group_bugs_button = 'Déplacer les anomalies';
$s_copy_group_bugs_button = 'Copier les anomalies';
$s_assign_group_bugs_button = 'Affecter les anomalies';
$s_resolve_group_bugs_button = 'Résoudre les anomalies';
$s_priority_group_bugs_button = 'Mettre à jour la priorité';
$s_status_group_bugs_button = 'Mettre à jour le statut';
$s_category_group_bugs_button = 'Mettre à jour la catégorie';
$s_view_status_group_bugs_button = 'Mettre à jour le statut d’affichage';
$s_set_sticky_group_bugs_button = 'Épingler / désépingler';
$s_product_version_group_bugs_button = 'Mettre à jour la version du produit';
$s_fixed_in_version_group_bugs_button = 'Mettre à jour la version de résolution';
$s_target_version_group_bugs_button = 'Mettre à jour la version ciblée';
$s_due_date_group_bugs_button = 'Mettre à jour la date d’échéance';
$s_update_severity_title = 'Mettre à jour la sévérité';
$s_update_severity_msg = 'Choisissez la sévérité de l’anomalie';
$s_update_severity_button = 'Mettre à jour la sévérité';
$s_hide_button = 'Afficher seulement les anomalies sélectionnées';
$s_printing_preferences_title = 'Choisir les champs à imprimer';
$s_printing_options_link = 'Options d’impression';
$s_bugnote_title = 'Auteur de la note';
$s_bugnote_date = 'Date de la note';
$s_bugnote_description = 'Description de la note';
$s_error_no_proceed = 'Merci d\'utiliser le bouton « Précédent » de votre navigateur web pour retourner à la page précédente. Vous pourrez y corriger les problèmes identifiés par cette erreur ou choisir une autre action. Vous pouvez aussi choisir une option de la barre de menu pour aller directement dans une nouvelle section.';
$s_login_error = 'Votre compte est peut être désactivé ou alors le code utilisateur et le mot de passe que vous avez saisis sont incorrects.';
$s_login_cookies_disabled = 'Votre navigateur ne sait pas gérer les témoins (cookies) ou refuse de les gérer.';
$s_logged_in_as = 'Connecté en tant que ';
$s_prefix_for_deleted_users = 'utilisateur';
$s_administrator = 'administrateur';
$s_myself = 'Moi-même';
$s_default_access_level = 'Niveau d’accès par défaut';
$s_issue_status_percentage = 'Pourcentage des statuts d’anomalie';
$s_access_levels_enum_string = '10:invité,25:rapporteur,40:testeur,55:développeur,70:gestionnaire,90:administrateur';
$s_no_access = 'inaccessible';
$s_project_status_enum_string = '10:développement,30:livré,50:stable,70:désuet';
$s_project_view_state_enum_string = '10:public,50:privé';
$s_view_state_enum_string = '10:public,50:privé';
$s_priority_enum_string = '10:aucune,20:basse,30:normale,40:élevée,50:urgente,60:immédiate';
$s_severity_enum_string = '10:fonctionnalité,20:simple,30:texte,40:cosmétique,50:mineur,60:majeur,70:critique,80:bloquant';
$s_reproducibility_enum_string = '10:toujours,30:quelques fois,50:aléatoire,70:non essayé,90:impossible à reproduire,100:sans objet';
$s_status_enum_string = '10:nouveau,20:retour  d’informations,30:accepté,40:confirmé,50:affecté,80:traité,90:fermé';
$s_resolution_enum_string = '10:ouvert,20:corrigé,30:rouvert,40:impossible à reproduire,50:impossible à corriger,60:doublon,70:pas une anomalie,80:suspendu,90:ne sera pas corrigé';
$s_projection_enum_string = '10:aucun,30:cosmétique,50:correction mineure,70:correction majeure,90:reconception';
$s_eta_enum_string = '10:aucun,20:< 1 jour,30:2–3 jours,40:< 1 semaine,50:< 1 mois,60:> 1 mois';
$s_sponsorship_enum_string = '0:non payé,1:demandé,2:payé';
$s_new_account_subject = 'Enregistrement de compte';
$s_new_account_greeting = 'Merci de vous être enregistré. Votre code d’utilisateur est « %1$s ». Pour compléter votre enregistrement, visiter l’hyperlien suivant (vérifiez qu’il est donné sur une seule ligne) et entrer votre propre mot de passe d’accès :';
$s_new_account_greeting_admincreated = 'L’utilisateur %1$s vous a créé un compte avec le nom d’utilisateur « %2$s ». Pour terminer la création, visitez l’URL suivante (soyez sûr qu’elle soit entrée sur une seule ligne) et définissez votre mot de passe :';
$s_new_account_username = 'Nom d’utilisateur :';
$s_new_account_message = 'Si vous n’avez fait aucune demande d’enregistrement, ignorez ce message et il n’arrivera rien.';
$s_new_account_do_not_reply = 'Ne pas répondre à ce message';
$s_new_account_email = 'Courriel :';
$s_new_account_IP = 'Adresse IP distante :';
$s_new_account_signup_msg = 'Le compte suivant a été créé :';
$s_reset_request_msg = 'Quelqu\'un (probablement vous) a demandé un changement de mot de passe avec vérification par courriel. Si ce n’était pas vous, ignorez ce message et il n’arrivera rien.

Si vous avez demandé cette vérification, visitez l’URL suivante pour changer votre mot de passe :';
$s_reset_request_admin_msg = 'Votre mot de passe a été réinitialisé. Veuillez aller sur l’URL suivante pour en définir un nouveau :';
$s_reset_request_in_progress_msg = 'Si vous avez fourni le nom d’utilisateur et l’adresse de courriel corrects pour votre compte, nous vous avons maintenant envoyé un message de confirmation à cette adresse de courriel. Une fois le message reçu, suivez les instructions fournies pour changer le mot de passe de votre compte.';
$s_email_notification_title_for_status_bug_new = 'L’anomalie suivante est maintenant au statut NOUVELLE (encore)';
$s_email_notification_title_for_status_bug_feedback = 'L’anomalie suivante nécessite un AVIS.';
$s_email_notification_title_for_status_bug_acknowledged = 'L’anomalie suivante a été ACCEPTÉE.';
$s_email_notification_title_for_status_bug_confirmed = 'L’anomalie suivante a été CONFIRMÉE.';
$s_email_notification_title_for_status_bug_assigned = 'L’anomalie suivante a été AFFECTÉE.';
$s_email_notification_title_for_status_bug_resolved = 'L’anomalie suivante a été RÉSOLUE.';
$s_email_notification_title_for_status_bug_closed = 'L’anomalie suivante a été FERMÉE.';
$s_email_notification_title_for_action_bug_submitted = 'L’anomalie suivante a été SOUMISE.';
$s_email_notification_title_for_action_bug_assigned = 'L’anomalie suivante a été AFFECTÉE.';
$s_email_notification_title_for_action_bug_unassigned = 'L’anomalie suivante a été DÉSASSIGNÉE.';
$s_email_notification_title_for_action_bug_reopened = 'L’anomalie suivante a été ROUVERTE.';
$s_email_notification_title_for_action_bug_deleted = 'L’anomalie suivante a été SUPPRIMÉE.';
$s_email_notification_title_for_action_bug_updated = 'L’anomalie suivante a été MISE À JOUR.';
$s_email_notification_title_for_action_sponsorship_added = 'L’anomalie suivante a été COMMANDITÉE.';
$s_email_notification_title_for_action_sponsorship_updated = 'Un commanditaire de l’anomalie suivante a été modifié.';
$s_email_notification_title_for_action_sponsorship_deleted = 'Un commanditaire de l’anomalie a été retiré.';
$s_email_notification_title_for_action_bugnote_submitted = 'Une NOTE a été ajoutée à cette anomalie.';
$s_email_notification_title_for_action_duplicate_of_relationship_added = 'L’anomalie suivante a été définie comme DOUBLON de l’anomalie %1$s.';
$s_email_notification_title_for_action_has_duplicate_relationship_added = 'L’anomalie %1$s a été définie comme DOUBLON de l’anomalie suivante.';
$s_email_notification_title_for_action_related_to_relationship_added = 'L’anomalie suivante a été LIÉE à l’anomalie %1$s.';
$s_email_notification_title_for_action_dependant_on_relationship_added = 'L’anomalie suivante a été définie comme PARENTE de l’anomalie %1$s.';
$s_email_notification_title_for_action_blocks_relationship_added = 'L’anomalie suivante a été définie comme ENFANT de l’anomalie %1$s.';
$s_email_notification_title_for_action_duplicate_of_relationship_deleted = 'L’anomalie suivante n’est plus définie comme DOUBLON de l’anomalie %1$s.';
$s_email_notification_title_for_action_has_duplicate_relationship_deleted = 'L’anomalie %1$s n’est plus définie comme DOUBLON de l’anomalie suivante.';
$s_email_notification_title_for_action_related_to_relationship_deleted = 'L’anomalie suivante n’est plus LIÉE à l’anomalie %1$s.';
$s_email_notification_title_for_action_dependant_on_relationship_deleted = 'L’anomalie suivante n’est plus définie comme PARENTE de l’anomalie %1$s.';
$s_email_notification_title_for_action_blocks_relationship_deleted = 'L’anomalie suivante n’est plus définie comme ENFANT de l’anomalie %1$s.';
$s_email_notification_title_for_action_relationship_child_resolved = 'L’anomalie LIÉE %1$s a été RÉSOLUE.';
$s_email_notification_title_for_action_relationship_child_closed = 'L’anomalie LIÉE %1$s a été FERMÉE.';
$s_email_notification_title_for_action_related_issue_deleted = 'L’anomalie LIÉE %1$s a été SUPPRIMÉE.';
$s_email_notification_title_for_action_monitor = 'L’anomalie %1$s est à présent suivi par l’utilisateur %2$s.';
$s_email_reporter = 'Rapportée par';
$s_email_handler = 'Affectée à';
$s_email_project = 'Projet';
$s_email_bug = 'Identifiant d’anomalie';
$s_email_category = 'Catégorie';
$s_email_reproducibility = 'Reproductibilité';
$s_email_severity = 'Sévérité';
$s_email_priority = 'Priorité';
$s_email_status = 'Statut';
$s_email_resolution = 'Résolution';
$s_email_duplicate = 'Doublon';
$s_email_fixed_in_version = 'Résolue dans la version';
$s_email_target_version = 'Version ciblée';
$s_email_date_submitted = 'Date de soumission';
$s_email_last_modified = 'Dernière modification';
$s_email_summary = 'Résumé';
$s_email_description = 'Description';
$s_email_additional_information = 'Informations complémentaires';
$s_email_steps_to_reproduce = 'Étapes pour reproduire';
$s_email_tag = 'Balises';
$s_email_due_date = 'Date d’échéance';
$s_account_protected_msg = 'Ce compte est protégé en écriture. Impossible de modifier sa configuration...';
$s_account_removed_msg = 'Votre compte a été supprimé...';
$s_confirm_delete_msg = 'Voulez-vous vraiment supprimer ce compte ?';
$s_delete_account_button = 'Supprimer le compte';
$s_manage_profiles_link = 'Profils';
$s_change_preferences_link = 'Préférences';
$s_edit_account_title = 'Modifier un compte';
$s_username = 'Nom d’utilisateur';
$s_username_label = 'Nom d’utilisateur';
$s_realname = 'Nom réel';
$s_realname_label = 'Nom réel';
$s_email = 'Courriel';
$s_email_label = 'Courriel';
$s_password = 'Mot de passe';
$s_new_password = 'Nouveau mot de passe';
$s_no_password_change = 'Le mot de passe est contrôlé par un autre système : il ne peut donc pas être modifié ici.';
$s_confirm_password = 'Confirmez le mot de passe';
$s_current_password = 'Mot de passe actuel';
$s_access_level = 'Niveau d’accès';
$s_access_level_label = 'Niveau d’accès';
$s_update_user_button = 'Mettre à jour l’utilisateur';
$s_verify_warning = 'L’information relative à votre compte a été vérifiée.';
$s_verify_change_password = 'Vous devez indiquer un mot de passe ici pour vous permettre de vous connecter à nouveau.';
$s_api_tokens_link = 'Jetons de l’API';
$s_api_token_create_form_title = 'Créer un jeton de l’API';
$s_api_token_create_button = 'Créer un jeton de l’API';
$s_api_token_name = 'Nom du jeton';
$s_api_token_disclose_message = 'Jeton à utiliser pour accéder à l’API.';
$s_api_token_displayed_once = 'Noter que ce jeton ne sera affiché qu’une fois.';
$s_api_tokens_title = 'Jetons de l’API';
$s_api_token_revoke_button = 'Révoquer';
$s_api_token_never_used = 'Jamais utilisé';
$s_api_token_revoked = 'Jeton de l’API « %s » révoqué.';
$s_last_used = 'Dernière utilisation';
$s_default_account_preferences_title = 'Préférences du compte';
$s_default_project = 'Projet par défaut';
$s_refresh_delay = 'Délai de rafraîchissement';
$s_minutes = 'minutes';
$s_redirect_delay = 'Délai de redirection';
$s_seconds = 'secondes';
$s_with_minimum_severity = 'Avec une sévérité minimale de';
$s_bugnote_order = 'Ordre de tri des activités';
$s_bugnote_order_asc = 'Croissant';
$s_bugnote_order_desc = 'Décroissant';
$s_email_on_new = 'Courriel en cas de nouvelle anomalie';
$s_email_on_assigned = 'Courriel en cas de changement d’assignation';
$s_email_on_feedback = 'Courriel de retour d’avis';
$s_email_on_resolved = 'Courriel en cas de résolution';
$s_email_on_closed = 'Courriel en cas de fermeture';
$s_email_on_reopened = 'Courriel en cas de réouverture';
$s_email_on_bugnote_added = 'Courriel en cas de nouvelle note';
$s_email_on_status_change = 'Courriel en cas de modification de statut';
$s_email_on_priority_change = 'Courriel en cas de changement de priorité';
$s_email_bugnote_limit = 'Nombre maximal de notes d’anomalie envoyées par courriel';
$s_email_full_issue_details = 'Envoyer les détails complets des anomalies par courriel';
$s_language = 'Langue';
$s_font_family = 'Famille de police';
$s_update_prefs_button = 'Mettre à jour les préférences';
$s_reset_prefs_button = 'Réinitialiser les préférences';
$s_timezone = 'Fuseau horaire';
$s_prefs_reset_msg = 'Les préférences ont été réinitialisées...';
$s_prefs_updated_msg = 'Les préférences ont été mises à jour...';
$s_select_profile = 'Sélectionner un profil';
$s_add_profile = 'Ajouter un profil';
$s_edit_profile = 'Modifier le profil';
$s_update_profile = 'Mettre à jour le profil';
$s_delete_profile = 'Supprimer le profil';
$s_delete_profile_confirm_msg = 'Voulez-vous réellement supprimer le profil « %1$s » ?';
$s_global_profile = 'Profil global';
$s_default_profile = 'Profil par défaut';
$s_profile_description = 'Description du système';
$s_my_sponsorship = 'Mes commandites';
$s_update_sponsorship_button = 'Modifier le statut de paiement';
$s_no_sponsored = 'Aucune anomalie commanditée affectée à vous-même n’a été trouvée.';
$s_own_sponsored = 'Anomalies que vous avez commanditées :';
$s_issues_handled = 'Anomalies commanditées qui vous ont été assignées :';
$s_no_own_sponsored = 'Vous n’avez commandité aucune anomalie.';
$s_sponsor = 'Commanditaire';
$s_sponsor_verb = 'Commanditer';
$s_amount = 'Montant';
$s_total_owing = 'Total dû';
$s_total_paid = 'Total payé';
$s_sponsor_hide = 'Masquer celles résolues et payées';
$s_sponsor_show = 'Tout afficher';
$s_payment_updated = 'Informations de paiement mises à jour.';
$s_account_updated_msg = 'Votre compte a été mis à jour avec succès...';
$s_email_updated = 'Adresse de courriel mise à jour avec succès';
$s_realname_updated = 'Nom réel mis à jour avec succès';
$s_password_updated = 'Mot de passe mis à jour avec succès';
$s_multiple_projects = 'Les anomalies sélectionnées proviennent de différents projets. Les paramètres ci-dessous s’appliquent à tous les projets. Si cela ne convient pas, sélectionnez un plus petit nombre d’anomalies.';
$s_new_bug_title = 'Nouvelle anomalie';
$s_feedback_bug_title = 'Demander des informations sur l’anomalie';
$s_acknowledged_bug_title = 'Accepter l’anomalie';
$s_confirmed_bug_title = 'Confirmer l’anomalie';
$s_assigned_bug_title = 'Affecter l’anomalie';
$s_new_bug_button = 'Nouvelle anomalie';
$s_feedback_bug_button = 'Demande d’avis';
$s_acknowledged_bug_button = 'Accepter l’anomalie';
$s_confirmed_bug_button = 'Confirmer l’anomalie';
$s_assigned_bug_button = 'Affecter l’anomalie';
$s_bug_close_msg = 'L’anomalie a été fermée...';
$s_close_immediately = 'Fermer immédiatement :';
$s_closed_bug_title = 'Fermer l’anomalie';
$s_bug_deleted_msg = 'L’anomalie a été supprimée...';
$s_delete_bug_sure_msg = 'Voulez-vous vraiment supprimer cette anomalie ?';
$s_monitor_bug_button = 'Surveiller';
$s_unmonitor_bug_button = 'Arrêter la surveillance';
$s_upload_file = 'Joindre un fichier';
$s_upload_files = 'Téléverser des fichiers';
$s_select_file = 'Sélectionner un fichier';
$s_select_files = 'Sélectionner des fichiers';
$s_upload_file_button = 'Téléverser le fichier';
$s_upload_files_button = 'Téléverser des fichiers';
$s_max_file_size_info = 'Taille maximale : %1$s %2$s';
$s_bug_reopened_msg = 'L’anomalie a été rouverte...';
$s_reopen_add_bugnote_title = 'Ajouter une note pour la réouverture de l’anomalie';
$s_bugnote_add_reopen_button = 'Ajouter une note et rouvrir l’anomalie';
$s_resolved_bug_title = 'Résoudre l’anomalie';
$s_resolved_bug_button = 'Résoudre l’anomalie';
$s_bug_resolved_msg = 'L’anomalie a été résolue. Entrez une note ci-dessous...';
$s_resolve_add_bugnote_title = 'Ajouter une note pour la résolution de l’anomalie';
$s_bugnote_add_resolve_button = 'Ajouter une note';
$s_from = 'De';
$s_to = 'À';
$s_sent_you_this_reminder_about = 'vous a envoyé ce rappel à propos de';
$s_bug_reminder = 'Envoyer un rappel';
$s_reminder_sent_to = 'Rappel envoyé à';
$s_reminder_sent_none = 'Aucun rappel n’a pu être envoyé';
$s_reminder_list_truncated = 'liste des destinataires tronquée';
$s_bug_send_button = 'Envoyer';
$s_reminder = 'Rappel';
$s_reminder_mentions = 'Vous pouvez maintenant faire mention des utilisateurs pour les notifier directement, au lieu d’utiliser une fonctionnalité de rappel. Par exemple, d’autres peuvent vous mentionner en écrivant %1s dans les questions et remarques et vous recevrez un courriel de notification.';
$s_reminder_explain = 'Cette note sera envoyée aux destinataires sélectionnés, en demandant leur avis sur cette anomalie.';
$s_reminder_monitor = 'Ces destinataires commenceront également à surveiller le problème ; ils pourront cesser la surveillance en utilisant le bouton « Arrêter la surveillance ».';
$s_reminder_store = 'Cette note sera enregistrée avec l’anomalie.';
$s_mentioned_you = 'vous a mentionné sur :';
$s_mentioned_in = 'Mentionné dans %1$s';
$s_confirm_sponsorship = 'Veuillez confirmer que vous souhaitez commanditer le bogue %1$d pour %2$s.';
$s_stick_bug_button = 'Coller';
$s_unstick_bug_button = 'Décoller';
$s_bug_updated_msg = 'Le bogue a été mis à jour...';
$s_back_to_bug_link = 'Retour à l’anomalie';
$s_update_simple_link = 'Mise à jour simple';
$s_updating_bug_advanced_title = 'Mise à jour des informations sur l’anomalie';
$s_id = 'Identifiant';
$s_category = 'Catégorie';
$s_severity = 'Sévérité';
$s_reproducibility = 'Reproductibilité';
$s_date_submitted = 'Date de soumission';
$s_last_update = 'Dernière mise à jour';
$s_reporter = 'Rapporteur';
$s_assigned_to = 'Affecté à';
$s_priority = 'Priorité';
$s_resolution = 'Résolution';
$s_status = 'État';
$s_duplicate_id = 'Identifiant en doublon';
$s_os = 'Système d’exploitation';
$s_platform = 'Plateforme';
$s_os_build = 'Version du système d’exploitation';
$s_projection = 'Projection';
$s_eta = 'Heure prévue d’arrivée';
$s_product_version = 'Version du produit';
$s_build = 'Construction';
$s_fixed_in_version = 'Résolue dans la version';
$s_target_version = 'Version ciblée';
$s_votes = 'Votes';
$s_summary = 'Résumé';
$s_synthesis = 'Synthèse';
$s_description = 'Description';
$s_steps_to_reproduce = 'Étapes pour reproduire';
$s_update_information_button = 'Mettre à jour les informations';
$s_sticky_issue = 'Anomalie épinglée';
$s_profile = 'Profil';
$s_updating_bug_simple_title = 'Mise à jour des informations de l’anomalie';
$s_view_revisions = 'Voir les révisions';
$s_view_num_revisions = 'Afficher %1$d révisions';
$s_revision = 'Révision';
$s_revision_by = 'le %1$s par %2$s';
$s_revision_drop = 'Jeter';
$s_bug_revision_dropped_history = 'Révision d’anomalie jetée';
$s_bugnote_revision_dropped_history = ' Note de révision jetée';
$s_all_revisions = 'Toutes les révisions';
$s_back_to_issue = 'Retour à l’anomalie';
$s_confirm_revision_drop = 'Êtes-vous certain de vouloir jeter cette révision de l’anomalie ?';
$s_activities_title = 'Activités';
$s_bugnote_attached_files = 'Fichiers attachés :';
$s_bugnote_deleted_msg = 'Note supprimée avec succès...';
$s_bug_notes_title = 'Notes';
$s_edit_bugnote_title = 'Modifier la note';
$s_no_bugnotes_msg = 'Cette anomalie n’a aucune note.';
$s_add_bugnote_title = 'Ajouter une note';
$s_add_bugnote_button = 'Ajouter une note';
$s_closed_bug_button = 'Fermer l’anomalie';
$s_bugnote_updated_msg = 'La note a été mise à jour avec succès...';
$s_last_edited = 'Dernière modification :';
$s_hide_content = 'Masquer le contenu';
$s_show_content = 'Afficher le contenu';
$s_file_icon_description = 'icône de fichier %1$s';
$s_unknown_file_extension = 'inconnu';
$s_webmaster_contact_information = 'Contactez <a href="mailto:%1$s" title="Contacter le webmaster par courriel.">l’administrateur</a> pour une assistance.';
$s_total_queries_executed = 'Requêtes exécutées au total : %1$d';
$s_unique_queries_executed = 'Requêtes uniques exécutées : %1$d';
$s_total_query_execution_time = 'Temps d’exécution total de la requête : %1$s secondes';
$s_page_execution_time = 'Temps d’exécution de la page : %1$s secondes';
$s_memory_usage = 'Utilisation de la mémoire : %1$s Kio';
$s_log_page_number = 'Nombre';
$s_log_page_time = 'Temps d’exécution';
$s_log_page_caller = 'Appelant';
$s_log_page_event = 'Événement';
$s_please_report = 'Veuillez rapporter ceci à l’%1$s.';
$s_warning_plain_password_authentication = '<strong>Attention :</strong> l’authentification par mot de passe en clair est utilisée, ceci exposera votre mot de passe aux administrateurs.';
$s_warning_default_administrator_account_present = '<strong>Attention :</strong> vous devriez désactiver le compte « administrator » par défaut ou changer son mot de passe.';
$s_warning_admin_directory_present = '<strong>Attention :</strong> le répertoire « admin » par défaut devrait être supprimé ou son accès devrait être restreint.';
$s_warning_change_setting = '<strong>Avertissement :</strong> « %1$s » n’est pas défini à sa valeur par défaut (%2$s).';
$s_warning_security_hazard = 'Ceci constitue un risque potentiel de sécurité puisque des informations sensibles peuvent être exposées.';
$s_warning_integrity_hazard = 'Ceci fera que MantisBT poursuivra quand même lorsque surviendront des erreurs et pourrait conduire à des problèmes d’intégrité des données ou du système.';
$s_warning_debug_email = '<strong>Avertissement :</strong> « <code>debug_email</code> » n’est pas défini à <code>OFF</code>, tous les courriels de notification seront envoyés à « %1$s ».';
$s_error_database_no_schema_version = '<strong>Erreur :</strong> le schéma de votre base de données est à la version 0. Cela peut se produire soit parce que la base de données est corrompue, soit parce que la valeur de « database_version » n’a pas pu être récupérée dans la table de configuration, ou encore parce que la structure de la base de données provient d’une version obsolète de MantisBT (d’avant la 1.0.0).';
$s_error_database_version_out_of_date_2 = '<strong>Attention :</strong> la structure de la base de données est sans doute dépassée. Veuillez la mettre à niveau depuis la <a href="admin/install.php">page d’installation</a> avant de vous identifier.';
$s_error_code_version_out_of_date = '<strong>Attention :</strong> la structure de la base de données est plus récente que le logiciel installé. Veuillez mettre à niveau le logiciel.';
$s_login_page_info = 'Bienvenue au système de suivi des anomalies.';
$s_login_title = 'Se connecter';
$s_save_login = 'Rester connecté';
$s_secure_session = 'Session sécurisée';
$s_secure_session_long = 'Restreindre l’utilisation de votre session à cette seule adresse IP.';
$s_choose_project = 'Choisir un projet';
$s_signup_link = 'S’inscrire avec un nouveau compte';
$s_lost_password_link = 'Vous avez oublié votre mot de passe ?';
$s_username_or_email = 'Nom d’utilisateur ou adresse de courriel';
$s_enter_password = 'Saisir le mot de passe pour « %s »';
$s_select_project_button = 'Choisir un projet';
$s_lost_password_title = 'Réinitialisation du mot de passe';
$s_lost_password_done_title = 'Message de mot de passe envoyé';
$s_lost_password_subject = 'Réinitialisation du mot de passe';
$s_lost_password_info = 'Pour réinitialiser votre mot de passe, merci d’indiquer le nom d’utilisateur et l’adresse de courriel du compte.<br /><br /> Si les données correspondent à un compte valide, une URL spéciale vous sera envoyé par courriel qui contient un code de validation pour votre compte. Veuillez alors suivre ce lien pour changer votre mot de passe.';
$s_lost_password_confirm_hash_OK = 'Votre confirmation a été acceptée. Merci de mettre à jour votre mot de passe.';
$s_open_and_assigned_to_me_label = 'Anomalies ouvertes qui me sont assignées :';
$s_open_and_reported_to_me_label = 'Anomalies ouvertes dont je suis le rapporteur :';
$s_newer_news_link = 'Actualités récentes';
$s_older_news_link = 'Actualités plus anciennes';
$s_archives = 'Archives';
$s_rss = 'RSS';
$s_site_information = 'Informations sur le site';
$s_mantis_version = 'Version de MantisBT';
$s_schema_version = 'Version du schéma de base de données';
$s_site_path = 'Chemin du site';
$s_core_path = 'Chemin du répertoire principal';
$s_plugin_path = 'Chemin des greffons';
$s_created_user_part1 = 'Utilisateur créé';
$s_created_user_part2 = 'avec un niveau d’accès de';
$s_create_new_account_title = 'Créer un nouveau compte';
$s_verify_password = 'Vérifier le mot de passe';
$s_enabled = 'Activé';
$s_enabled_label = 'Activé';
$s_protected = 'Protégé';
$s_protected_label = 'Protégé';
$s_create_user_button = 'Créer l’utilisateur';
$s_hide_disabled = 'Masquer les désactivés';
$s_filter_button = 'Appliquer le filtre';
$s_default_filter = 'Filtre par défaut';
$s_create_filter_link = 'Créer un lien permanent';
$s_create_short_link = 'Créer un lien raccourci';
$s_filter_permalink = 'Ci-dessous un lien permanent vers le filtre actuel :';
$s_manage_users_link = 'Utilisateurs';
$s_manage_projects_link = 'Projets';
$s_manage_custom_field_link = 'Champs personnalisés';
$s_manage_global_profiles_link = 'Profils globaux';
$s_manage_plugin_link = 'Greffons';
$s_permissions_summary_report = 'Rapport des permissions';
$s_manage_config_link = 'Configuration';
$s_manage_threshold_config = 'Seuils de flux de travail';
$s_manage_email_config = 'Notifications par courriel';
$s_manage_workflow_config = 'Transitions du flux de travail';
$s_manage_workflow_graph = 'Graphique du flux de travail';
$s_manage_tags_link = 'Balises';
$s_create_new_account_link = 'Créer un nouveau compte';
$s_projects_link = 'Projets';
$s_documentation_link = 'Documentation';
$s_new_accounts_title = 'Nouveaux comptes';
$s_1_week_title = '1 semaine';
$s_never_logged_in_title = 'Ne s’est jamais connecté';
$s_prune_accounts = 'Effacer des comptes';
$s_hide_inactive = 'Masquer les inactifs';
$s_show_disabled = 'Afficher les désactivés';
$s_manage_accounts_title = 'Gérer les comptes';
$s_p = 'p';
$s_date_created = 'Date de création';
$s_last_visit = 'Dernière visite';
$s_last_visit_label = 'Dernière visite :';
$s_edit_user_link = 'Modifier l’utilisateur';
$s_search_user_hint = 'Nom d’utilisateur, nom véritable ou adresse courriel';
$s_separate_list_items_by = '(séparez les éléments de la liste par « %1$s »)';
$s_config_all_projects = 'Note : ces réglages affectent par défaut tous les projets, à moins de réglages spécifiques au niveau de chaque projet.';
$s_config_project = 'Note : ces réglages affectent uniquement le projet %1$s.';
$s_colour_coding = 'Le code couleurs suivant s’applique au tableau ci-dessous :';
$s_colour_project = 'Les paramètres du projet sont prioritaires.';
$s_colour_global = 'Tous les paramètres spécifiques d’un projet s’appliquent à la place de ceux de la configuration par défaut.';
$s_issue_reporter = 'Utilisateur ayant rapporté l’anomalie';
$s_issue_handler = 'Utilisateur prenant en charge l’anomalie';
$s_users_added_bugnote = 'Utilisateurs ayant ajouté des notes d’anomalie.';
$s_category_assigned_to = 'Propriétaire de la catégorie';
$s_email_notify_users = 'Tout utilisateur avec un niveau d’accès';
$s_change_configuration = 'Mettre à jour la configuration';
$s_message = 'Message';
$s_default_notify = 'Définition des états de notification par défaut à';
$s_action_notify = 'Définition des états de notification d’action à';
$s_notify_defaults_change_access = 'Qui peut modifier les états par défaut de notification';
$s_notify_actions_change_access = 'Qui peut modifier les états par défaut de notification :';
$s_revert_to_system = 'Supprimer tous les paramètres spécifiques des projets';
$s_revert_to_all_project = 'Supprimer les paramètres spécifiques du projet';
$s_non_existent = 'inexistant';
$s_current_status = 'État actuel';
$s_next_status = 'État suivant';
$s_workflow = 'Flux de travail';
$s_workflow_thresholds = 'Seuils qui affectent le flux de travail';
$s_threshold = 'Seuil';
$s_status_level = 'État';
$s_alter_level = 'Qui peut changer cette valeur';
$s_validation = 'Validation du flux de travail';
$s_comment = 'Commentaire de validation';
$s_superfluous = 'La connexion d’un état vers lui-même est implicite, il n’est pas nécessaire de l’établir explicitement.';
$s_unreachable = 'Vous ne pouvez pas positionner une anomalie à cet état';
$s_no_exit = 'Vous ne pouvez pas positionner une anomalie hors de cet état';
$s_access_levels = 'Niveaux d’accès';
$s_access_change = 'Niveau d’accès minimal pour modifier cet état';
$s_desc_bug_submit_status = 'État à positionner pour un nouvelle anomalie';
$s_desc_bug_reopen_status = 'État des anomalies rouvertes';
$s_desc_bug_resolved_status_threshold = 'État dans lequel une anomalie est considérée comme résolue';
$s_desc_bug_closed_status_threshold = 'État dans lequel une anomalie est considérée comme close';
$s_workflow_change_access_label = 'Utilisateurs habilités à modifier le flux de travaux :';
$s_access_change_access_label = 'Utilisateurs habilités à changer les niveaux d’accès :';
$s_default_not_in_flow = 'L’état par défaut de %1$s n’est pas sélectionné dans les états suivants pour %2$s. Il sera ignoré.';
$s_allowed_access_levels = 'Autorisé pour tout utilisateur avec un niveau d’accès';
$s_assign_issue = 'Affecter une anomalie';
$s_allow_reporter_close = 'Permettre aux rapporteurs de clore une anomalie';
$s_allow_reporter_reopen = 'Permettre aux rapporteurs de rouvrir une anomalie';
$s_set_status_assigned = 'Changer l’état lors de l’assignation à quelqu’un';
$s_edit_others_bugnotes = 'Modifier les notes d’autres utilisateurs';
$s_edit_own_bugnotes = 'Modifier ses propres notes';
$s_delete_others_bugnotes = 'Supprimer des notes d’autres utilisateurs';
$s_delete_own_bugnotes = 'Supprimer ses propres notes';
$s_change_view_state_own_bugnotes = 'Modifier l’état de visibilité de ses propres notes';
$s_limit_access = 'Restreindre l’accès des rapporteurs aux anomalies qu’ils ont soumises';
$s_submit_status = 'État à positionner pour une nouvelle anomalie';
$s_assigned_status = 'État à positionner pour les anomalies auto-affectées';
$s_resolved_status = 'État quand une anomalie est considérée comme résolue';
$s_readonly_status = 'État dans lequel une anomalie ne peut plus être modifiée';
$s_reopen_status = 'État à positionner pour une anomalie rouverte';
$s_reopen_resolution = 'Résolution à positionner pour une anomalie rouverte';
$s_limit_view_unless_threshold_option = 'Afficher les problèmes des autres utilisateurs (si non positionné, l’accès sera limité aux problèmes signalés, gérés ou suivis par l’utilisateur)';
$s_export_issues = 'Exporter des anomalies';
$s_config_delete_sure = 'Voulez-vous vraiment supprimer la configuration pour %1$s dans le projet %2$s ?';
$s_delete_config_button = 'Supprimer la configuration';
$s_configuration_report = 'Rapport de configuration';
$s_database_configuration = 'Configuration de la base de données';
$s_configuration_option = 'Option de configuration';
$s_configuration_option_type = 'Type';
$s_configuration_option_value = 'Valeur';
$s_all_users = 'Tous les utilisateurs';
$s_set_configuration_option = 'Définir l’option de configuration';
$s_delete_config_sure_msg = 'Voulez-vous vraiment supprimer cette option de configuration ?';
$s_configuration_corrupted = 'La configuration dans la base de données est corrompue.';
$s_set_configuration_option_action_create = 'Créer une option de configuration';
$s_set_configuration_option_action_edit = 'Modifier une option de configuration';
$s_set_configuration_option_action_clone = 'Dupliquer une option de configuration';
$s_set_configuration_option_action_view = 'Afficher l’option de configuration';
$s_show_all_complex = 'Afficher la valeur pour toutes les options « %1$s »';
$s_plugin = 'Greffon';
$s_plugins_installed = 'Greffons installés';
$s_plugins_available = 'Greffons disponibles';
$s_plugins_missing = 'Greffons absents ou non valides';
$s_plugins_missing_description = 'Les greffons dans la liste ci-dessous sont enregistrés, mais leur code source n’a pas pu être trouvé dans « %s », ni chargé à partir de là.<br> Essayez de les réinstaller ou de les supprimer.';
$s_plugin_description = 'Description';
$s_plugin_problem_description = 'Description du problème';
$s_plugin_author = 'Auteur : %1$s';
$s_plugin_url = 'Site web :';
$s_plugin_depends = 'Dépendances';
$s_plugin_no_depends = 'Aucune dépendance';
$s_plugin_priority = 'Priorité';
$s_plugin_protected = 'Protégé';
$s_plugin_actions = 'Actions';
$s_plugin_install = 'Installer';
$s_plugin_upgrade = 'Mettre à niveau';
$s_plugin_uninstall = 'Désinstaller';
$s_plugin_manual_fix = 'Intervention manuelle nécessaire';
$s_plugin_uninstall_message = 'Voulez-vous vraiment désinstaller le greffon « %1$s » ?';
$s_plugin_remove_message = 'Êtes-vous sûr de vouloir supprimer le greffon « %1$s » non valide ou manquant ?';
$s_plugin_key_label = 'Clé :';
$s_plugin_key_met = 'greffon prêt';
$s_plugin_key_unmet = 'dépendances non satisfaites';
$s_plugin_key_dated = 'dépendances périmées';
$s_plugin_key_upgrade = 'mise à niveau requise';
$s_plugin_invalid_description = 'Greffon non valide';
$s_plugin_invalid_status_message = 'Les propriétés obligatoires suivantes ne sont pas définies dans la classe du greffon : %s.';
$s_plugin_missing_description = 'Greffon manquant';
$s_plugin_missing_status_message = 'Le greffon est installé dans la base de données de Mantis, mais n’a pas pu être chargé.';
$s_plugin_missing_class_description = 'Classe de base du greffon absente';
$s_plugin_missing_class_status_message = 'Un répertoire pour ce greffon a été trouvé, mais il n’y avait pas de code correspondant au greffon dedans. Assurez-vous que le nom du répertoire correspond au nom de base du greffon (sensible à la casse).';
$s_project_added_msg = 'Nouveau projet installé avec succès...';
$s_category_added_msg = 'Nouvelle catégorie créée avec succès...';
$s_category_deleted_msg = 'Catégorie effacée avec succès...';
$s_category_delete_confirm_msg = 'Êtes-vous sûr de vouloir supprimer la catégorie « %1$s » ?';
$s_delete_category_button = 'Supprimer la catégorie';
$s_edit_project_category_title = 'Modifier la catégorie du projet';
$s_update_category_button = 'Mettre à jour la catégorie';
$s_category_updated_msg = 'La catégorie a été modifiée avec succès...';
$s_create_first_project = 'Créer un projet afin d’inventorier les anomalies.';
$s_add_subproject_title = 'Ajouter un sous-projet';
$s_project_deleted_msg = 'Le projet a été supprimé avec succès...';
$s_project_delete_msg = 'Voulez-vous vraiment supprimer le projet « %1$s » ?<br>Cela supprimera aussi les %2$s rapports d’anomalies associés.';
$s_project_delete_button = 'Supprimer le projet';
$s_edit_project_title = 'Modifier le  projet';
$s_project_name = 'Nom du projet';
$s_project_name_label = 'Nom du projet :';
$s_view_status = 'Visibilité';
$s_public = 'public';
$s_private = 'privé';
$s_update_project_button = 'Mettre à jour le projet';
$s_delete_project_button = 'Supprimer le projet';
$s_copy_from = 'Copier depuis';
$s_copy_to = 'Copier vers';
$s_categories_and_version_title = 'Catégories et versions';
$s_categories = 'Catégories';
$s_add_category_button = 'Ajouter la catégorie';
$s_add_and_edit_category_button = 'Ajouter et modifier une catégorie';
$s_versions = 'Versions';
$s_add_version_button = 'Ajouter une version';
$s_add_and_edit_version_button = 'Ajouter et modifier une version';
$s_actions = 'Actions';
$s_version = 'Version';
$s_version_label = 'Version :';
$s_timestamp = 'Horodatage';
$s_subprojects = 'Sous-projets';
$s_add_subproject = 'Ajouter en tant que sous-projet';
$s_create_new_subproject_link = 'Créer un nouveau sous-projet';
$s_unlink_link = 'Dissocier';
$s_show_global_users = 'Afficher les utilisateurs avec accès global';
$s_hide_global_users = 'Masquer les utilisateurs avec accès global';
$s_review_changes = 'Relire les modifications';
$s_review_changes_confirmation = 'Voulez-vous vraiment appliquer ces modifications ?';
$s_review_changes_empty = 'Aucune modification sélectionnée';
$s_add_project_title = 'Ajouter un projet';
$s_upload_file_path = 'Chemin de téléversement de fichier';
$s_add_project_button = 'Ajouter le projet';
$s_projects_title = 'Projets';
$s_projects_title_label = 'Projets';
$s_name = 'Nom';
$s_project_updated_msg = 'Le projet a été mis à jour avec succès...';
$s_version_added_msg = 'La version a été enregistrée avec succès...';
$s_version_deleted_msg = 'La version a été supprimée avec succès...';
$s_version_delete_sure = 'Voulez-vous vraiment supprimer la version « %1$s » ?';
$s_delete_version_button = 'Supprimer la version';
$s_edit_project_version_title = 'Modifier la version du projet';
$s_update_version_button = 'Mettre à jour la version';
$s_released = 'Publiée';
$s_not_released = 'Pas encore publiée';
$s_scheduled_release = 'Publication planifiée';
$s_obsolete = 'Périmée';
$s_version_updated_msg = 'La version a été mise à jour avec succès...';
$s_account_delete_protected_msg = 'Ce compte est protégé. Vous ne pouvez pas le supprimer.';
$s_account_deleted_msg = 'Compte supprimé...';
$s_delete_account_sure_msg = 'Voulez-vous vraiment supprimer le compte « %1$s » ?';
$s_notify_user = 'Notifier l’utilisateur de cette modification';
$s_accounts_pruned_msg = 'Tous les comptes qui datent de plus d’une semaine et qui ne se sont jamais connectés ont été effacés.';
$s_prune_accounts_button = 'Supprimer';
$s_confirm_account_pruning = 'Êtes-vous sûr de vouloir supprimer les comptes qui ne se sont jamais connectés ?';
$s_edit_user_title = 'Modifier un utilisateur';
$s_account_unlock_button = 'Déverrouiller le compte';
$s_reset_password_button = 'Effacer le mot de passe';
$s_delete_user_button = 'Supprimer l’utilisateur';
$s_impersonate_user_button = 'Se faire passer pour un utilisateur';
$s_reset_password_msg = 'La réinitialisation du mot de passe envoie un hyperlien de confirmation par courriel.';
$s_reset_password_msg2 = 'Réinitialiser le mot de passe le laisse à blanc : l’utilisateur devra utiliser l’hyperlien fourni par courriel pour définir un nouveau mot de passe avant de pouvoir se connecter.';
$s_show_all_users = 'Tous';
$s_users_unused = 'Inutilisé';
$s_users_new = 'Nouveau';
$s_email_not_unique = 'L’adresse de messagerie est associée avec au moins un autre compte utilisateur';
$s_account_reset_msg = 'Un message de confirmation a été envoyé à l’adresse courriel de l’utilisateur sélectionné. Grace à ceci, l’utilisateur sera en mesure de modifier son mot de passe.';
$s_account_reset_msg2 = 'Mot de passe du compte a été mis à blanc...';
$s_account_unlock_msg = 'Le compte a été déverrouillé.';
$s_manage_user_updated_msg = 'Le compte a été mis à jour...';
$s_email_user_updated_subject = 'Compte mis à jour';
$s_email_user_updated_msg = 'Votre compte a été mis à jour par un administrateur. Une liste des modifications est affichée ci-dessous. Vous pouvez mettre à jour les détails et préférences de votre compte en visitant l’URL suivante :';
$s_main_link = 'Accueil';
$s_view_bugs_link = 'Afficher les anomalies';
$s_report_bug_link = 'Rapporter une anomalie';
$s_changelog_link = 'Historique des changements';
$s_roadmap_link = 'Feuille de route';
$s_summary_link = 'Résumé';
$s_account_link = 'Mon compte';
$s_users_link = 'Utilisateurs';
$s_manage_link = 'Gérer';
$s_edit_news_link = 'Modifier les actualités';
$s_docs_link = 'Documentation';
$s_logout_link = 'Se déconnecter';
$s_my_view_link = 'Mon affichage';
$s_invite_users = 'Inviter des utilisateurs';
$s_my_view_title_unassigned = 'Non affectée';
$s_my_view_title_recent_mod = 'Modifiée récemment';
$s_my_view_title_reported = 'Rapportée par moi';
$s_my_view_title_assigned = 'Affectée à moi (non résolue)';
$s_my_view_title_resolved = 'Résolue';
$s_my_view_title_monitored = 'Surveillée par moi';
$s_my_view_title_feedback = 'En attente de mon avis';
$s_my_view_title_verify = 'En attente de ma confirmation de résolution';
$s_my_view_title_my_comments = 'Anomalies que j’ai commentées';
$s_news_added_msg = 'Actualité ajoutée...';
$s_news_deleted_msg = 'Actualité supprimée...';
$s_delete_news_sure_msg = 'Voulez-vous vraiment supprimer cette actualité ?';
$s_delete_news_item_button = 'Supprimer l’actualité';
$s_edit_news_title = 'Modifier les actualités';
$s_headline = 'Titre';
$s_body = 'Corps';
$s_update_news_button = 'Mettre à jour les actualités';
$s_add_news_title = 'Ajouter une actualité';
$s_post_to = 'Publier dans';
$s_post_news_button = 'Publier les actualités';
$s_edit_or_delete_news_title = 'Modifier ou supprimer des actualités';
$s_edit_post = 'Modifier l’actualité';
$s_delete_post = 'Supprimer l’actualité';
$s_select_post = 'Sélectionner l’actualité';
$s_news_updated_msg = 'Actualités mises à jour...';
$s_back_link = 'Arrière';
$s_file_uploaded_msg = 'Le fichier a bien été envoyé.';
$s_upload_file_title = 'Téléverser un fichier';
$s_title = 'Titre';
$s_project_file_deleted_msg = 'Fichier projet supprimé.';
$s_confirm_file_delete_msg = 'Voulez-vous vraiment supprimer le fichier « %1$s » ?';
$s_filename = 'Nom du fichier';
$s_filename_label = 'Nom du fichier :';
$s_file_update_button = 'Modifier le fichier';
$s_file_delete_button = 'Supprimer le fichier';
$s_project_documentation_title = 'Documentation du projet';
$s_user_documentation = 'Documentation pour l’utilisateur';
$s_project_documentation = 'Documentation du projet';
$s_add_file = 'Ajouter un fichier';
$s_project_document_updated = 'Le projet a été modifié.';
$s_project_user_added_msg = 'L’utilisateur a bien été ajouté au projet.';
$s_project_removed_user_msg = 'L’utilisateur a bien été retiré du projet.';
$s_remove_user_sure_msg = 'Souhaitez-vous vraiment retirer le compte « %1$s » utilisateur des projets « %2$s » ?';
$s_remove_user_button = 'Retirer un utilisateur';
$s_remove_all_users_sure_msg = 'Souhaitez-vous vraiment retirer tous les utilisateurs du projet ?';
$s_remove_all_users_button = 'Retirer tous les utilisateurs';
$s_add_user_title = 'Ajouter un utilisateur au projet';
$s_add_user_button = 'Ajouter un utilisateur';
$s_project_selection_title = 'Sélection du projet';
$s_remove_link = 'Retirer';
$s_remove_all_link = 'Tout retirer';
$s_remove_project_user_title = 'Retirer l’accès de l’utilisateur au projet';
$s_modify_project_user_title = 'Modifier l’accès de l’utilisateur au projet';
$s_updated_user_msg = 'Utilisateur modifié avec succès.';
$s_must_enter_category = 'Vous devez choisir une catégorie.';
$s_must_enter_severity = 'Vous devez sélectionner une sévérité.';
$s_must_enter_reproducibility = 'Vous devez sélectionner une reproductibilité.';
$s_must_enter_summary = 'Vous devez saisir un résumé.';
$s_must_enter_description = 'Vous devez saisir une description.';
$s_report_more_bugs = 'Rapporter d’autres anomalies';
$s_submission_thanks_msg = 'Merci de votre contribution.';
$s_simple_report_link = 'Rapport simplifié';
$s_enter_report_details_title = 'Saisissez les détails de l’anomalie';
$s_required = 'obligatoire';
$s_select_category = 'Sélectionnez une catégorie';
$s_select_reproducibility = 'Sélectionnez la reproductibilité';
$s_select_severity = 'Sélectionnez la sévérité';
$s_or_fill_in = 'Ou remplir';
$s_assign_to = 'Affectée à';
$s_additional_information = 'Informations complémentaires';
$s_submit_report_button = 'Soumettre l’anomalie';
$s_check_report_more_bugs = 'Cocher pour rapporter d’autres anomalies';
$s_report_stay = 'Rapport gardé en attente';
$s_selected_project = 'Projet sélectionné';
$s_valid_project_msg = 'Vous devez choisir un projet valide.';
$s_signup_done_title = 'Enregistrement du compte complété.';
$s_password_emailed_msg = 'Félicitations. Vous vous êtes inscrit avec succès. Un courriel de confirmation vient de vous être envoyé pour vérifier votre adresse courriel. Votre compte sera activé lorsque vous visiterez le lien fourni dans ce courriel.';
$s_no_reponse_msg = 'Vous avez sept jours pour terminer le processus de confirmation du compte ; si vous ne parvenez pas à le faire durant cette période, ce compte nouvellement créé pourra être purgé automatiquement.';
$s_signup_captcha_request_label = 'Entrez le code comme indiqué dans la case à côté :';
$s_signup_captcha_play = 'Lire l’audio du code Captcha';
$s_signup_captcha_refresh = 'Générer un nouveau code';
$s_signup_info = 'Pour compléter ce formulaire et vérifier vos réponses, un message de confirmation vous sera envoyé à l’adresse de courriel spécifiée.<br /> Vous pourrez activer votre compte en visitant le lien contenu dans ce courriel. Si vous ne parvenez pas à le faire dans les sept jours suivants, le compte pourra être purgé.<br /> Vous devez spécifier une adresse valide pour recevoir le courriel de confirmation du compte.';
$s_signup_title = 'S’inscrire';
$s_signup_button = 'S’inscrire';
$s_no_password_request = 'Votre mot de passe est géré par un autre système. Veuillez contacter votre administrateur du système.';
$s_edit_site_settings_title = 'Modifier la configuration du site';
$s_save_settings_button = 'Enregistrer la configuration';
$s_site_settings_title = 'Configuration du site';
$s_system_info_link = 'Informations du système';
$s_site_settings_link = 'Modifier la configuration du site';
$s_site_settings_updated_msg = 'La configuration du site a bien été mise à jour';
$s_summary_title = 'Résumé';
$s_summary_advanced_link = 'Synthèse avancée';
$s_by_project = 'Par projet';
$s_by_status = 'Par état';
$s_by_date = 'Par date (en jours)';
$s_by_severity = 'Par sévérité';
$s_by_resolution = 'Par résolution';
$s_by_category = 'Par catégorie';
$s_by_priority = 'Par priorité';
$s_by_developer = 'Par développeur';
$s_by_reporter = 'Par rapporteur';
$s_reporter_by_resolution = 'Rapporteurs par résolution';
$s_reporter_effectiveness = 'Efficacité du rapporteur';
$s_developer_by_resolution = 'Développeurs par résolution';
$s_percentage_fixed = '% corrigé(s)';
$s_percentage_errors = '% faux';
$s_errors = 'Faux';
$s_opened = 'Ouvert';
$s_resolved = 'Résolue';
$s_total = 'Total';
$s_balance = 'Solde';
$s_most_active = 'Les plus actifs';
$s_score = 'Pointage';
$s_days = 'Jours';
$s_time_stats = 'Statistiques temporelles de résolution des anomalies (en jours)';
$s_longest_open_bug = 'Anomalie la plus longtemps ouverte';
$s_longest_open = 'La plus longtemps ouverte';
$s_average_time = 'Temps moyen';
$s_total_time = 'Temps total';
$s_developer_stats = 'Statistiques par développeur';
$s_reporter_stats = 'Statistiques par rapporteur';
$s_orct = '(ouvertes/résolues/closes/total)';
$s_summary_header = 'ouvertes/résolues/closes/total/résolues taux/taux';
$s_summary_notice_filter_is_applied = 'Un filtre a été appliqué';
$s_any = 'tous';
$s_all = 'tous';
$s_show = 'Afficher';
$s_changed = 'Mise en évidence des modifiés (heures)';
$s_viewing_bugs_title = 'Liste des anomalies';
$s_updated = 'Mis à jour';
$s_sticky = 'Afficher les anomalies épinglées';
$s_sort = 'Trier par';
$s_issue_id = 'Anomalie nº';
$s_recently_visited = 'Visité récemment';
$s_priority_abbreviation = 'P';
$s_note_user_id = 'Note par';
$s_filter_match_type = 'Type de correspondance';
$s_filter_match_all = 'Toutes les conditions';
$s_filter_match_any = 'N’importe quelle condition';
$s_none = 'néant';
$s_current = 'actuel';
$s_search = 'Rechercher';
$s_view_prev_link = 'Voir les précédentes';
$s_view_next_link = 'Voir les suivantes';
$s_prev = 'Précédentes';
$s_next = 'Suivantes';
$s_first = 'Premières';
$s_last = 'Dernières';
$s_start_date_label = 'Date de début :';
$s_end_date_label = 'Date de fin :';
$s_use_date_filters = 'Filtrer par date de soumission';
$s_use_last_updated_date_filters = 'Filtrer par date de dernière modification';
$s_yes = 'Oui';
$s_no = 'Non';
$s_open_filters = 'Changer de filtre';
$s_or_unassigned = 'Ou non affecté';
$s_ok = 'Valider';
$s_select_all = 'Sélectionner tous';
$s_use_query = 'Utiliser le filtre';
$s_delete_query = 'Supprimer le filtre';
$s_query_deleted = 'Filtre « %s » supprimé';
$s_save_query = 'Enregistrer le filtre utilisé';
$s_reset_query = 'Réinitialiser le filtre';
$s_query_name = 'Nom du filtre';
$s_query_name_label = 'Nom du filtre :';
$s_query_exists = 'Ce filtre particulier semble déjà exister.';
$s_query_dupe_name = 'Un autre filtre porte déjà ce nom. Merci de choisir un autre nom pour ce filtre.';
$s_query_blank_name = 'Vous ne pouvez pas enregistrer un filtre sans nom. Merci de nommer ce filtre avant de l’enregistrer.';
$s_query_name_too_long = 'Vous ne pouvez pas enregistrer un filtre avec un nom de plus de 64 caractères. Veuillez choisir un nom plus court.';
$s_query_store_error = 'Une erreur s’est produite durant l’enregistrement de ce filtre.';
$s_open_queries = 'Gérer les filtres';
$s_query_delete_msg = 'Voulez-vous vraiment supprimer le filtre « %1$s » ?';
$s_edit_filter = 'Modifier le filtre';
$s_owner = 'Propriétaire';
$s_update_filter = 'Mettre à jour le filtre';
$s_current_project = 'Projet actuel';
$s_stored_project = 'Projet enregistré';
$s_available_filter_for_project = 'Filtres disponibles pour le projet';
$s_manage_filter_page_title = 'Gérer les filtres';
$s_manage_filter_edit_page_title = 'Modifier le filtre';
$s_apply_filter_button = 'Appliquer';
$s_temporary_filter = 'Filtre temporaire';
$s_set_as_persistent_filter = 'Définir comme filtre persistant';
$s_view_simple_link = 'Vue simplifiée';
$s_product_build = 'Construction du produit';
$s_bug_assign_to_button = 'Affecter à :';
$s_bug_status_to_button = 'Changer l’état en :';
$s_reopen_bug_button = 'Rouvrir';
$s_attached_files = 'Pièces jointes';
$s_publish = 'Publier';
$s_browser_does_not_support_audio = 'Votre navigateur ne prend pas en charge la balise « audio ».';
$s_browser_does_not_support_video = 'Votre navigateur ne prend pas en charge la balise « video ».';
$s_bug_view_title = 'Voir les détails de l’anomalie';
$s_no_users_monitoring_bug = 'Aucun utilisateur ne surveille cette anomalie.';
$s_users_monitoring_bug = 'Utilisateurs surveillant cette anomalie';
$s_monitoring_user_list = 'Liste d’utilisateurs';
$s_no_users_sponsoring_bug = 'Aucun utilisateur ne commandite cette anomalie.';
$s_users_sponsoring_bug = 'Utilisateurs commanditant cette anomalie';
$s_sponsors_list = 'Liste des commanditaires';
$s_total_sponsorship_amount = 'Commandite totale = %1$s';
$s_add_custom_field_button = 'Ajouter un champ personnalisé';
$s_delete_custom_field_button = 'Supprimer le champ personnalisé';
$s_delete_custom_field_everywhere = 'Supprimer les champs personnalisés partout';
$s_update_custom_field_button = 'Mettre à jour le champ personnalisé';
$s_add_existing_custom_field = 'Ajouter ce champ personnalisé existant';
$s_edit_custom_field_title = 'Modifier un champ personnalisé';
$s_custom_field = 'Champ';
$s_custom_field_label = 'Champ :';
$s_custom_fields_setup = 'Champs personnalisés';
$s_custom_field_name = 'Nom';
$s_custom_field_project_count = 'Nombre de projets';
$s_custom_field_type = 'Type';
$s_custom_field_possible_values = 'Valeurs possibles';
$s_custom_field_default_value = 'Valeur par défaut';
$s_custom_field_valid_regexp = 'Expression rationnelle';
$s_custom_field_access_level_r = 'Accès en lecture';
$s_custom_field_access_level_rw = 'Accès en écriture';
$s_custom_field_length_min = 'Taille min.';
$s_custom_field_length_max = 'Taille max.';
$s_custom_field_filter_by = 'Ajouter au filtre';
$s_custom_field_display_report = 'Afficher lors du rapport d’anomalies';
$s_custom_field_display_update = 'Afficher lors de la mise à jour d’anomalies';
$s_custom_field_display_resolved = 'Montrer lors de la résolution d’anomalies';
$s_custom_field_display_closed = 'Montrer lors de la fermeture d’anomalies';
$s_custom_field_require_report = 'Nécessaire au rapport';
$s_custom_field_require_update = 'Nécessaire à la mise à jour';
$s_custom_field_require_resolved = 'Nécessaire à la résolution';
$s_custom_field_require_closed = 'Nécessaire à la clôture';
$s_link_custom_field_to_project_title = 'Lier un champ personnalisé aux projets';
$s_link_custom_field_to_project_button = 'Lier le champ personnalisé';
$s_linked_projects_label = 'Projets liés';
$s_custom_field_sequence = 'Séquence';
$s_custom_field_sequence_label = 'Séquence';
$s_custom_field_type_enum_string = '0:Chaîne de caractères,1:Nombre entier,2:Nombre réel,3:Énumération,4:Courriel,5:Case à cocher,6:Liste,7:Liste à sélection multiple,8:Date,9:Bouton radio, 10:Zone de texte';
$s_confirm_used_custom_field_deletion = 'Le champ « %1$s » est actuellement lié à au moins un projet.  Si vous continuez, toutes les valeurs de ce champ seront supprimées.  Cette action ne peut être annulée.  Si vous ne voulez pas supprimer ce champ, cliquez sur le bouton Retour de votre navigateur.  Sinon pour supprimer ce champ, cliquez sur le bouton ci dessous.';
$s_confirm_custom_field_deletion = 'Voulez-vous vraiment supprimer le champ personnalisé « %1$s » et toutes les valeurs associées ?';
$s_field_delete_button = 'Supprimer le champ';
$s_confirm_custom_field_unlinking = 'Voulez-vous vraiment dissocier le champ personnalisé « %1$s » du projet ? Les valeurs ne seront pas supprimées tant que le champ personnalisé ne sera pas lui-même supprimé.';
$s_field_remove_button = 'Retirer le champ';
$s_hide_status = 'Masquer l’état';
$s_filter_closed = 'Close';
$s_filter_resolved = 'Résolue';
$s_hide_closed = 'Masquer les closes';
$s_hide_resolved = 'Cacher les résolues';
$s_and_above = 'Et supérieur';
$s_advanced_filters = 'Filtres avancés';
$s_simple_filters = 'Filtres simples';
$s_monitored_by = 'Surveillé par';
$s_attachments = 'pièce(s) jointe(s)';
$s_bytes = 'octets';
$s_kib = 'Kio';
$s_attachment_missing = 'Pièce jointe manquante';
$s_attachment_count = 'Nombre de pièces jointes';
$s_view_attachments_for_issue = 'Voir %1$d pièce(s) jointe(s) pour l’anomalie nº %2$d';
$s_warning_update_custom_field_type = '<strong>Avertissement :</strong> le champ personnalisé « %1$s » a déjà des valeurs stockées. Changer le type de champ peut provoquer des erreurs et un comportement inattendu si les valeurs existantes ne sont pas cohérentes avec le nouveau type du champ. Pour continuer, cliquer sur le bouton ci-dessous.';
$s_phpmailer_language = 'fr';
$s_sponsors = '%1$d commanditaire(s)';
$s_sponsorship_added = 'Commandite ajoutée';
$s_sponsorship_updated = 'Commandite mise à jour';
$s_sponsorship_deleted = 'Commandite supprimée';
$s_sponsorship_paid = 'Commandite payée';
$s_sponsorship_more_info = 'Davantage d’informations sur la commandite';
$s_sponsorship_total = 'Total de commandite';
$s_changelog = 'Liste des changements';
$s_changelog_empty = 'Liste des changements indisponible';
$s_changelog_empty_manager = 'Aucune information du journal de modifications disponible. Les anomalies sont incluses une fois que les projets ont des versions et qu’elles sont résolues et définies à l’état « corrigé dans la version ».';
$s_roadmap = 'Feuille de route';
$s_resolved_progress = '%1$d anomalie(s) résolue(s) sur %2$d';
$s_roadmap_empty = 'Aucune information de feuille de route disponible';
$s_roadmap_empty_manager = 'Aucune information de feuille de route disponible. Les anomalies sont incluses une fois que les projets ont des versions et que les anomalies ont une « version cible » indiquée.';
$s_http_auth_realm = 'Accès à MantisBT';
$s_bug = 'anomalie';
$s_bugs = 'anomalies';
$s_add_new_relationship = 'Nouvelle relation';
$s_this_bug = 'Anomalie actuelle';
$s_relationship_added = 'Relation ajoutée';
$s_relationship_deleted = 'Relation supprimée';
$s_no_relationship = 'aucune relation';
$s_relationship_replaced = 'Relation remplacée';
$s_replace_relationship_button = 'Remplacer';
$s_relationship_with_parent = 'Relation avec l’anomalie parente';
$s_delete_relationship_sure_msg = 'Êtes-vous sûr{{GENDER:||e}} de vouloir supprimer cette relation ?';
$s_relationship_warning_blocking_bugs_not_resolved = 'Tous les enfants de cette anomalie ne sont pas encore résolus ou fermés.';
$s_relationship_warning_blocking_bugs_not_resolved_2 = '<b>Attention :</b> tous les anomalies enfants de cette anomalie ne sont pas encore résolues ou fermées.<br /> Avant de <b>résoudre ou fermer</b> une anomalie parente, toutes ses anomalies enfants devraient être résolues ou fermées.';
$s_create_child_bug_button = 'Cloner';
$s_bug_cloned_to = 'Anomalie clonée';
$s_bug_created_from = 'Anomalie générée depuis';
$s_copy_from_parent = 'Copier les données étendues de l’anomalie parente';
$s_copy_notes_from_parent = 'Copier les notes d’anomalie';
$s_copy_attachments_from_parent = 'Copier les pièces jointes';
$s_with = 'avec';
$s_relation_graph = 'Graphique des relations';
$s_dependency_graph = 'Graphique des dépendances';
$s_vertical = 'Vertical';
$s_horizontal = 'Horizontal';
$s_view_issue = 'Afficher l’anomalie';
$s_show_summary = 'Afficher le résumé';
$s_hide_summary = 'Masquer le résumé';
$s_perm_rpt_capability = 'Capacité';
$s_view = 'Voir';
$s_issues = 'Anomalies';
$s_report_issue = 'Rapporter une anomalie';
$s_update_issue = 'Mettre à jour une anomalie';
$s_monitor_issue = 'Surveiller une anomalie';
$s_handle_issue = 'Se charger d’une anomalie';
$s_move_issue = 'Déplacer une anomalie';
$s_delete_issue = 'Supprimer une anomalie';
$s_reopen_issue = 'Rouvrir une anomalie';
$s_view_private_issues = 'Voir les anomalies privées';
$s_update_readonly_issues = 'Mettre à jour les anomalies en lecture seule';
$s_update_issue_status = 'Mettre à jour le statut d’une anomalie';
$s_set_view_status = 'Définir l’état d’affichage lors du rapport d’une nouvelle anomalie ou d’une note';
$s_update_view_status = 'Mettre à jour la visibilité d’une anomalie ou d’une note existante.';
$s_view_issue_revisions = 'Voir les versions d’un problème ou d’une note.';
$s_drop_issue_revisions = 'Retirer des versions d’un problème ou d’une note.';
$s_set_sticky = 'Épingler / Désépingler un problème';
$s_show_list_of_users_monitoring_issue = 'Afficher la liste des utilisateurs surveillant une anomalie';
$s_add_users_monitoring_issue = 'Ajouter des surveillants à une anomalie';
$s_remove_users_monitoring_issue = 'Supprimer des surveillants d’une anomalie';
$s_notes = 'Notes';
$s_add_notes = 'Ajouter des notes';
$s_view_private_notes = 'Voir les notes privées des autres utilisateurs';
$s_news = 'Actualités';
$s_view_private_news = 'Afficher les actualités privées';
$s_manage_news = 'Gérer les actualités';
$s_view_list_of_attachments = 'Afficher la liste des pièces jointes';
$s_download_attachments = 'Télécharger les pièces jointes';
$s_delete_attachments = 'Supprimer les pièces jointes';
$s_delete_attachment_sure_msg = 'Êtes-vous sûr{{GENDER:||e}} de vouloir supprimer cette pièce jointe ?';
$s_upload_issue_attachments = 'Mettre à jour les pièces jointes d’une anomalie';
$s_filters = 'Filtres';
$s_save_filters = 'Enregistrer les filtres';
$s_save_filters_as_shared = 'Enregistrer et partager les filtres';
$s_use_saved_filters = 'Utiliser les filtres enregistrés';
$s_create_project = 'Créer un projet';
$s_delete_project = 'Supprimer un projet';
$s_manage_project = 'Gérer les projets';
$s_manage_user_access_to_project = 'Gérer les droits d’accès utilisateur à un projet';
$s_automatically_included_in_private_projects = 'Automatiquement inclus dans les projets privés';
$s_project_documents = 'Documents du projet';
$s_view_project_documents = 'Afficher les documents du projet';
$s_upload_project_documents = 'Téléverser des documents du projet';
$s_link_custom_fields_to_projects = 'Lier des champs personnalisés aux projets';
$s_sponsorships = 'Commanditaires';
$s_view_sponsorship_details = 'Voir les détails des commanditaires';
$s_view_sponsorship_total = 'Voir le total des commanditaires';
$s_sponsor_issue = 'Commanditer l’anomalie';
$s_assign_sponsored_issue = 'Affecter une anomalie commanditée';
$s_handle_sponsored_issue = 'Traiter une anomalie commanditée';
$s_others = 'Autres';
$s_see_email_addresses_of_other_users = 'Voir les adresses de courriel des autres utilisateurs';
$s_send_reminders = 'Envoyer des rappels';
$s_receive_reminders = 'Recevoir des rappels';
$s_add_profiles = 'Ajouter des profils';
$s_notify_of_new_user_created = 'Notifier la création d’un nouvel utilisateur';
$s_email_notification = 'Notification par courriel';
$s_status_changed_to = 'Changer l’état en';
$s_email_on_deleted = 'Courriel à la suppression';
$s_email_on_sponsorship_changed = 'Courriel au changement de commandite';
$s_email_on_relationship_changed = 'Courriel au changement d’une relation';
$s_email_on_updated = 'Courriel à la mise à jour';
$s_email_on_monitor = 'Envoyer un courriel lorsqu’un utilisateur commence à surveiller';
$s_view_tags = 'Voir les balises attachées à une anomalie';
$s_attach_tags = 'Attacher des balises à une anomalie';
$s_detach_tags = 'Détacher des balises d’une anomalie';
$s_detach_own_tags = 'Détacher des balises attachées par le même utilisateur';
$s_create_new_tags = 'Créer de nouvelles balises';
$s_edit_tags = 'Modifier les noms et descriptions de balises';
$s_edit_own_tags = 'Modifier les balises créées par le même utilisateur';
$s_loading = 'Chargement en cours...';
$s_between_date = 'Entre';
$s_on_or_before_date = 'Pendant ou avant';
$s_before_date = 'Avant';
$s_after_date = 'Après';
$s_on_or_after_date = 'Pendant ou après';
$s_from_date = 'De';
$s_to_date = 'À';
$s_on_date = 'Le';
$s_wiki = 'Wiki';
$s_tags = 'Balises';
$s_tag_details = 'Détails de la balise : %1$s';
$s_tag_id = 'Identifiant de balise';
$s_tag_name = 'Nom';
$s_tag_creator = 'Créateur';
$s_tag_created = 'Date de création';
$s_tag_updated = 'Dernière mise à jour';
$s_tag_description = 'Description de balise';
$s_tag_statistics = 'Statistiques d’utilisation';
$s_tag_update = 'Mettre à jour la balise : %1$s';
$s_tag_update_return = 'Retour à la balise';
$s_tag_update_button = 'Mettre à jour la balise';
$s_tag_delete_button = 'Supprimer la balise';
$s_tag_delete_message = 'Êtes-vous sûr{{GENDER:||e}} de vouloir supprimer cette balise ?';
$s_tag_existing = 'Balises existantes';
$s_tag_none_attached = 'Aucune balise n’est attachée.';
$s_tag_attach = 'Attacher';
$s_tag_attach_long = 'Attacher des balises';
$s_tag_attach_failed = 'Échec d’attachement de la balise.';
$s_tag_detach = 'Détacher « %1$s »';
$s_tag_separate_by = '(Séparer par « %1$s »)';
$s_tag_invalid_name = 'Nom de balise invalide.';
$s_tag_create_denied = 'Création refusée.';
$s_tag_attach_denied = 'Attachement refusé.';
$s_tag_filter_default = 'Anomalies attachées (%1$s)';
$s_tag_history_attached = 'Balise attachée';
$s_tag_history_detached = 'Balise détachée';
$s_tag_history_renamed = 'Balise renommée';
$s_tag_related = 'Étiquettes apparentées';
$s_tag_related_issues = 'Anomalies partagées (%1$s)';
$s_tag_stats_attached = 'Anomalies attachées : %1$s';
$s_tag_create = 'Créer la balise';
$s_show_all_tags = 'Toutes';
$s_time_tracking_billing_link = 'Suivi du temps';
$s_time_tracking = 'Suivi du temps';
$s_time_tracking_time_spent = 'Temps passé :';
$s_time_tracking_get_info_button = 'Obtenir les informations de suivi du temps';
$s_time_tracking_cost_per_hour = 'Coût horaire';
$s_time_tracking_cost_per_hour_label = 'Coût horaire :';
$s_time_tracking_cost = 'Coût';
$s_time_tracking_cost_label = 'Coût :';
$s_total_time_for_issue = 'Temps total pour l’anomalie = %1$s';
$s_time_tracking_stopwatch_start = 'Démarrer';
$s_time_tracking_stopwatch_stop = 'Arrêt';
$s_time_tracking_stopwatch_reset = 'Réinitialiser';
$s_access_denied = 'Accès refusé.';
$s_manage_columns_config = 'Gérer les colonnes';
$s_all_columns_title = 'Toutes les colonnes disponibles';
$s_csv_columns_title = 'Colonnes CSV';
$s_view_issues_columns_title = 'Colonnes d’affichage des anomalies';
$s_print_issues_columns_title = 'Colonnes d’impression des anomalies';
$s_excel_columns_title = 'Colonnes Excel';
$s_update_columns_as_global_default = 'Mettre à jour les colonnes aux valeurs par défaut globales pour tous les projets';
$s_update_columns_for_current_project = 'Mettre à jour les colonnes pour le projet actuel';
$s_update_columns_as_my_default = 'Mettre à jour les colonnes aux valeurs par défaut pour tous les projets';
$s_reset_columns_configuration = 'Réinitialiser la configuration des colonnes';
$s_copy_columns_from = 'Copier les colonnes de';
$s_copy_columns_to = 'Copier les colonnes vers';
$s_due_date = 'Date d’échéance';
$s_overdue = 'en retard';
$s_overdue_since = 'En retard depuis %1$s';
$s_overdue_one_day = 'Le retard est inférieur à un jour';
$s_overdue_days = 'Retard dans %1$d jours';
$s_view_account_title = 'Informations sur l’utilisateur';
$s_manage_user = 'Gérer l’utilisateur';
$s_word_separator = '&#32;';
$s_label = '%1$s :';
$s_install_information = 'Informations d’installation de MantisBT';
$s_database_information = 'Informations sur la base de données MantisBT';
$s_path_information = 'Informations sur les chemins de MantisBT';
$s_mantisbt_database_statistics = 'Statistiques de la base de données MantisBT';
$s_php_version = 'Version de PHP';
$s_adodb_version = 'Version d’ADOdb';
$s_database_driver = 'Pilote de la base de données';
$s_database_version_description = 'Version de la base de données, description';
$s_month_january = 'janvier';
$s_month_february = 'février';
$s_month_march = 'mars';
$s_month_april = 'avril';
$s_month_may = 'mai';
$s_month_june = 'juin';
$s_month_july = 'juillet';
$s_month_august = 'août';
$s_month_september = 'septembre';
$s_month_october = 'octobre';
$s_month_november = 'novembre';
$s_month_december = 'décembre';
$s_timeline_issue_created = '<span class="username">%1$s</span> a créé l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_issue_file_added = '<span class="username">%1$s</span> a attaché le fichier <em>%3$s</em> à l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_issue_file_deleted = '<span class="username">%1$s</span> a supprimé le fichier <em>%3$s</em> de l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_issue_note_created = '<span class="username">%1$s</span> a ajouté un commentaire sur l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_issue_monitor = '<span class="username">%1$s</span> surveille l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_issue_unmonitor = '<span class="username">%1$s</span> a arrêté de surveiller l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_issue_tagged = '<span class="username">%1$s</span> a ajouté la balise <span class="tag_name">%3$s</span> à l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_issue_untagged = '<span class="username">%1$s</span> a supprimé la balise <span class="tag_name">%3$s</span> de l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_issue_resolved = '<span class="username">%1$s</span> a résolu l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_issue_closed = '<span class="username">%1$s</span> a clos l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_issue_reopened = '<span class="username">%1$s</span> a rouvert l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_issue_assigned = '<span class="username">%1$s</span> a affecté l’anomalie <span class="issue_id">%2$s</span> à <span class="username">%3$s</span>';
$s_timeline_issue_assigned_to_self = '<span class="username">%1$s</span> a pris en charge l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_issue_unassigned = '<span class="username">%1$s</span> a désassigné l’anomalie <span class="issue_id">%2$s</span>';
$s_timeline_no_activity = 'Aucune activité dans l’intervalle de temps.';
$s_timeline_title = 'Chronologie';
$s_timeline_more = 'Plus d’événements...';
$s_missing_error_string = 'Chaîne d’erreur manquante :%1$s';
$MANTIS_ERROR[ERROR_GENERIC] = 'Une erreur s’est produite. Merci de signaler cette erreur à votre administrateur local.';
$MANTIS_ERROR[ERROR_SQL] = 'Erreur SQL détectée.';
$MANTIS_ERROR[ERROR_REPORT] = 'Il y avait une erreur dans votre rapport.';
$MANTIS_ERROR[ERROR_NO_FILE_SPECIFIED] = 'Aucun fichier spécifié.';
$MANTIS_ERROR[ERROR_FILE_DISALLOWED] = 'Type de fichier interdit.';
$MANTIS_ERROR[ERROR_NO_DIRECTORY] = 'Le répertoire n’existe pas. Veuillez vérifier les paramètres du projet.';
$MANTIS_ERROR[ERROR_DUPLICATE_PROJECT] = 'Un projet ayant ce nom existe déjà.';
$MANTIS_ERROR[ERROR_EMPTY_FIELD] = 'Le champ obligatoire « %1$s » n’est pas renseigné. Veuillez vérifier à nouveau votre saisie.';
$MANTIS_ERROR[ERROR_INVALID_FIELD_VALUE] = 'Valeur non valide pour « %1$s »';
$MANTIS_ERROR[ERROR_PROTECTED_ACCOUNT] = 'Ce compte est protégé. Vous n’êtes pas autorisé à effectuer l’opération demandée tant que la protection du compte n’est pas levée.';
$MANTIS_ERROR[ERROR_ACCESS_DENIED] = 'Accès interdit.';
$MANTIS_ERROR[ERROR_UPLOAD_FAILURE] = 'Échec du dépôt de fichier. Le fichier n’est pas lisible par MantisBT. Vérifiez la configuration du projet.';
$MANTIS_ERROR[ERROR_FILE_TOO_BIG] = 'Échec du téléversement de fichier. La taille du fichier était vraisemblablement plus grande que ce qui est actuellement permis par cette installation de PHP.';
$MANTIS_ERROR[ERROR_FILE_NAME_TOO_LONG] = 'Le nom de fichier « %1$s » est trop long.';
$MANTIS_ERROR[ERROR_GPC_VAR_NOT_FOUND] = 'Un paramètre requis par cette page (%1$s) n’a pas été trouvé.';
$MANTIS_ERROR[ERROR_USER_NAME_NOT_UNIQUE] = 'Ce nom d’utilisateur est déjà utilisé. Merci de recommencer et choisir un autre nom.';
$MANTIS_ERROR[ERROR_USER_EMAIL_NOT_UNIQUE] = 'Cette adresse de courriel est déjà utilisée. Veuillez revenir en arrière et en sélectionner une autre.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_NOT_FOUND] = 'L’option de configuration « %1$s » n’a pas été trouvée.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_CANT_BE_SET_IN_DB] = 'L’option de configuration « %1$s » ne peut pas être définie dans la base de données. Elle doit être définie dans config_inc.php.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_BAD_SYNTAX] = 'Impossible de définir l’option de configuration « %1$s » : %2$s';
$MANTIS_ERROR[ERROR_LANG_STRING_NOT_FOUND] = 'Chaîne « %1$s » introuvable.';
$MANTIS_ERROR[ERROR_BUGNOTE_NOT_FOUND] = 'Note introuvable.';
$MANTIS_ERROR[ERROR_DB_FIELD_NOT_FOUND] = 'Champ « %1$s » de la base de données introuvable.';
$MANTIS_ERROR[ERROR_HANDLER_ACCESS_TOO_LOW] = 'Cette personne ne possède pas les droits d’accès suffisants pour prendre en charge une anomalie à cet état.';
$MANTIS_ERROR[ERROR_PROJECT_HIERARCHY_DISABLED] = 'La hiérarchie de projet (sous-projets) est désactivée.';
$MANTIS_ERROR[ERROR_PROJECT_NOT_FOUND] = 'Projet « %1$s » introuvable.';
$MANTIS_ERROR[ERROR_PROJECT_NAME_NOT_UNIQUE] = 'Un projet ayant ce nom existe déjà. Revenez en arrière et saisissez un autre nom.';
$MANTIS_ERROR[ERROR_PROJECT_NAME_INVALID] = 'Nom de projet spécifié non valide.  Les noms de projet ne peuvent être vides.';
$MANTIS_ERROR[ERROR_PROJECT_RECURSIVE_HIERARCHY] = 'Cette opération va créer une récursion dans la hiérarchie des sous-projets.';
$MANTIS_ERROR[ERROR_PROJECT_SUBPROJECT_DUPLICATE] = 'Le projet « %1$s » est déjà un sous-projet de « %2$s ».';
$MANTIS_ERROR[ERROR_PROJECT_SUBPROJECT_NOT_FOUND] = 'Le projet « %1$s » n’est pas un sous-projet de « %2$s ».';
$MANTIS_ERROR[ERROR_USER_BY_NAME_NOT_FOUND] = 'Utilisateur nommé « %1$s » introuvable.';
$MANTIS_ERROR[ERROR_USER_BY_ID_NOT_FOUND] = 'Utilisateur avec l’ID « %1$d » introuvable.';
$MANTIS_ERROR[ERROR_USER_BY_EMAIL_NOT_FOUND] = 'Utilisateur avec l’adresse de courriel « %1$s » introuvable.';
$MANTIS_ERROR[ERROR_USER_BY_REALNAME_NOT_FOUND] = 'Utilisateur avec le nom réel « %1$s » introuvable.';
$MANTIS_ERROR[ERROR_AUTH_INVALID_COOKIE] = 'Les informations de connexion enregistrées par votre navigateur ne sont pas valides. Votre compte a peut-être été supprimé ?';
$MANTIS_ERROR[ERROR_USER_PREFS_NOT_FOUND] = 'Les préférences n’ont pas pu être trouvées pour cet utilisateur.';
$MANTIS_ERROR[ERROR_NEWS_NOT_FOUND] = 'Actualité introuvable.';
$MANTIS_ERROR[ERROR_USER_CREATE_PASSWORD_MISMATCH] = 'Le mot de passe ne correspond pas à la vérification.';
$MANTIS_ERROR[ERROR_USER_CURRENT_PASSWORD_MISMATCH] = 'Le mot de passe actuel est incorrect.';
$MANTIS_ERROR[ERROR_GPC_ARRAY_EXPECTED] = 'Un tableau était attendu mais une chaîne a été reçue pour %1$s.';
$MANTIS_ERROR[ERROR_GPC_ARRAY_UNEXPECTED] = 'Une chaîne était attendue mais un tableau a été reçu pour %1$s.';
$MANTIS_ERROR[ERROR_GPC_NOT_NUMBER] = 'Un nombre était attendu pour %1$s.';
$MANTIS_ERROR[ERROR_BUG_NOT_FOUND] = 'Anomalie %1$d introuvable.';
$MANTIS_ERROR[ERROR_FILTER_NOT_FOUND] = 'Filtre %1$s introuvable.';
$MANTIS_ERROR[ERROR_EMAIL_INVALID] = 'Adresse de courriel non valide.';
$MANTIS_ERROR[ERROR_EMAIL_DISPOSABLE] = 'L’utilisation d’une adresse de courriel jetable n’est pas autorisée.';
$MANTIS_ERROR[ERROR_USER_PROFILE_NOT_FOUND] = 'Profil introuvable.';
$MANTIS_ERROR[ERROR_FILE_NOT_ALLOWED] = 'Type de fichier non autorisé pour les téléversements.';
$MANTIS_ERROR[ERROR_FILE_DUPLICATE] = 'Fichier dupliqué. Supprimez d’abord le premier.';
$MANTIS_ERROR[ERROR_FILE_INVALID_UPLOAD_PATH] = 'Chemin de téléversement non valide. Soit le répertoire n’existe pas, soit le serveur web ne peut pas y écrire.';
$MANTIS_ERROR[ERROR_FILE_NO_UPLOAD_FAILURE] = 'Aucun fichier téléversé. Veuillez revenir en arrière et choisir un fichier avant de cliquer sur le bouton « Téléverser ».';
$MANTIS_ERROR[ERROR_FILE_MOVE_FAILED] = 'Le fichier téléversé n’a pas pu être déplacé vers le répertoire de stockage des fichiers. Soit le répertoire n’existe pas, soit le serveur web ne peut pas y écrire.';
$MANTIS_ERROR[ERROR_FILE_NOT_FOUND] = 'Pièce jointe avec l’id « %1$d » non trouvée.';
$MANTIS_ERROR[ERROR_BUG_DUPLICATE_SELF] = 'Impossible de marquer cette anomalie comme doublon d’elle-même.';
$MANTIS_ERROR[ERROR_BUG_REVISION_NOT_FOUND] = 'Version d’anomalie introuvable.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_NOT_FOUND] = 'Champ personnalisé introuvable.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_NAME_NOT_UNIQUE] = 'Ceci est un nom dupliqué.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_NAME_INVALID] = 'Nom de champ personnalisé « %1$s » non valide : les virgules ne sont pas autorisées. Voir la section « Localisation des noms de champ personnalisés » dans le Guide d’administration pour un palliatif.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_IN_USE] = 'Au moins un projet utilise encore ce champ.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_INVALID_VALUE] = 'Valeur non valide pour le champ « %1$s ».';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_INVALID_DEFINITION] = 'Définition de champ personnalisé non valide.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_INVALID_PROPERTY] = 'Propriété de champ personnalisé non valide (%1$s).';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_NOT_LINKED_TO_PROJECT] = 'Champ personnalisé « %1$s » (identifiant %2$s ) non lié au projet actuellement actif.';
$MANTIS_ERROR[ERROR_LDAP_AUTH_FAILED] = 'Échec d’identification par LDAP.';
$MANTIS_ERROR[ERROR_LDAP_SERVER_CONNECT_FAILED] = 'Échec de connexion au serveur LDAP.';
$MANTIS_ERROR[ERROR_LDAP_UPDATE_FAILED] = 'Échec de mise à jour de l’enregistrement LDAP.';
$MANTIS_ERROR[ERROR_LDAP_USER_NOT_FOUND] = 'Enregistrement d’utilisateur LDAP introuvable.';
$MANTIS_ERROR[ERROR_LDAP_UNABLE_TO_SET_MIN_TLS] = 'Échec lors de la définition de la version minimum de TLS sur le serveur LDAP.';
$MANTIS_ERROR[ERROR_LDAP_UNABLE_TO_STARTTLS] = 'Impossible de lancer StartTLS sur le serveur LDAP.';
$MANTIS_ERROR[ERROR_DB_CONNECT_FAILED] = 'Échec de connexion à la base de données. L’erreur renvoyée était #%1$d : %2$s.';
$MANTIS_ERROR[ERROR_DB_QUERY_FAILED] = 'Échec de la requête de base de données. L’erreur renvoyée par la base de données était nº %1$d : %2$s pour la requête : %3$s.';
$MANTIS_ERROR[ERROR_DB_SELECT_FAILED] = 'Échec de sélection de la base de données. L’erreur renvoyée par la base de données était nº %1$d : %2$s.';
$MANTIS_ERROR[ERROR_DB_IDENTIFIER_TOO_LONG] = 'L’identificateur de la base de données « %1$s » est trop long. Essayez de réduire la taille de g_db_table_prefix/suffix';
$MANTIS_ERROR[ERROR_CATEGORY_DUPLICATE] = 'Une catégorie avec ce nom existe déjà.';
$MANTIS_ERROR[ERROR_NO_COPY_ACTION] = 'Aucune action de copie n’a été spécifiée.';
$MANTIS_ERROR[ERROR_CATEGORY_NOT_FOUND] = 'Catégorie introuvable.';
$MANTIS_ERROR[ERROR_CATEGORY_NOT_FOUND_FOR_PROJECT] = 'Catégorie « %1$s » introuvable pour le projet « %2$s ».';
$MANTIS_ERROR[ERROR_CATEGORY_CANNOT_DELETE_DEFAULT] = 'Cette catégorie ne peut pas être supprimée, car elle est définie comme « catégorie par défaut pour les déplacements ».';
$MANTIS_ERROR[ERROR_CATEGORY_CANNOT_DELETE_HAS_ISSUES] = 'La catégorie « %1$s » n’a pas pu être supprimée, parce qu’elle est associée avec une ou plusieurs questions.';
$MANTIS_ERROR[ERROR_VERSION_DUPLICATE] = 'Une version avec ce nom existe déjà.';
$MANTIS_ERROR[ERROR_VERSION_NOT_FOUND] = 'Version « %1$s » introuvable.';
$MANTIS_ERROR[ERROR_USER_NAME_INVALID] = 'Nom d’utilisateur incorrect. Le nom d’utilisateur ne peut contenir que les lettres latines, chiffres, espaces, traits d’union, points, signes plus et traits de soulignement.';
$MANTIS_ERROR[ERROR_USER_REAL_NAME_INVALID] = 'Nom réel non valide.';
$MANTIS_ERROR[ERROR_USER_DOES_NOT_HAVE_REQ_ACCESS] = 'L\'utilisateur n’a pas le niveau d’accès requis';
$MANTIS_ERROR[ERROR_SPONSORSHIP_NOT_ENABLED] = 'Prise en charge des commandites non activée.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_NOT_FOUND] = 'Commandite %1$d introuvable.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_AMOUNT_TOO_LOW] = 'La commandite (%1$s) est inférieure au montant minimum (%2$s).';
$MANTIS_ERROR[ERROR_SPONSORSHIP_HANDLER_ACCESS_LEVEL_TOO_LOW] = 'Le responsable n’a pas le niveau d’accès requis pour manipuler les anomalies commanditées.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_ASSIGNER_ACCESS_LEVEL_TOO_LOW] = 'Accès refusé. L’assignation des anomalies commanditées requiert un niveau d’accès plus élevé.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_SPONSOR_NO_EMAIL] = 'Le commanditaire n’a pas fourni de courriel. Veuillez mettre à jour votre profil.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_INVALID] = 'L’option de configuration « %1$s » a une valeur « %2$s » non valide.';
$MANTIS_ERROR[ERROR_BUG_READ_ONLY_ACTION_DENIED] = 'L’action ne peut être exécutée car l’anomalie « %1$d » est en lecture seule.';
$MANTIS_ERROR[ERROR_BUG_RESOLVE_DEPENDANTS_BLOCKING] = 'Cette anomalie ne peut pas être résolue tant que toutes les anomalies qui en dépendent n’ont pas été résolues. Demandez à votre administrateur système l’accès au projet si vous n’observez aucune dépendance.';
$MANTIS_ERROR[ERROR_RELATIONSHIP_NOT_FOUND] = 'Relation introuvable.';
$MANTIS_ERROR[ERROR_RELATIONSHIP_ACCESS_LEVEL_TO_DEST_BUG_TOO_LOW] = 'Accès refusé : l’anomalie nº %1$d requiert un niveau d’accès plus élevé.';
$MANTIS_ERROR[ERROR_RELATIONSHIP_SAME_BUG] = 'Une anomalie ne peut pas être reliée à elle-même.';
$MANTIS_ERROR[ERROR_SIGNUP_NOT_MATCHING_CAPTCHA] = 'Les éléments de confirmations ne correspondent pas. Veuillez réessayer.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_NOT_ENABLED] = 'La fonctionnalité « Mot de passe oublié ? » n’est pas disponible.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_NO_EMAIL_SPECIFIED] = 'Vous devez fournir une adresse de courriel pour réinitialiser le mot de passe.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_NOT_MATCHING_DATA] = 'Les informations fournies ne correspondent à aucun compte enregistré !';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_CONFIRM_HASH_INVALID] = 'L’hyperlien de confirmation n’est pas valide ou a déjà été utilisé. Veuillez vous inscrire à nouveau.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_MAX_IN_PROGRESS_ATTEMPTS_REACHED] = 'Nombre maximum de demandes en cours atteint. Veuillez contacter l’administrateur du système.';
$MANTIS_ERROR[ERROR_USER_CHANGE_LAST_ADMIN] = 'Vous ne pouvez pas supprimer ou destituer l’unique compte administrateur du système. Pour réaliser l’action que vous avez demandée vous devez d’abord créer un autre compte administrateur.';
$MANTIS_ERROR[ERROR_PAGE_REDIRECTION] = 'Erreur de réacheminement, vérifier qu’il n’y a aucune espace à l’extérieur du bloc PHP block (&lt;?php ?&gt;) dans les fichiers « config_inc.php » ou « custom_*.php ».';
$MANTIS_ERROR[ERROR_TAG_NOT_FOUND] = 'Balise « %1$s » introuvable.';
$MANTIS_ERROR[ERROR_TAG_DUPLICATE] = 'La balise « %1$s » existe déjà.';
$MANTIS_ERROR[ERROR_TAG_NAME_INVALID] = 'Nom de balise « %1$s » non valide.';
$MANTIS_ERROR[ERROR_TAG_NOT_ATTACHED] = 'Cette balise n’est pas attachée à cette anomalie.';
$MANTIS_ERROR[ERROR_TAG_ALREADY_ATTACHED] = 'Cette balise est déjà associée à cette anomalie.';
$MANTIS_ERROR[ERROR_TOKEN_NOT_FOUND] = 'Jeton introuvable.';
$MANTIS_ERROR[ERROR_EVENT_UNDECLARED] = 'L’événement « %1$s » n’est pas encore défini.';
$MANTIS_ERROR[ERROR_PLUGIN_NOT_REGISTERED] = 'Le greffon « %1$s » n’est pas enregistré.';
$MANTIS_ERROR[ERROR_PLUGIN_NOT_LOADED] = 'Le greffon « %1$s » n’est pas chargé, assurez-vous de satisfaire ses dépendances.';
$MANTIS_ERROR[ERROR_PLUGIN_ALREADY_INSTALLED] = 'Le greffon « %1$s » est déjà installé.';
$MANTIS_ERROR[ERROR_PLUGIN_CLASS_NOT_FOUND] = 'La classe « %2$s » n’est pas définie dans le greffon « %1$s ».';
$MANTIS_ERROR[ERROR_PLUGIN_PAGE_NOT_FOUND] = 'La page « %2$s » n’existe pas dans le greffon « %1$s ».';
$MANTIS_ERROR[ERROR_PLUGIN_FILE_NOT_FOUND] = 'Le fichier « %2$s » n’existe pas dans le greffon « %1$s ».';
$MANTIS_ERROR[ERROR_PLUGIN_INSTALL_FAILED] = 'Échec de l’installation du greffon : %1$s.';
$MANTIS_ERROR[ERROR_PLUGIN_UPGRADE_FAILED] = 'La mise à jour du schéma pour le greffon a échoué à l’étape nº %1$s. L’erreur « %2$s » s’est produite lors de l’exécution de la/des instruction(s) suivante(s) :<br> %3$s';
$MANTIS_ERROR[ERROR_PLUGIN_UPGRADE_NEEDED] = 'Le greffon « %1$s » doit être mis à niveau avant de pouvoir accéder à cette page.';
$MANTIS_ERROR[ERROR_PLUGIN_INVALID_PAGE] = 'Le format de la page du greffon spécifié « %1$s » n’est pas valide. Il doit correspondre à « Plugin[/chemin/vers/la]/page ».';
$MANTIS_ERROR[ERROR_PLUGIN_INVALID_FILE] = 'Le format du fichier du greffon spécifié « %1$s » n’est pas valide. Il doit correspondre à « Plugin[/chemin/vers/le]/fichier[.ext] ».';
$MANTIS_ERROR[ERROR_PLUGIN_GENERIC] = 'Une erreur inconnue « %1$s » s’est produite durant l’exécution du greffon « %2$s ».';
$MANTIS_ERROR[ERROR_COLUMNS_DUPLICATE] = 'Le champ « %1$s » comporte une colonne « %2$s » en doublon.';
$MANTIS_ERROR[ERROR_COLUMNS_INVALID] = 'Le champ « %1$s » comporte une colonne « %2$s » non valide.';
$MANTIS_ERROR[ERROR_SESSION_VAR_NOT_FOUND] = 'La variable de session « %1$s » n’a pas été trouvée.';
$MANTIS_ERROR[ERROR_SESSION_NOT_VALID] = 'Votre session n’est plus valide.';
$MANTIS_ERROR[ERROR_FORM_TOKEN_INVALID] = 'Jeton de sécurité du formulaire non valide. Cela peut être dû à un dépassement de délai de la session ou à un double envoi accidentel du formulaire.';
$MANTIS_ERROR[ERROR_CRYPTO_MASTER_SALT_INVALID] = 'Pour des raisons de sécurité, MantisBT ne fonctionne pas quand « $g_crypto_master_salt » n’est pas spécifié correctement dans « config_inc.php » ou contient moins de 16 caractères.';
$MANTIS_ERROR[ERROR_INVALID_REQUEST_METHOD] = 'Impossible d’accéder à cette page avec cette fonction.';
$MANTIS_ERROR[ERROR_INVALID_SORT_FIELD] = 'Champ de tri non valide.';
$MANTIS_ERROR[ERROR_INVALID_DATE_FORMAT] = 'Format de date non valide.';
$MANTIS_ERROR[ERROR_INVALID_RESOLUTION] = 'La résolution « %1$s » n’est pas autorisée pour l’état « %2$s ».';
$MANTIS_ERROR[ERROR_UPDATING_TIMEZONE] = 'Impossible de mettre à jour le fuseau horaire.';
$MANTIS_ERROR[ERROR_DEPRECATED_SUPERSEDED] = 'Fonctionnalité désuète : « %1$s », utilisez plutôt « %2$s ».';
$MANTIS_ERROR[ERROR_DISPLAY_USER_ERROR_INLINE] = 'AVERTISSEMENT : le système est configuré pour afficher les erreurs MantisBT (E_USER_ERROR) en ligne. L’exécution du programme se poursuivra ; cela peut conduire à des problèmes d’intégrité du système ou des données.';
$MANTIS_ERROR[ERROR_TYPE_MISMATCH] = 'Type de données incompatible. Activez les messages d’erreur détaillés pour plus d’informations.';
$MANTIS_ERROR[ERROR_BUG_CONFLICTING_EDIT] = 'Cette anomalie a été mise à jour par un autre utilisateur, veuillez y retourner et soumettre à nouveau vos modifications.';
$MANTIS_ERROR[ERROR_SPAM_SUSPECTED] = 'Vous avez atteint la limite d’activité autorisée de %d événements durant les %d dernières secondes ; votre action a été bloquée pour éviter le pourriel, veuillez essayer à nouveau ultérieurement.';
$MANTIS_ERROR[ERROR_FIELD_TOO_LONG] = 'Le champ « %1$s » doit comporter moins de %2$d caractères.';
$MANTIS_ERROR[ERROR_API_TOKEN_NAME_NOT_UNIQUE] = 'Le nom de jeton de l’API « %s » est déjà utilisé. Veuillez revenir en arrière et en sélectionner un autre.';
$MANTIS_ERROR[ERROR_LOGFILE_NOT_WRITABLE] = 'Le fichier spécifié dans $g_log_destination « %s » ne peut pas être écrit.';
$MANTIS_ERROR[ERROR_MONITOR_ACCESS_TOO_LOW] = 'L’utilisateur ajouté n’a pas les droits d’accès suffisants pour suivre ce problème.';
$MANTIS_ERROR[ERROR_USER_TOKEN_NOT_FOUND] = 'Jeton de l’API pour l’utilisateur non trouvé avec l’identifiant « %d ».';
$s_dropzone_default_message = 'Joignez les fichiers en les glissant-déposant, en les sélectionnant ou en les collant.';
$s_dropzone_fallback_message = 'Votre navigateur ne prend pas en charge le téléversement de fichiers par glisser-déposer.';
$s_dropzone_file_too_big = 'Le fichier est trop gros ({{filesize}} Mio). Taille maximale : {{maxFilesize}} Mio.';
$s_dropzone_invalid_file_type = 'Vous ne pouvez téléverser aucun fichier de ce type.';
$s_dropzone_response_error = 'Le serveur a répondu avec le code {{statusCode}}.';
$s_dropzone_cancel_upload = 'Annuler le téléversement';
$s_dropzone_cancel_upload_confirmation = 'Êtes-vous sûr(e) de vouloir annuler ce téléversement ?';
$s_dropzone_remove_file = 'Supprimer le fichier';
$s_dropzone_max_files_exceeded = 'Vous ne pouvez plus téléverser de fichiers.';
$s_dropzone_not_supported = 'Dropzone.js ne prend pas en charge les navigateurs plus anciens !';
$s_dropzone_multiple_files_too_big = 'Les fichiers suivants dépassent la taille maximale autorisée ({{maxFilesize}} Mio) : {{files}}';
$s_dropzone_multiple_filenames_too_long = 'Les noms de fichier suivants sont trop longs (au maximum {{maxFilenameLength}} caractères) : {{files}}';
$s_save = 'Enregistrer';
$s_reset = 'Réinitialiser';
$s_persist = 'Rendre persitant';
$s_load = 'Charger';
$s_apply_changes = 'Appliquer les modifications';
$s_undo = 'Annuler';
$s_edit = 'Modifier';
$s_submit = 'Soumettre';
$s_update = 'Mettre à jour';
$s_delete = 'Supprimer';
$s_make_default = 'En faire le profil par défaut';
$s_clear_default = 'Effacer les valeurs par défaut';
$s_print = 'Imprimer';
$s_jump = 'Aller';
$s_change = 'Changement';
$s_go_back = 'Retour';
$s_proceed = 'Poursuivre';
$s_move = 'Déplacer';
$s_close = 'Fermer';
$s_add = 'Ajouter';
$s_login = 'Se connecter';
