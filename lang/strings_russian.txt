<?php
/** MantisBT - a php based bugtracking system
 *
 * Copyright (C) 2000 - 2002  Kenzaburo Ito - <EMAIL>
 * Copyright (C) 2002 - 2016  MantisBT Team - <EMAIL>
 *
 * MantisBT is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * MantisBT is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with MantisBT.  If not, see <http://www.gnu.org/licenses/>.
 *
 * **********************************************************************
 * ** This file contains translations stored in translatewiki.net.     **
 * ** See https://translatewiki.net/wiki/Project:About for information **
 * ** on copyright/license for translatewiki.net translations.         **
 * **********************************************************************
 * **                                                                  **
 * **                      DO NOT UPDATE MANUALLY                      **
 * **                                                                  **
 * ** To improve a translation please visit https://translatewiki.net  **
 * ** Detailed instructions on how to create or update translations at **
 * ** http://www.mantisbt.org/wiki/doku.php/mantisbt:translationshowto **
 * **********************************************************************
 */
/** Russian (русский)
 * 
 * See the qqq 'language' for message documentation incl. usage of parameters
 * To improve a translation please visit https://translatewiki.net
 *
 * @ingroup Language
 * @file
 *
 * <AUTHOR>
 * <AUTHOR> Chumakov
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR> Melon
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR> bulldog
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR> B Poccuu
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR> Yves
 * <AUTHOR> Tchernof
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR> Сигачёв
 */

$s_actiongroup_menu_copy = 'Скопировать';
$s_actiongroup_menu_assign = 'Назначить';
$s_actiongroup_menu_resolve = 'Решить';
$s_actiongroup_menu_update_priority = 'Изменить приоритет';
$s_actiongroup_menu_update_status = 'Изменить состояние';
$s_actiongroup_menu_update_severity = 'Изменить влияние';
$s_actiongroup_menu_update_view_status = 'Изменить видимость';
$s_actiongroup_menu_update_category = 'Изменить категорию';
$s_actiongroup_menu_set_sticky = 'Приклеить / отклеить';
$s_actiongroup_menu_update_field = 'Изменить %1$s';
$s_actiongroup_menu_update_product_version = 'Изменить версию продукта';
$s_actiongroup_menu_update_target_version = 'Изменить целевую версию';
$s_actiongroup_menu_update_fixed_in_version = 'Изменить "решена в версии"';
$s_actiongroup_menu_update_product_build = 'Изменить версию сборки продукта';
$s_actiongroup_menu_update_due_date = 'Обновление даты оплаты';
$s_actiongroup_menu_add_note = 'Добавить заметку';
$s_actiongroup_menu_attach_tags = 'Добавить метки';
$s_actiongroup_bugs = 'Выбранные задачи';
$s_actiongroup_error_issue_is_readonly = 'Задача открыта только для чтения.';
$s_all_projects = 'Все проекты';
$s_move_bugs = 'Переместить задачи';
$s_operation_successful = 'Действие успешно выполнено.';
$s_operation_warnings = 'Операция завершена с предупреждениями.';
$s_operation_failed = 'Операция не была успешно завершена.';
$s_date_order = 'Очередность выпуска';
$s_print_all_bug_page_link = 'Печать отчетов';
$s_csv_export = 'Экспорт в CSV';
$s_excel_export = 'Экспорт в Excel';
$s_login_anonymously = 'Войти анонимно';
$s_anonymous = 'Анонимный Пользователь';
$s_jump_to_bugnotes = 'Перейти к комментариям';
$s_jump_to_history = 'Перейти к истории';
$s_display_history = 'Показать историю';
$s_public_project_msg = 'Это проект с ПУБЛИЧНЫМ доступом. Доступен для всех пользователей.';
$s_private_project_msg = 'Это проект с ПРИВАТНЫМ доступом. Доступен только администраторам и явно указанным пользователям.';
$s_access_level_project = 'Уровень доступа к проекту';
$s_view_submitted_bug_link = 'Просмотреть созданную задачу %1$s';
$s_assigned_projects = 'Назначенные проекты';
$s_assigned_projects_label = 'Назначенные проекты';
$s_unassigned_projects_label = 'Неназначенные проекты';
$s_copy_users = 'Копирование пользователей';
$s_copy_categories_from = 'Копировать категории из';
$s_copy_categories_to = 'Копировать категории в';
$s_copy_categories_exclude_inherited = 'Исключить унаследованные категории';
$s_copy_versions_from = 'Копировать версии из';
$s_copy_versions_to = 'Копировать версии в';
$s_copy_users_from = 'Копировать пользователей из';
$s_copy_users_to = 'Копировать пользователей в';
$s_bug_history = 'История изменений';
$s_field = 'Поле';
$s_old_value = 'Старое значение';
$s_new_value = 'Новое значение';
$s_date_modified = 'Дата изменения';
$s_bugnote = 'Комментарий';
$s_bugnote_view_state = 'Видимость комментария';
$s_bug_monitor = 'Отслеживать';
$s_bug_end_monitor = 'Не отслеживать';
$s_announcement = 'Объявление';
$s_stays_on_top = 'Поверх всех';
$s_bugnote_link_title = 'Ссылка на комментарий';
$s_delete_bugnote_button = 'Удалить комментарий';
$s_delete_bugnote_sure_msg = 'Вы уверены, что хотите удалить этот комментарий?';
$s_bug_relationships = 'Связи';
$s_empty_password_sure_msg = 'У пользователя пустой пароль. Вы уверены, что это то, что вам нужно?';
$s_empty_password_button = 'Использовать пустой пароль';
$s_reauthenticate_title = 'Авторизация';
$s_reauthenticate_message = 'Вы переходите на защищенную страницу, но время авторизации истекло. Пожалуйста, авторизируйтесь для продолжения работы.';
$s_no_category = '(Без категории)';
$s_global_categories = 'Глобальные категории';
$s_inherit = 'Наследовать категории';
$s_inherit_global = 'Наследовать глобальные категории';
$s_inherit_parent = 'Наследовать родительские категории';
$s_update_subproject_inheritance = 'Изменить наследование для подпроектов';
$s_duplicate_of = 'дублирует';
$s_has_duplicate = 'дублируется';
$s_related_to = 'связана с';
$s_dependant_on = 'зависит от';
$s_blocks = 'блокирует';
$s_new_bug = 'Новая задача';
$s_bugnote_added = 'Комментарий добавлен';
$s_bugnote_edited = 'Комментарий изменен';
$s_bugnote_deleted = 'Комментарий удалён';
$s_summary_updated = 'Тема изменена';
$s_description_updated = 'Описание изменено';
$s_additional_information_updated = 'Дополнительные сведения изменены';
$s_steps_to_reproduce_updated = 'Шаги по воспроизведению изменены';
$s_file_added = 'Файл добавлен';
$s_file_deleted = 'Файл удалён';
$s_bug_deleted = 'Задача удалена';
$s_make_private = 'Сделать приватным';
$s_make_public = 'Сделать публичным';
$s_create_new_project_link = 'Создать новый проект';
$s_opensearch_id_short = '%s Id';
$s_opensearch_id_description = '%s: поиск по ID проблемы';
$s_opensearch_text_short = '%s Текст';
$s_opensearch_text_description = '%s: полнотекстовый поиск';
$s_select_option = '(выбрать)';
$s_bug_actiongroup_access = 'У вас недостаточно прав для выполнения этого действия.';
$s_bug_actiongroup_status = 'Эту задачу нельзя перевести в выбранное состояние';
$s_bug_actiongroup_category = 'Эту задачу нельзя перевести в выбранную категорию';
$s_bug_actiongroup_handler = 'Указанному пользователю не разрешается быть ответственным за эту задачу';
$s_bug_actiongroup_version = 'Запрошенной версии нет в этом проекте';
$s_close_bugs_conf_msg = 'Вы уверены, что хотите закрыть эти задачи?';
$s_delete_bugs_conf_msg = 'Вы уверены, что хотите удалить эти задачи?';
$s_move_bugs_conf_msg = 'Переместить задачи в';
$s_copy_bugs_conf_msg = 'Копировать задачи в';
$s_assign_bugs_conf_msg = 'Назначить задачи';
$s_resolve_bugs_conf_msg = 'Выберите вариант решения задач';
$s_priority_bugs_conf_msg = 'Выберите приоритет задач';
$s_status_bugs_conf_msg = 'Выберите состояние задач';
$s_view_status_bugs_conf_msg = 'Выберите видимость задач';
$s_category_bugs_conf_msg = 'Выберите категорию задач';
$s_set_sticky_bugs_conf_msg = 'Вы уверены, что хотите приклеить/отклеить эти задачи?';
$s_product_version_bugs_conf_msg = 'Обновить версию продукта до';
$s_fixed_in_version_bugs_conf_msg = 'Изменить "решен в версии" на';
$s_target_version_bugs_conf_msg = 'Изменить целевую версию на';
$s_due_date_bugs_conf_msg = 'Обновление за дату';
$s_close_group_bugs_button = 'Закрыть задачи';
$s_delete_group_bugs_button = 'Удалить задачи';
$s_move_group_bugs_button = 'Переместить задачи';
$s_copy_group_bugs_button = 'Копировать задачи';
$s_assign_group_bugs_button = 'Назначить задачи';
$s_resolve_group_bugs_button = 'Решить задачи';
$s_priority_group_bugs_button = 'Изменить приоритет';
$s_status_group_bugs_button = 'Изменить состояние';
$s_category_group_bugs_button = 'Изменить категорию';
$s_view_status_group_bugs_button = 'Изменить видимость';
$s_set_sticky_group_bugs_button = 'Приклеить/отклеить';
$s_product_version_group_bugs_button = 'Обновить версию продукта';
$s_fixed_in_version_group_bugs_button = 'Изменить "решен в версии"';
$s_target_version_group_bugs_button = 'Изменить целевую версию';
$s_due_date_group_bugs_button = 'Обновление даты оплаты';
$s_update_severity_title = 'Обновление влияния';
$s_update_severity_msg = 'Выберите обновление влияния';
$s_update_severity_button = 'Обновить влияние';
$s_hide_button = 'Показать только выбранные';
$s_printing_preferences_title = 'Выберите поля для печати';
$s_printing_options_link = 'Параметры печати';
$s_bugnote_title = 'Автор комментария';
$s_bugnote_date = 'Дата комментария';
$s_bugnote_description = 'Текст комментария';
$s_error_no_proceed = 'Воспользуйтесь кнопкой "Назад" своего браузера для возврата на предыдущую страницу. Там вы сможете исправить проблемы, показанные в этом сообщении об ошибке, или выбрать другое действие. Можно также перейти сразу в другой раздел, напрямую выбрав пункт меню.';
$s_login_error = 'Возможно, ваша учётная запись заблокирована, или введённое регистрационное имя/пароль неправильны.';
$s_login_cookies_disabled = 'Ваш браузер или не умеет обрабатывать cookies, или не принимает их.';
$s_logged_in_as = 'Пользователь';
$s_prefix_for_deleted_users = 'пользователь';
$s_administrator = 'администратор';
$s_myself = 'Я';
$s_default_access_level = 'Уровень доступа по умолчанию';
$s_issue_status_percentage = 'Соотношение задач';
$s_access_levels_enum_string = '10:наблюдатель,25:автор,40:редактор,55:разработчик,70:руководитель,90:администратор';
$s_no_access = 'нет доступа';
$s_project_status_enum_string = '10:в разработке,30:выпущен,50:стабильный,70:устарел';
$s_project_view_state_enum_string = '10:публичный,50:приватный';
$s_view_state_enum_string = '10:публичная,50:приватная';
$s_priority_enum_string = '10:нет,20:низкий,30:обычный,40:высокий,50:срочный,60:неотложный';
$s_severity_enum_string = '10:нововведение,20:пустяк,30:текст/опечатка,40:настройка,50:малое,60:большое,70:критическое,80:блокирующее';
$s_reproducibility_enum_string = '10:всегда,30:иногда,50:произвольно,70:не проверялась,90:не воспроизводится,100:неприменимо';
$s_status_enum_string = '10:новая,20:обратная связь,30:рассматривается,40:подтверждена,50:назначена,80:решена,90:закрыта';
$s_resolution_enum_string = '10:открыта,20:решена,30:переоткрыта,40:не удаётся воспроизвести,50:нерешаема,60:повтор,70:изменения не нужны,80:отложена,90:отказ в исправлении';
$s_projection_enum_string = '10:нет,30:мелкие поправки,50:небольшие исправления,70:большая переделка,90:полный пересмотр';
$s_eta_enum_string = '10:нет,20:до 1 дня,30:2-3 дня,40:до 1 недели,50:до 1 месяца,60:более 1 месяца';
$s_sponsorship_enum_string = '0:не оплачен,1:оплата запрошена,2:оплачен';
$s_new_account_subject = 'Регистрация учётной записи';
$s_new_account_greeting = 'Спасибо за регистрацию. Создана учётная запись пользователя "%1$s". Для завершения регистрации, перейдите по следующей ссылке (убедитесь, что она введена в одну строку), и установите собственный пароль для доступа:';
$s_new_account_greeting_admincreated = 'Пользователь %1$s создал аккаунт для вас с именем пользователя "%2$s". Для завершения регистрации, перейдите по следующей ссылке (проверьте, чтобы ссылка была в одну строку, иначе она не сработает) и введите в соответствующие поля удобный для вас пароль доступа (будьте внимательны, пароль надо ввести в два поля):';
$s_new_account_username = 'Пользователь:';
$s_new_account_message = 'Если вы не запрашивали никакой регистрации, проигнорируйте это сообщение, и ничего не произойдет.';
$s_new_account_do_not_reply = 'НЕ ОТВЕЧАЙТЕ НА ЭТО СООБЩЕНИЕ';
$s_new_account_email = 'Адрес электронной почты:';
$s_new_account_IP = 'IP-адрес клиента:';
$s_new_account_signup_msg = 'Создана следующая учётная запись:';
$s_reset_request_msg = 'Получен запрос на изменение пароля с помощью проверочного письма';
$s_reset_request_admin_msg = 'Ваш пароль был сброшен. Пожалуйста, перейдите по следующему URL-адресу, чтобы установить новый:';
$s_reset_request_in_progress_msg = 'Если вы ввели корректное имя и адрес электронной почты своей учётной записи, то на этот адрес отправлено подтверждающее сообщение. Для смены пароля своей учётной записи следуйте указаниям, приведённым в письме.';
$s_email_notification_title_for_status_bug_new = 'Следующей задаче присвоено состояние НОВАЯ (повторно):';
$s_email_notification_title_for_status_bug_feedback = 'По следующей задаче НУЖНА ОБРАТНАЯ СВЯЗЬ.';
$s_email_notification_title_for_status_bug_acknowledged = 'Следующая задача РАССМАТРИВАЕТСЯ:';
$s_email_notification_title_for_status_bug_confirmed = 'Следующая задача ПОДТВЕРЖДЕНА:';
$s_email_notification_title_for_status_bug_assigned = 'Следующая задача НАЗНАЧЕНА:';
$s_email_notification_title_for_status_bug_resolved = 'Следующая задача РЕШЕНА:';
$s_email_notification_title_for_status_bug_closed = 'Следующая задача ЗАКРЫТА:';
$s_email_notification_title_for_action_bug_submitted = 'Следующая задача СОЗДАНА:';
$s_email_notification_title_for_action_bug_assigned = 'Следующая задача НАЗНАЧЕНА:';
$s_email_notification_title_for_action_bug_unassigned = 'Следующая задача теперь НЕ НАЗНАЧЕНА.';
$s_email_notification_title_for_action_bug_reopened = 'Следующая задача ПЕРЕОТКРЫТА:';
$s_email_notification_title_for_action_bug_deleted = 'Следующая задача УДАЛЕНА:';
$s_email_notification_title_for_action_bug_updated = 'Следующая задача ИЗМЕНЕНА:';
$s_email_notification_title_for_action_sponsorship_added = 'Предложена ОПЛАТА следующей задачи:';
$s_email_notification_title_for_action_sponsorship_updated = 'Оплата следующей задачи изменена:';
$s_email_notification_title_for_action_sponsorship_deleted = 'Оплата следующей задачи отменена:';
$s_email_notification_title_for_action_bugnote_submitted = 'К следующей задаче добавлен КОММЕНТАРИЙ:';
$s_email_notification_title_for_action_duplicate_of_relationship_added = 'Следующая задача помечена как ДУБЛЬ задачи %1$s:';
$s_email_notification_title_for_action_has_duplicate_relationship_added = 'Задача %1$s помечена как ДУБЛЬ следующей задачи:';
$s_email_notification_title_for_action_related_to_relationship_added = 'Следующая задача помечен как СВЯЗАННАЯ С задачей %1$s:';
$s_email_notification_title_for_action_dependant_on_relationship_added = 'Следующая задача помечена как  РОДИТЕЛЬСКАЯ для задачи %1$s:';
$s_email_notification_title_for_action_blocks_relationship_added = 'Следующая задача помечена как ДОЧЕРНЯЯ для задачи %1$s:';
$s_email_notification_title_for_action_duplicate_of_relationship_deleted = 'Следующая задача больше НЕ ДУБЛИРУЕТ задачу %1$s:';
$s_email_notification_title_for_action_has_duplicate_relationship_deleted = 'С задачи %1$s снята пометка ДУБЛЯ следующей задачи:';
$s_email_notification_title_for_action_related_to_relationship_deleted = 'Со следующей задачи снята пометка СВЯЗАННОЙ с задачей %1$s:';
$s_email_notification_title_for_action_dependant_on_relationship_deleted = 'Со следующей задачи снята пометка РОДИТЕЛЬСКОЙ для задачи %1$s:';
$s_email_notification_title_for_action_blocks_relationship_deleted = 'Со следующей задачи снята пометка ДОЧЕРНЕЙ для задачи %1$s:';
$s_email_notification_title_for_action_relationship_child_resolved = 'СВЯЗАННАЯ задача %1$s РЕШЕНА:';
$s_email_notification_title_for_action_relationship_child_closed = 'СВЯЗАННАЯ задача %1$s ЗАКРЫТА:';
$s_email_notification_title_for_action_related_issue_deleted = 'СВЯЗАННАЯ задача %1$s УДАЛЕНА.';
$s_email_notification_title_for_action_monitor = 'Задача %1$s отслеживается пользователем %2$s.';
$s_email_reporter = 'Инициатор';
$s_email_handler = 'Ответственный';
$s_email_project = 'Проект';
$s_email_bug = 'Номер задачи';
$s_email_category = 'Категория';
$s_email_reproducibility = 'Воспроизводимость';
$s_email_severity = 'Влияние';
$s_email_priority = 'Приоритет';
$s_email_status = 'Состояние';
$s_email_resolution = 'Решение';
$s_email_duplicate = 'Дублирует';
$s_email_fixed_in_version = 'Решена в версии';
$s_email_target_version = 'Целевая версия';
$s_email_date_submitted = 'Создана';
$s_email_last_modified = 'Изменена';
$s_email_summary = 'Тема';
$s_email_description = 'Описание';
$s_email_additional_information = 'Дополнительная информация';
$s_email_steps_to_reproduce = 'Шаги по воспроизведению';
$s_email_tag = 'Метки';
$s_email_due_date = 'Дата';
$s_account_protected_msg = 'Учётная запись защищена. Изменение настроек невозможно...';
$s_account_removed_msg = 'Ваша учётная запись удалена...';
$s_confirm_delete_msg = 'Вы уверены, что хотите удалить собственную учётную запись?';
$s_delete_account_button = 'Удалить учётную запись';
$s_manage_profiles_link = 'Профили';
$s_change_preferences_link = 'Настройки';
$s_edit_account_title = 'Изменение учётной записи';
$s_username = 'Пользователь';
$s_username_label = 'Имя пользователя';
$s_realname = 'Имя';
$s_realname_label = 'Имя';
$s_email = 'E-mail';
$s_email_label = 'Электронная почта';
$s_password = 'Пароль';
$s_new_password = 'Новый пароль';
$s_no_password_change = 'Пароль управляется другой системой, а здесь его изменять нельзя.';
$s_confirm_password = 'Подтверждение пароля';
$s_current_password = 'Текущий пароль';
$s_access_level = 'Доступ';
$s_access_level_label = 'Уровень доступа';
$s_update_user_button = 'Изменить пользователя';
$s_verify_warning = 'Сведения вашей учётной записи были проверены.';
$s_verify_change_password = 'Чтобы вы смогли снова войти в систему, здесь необходимо установить пароль.';
$s_api_tokens_link = 'API-токены';
$s_api_token_create_form_title = 'Создать токен API';
$s_api_token_create_button = 'Создать токен API';
$s_api_token_name = 'Название токена';
$s_api_token_disclose_message = 'Токен, используемый для доступа к API.';
$s_api_token_displayed_once = 'Обратите внимание, что этот токен отображается только один раз.';
$s_api_tokens_title = 'API-токены';
$s_api_token_revoke_button = 'Отозвать';
$s_api_token_never_used = 'Никогда не использован';
$s_api_token_revoked = 'API токен "%s" отозван.';
$s_last_used = 'Последнее использование';
$s_default_account_preferences_title = 'Настройки учётной записи';
$s_default_project = 'Проект по умолчанию';
$s_refresh_delay = 'Интервал обновления, с';
$s_minutes = 'мин';
$s_redirect_delay = 'Задержка перед перенаправлением, с';
$s_seconds = 'с';
$s_with_minimum_severity = 'С влиянием не менее';
$s_bugnote_order = 'Порядок сортировки комментариев';
$s_bugnote_order_asc = 'По возраст.';
$s_bugnote_order_desc = 'По убыванию';
$s_email_on_new = 'Уведомлять о создании';
$s_email_on_assigned = 'Уведомлять о назначении';
$s_email_on_feedback = 'Уведомлять о запросе обратной связи';
$s_email_on_resolved = 'Уведомлять о решении';
$s_email_on_closed = 'Уведомлять о закрытии';
$s_email_on_reopened = 'Уведомлять о переоткрытии';
$s_email_on_bugnote_added = 'Уведомлять о добавлении комментария';
$s_email_on_status_change = 'Уведомлять об изменении состояния';
$s_email_on_priority_change = 'Уведомлять об изменении приоритета';
$s_email_bugnote_limit = 'Ограничение почтовых уведомлений';
$s_email_full_issue_details = 'Отправить все подробности задачи по e-mail';
$s_language = 'Язык';
$s_font_family = 'Семейство шрифтов';
$s_update_prefs_button = 'Изменить настройки';
$s_reset_prefs_button = 'Сбросить настройки';
$s_timezone = 'Часовой пояс';
$s_prefs_reset_msg = 'Настройки сброшены...';
$s_prefs_updated_msg = 'Настройки изменены...';
$s_select_profile = 'Выбор профиля';
$s_add_profile = 'Добавить профиль';
$s_edit_profile = 'Изменить профиль';
$s_update_profile = 'Обновить профиль';
$s_delete_profile = 'Удалить профиль';
$s_delete_profile_confirm_msg = 'Вы уверены, что хотите удалить профиль «%1$s»?';
$s_global_profile = 'Глобальный профиль';
$s_default_profile = 'Профиль по умолчанию';
$s_profile_description = 'Описание системы';
$s_my_sponsorship = 'Спонсирование';
$s_update_sponsorship_button = 'Изменить состояние оплаты';
$s_no_sponsored = 'Не найдено спонсируемых задач, назначенных вам.';
$s_own_sponsored = 'Задачи, спонсируемые вами:';
$s_issues_handled = 'Спонсируемые задачи, назначенные вам:';
$s_no_own_sponsored = 'Вы не спонсируете никакие задачи.';
$s_sponsor = 'Спонсор';
$s_sponsor_verb = 'Спонсировать';
$s_amount = 'Размер оплаты';
$s_total_owing = 'Всего причитается';
$s_total_paid = 'Всего уплачено';
$s_sponsor_hide = 'Скрыть решенные оплаченные';
$s_sponsor_show = 'Показать все';
$s_payment_updated = 'Сведения об оплате изменены.';
$s_account_updated_msg = 'Ваша учётная запись успешно изменена...';
$s_email_updated = 'Адрес электронной почты успешно изменен';
$s_realname_updated = 'Имя успешно изменено';
$s_password_updated = 'Пароль успешно изменен';
$s_multiple_projects = 'Выбранные вами задачи относятся к разным проектам. Приведенные ниже параметры отражают установки для всех проектов. Если это неверно, пожалуйста, повторите изменение, выбрав меньше задач.';
$s_new_bug_title = 'Создание задачи';
$s_feedback_bug_title = 'Запрос обратной связи по задаче';
$s_acknowledged_bug_title = 'Принятие задачи к рассмотрению';
$s_confirmed_bug_title = 'Подтверждение необходимости задачи (или существования ошибки)';
$s_assigned_bug_title = 'Назначение задачи';
$s_new_bug_button = 'Создать задачу';
$s_feedback_bug_button = 'Запросить обратную связь';
$s_acknowledged_bug_button = 'Принять к рассмотрению';
$s_confirmed_bug_button = 'Подтвердить задачу';
$s_assigned_bug_button = 'Назначить';
$s_bug_close_msg = 'Задача закрыта...';
$s_close_immediately = 'Закрыть немедленно:';
$s_closed_bug_title = 'Закрыть задачу';
$s_bug_deleted_msg = 'Задача удалена...';
$s_delete_bug_sure_msg = 'Вы уверены, что хотите удалить эту задачу?';
$s_monitor_bug_button = 'Отслеживать';
$s_unmonitor_bug_button = 'Не отслеживать';
$s_upload_file = 'Загрузить файл';
$s_upload_files = 'Загрузить файлы';
$s_select_file = 'Выбор файла';
$s_select_files = 'Выбрать файлы';
$s_upload_file_button = 'Загрузить';
$s_upload_files_button = 'Загрузить файлы';
$s_max_file_size_info = 'Размер до: %1$s %2$s';
$s_bug_reopened_msg = 'Задача переоткрыта...';
$s_reopen_add_bugnote_title = 'Укажите причины переоткрытия задачи';
$s_bugnote_add_reopen_button = 'Добавить комментарий и переоткрыть';
$s_resolved_bug_title = 'Решение задачи';
$s_resolved_bug_button = 'Решить';
$s_bug_resolved_msg = 'Задача решена. Прокомментируйте решение...';
$s_resolve_add_bugnote_title = 'Добавление комментария к решению задачи';
$s_bugnote_add_resolve_button = 'Добавить комментарий';
$s_from = 'От';
$s_to = 'Кому';
$s_sent_you_this_reminder_about = 'напоминает вам о задаче';
$s_bug_reminder = 'Отправить напоминание';
$s_reminder_sent_to = 'Напоминание отправлено:';
$s_reminder_sent_none = 'Уведомления не могут быть отправлены';
$s_reminder_list_truncated = 'список получателей сокращен';
$s_bug_send_button = 'Отправить';
$s_reminder = 'Напоминание';
$s_reminder_mentions = 'Теперь вы можете отметить пользователей, чтобы активировать уведомления для них, а не с помощью функции напоминания. Например, другие могут отметить вас на написание %1s вопросы и заметки, и вы получите уведомление по электронной почте.';
$s_reminder_explain = 'Выбранным адресатам будет отправлено напоминание о желательности обратной связи на эту задачу.';
$s_reminder_monitor = 'Они также автоматически войдет в состав тех, кто отслеживает задачу. Впоследствии адресаты могут отказаться от отслеживания кнопкой «Не отслеживать».';
$s_reminder_store = 'Текст этого напоминания будет сохранен в задаче.';
$s_mentioned_you = 'упомянул вас в:';
$s_mentioned_in = 'Упоминается в %1$s';
$s_confirm_sponsorship = 'Пожалуйста, подтвердите, что собираетесь оплатить задачу %1$d в размере %2$s.';
$s_stick_bug_button = 'Приклеить';
$s_unstick_bug_button = 'Отклеить';
$s_bug_updated_msg = 'Задача успешно изменена...';
$s_back_to_bug_link = 'Назад к задаче';
$s_update_simple_link = 'Простой режим изменения';
$s_updating_bug_advanced_title = 'Изменение сведений о задаче';
$s_id = 'Номер';
$s_category = 'Категория';
$s_severity = 'Влияние';
$s_reproducibility = 'Воспроизводимость';
$s_date_submitted = 'Создан';
$s_last_update = 'Изменен';
$s_reporter = 'Инициатор';
$s_assigned_to = 'Ответственный';
$s_priority = 'Приоритет';
$s_resolution = 'Решение';
$s_status = 'Состояние';
$s_duplicate_id = 'Номер дубля';
$s_os = 'Операционная система';
$s_platform = 'Платформа';
$s_os_build = 'Версия ОС';
$s_projection = 'Трудоемкость';
$s_eta = 'Примерный срок';
$s_product_version = 'Версия продукта';
$s_build = 'Сборка';
$s_fixed_in_version = 'Решена в версии';
$s_target_version = 'Целевая версия';
$s_votes = 'Голоса';
$s_summary = 'Тема';
$s_synthesis = 'Синтез';
$s_description = 'Описание';
$s_steps_to_reproduce = 'Шаги по воспроизведению';
$s_update_information_button = 'Изменить данные';
$s_sticky_issue = 'Приклеенная задача';
$s_profile = 'Профиль';
$s_updating_bug_simple_title = 'Изменение сведений о задаче';
$s_view_revisions = 'Просмотр редакций';
$s_view_num_revisions = 'Просмотр %1$d редакций';
$s_revision = 'Редакция';
$s_revision_by = '%1$s от %2$s';
$s_revision_drop = 'Удалить';
$s_bug_revision_dropped_history = 'Редакция задачи удалена';
$s_bugnote_revision_dropped_history = 'Редакция комментария удалена';
$s_all_revisions = 'Все редакции';
$s_back_to_issue = 'Вернуться к задаче';
$s_confirm_revision_drop = 'Вы действительно хотите удалить эту редакцию задачи?';
$s_activities_title = 'Деятельность';
$s_bugnote_attached_files = 'Прикрепленные файлы:';
$s_bugnote_deleted_msg = 'Комментарий удалён...';
$s_bug_notes_title = 'Комментарии';
$s_edit_bugnote_title = 'Изменение комментария';
$s_no_bugnotes_msg = 'К этой задаче нет комментариев.';
$s_add_bugnote_title = 'Добавить комментарий';
$s_add_bugnote_button = 'Добавить';
$s_closed_bug_button = 'Закрыть задачу';
$s_bugnote_updated_msg = 'Комментарий изменен...';
$s_last_edited = 'Последние изменения:';
$s_hide_content = 'Скрыть содержимое';
$s_show_content = 'Показать содержимое';
$s_file_icon_description = 'иконка файла %1$s';
$s_unknown_file_extension = 'неизвестное';
$s_webmaster_contact_information = 'Для получения помощи вы можете <a href="mailto:%1$s" title="Написать веб-мастеру по эл. почте">связаться с администратором</a>.';
$s_total_queries_executed = 'Всего выполнено запросов: %1$d';
$s_unique_queries_executed = 'Выполнено уникальных запросов: %1$d';
$s_total_query_execution_time = 'Общее время выполнения запроса: %1$s секунд';
$s_page_execution_time = 'Время формирования страницы: %1$s секунд';
$s_memory_usage = 'Использовано памяти: %1$s Кб';
$s_log_page_number = 'Номер';
$s_log_page_time = 'Время выполнения';
$s_log_page_caller = 'Звонящий';
$s_log_page_event = 'Событие';
$s_please_report = 'Пожалуйста, перешлите это %1$s.';
$s_warning_plain_password_authentication = '<strong>Внимание:</strong> Пароли хранятся в открытом виде. Это делает ваш пароль видимым для администраторов.';
$s_warning_default_administrator_account_present = '<strong>ПРЕДУПРЕЖДЕНИЕ:</strong> Вы должны удалить профиль \'administrator\' или изменить для него пароль.';
$s_warning_admin_directory_present = '<strong>Внимание:</strong> каталог «admin» должен быть удалён или доступ к нему должен быть ограничен.';
$s_warning_change_setting = '<strong>Внимание:</strong> для «%1$s» не установлено значение по умолчанию (%2$s).';
$s_warning_security_hazard = 'Это несёт потенциальную угрозу безопасности, поскольку может привести к разглашению конфиденциальных сведений.';
$s_warning_integrity_hazard = 'Это заставит MantisBT продолжать работу, когда возникают ошибки, и может привести к проблемам с целостностью данных системы.';
$s_warning_debug_email = '<strong>Внимание:</strong> "<code>debug_email</code>" не установлен в <code>OFF</code>, все почтовые события будут отправляться "%1$s".';
$s_error_database_no_schema_version = '<strong>Ошибка:</strong> Версия схемы вашей базы данных равна 0. Это может произойти либо из-за повреждения базы данных, либо из-за того, что значение «database_version» невозможно извлечь из таблицы конфигурации или структура базы данных — из устаревшей версии MantisBT (ниже 1.0.0).';
$s_error_database_version_out_of_date_2 = '<strong>ПРЕДУПРЕЖДЕНИЕ:</strong> Структура базы данных, возможно, устарела. Пожалуйста <a href="admin/install.php">произведите обновление здесь</a> перед тем как продолжить.';
$s_error_code_version_out_of_date = '<strong>Внимание:</strong> Структура базы данных более новой версии, нежели чем установленное приложение. Пожалуйста, обновите приложение.';
$s_login_page_info = 'Добро пожаловать в журнал задач.';
$s_login_title = 'Вход';
$s_save_login = 'Оставаться в системе';
$s_secure_session = 'Защищенная сессия';
$s_secure_session_long = 'Доступ к этой сессии будет только с данного IP-адреса (не рекомендуется при плохой связи, когда возможны частые обновления ip-адреса).';
$s_choose_project = 'Выбор проекта';
$s_signup_link = 'Зарегистрировать новую учётную запись';
$s_lost_password_link = 'Забыли пароль?';
$s_username_or_email = 'Имя пользователя или адрес электронной почты';
$s_enter_password = 'Введите пароль для \'%s\'';
$s_select_project_button = 'Выбрать проект';
$s_lost_password_title = 'Сброс пароля';
$s_lost_password_done_title = 'Сообщение отправлено';
$s_lost_password_subject = 'Сброс пароля';
$s_lost_password_info = 'Для восстановления пароля введите регистрационное имя и адрес электронной почты своей учётной записи.<br /><br />Если данные соответствует существующей учётной записи, вам будет выслана специальная ссылка на страницу с кодом проверки. Для смены пароля перейдите по этой ссылке.';
$s_lost_password_confirm_hash_OK = 'Подтверждение принято. Обновите свой пароль.';
$s_open_and_assigned_to_me_label = 'Открытых и назначенных мне:';
$s_open_and_reported_to_me_label = 'Открытых, созданных мной:';
$s_newer_news_link = 'Свежие новости';
$s_older_news_link = 'Старые новости';
$s_archives = 'Архивы';
$s_rss = 'Каналы RSS';
$s_site_information = 'О сайте';
$s_mantis_version = 'Версия MantisBT';
$s_schema_version = 'Версия схемы базы данных';
$s_site_path = 'Каталог сайта';
$s_core_path = 'Путь к корню';
$s_plugin_path = 'Путь к расширениям';
$s_created_user_part1 = 'Созданная учётная запись';
$s_created_user_part2 = 'с уровнем доступа';
$s_create_new_account_title = 'Создание новой учётной записи';
$s_verify_password = 'Подтверждение пароля';
$s_enabled = 'Активен';
$s_enabled_label = 'Активен';
$s_protected = 'Защищен';
$s_protected_label = 'Защищен';
$s_create_user_button = 'Создать';
$s_hide_disabled = 'Скрыть заблокированные';
$s_filter_button = 'Фильтровать';
$s_default_filter = 'Фильтр по умолчанию';
$s_create_filter_link = 'Создать постоянную ссылку';
$s_create_short_link = 'Создать короткую ссылку (TinyURL)';
$s_filter_permalink = 'Постоянная ссылка на ваш фильтр:';
$s_manage_users_link = 'Пользователи';
$s_manage_projects_link = 'Проекты';
$s_manage_custom_field_link = 'Настраиваемые поля';
$s_manage_global_profiles_link = 'Глобальные профили';
$s_manage_plugin_link = 'Плагины';
$s_permissions_summary_report = 'Текущая матрица прав';
$s_manage_config_link = 'Настройка';
$s_manage_threshold_config = 'Ограничения процесса';
$s_manage_email_config = 'Почтовые уведомления';
$s_manage_workflow_config = 'Переходы состояний процесса';
$s_manage_workflow_graph = 'График рабочего процесса';
$s_manage_tags_link = 'Теги';
$s_create_new_account_link = 'Создать учётную запись';
$s_projects_link = 'Проекты';
$s_documentation_link = 'Документация';
$s_new_accounts_title = 'Новые учетные записи';
$s_1_week_title = 'за неделю';
$s_never_logged_in_title = 'Никогда не входившие в систему';
$s_prune_accounts = 'Удалить учетные записи';
$s_hide_inactive = 'Скрыть неактивные';
$s_show_disabled = 'Показать отключенные';
$s_manage_accounts_title = 'Управление пользователями';
$s_p = 'з';
$s_date_created = 'Создана';
$s_last_visit = 'Посл. вход';
$s_last_visit_label = 'Последний вход:';
$s_edit_user_link = 'Изменить учётную запись';
$s_search_user_hint = 'Имя пользователя, настоящее имя или эл. почта';
$s_separate_list_items_by = '(отделяйте пункты списка с помощью «%1$s»)';
$s_config_all_projects = 'Примечание: эти значения влияют на ВСЕ ПРОЕКТЫ.';
$s_config_project = 'Примечание: эти значения влияют только на проект %1$s.';
$s_colour_coding = 'В таблице используются следующие цветовые обозначения:';
$s_colour_project = 'Значение для проекта имеет приоритет перед другими.';
$s_colour_global = 'Все значения для проекта имеют приоритет перед настройками по умолчанию.';
$s_issue_reporter = 'Инициатор';
$s_issue_handler = 'Ответственный';
$s_users_added_bugnote = 'Комментаторы';
$s_category_assigned_to = 'Владелец категории';
$s_email_notify_users = 'Любой пользователь с уровнем доступа';
$s_change_configuration = 'Изменить настройки';
$s_message = 'Уведомление';
$s_default_notify = 'Установка флагов уведомления по умолчанию в';
$s_action_notify = 'Установка флагов уведомления для действия в';
$s_notify_defaults_change_access = 'Право изменения уведомлений по умолчанию';
$s_notify_actions_change_access = 'Право изменения уведомлений:';
$s_revert_to_system = 'Удалить все настройки проекта';
$s_revert_to_all_project = 'Удалить настройки, специфические для проекта';
$s_non_existent = 'несуществующий';
$s_current_status = 'Текущее состояние';
$s_next_status = 'Следующее состояние';
$s_workflow = 'Допустимые переходы';
$s_workflow_thresholds = 'Пороги, влияющие на процесс';
$s_threshold = 'Порог';
$s_status_level = 'Состояние';
$s_alter_level = 'Кто может изменять это значение';
$s_validation = 'Проверка потока';
$s_comment = 'Комментарий к проверке';
$s_superfluous = 'Стрелка от состояния к нему же подразумевается, и её не нужно указывать явно';
$s_unreachable = 'Вы не можете переводить задачу в это состояние';
$s_no_exit = 'Вы не можете изменить состояние этой задачи';
$s_access_levels = 'Уровни доступа';
$s_access_change = 'Минимальный уровень доступа для перевода в состояние';
$s_desc_bug_submit_status = 'Состояние для новых задач';
$s_desc_bug_reopen_status = 'Состояние для переоткрытых задач';
$s_desc_bug_resolved_status_threshold = 'Состояние, когда задача считается решённой';
$s_desc_bug_closed_status_threshold = 'Состояние, когда задача считается закрытой';
$s_workflow_change_access_label = 'Кто может изменять процесс:';
$s_access_change_access_label = 'Кто может изменять уровни доступа:';
$s_default_not_in_flow = 'Состояние по умолчанию для %1$s не указано среди последующих состояний %2$s. Оно будет игнорироваться.';
$s_allowed_access_levels = 'Разрешено для любого пользователя с уровнем доступа';
$s_assign_issue = 'Назначить задачу';
$s_allow_reporter_close = 'Разрешить инициатору закрывать задачу';
$s_allow_reporter_reopen = 'Разрешить инициатору переоткрывать задачу';
$s_set_status_assigned = 'Устанавливать состояние при назначении ответственного';
$s_edit_others_bugnotes = 'Редактировать комментарии других';
$s_edit_own_bugnotes = 'Редактировать собственные комментарии';
$s_delete_others_bugnotes = 'Удалять комментарии других';
$s_delete_own_bugnotes = 'Удалять свои комментарии';
$s_change_view_state_own_bugnotes = 'Изменить просмотр состояний своих комментариев';
$s_limit_access = 'Предоставить инициатору доступ только к собственным задачам';
$s_submit_status = 'Состояние для новых задач';
$s_assigned_status = 'Состояние для автоназначаемых задач';
$s_resolved_status = 'Состояние для решенных задач';
$s_readonly_status = 'Состояние для перевода задачи в режим "только для чтения"';
$s_reopen_status = 'Состояние для переоткрытой задачи';
$s_reopen_resolution = 'Вид решения для переоткрытой задачи';
$s_limit_view_unless_threshold_option = 'Просмотр проблем других пользователей (если не установлен, доступ к проблемам, о которых сообщается, которые обрабатываются или отслеживаются пользователем будет ограничен)';
$s_export_issues = 'Экспортировать задачи';
$s_config_delete_sure = 'Вы уверены, что хотите удалить настройки для %1$s в проекте %2$s?';
$s_delete_config_button = 'Удалить настройки';
$s_configuration_report = 'Отчет о конфигурации';
$s_database_configuration = 'Конфигурация базы данных';
$s_configuration_option = 'Параметр конфигурации';
$s_configuration_option_type = 'Тип';
$s_configuration_option_value = 'Значение';
$s_all_users = 'Все пользователи';
$s_set_configuration_option = 'Установить параметр';
$s_delete_config_sure_msg = 'Вы уверены, что хотите удалить этот параметр?';
$s_configuration_corrupted = 'Конфигурация в базе данных нарушена.';
$s_set_configuration_option_action_create = 'Создать параметр';
$s_set_configuration_option_action_edit = 'Изменение параметра конфигурации';
$s_set_configuration_option_action_clone = 'Клонировать параметр';
$s_set_configuration_option_action_view = 'Просмотр параметра конфигурации';
$s_show_all_complex = 'Показать значение для всех параметров "%1$s"';
$s_plugin = 'Плагин';
$s_plugins_installed = 'Установленные плагины';
$s_plugins_available = 'Доступные плагины';
$s_plugins_missing = 'Отсутствующие или неверные плагины';
$s_plugins_missing_description = 'Плагины в списке ниже зарегистрированы, но их исходный код не может быть найден или загружен из «%s».<br>Попробуйте переустановить или удалить их.';
$s_plugin_description = 'Описание';
$s_plugin_problem_description = 'Описание проблемы';
$s_plugin_author = 'Автор: %1$s';
$s_plugin_url = 'Веб-сайт:';
$s_plugin_depends = 'Зависимости';
$s_plugin_no_depends = 'Нет зависимостей';
$s_plugin_priority = 'Очерёдность';
$s_plugin_protected = 'Защищён';
$s_plugin_actions = 'Действия';
$s_plugin_install = 'Установить';
$s_plugin_upgrade = 'Обновить';
$s_plugin_uninstall = 'Удалить';
$s_plugin_manual_fix = 'Требуется ручное вмешательство';
$s_plugin_uninstall_message = 'Вы уверены, что хотите удалить плагин «%1$s»?';
$s_plugin_remove_message = 'Вы действительно хотите удалить недействительный или отсутствующий плагин «%1$s»?';
$s_plugin_key_label = 'Ключ:';
$s_plugin_key_met = 'плагин готов';
$s_plugin_key_unmet = 'неустановленные зависимости';
$s_plugin_key_dated = 'устаревшие зависимости';
$s_plugin_key_upgrade = 'необходимо обновление';
$s_plugin_invalid_description = 'Неверный плагин';
$s_plugin_invalid_status_message = 'Следующие обязательные свойства не определены в классе подключаемого плагина: %s.';
$s_plugin_missing_description = 'Отсутствующий плагин';
$s_plugin_missing_status_message = 'Плагин установлен в базе данных Mantis, но не может быть загружен.';
$s_plugin_missing_class_description = 'Отсутствует базовый класс плагина';
$s_plugin_missing_class_status_message = 'Был найден каталог для этого подключаемого модуля, но внутри него не было соответствующего кода подключаемого модуля. Убедитесь, что имя каталога соответствует базовому имени плагина (с учетом регистра)';
$s_project_added_msg = 'Проект добавлен...';
$s_category_added_msg = 'Категория добавлена...';
$s_category_deleted_msg = 'Категория удалена...';
$s_category_delete_confirm_msg = 'Вы уверены, что хотите удалить категорию "%1$s"?';
$s_delete_category_button = 'Удалить категорию';
$s_edit_project_category_title = 'Изменение категорий проекта';
$s_update_category_button = 'Изменить категорию';
$s_category_updated_msg = 'Категория изменена...';
$s_create_first_project = 'Создайте проект для ведения задач.';
$s_add_subproject_title = 'Добавить подпроект';
$s_project_deleted_msg = 'Проект успешно удалён...';
$s_project_delete_msg = 'Вы уверены, что хотите удалить проект "%1$s"?<br>Это также удалит прикрепленные сообщения о жалобах: %2$s';
$s_project_delete_button = 'Удалить проект';
$s_edit_project_title = 'Изменить проект';
$s_project_name = 'Название проекта';
$s_project_name_label = 'Название проекта:';
$s_view_status = 'Видимость';
$s_public = 'публичная';
$s_private = 'приватная';
$s_update_project_button = 'Изменить проект';
$s_delete_project_button = 'Удалить проект';
$s_copy_from = 'Скопировать из';
$s_copy_to = 'Скопировать в';
$s_categories_and_version_title = 'Категории и версии';
$s_categories = 'Категории';
$s_add_category_button = 'Добавить категорию';
$s_add_and_edit_category_button = 'Добавить и редактировать категорию';
$s_versions = 'Версии';
$s_add_version_button = 'Добавить версию';
$s_add_and_edit_version_button = 'Добавить и редактировать версию';
$s_actions = 'Действия';
$s_version = 'Версия';
$s_version_label = 'Версия:';
$s_timestamp = 'Дата';
$s_subprojects = 'Подпроекты';
$s_add_subproject = 'Добавить как подпроект';
$s_create_new_subproject_link = 'Создать новый подпроект';
$s_unlink_link = 'Разорвать связь';
$s_show_global_users = 'Показывать участников с глобальным доступом';
$s_hide_global_users = 'Скрывать участников с глобальным доступом';
$s_review_changes = 'Проверка изменений';
$s_review_changes_confirmation = 'Точно применяем эти изменения?';
$s_review_changes_empty = 'Изменения не выбраны';
$s_add_project_title = 'Добавление проекта';
$s_upload_file_path = 'Путь для загружаемых файлов';
$s_add_project_button = 'Добавить проект';
$s_projects_title = 'Проекты';
$s_projects_title_label = 'Проекты';
$s_name = 'Название';
$s_project_updated_msg = 'Проект изменен...';
$s_version_added_msg = 'Версия добавлена...';
$s_version_deleted_msg = 'Версия удалена...';
$s_version_delete_sure = 'Вы уверены, что хотите удалить версию "%1$s"?';
$s_delete_version_button = 'Удалить версию';
$s_edit_project_version_title = 'Изменение версии проекта';
$s_update_version_button = 'Изменить версию';
$s_released = 'Выпущена';
$s_not_released = 'Пока не выпущена';
$s_scheduled_release = 'Запланировано на выпуск';
$s_obsolete = 'Устарела';
$s_version_updated_msg = 'Версия изменена...';
$s_account_delete_protected_msg = 'Учётная запись защищена. Удаление невозможно.';
$s_account_deleted_msg = 'Учётная запись удалена...';
$s_delete_account_sure_msg = 'Вы уверены, что хотите удалить учётную запись «%1$s»?';
$s_notify_user = 'Уведомить пользователя об этих изменениях';
$s_accounts_pruned_msg = 'Все пользователи, ни разу не входившие в систему, чьи учетные записи заведены более недели назад, удалены';
$s_prune_accounts_button = 'Очистить';
$s_confirm_account_pruning = 'Вы уверены, что хотите удалить старые неиспользовавшиеся учетные записи?';
$s_edit_user_title = 'Изменение сведений о пользователе';
$s_account_unlock_button = 'Разблокировать учётную запись';
$s_reset_password_button = 'Сбросить пароль';
$s_delete_user_button = 'Удалить учётную запись';
$s_impersonate_user_button = 'Открыть сайт от лица этого пользователя';
$s_reset_password_msg = 'Сброс пароля приводит к отправке по электронной почте ссылки на страницу подтверждения.';
$s_reset_password_msg2 = 'Сброс пароля приводит к установке пустого пароля.';
$s_show_all_users = 'Все';
$s_users_unused = 'Неиспользуемые';
$s_users_new = 'Новые';
$s_email_not_unique = 'Адрес электронной почты связан с по крайней мере одной другой учётной записью пользователя';
$s_account_reset_msg = 'По адресу электронной почты указанного пользователя отправлен запрос подтверждения. С его помощью пользователь сможет изменить пароль.';
$s_account_reset_msg2 = 'Пароль к учётной записи установлен пустым...';
$s_account_unlock_msg = 'Учётная запись разблокирована.';
$s_manage_user_updated_msg = 'Учётная запись изменена ...';
$s_email_user_updated_subject = 'Учётная запись обновлена';
$s_email_user_updated_msg = 'Ваша учётная запись была обновлена администратором. Список изменений находится ниже. Вы можете обновить подробности и настройки вашей учётной записи в любое время, перейдя по ссылке:';
$s_main_link = 'Главная';
$s_view_bugs_link = 'Список задач';
$s_report_bug_link = 'Создать задачу';
$s_changelog_link = 'Журнал';
$s_roadmap_link = 'План';
$s_summary_link = 'Тема';
$s_account_link = 'Мой аккаунт';
$s_users_link = 'Пользователи';
$s_manage_link = 'Управление';
$s_edit_news_link = 'Изменение новостей';
$s_docs_link = 'Документация';
$s_logout_link = 'Выход';
$s_my_view_link = 'Обзор';
$s_invite_users = 'Пригласить участников';
$s_my_view_title_unassigned = 'Неназначенные';
$s_my_view_title_recent_mod = 'Недавно измененные';
$s_my_view_title_reported = 'Созданные мной';
$s_my_view_title_assigned = 'Назначенные мне, нерешенные';
$s_my_view_title_resolved = 'Решенные';
$s_my_view_title_monitored = 'Отслеживаемые мной';
$s_my_view_title_feedback = 'Ожидающие обратной связи от меня';
$s_my_view_title_verify = 'Ожидающие подтверждения решения от меня';
$s_my_view_title_my_comments = 'Задачи, которые я комментировал';
$s_news_added_msg = 'Новость добавлена...';
$s_news_deleted_msg = 'Удаление новости...';
$s_delete_news_sure_msg = 'Вы уверены, что хотите удалить эту новость?';
$s_delete_news_item_button = 'Удалить новость';
$s_edit_news_title = 'Изменение новости';
$s_headline = 'Заголовок';
$s_body = 'Сообщение';
$s_update_news_button = 'Изменить новость';
$s_add_news_title = 'Добавление новости';
$s_post_to = 'Поместить в';
$s_post_news_button = 'Добавить новость';
$s_edit_or_delete_news_title = 'Изменение или удаление новостей';
$s_edit_post = 'Изменить новость';
$s_delete_post = 'Удалить новость';
$s_select_post = 'Выбор новости';
$s_news_updated_msg = 'Новость изменена...';
$s_back_link = 'Назад';
$s_file_uploaded_msg = 'Файл успешно загружен';
$s_upload_file_title = 'Загрузить файл';
$s_title = 'Название';
$s_project_file_deleted_msg = 'Файл проекта удалён';
$s_confirm_file_delete_msg = 'Вы уверены, что хотите удалить файл "%1$s"?';
$s_filename = 'Имя файла';
$s_filename_label = 'Имя файла:';
$s_file_update_button = 'Заменить файл';
$s_file_delete_button = 'Удалить файл';
$s_project_documentation_title = 'Документация проекта';
$s_user_documentation = 'Пользовательская документация';
$s_project_documentation = 'Документация проекта';
$s_add_file = 'Добавить файл';
$s_project_document_updated = 'Проект изменен';
$s_project_user_added_msg = 'Пользователь успешно добавлен в проект.';
$s_project_removed_user_msg = 'Пользователь удалён из проекта.';
$s_remove_user_sure_msg = 'Вы уверены, что хотите удалить пользователя «%1$s» из «%2$s»?';
$s_remove_user_button = 'Удалить пользователя';
$s_remove_all_users_sure_msg = 'Вы уверены, что хотите удалить всех пользователей этого проекта?';
$s_remove_all_users_button = 'Удалить всех пользователей';
$s_add_user_title = 'Добавление пользователя в проект';
$s_add_user_button = 'Добавить пользователя';
$s_project_selection_title = 'Выбор проекта';
$s_remove_link = 'Удалить';
$s_remove_all_link = 'Удалить всех';
$s_remove_project_user_title = 'Удалить доступ пользователей из проекта';
$s_modify_project_user_title = 'Изменить доступ пользователей в проекте';
$s_updated_user_msg = 'Пользователь изменен';
$s_must_enter_category = 'Нужно выбрать категорию.';
$s_must_enter_severity = 'Нужно указать влияние.';
$s_must_enter_reproducibility = 'Нужно указать воспроизводимость';
$s_must_enter_summary = 'Нужно указать тему.';
$s_must_enter_description = 'Нужно описать подробности';
$s_report_more_bugs = 'Создать ещё задачу';
$s_submission_thanks_msg = 'Спасибо за информацию!';
$s_simple_report_link = 'Простая форма';
$s_enter_report_details_title = 'Введите данные задачи';
$s_required = 'Поле, обязательное для заполнения';
$s_select_category = 'Укажите категорию';
$s_select_reproducibility = 'Укажите воспроизводимость';
$s_select_severity = 'Укажите степень влияния';
$s_or_fill_in = 'Или заполните';
$s_assign_to = 'Назначить';
$s_additional_information = 'Дополнительные сведения';
$s_submit_report_button = 'Создать задачу';
$s_check_report_more_bugs = 'отметьте, если собираетесь вводить несколько задач';
$s_report_stay = 'Продолжить создание задач';
$s_selected_project = 'Выбранный проект';
$s_valid_project_msg = 'Нужно выбрать допустимый проект.';
$s_signup_done_title = 'Учётная запись зарегистрирована.';
$s_password_emailed_msg = 'Поздравляем! Вы успешно зарегистрированы. Для проверки адреса вам выслано подтверждающее письмо. Переход по ссылке, высланной в письме, активирует вашу учётную запись.';
$s_no_reponse_msg = 'У вас есть семь дней для подтверждения учётной записи. Если за это время вы не активируете её, зарегистрированная учётная запись может быть удалена.';
$s_signup_captcha_request_label = 'Введите код в точности как в прямоугольнике справа:';
$s_signup_captcha_play = 'Воспроизвести код капчи как аудио';
$s_signup_captcha_refresh = 'Сформировать новый код';
$s_signup_info = 'После заполнения этой формы и проверки ваших ответов, на указанный вами адрес будет выслано подтверждающее сообщение.<br />Используя ссылку, полученную в этом сообщении, вы сможете активировать свою учётную запись. Если вы не активируете учётную запись в течение 7 дней, она может быть удалена.<br />Чтобы получить подтверждающее сообщение, необходимо указать работоспособный адрес.';
$s_signup_title = 'Регистрация';
$s_signup_button = 'Зарегистрироваться';
$s_no_password_request = 'Ваш пароль управляется другой системой. Свяжитесь со своим системным администратором.';
$s_edit_site_settings_title = 'Изменить настройки сервера';
$s_save_settings_button = 'Сохранить настройки';
$s_site_settings_title = 'Настройки сервера';
$s_system_info_link = 'Сведения о системе';
$s_site_settings_link = 'Настройки сервера';
$s_site_settings_updated_msg = 'Настройки сервера успешно изменены';
$s_summary_title = 'Тема';
$s_summary_advanced_link = 'Графики';
$s_by_project = 'По проекту';
$s_by_status = 'По состоянию';
$s_by_date = 'За период в днях';
$s_by_severity = 'По влиянию';
$s_by_resolution = 'По решению';
$s_by_category = 'По категориям';
$s_by_priority = 'По приоритету';
$s_by_developer = 'По участникам';
$s_by_reporter = 'По инициаторам';
$s_reporter_by_resolution = 'Решения по инициаторам';
$s_reporter_effectiveness = 'Эффективность инициаторов';
$s_developer_by_resolution = 'Решения по участникам';
$s_percentage_fixed = '% решенных';
$s_percentage_errors = '% ошибочных';
$s_errors = 'Ошибочные';
$s_opened = 'Открыта';
$s_resolved = 'Решена';
$s_total = 'Всего';
$s_balance = 'Баланс';
$s_most_active = 'Наиболее активные';
$s_score = 'Очки';
$s_days = 'Дней';
$s_time_stats = 'Длительность решения задач, дней';
$s_longest_open_bug = 'Дольше всего открытая задача';
$s_longest_open = 'Дольше всего открытая';
$s_average_time = 'Среднее время';
$s_total_time = 'Общее время';
$s_developer_stats = 'Статистика участников';
$s_reporter_stats = 'Статистика инициаторов';
$s_orct = '(открытых/решенных/закрытых/всего)';
$s_summary_header = 'открытые/решённые/закрытые/всего/коэффициент решённых/коэффициент';
$s_summary_notice_filter_is_applied = 'Применён фильтр';
$s_any = 'любые';
$s_all = 'все';
$s_show = 'Выводить по';
$s_changed = 'Выделять измененные за, ч:';
$s_viewing_bugs_title = 'Список задач';
$s_updated = 'Изменена';
$s_sticky = 'Выводить приклеенные';
$s_sort = 'Сортировать по';
$s_issue_id = 'Задача №';
$s_recently_visited = 'Недавние';
$s_priority_abbreviation = 'П';
$s_note_user_id = 'Комментарий от';
$s_filter_match_type = 'Тип соответствия';
$s_filter_match_all = 'Все условия';
$s_filter_match_any = 'Любое условие';
$s_none = 'пустые';
$s_current = 'текущий';
$s_search = 'Поиск';
$s_view_prev_link = 'Пред.';
$s_view_next_link = 'След.';
$s_prev = 'Пред.';
$s_next = 'След.';
$s_first = 'Первая';
$s_last = 'Последняя';
$s_start_date_label = 'С:';
$s_end_date_label = 'По:';
$s_use_date_filters = 'Фильтровать по дате регистрации';
$s_use_last_updated_date_filters = 'Фильтровать по дате последнего обновления';
$s_yes = 'Да';
$s_no = 'Нет';
$s_open_filters = 'Изменить фильтр';
$s_or_unassigned = 'Или неназначенные';
$s_ok = 'OK';
$s_select_all = 'Выбрать все';
$s_use_query = 'Использовать фильтр';
$s_delete_query = 'Удалить фильтр';
$s_query_deleted = 'Фильтр "%s" удалён';
$s_save_query = 'Сохранить текущий фильтр';
$s_reset_query = 'Очистить фильтр';
$s_query_name = 'Название фильтра';
$s_query_name_label = 'Название фильтра:';
$s_query_exists = 'Подобный фильтр, по видимому, уже существует.';
$s_query_dupe_name = 'Другому фильтру уже присвоено это название. Пожалуйста, выберите другое название для этого фильтра.';
$s_query_blank_name = 'Фильтры нельзя сохранять без названия. Перед сохранением назовите фильтр.';
$s_query_name_too_long = 'Вы не можете сохранить фильтр с именем больше 64 символов. Пожалуйста задайте покороче имя фильтра.';
$s_query_store_error = 'При сохранении этого фильтра произошла ошибка.';
$s_open_queries = 'Управление фильтрами';
$s_query_delete_msg = 'Вы уверены, что хотите удалить фильтр "%1$s"?';
$s_edit_filter = 'Изменение фильтра';
$s_owner = 'Владелец';
$s_update_filter = 'Обновить фильтр';
$s_current_project = 'Текущий проект';
$s_stored_project = 'Сохраненный проект';
$s_available_filter_for_project = 'Доступные фильтры для проекта';
$s_manage_filter_page_title = 'Управление фильтрами';
$s_manage_filter_edit_page_title = 'Изменение фильтра';
$s_apply_filter_button = 'Применить';
$s_temporary_filter = 'Временный фильтр';
$s_set_as_persistent_filter = 'Установить постоянным фильтром';
$s_view_simple_link = 'Простой вид';
$s_product_build = 'Сборка продукта';
$s_bug_assign_to_button = 'Назначить:';
$s_bug_status_to_button = 'Перевести в:';
$s_reopen_bug_button = 'Переоткрыть';
$s_attached_files = 'Вложенные файлы';
$s_publish = 'Опубликовать';
$s_browser_does_not_support_audio = 'Ваш браузер не поддерживает тег audio.';
$s_browser_does_not_support_video = 'Ваш браузер не поддерживает тег video.';
$s_bug_view_title = 'Просмотр задачи';
$s_no_users_monitoring_bug = 'Никто из пользователей не отслеживает задачу.';
$s_users_monitoring_bug = 'Отслеживающие';
$s_monitoring_user_list = 'Пользователи';
$s_no_users_sponsoring_bug = 'Никто из пользователей не спонсирует задачу.';
$s_users_sponsoring_bug = 'Пользователи, спонсирующие задачу';
$s_sponsors_list = 'Спонсоры';
$s_total_sponsorship_amount = 'Всего к оплате = %1$s';
$s_add_custom_field_button = 'Создать настраиваемое поле';
$s_delete_custom_field_button = 'Удалить настраиваемое поле';
$s_delete_custom_field_everywhere = 'Удалить настраиваемое поле отовсюду';
$s_update_custom_field_button = 'Изменить настраиваемое поле';
$s_add_existing_custom_field = 'Добавить существующее настраиваемое поле';
$s_edit_custom_field_title = 'Изменение настраиваемого поля';
$s_custom_field = 'Поле';
$s_custom_field_label = 'Поле:';
$s_custom_fields_setup = 'Настраиваемые поля';
$s_custom_field_name = 'Название';
$s_custom_field_project_count = 'Исп. в проектах';
$s_custom_field_type = 'Тип';
$s_custom_field_possible_values = 'Допустимые значения';
$s_custom_field_default_value = 'Значение по умолчанию';
$s_custom_field_valid_regexp = 'Регулярное выражение';
$s_custom_field_access_level_r = 'Доступ на чтение';
$s_custom_field_access_level_rw = 'Доступ на запись';
$s_custom_field_length_min = 'Наименьшая длина';
$s_custom_field_length_max = 'Наибольшая длина';
$s_custom_field_filter_by = 'Добавить в фильтр';
$s_custom_field_display_report = 'Показывать при создании задачи';
$s_custom_field_display_update = 'Показывать при изменении задачи';
$s_custom_field_display_resolved = 'Показывать при решении задачи';
$s_custom_field_display_closed = 'Показывать при закрытии задачи';
$s_custom_field_require_report = 'Обязательно при создании';
$s_custom_field_require_update = 'Обязательно при изменении';
$s_custom_field_require_resolved = 'Обязательно при решении';
$s_custom_field_require_closed = 'Обязательно при закрытии';
$s_link_custom_field_to_project_title = 'Привязка настраиваемых полей к проектам';
$s_link_custom_field_to_project_button = 'Привязать настраиваемое поле';
$s_linked_projects_label = 'Связанные проекты:';
$s_custom_field_sequence = 'Последовательность';
$s_custom_field_sequence_label = 'Последовательность';
$s_custom_field_type_enum_string = '0:строка,1:целое,2:вещественное,3:перечисление,4:адрес е-mail,5:галочка,6:список,7:список с мультивыбором,8:дата,9:переключатель,10:текстовая область';
$s_confirm_used_custom_field_deletion = 'Поле "%1$s" сейчас связано как минимум с одним проектом. Если продолжить, все значения для этого поля будут безвозвратно удалены. Такое действие отменить невозможно. Если вы не хотите удалять это поле, нажмите кнопку \'Назад\' своего браузера.  Для удаления нажмите кнопку, показанную ниже.';
$s_confirm_custom_field_deletion = 'Вы уверены, что хотите удалить поле "%1$s" и все связанные с ним значения?';
$s_field_delete_button = 'Удалить поле';
$s_confirm_custom_field_unlinking = 'Вы уверены, что хотите отвязать настраиваемое поле "%1$s" от проекта? Значения не будут удалены до тех пор, пока само настраиваемое поле не будет удалено.';
$s_field_remove_button = 'Удалить поле';
$s_hide_status = 'Скрыть состояние';
$s_filter_closed = 'Закрытые';
$s_filter_resolved = 'Решенные';
$s_hide_closed = 'Скрыть закрытые';
$s_hide_resolved = 'Скрыть решенные';
$s_and_above = 'И след.';
$s_advanced_filters = 'Расширенные фильтры';
$s_simple_filters = 'Простые фильтры';
$s_monitored_by = 'Отслеживают';
$s_attachments = 'вложения';
$s_bytes = 'байт';
$s_kib = 'КиБ';
$s_attachment_missing = 'Вложение отсутствует';
$s_attachment_count = 'Количество вложений';
$s_view_attachments_for_issue = 'Открыть %1$d вложение (я, й) для задачи #%2$d';
$s_warning_update_custom_field_type = '<strong>Внимание:</strong> Пользовательское поле «%1$s» уже имеет сохранённые значения. Изменение типа поля может привести к ошибкам и неожиданным последствиям, если существующие значения не соответствуют новому типу поля. Чтобы продолжить, нажмите кнопку ниже';
$s_phpmailer_language = 'ru';
$s_sponsors = 'Оплачивающих - %1$d';
$s_sponsorship_added = 'Оплата добавлена';
$s_sponsorship_updated = 'Оплата изменена';
$s_sponsorship_deleted = 'Оплата отменена';
$s_sponsorship_paid = 'Уплачено';
$s_sponsorship_more_info = 'Порядок оплаты задач';
$s_sponsorship_total = 'Сумма к оплате';
$s_changelog = 'Журнал изменений';
$s_changelog_empty = 'Журнал изменений пуст';
$s_changelog_empty_manager = 'Журнал изменений пуст. Задачи указываются в журнале, когда в проекте настроены версии, и при закрытии задач указывается «исправлено в версии».';
$s_roadmap = 'План — дорожная карта';
$s_resolved_progress = '%1$d из %2$d задач решено.';
$s_roadmap_empty = 'Нет информации по плану развития';
$s_roadmap_empty_manager = 'План-дорожная карта пуст. Задачи включаются в план, когда в проекте настроены версии, а в задачах указывается «целевая версия».';
$s_http_auth_realm = 'Вход в MantisBT';
$s_bug = 'Задача';
$s_bugs = 'задач';
$s_add_new_relationship = 'Создать связь';
$s_this_bug = 'Текущая задача';
$s_relationship_added = 'Связь добавлена';
$s_relationship_deleted = 'Связь удалена';
$s_no_relationship = 'нет связи';
$s_relationship_replaced = 'Связь заменена';
$s_replace_relationship_button = 'Заменить';
$s_relationship_with_parent = 'Связь с родительской задачей';
$s_delete_relationship_sure_msg = 'Вы уверены, что хотите убрать эту связь?';
$s_relationship_warning_blocking_bugs_not_resolved = 'Не все зависимые задачи решены или закрыты.';
$s_relationship_warning_blocking_bugs_not_resolved_2 = '<strong>Внимание:</strong> не все потомки этой задачи решены или закрыты.<br />До <strong>решения/закрытия</strong> родительской задачи, все дочерние задачи должны быть решены или закрыты.';
$s_create_child_bug_button = 'Клонировать';
$s_bug_cloned_to = 'Задача клонирована в';
$s_bug_created_from = 'Задача создана из';
$s_copy_from_parent = 'Скопировать расширенные данные из родительской задачи';
$s_copy_notes_from_parent = 'Скопировать заметки к задаче';
$s_copy_attachments_from_parent = 'Скопировать вложения';
$s_with = 'с';
$s_relation_graph = 'Граф связей';
$s_dependency_graph = 'Граф зависимостей';
$s_vertical = 'Вертикальный';
$s_horizontal = 'Горизонтальный';
$s_view_issue = 'Просмотр задачи';
$s_show_summary = 'Показать описание';
$s_hide_summary = 'Скрыть описание';
$s_perm_rpt_capability = 'Возможность';
$s_view = 'Посмотреть';
$s_issues = 'Задачи';
$s_report_issue = 'Создать задачу';
$s_update_issue = 'Измененить задачи';
$s_monitor_issue = 'Отслеживать задачу';
$s_handle_issue = 'Взять в работу';
$s_move_issue = 'Перенести задачу';
$s_delete_issue = 'Удалить задачу';
$s_reopen_issue = 'Переоткрыть задачу';
$s_view_private_issues = 'Просмотр приватных задач';
$s_update_readonly_issues = 'Изменение задач, доступных только для чтения';
$s_update_issue_status = 'Изменение состояния задачи';
$s_set_view_status = 'Установка видимости при добавлении новой задачи или заметки';
$s_update_view_status = 'Изменение видимости существующей задачи или заметки';
$s_view_issue_revisions = 'Просмотреть исправления проблемы или примечания';
$s_drop_issue_revisions = 'Отпустите проблему или отметьте исправления';
$s_set_sticky = 'Прилепить/отклеить проблему';
$s_show_list_of_users_monitoring_issue = 'Показать список пользователей, отслеживающих задачу';
$s_add_users_monitoring_issue = 'Добавить отслеживания к проблеме';
$s_remove_users_monitoring_issue = 'Убрать отслеживающих задачу';
$s_notes = 'Комментарии';
$s_add_notes = 'Добавление комментариев';
$s_view_private_notes = 'Просмотр других приватных комментариев';
$s_news = 'Новости';
$s_view_private_news = 'Просмотр приватных новостей';
$s_manage_news = 'Управление новостями';
$s_view_list_of_attachments = 'Просмотр списка вложений';
$s_download_attachments = 'Загрузка вложений';
$s_delete_attachments = 'Удаление вложений';
$s_delete_attachment_sure_msg = 'Вы уверены, что хотите удалить это вложение?';
$s_upload_issue_attachments = 'Добавление вложения к задаче';
$s_filters = 'Фильтры';
$s_save_filters = 'Сохранение фильтров';
$s_save_filters_as_shared = 'Сохранение фильтров в качестве общих';
$s_use_saved_filters = 'Использование сохраненных фильтров';
$s_create_project = 'Создание проекта';
$s_delete_project = 'Удаление проекта';
$s_manage_project = 'Управление проектом';
$s_manage_user_access_to_project = 'Управление доступом пользователей к проекту';
$s_automatically_included_in_private_projects = 'Автоматическое включение в приватные проекты';
$s_project_documents = 'Документация проектов';
$s_view_project_documents = 'Просмотр документации проектов';
$s_upload_project_documents = 'Добавление документации проектов';
$s_link_custom_fields_to_projects = 'Привязка настраиваемых полей к проектам';
$s_sponsorships = 'Оплата';
$s_view_sponsorship_details = 'Просмотр сведений об оплате';
$s_view_sponsorship_total = 'Просмотр итоговых сумм оплаты';
$s_sponsor_issue = 'Предложить оплату задачи';
$s_assign_sponsored_issue = 'Назначение оплачиваемой задачи';
$s_handle_sponsored_issue = 'Обработка оплачиваемой задачи';
$s_others = 'Прочие';
$s_see_email_addresses_of_other_users = 'Просмотр адресов e-mail других пользователей';
$s_send_reminders = 'Отправка напоминаний';
$s_receive_reminders = 'Получать напоминания';
$s_add_profiles = 'Добавление профилей';
$s_notify_of_new_user_created = 'Уведомление о создании нового пользователя';
$s_email_notification = 'Уведомление по электронной почте';
$s_status_changed_to = 'При изменении состояния на';
$s_email_on_deleted = 'Извещать при удалении';
$s_email_on_sponsorship_changed = 'Извещать при изменении оплаты';
$s_email_on_relationship_changed = 'Извещать при изменении связей';
$s_email_on_updated = 'Сообщить об обновлении';
$s_email_on_monitor = 'Электронная почта, когда участник начинает мониторинг';
$s_view_tags = 'Просмотр тегов, прикрепленных к багу';
$s_attach_tags = 'Прикрепите теги к багу';
$s_detach_tags = 'Отключить теги от бага';
$s_detach_own_tags = 'Отключить теги, прикрепленные одним и тем же участникам';
$s_create_new_tags = 'Создать новые метки';
$s_edit_tags = 'Редактировать имена и описания меток';
$s_edit_own_tags = 'Редактировать метки, созданные тем же пользователем';
$s_loading = 'Загрузка...';
$s_between_date = 'Между';
$s_on_or_before_date = 'На дату или до';
$s_before_date = 'До';
$s_after_date = 'После';
$s_on_or_after_date = 'На дату или после';
$s_from_date = 'С';
$s_to_date = 'До';
$s_on_date = 'На';
$s_wiki = 'Вики';
$s_tags = 'Метки';
$s_tag_details = 'Сведения о метке: %1$s';
$s_tag_id = 'ID метки';
$s_tag_name = 'Название';
$s_tag_creator = 'Автор';
$s_tag_created = 'Дата создания';
$s_tag_updated = 'Последнее изменение';
$s_tag_description = 'Описание метки';
$s_tag_statistics = 'Статистика использования';
$s_tag_update = 'Обновить метку: %1$s';
$s_tag_update_return = 'Назад';
$s_tag_update_button = 'Изменить метку';
$s_tag_delete_button = 'Удалить метку';
$s_tag_delete_message = 'Вы уверены, что хотите удалить эту метку?';
$s_tag_existing = '<Метки>';
$s_tag_none_attached = 'Нет связанных меток.';
$s_tag_attach = 'Добавить';
$s_tag_attach_long = 'Добавить метки';
$s_tag_attach_failed = 'Невозможно добавить метку.';
$s_tag_detach = 'Убрать \'%1$s\'';
$s_tag_separate_by = '(разделять \'%1$s\')';
$s_tag_invalid_name = 'Недопустимое имя метки.';
$s_tag_create_denied = 'В доступе на создание метки отказано.';
$s_tag_attach_denied = 'Нет разрешения помечать объекты.';
$s_tag_filter_default = 'Помеченные задачи (%1$s)';
$s_tag_history_attached = 'Метка добавлена';
$s_tag_history_detached = 'Метка убрана';
$s_tag_history_renamed = 'Метка переименована';
$s_tag_related = 'Связанные метки';
$s_tag_related_issues = 'Общие задачи (%1$s)';
$s_tag_stats_attached = 'Прикрепленные задачи: %1$s';
$s_tag_create = 'Создать метку';
$s_show_all_tags = 'Все';
$s_time_tracking_billing_link = 'Учёт времени';
$s_time_tracking = 'Учет времени';
$s_time_tracking_time_spent = 'Затраченное время:';
$s_time_tracking_get_info_button = 'Получить сведения об учете времени';
$s_time_tracking_cost_per_hour = 'Цена / час';
$s_time_tracking_cost_per_hour_label = 'Цена / Час:';
$s_time_tracking_cost = 'Цена';
$s_time_tracking_cost_label = 'Цена:';
$s_total_time_for_issue = 'Общее время задачи = %1$s';
$s_time_tracking_stopwatch_start = 'Запустить';
$s_time_tracking_stopwatch_stop = 'Остановить';
$s_time_tracking_stopwatch_reset = 'Сбросить';
$s_access_denied = 'В доступе отказано.';
$s_manage_columns_config = 'Отображаемые столбцы';
$s_all_columns_title = 'Все имеющиеся столбцы';
$s_csv_columns_title = 'Столбцы CSV';
$s_view_issues_columns_title = 'Просмотр задач';
$s_print_issues_columns_title = 'Печать задач';
$s_excel_columns_title = 'Столбцы Excel';
$s_update_columns_as_global_default = 'Обновление столбцов, как глобальное, по умолчанию для всех проектов';
$s_update_columns_for_current_project = 'Обновить столбцы для текущего проекта';
$s_update_columns_as_my_default = 'Обновление колонок по умолчанию для всех проектов';
$s_reset_columns_configuration = 'Сбросить конфигурацию столбцов';
$s_copy_columns_from = 'Скопировать столбцы из';
$s_copy_columns_to = 'Скопировать столбцы в';
$s_due_date = 'Дата окончания';
$s_overdue = 'Просрочено';
$s_overdue_since = 'Просрочено с %1$s';
$s_overdue_one_day = 'Просрочено менее чем за сутки';
$s_overdue_days = 'Просрочено через %1$d дн.';
$s_view_account_title = 'Информация о пользователе';
$s_manage_user = 'Изменить учётную запись';
$s_label = '%1$s:';
$s_install_information = 'Установка MantisBT';
$s_database_information = 'Информация о базе данных MantisBT';
$s_path_information = 'Путь к MantisBT';
$s_mantisbt_database_statistics = 'Статистика базы данных MantisBT';
$s_php_version = 'Версия PHP';
$s_adodb_version = 'Версия ADOdb';
$s_database_driver = 'Драйвер базы данных';
$s_database_version_description = 'Версия база данных, описание';
$s_month_january = 'января';
$s_month_february = 'февраля';
$s_month_march = 'марта';
$s_month_april = 'апреля';
$s_month_may = 'мая';
$s_month_june = 'июня';
$s_month_july = 'июля';
$s_month_august = 'августа';
$s_month_september = 'сентября';
$s_month_october = 'октября';
$s_month_november = 'ноября';
$s_month_december = 'декабря';
$s_timeline_issue_created = '<span class="username">%1$s</span> создал задачу <span class="issue_id">%2$s</span>';
$s_timeline_issue_file_added = '<span class="username">%1$s</span> прикрепил файл <em>%3$s</em> к задаче <span class="issue_id">%2$s</span>';
$s_timeline_issue_file_deleted = '<span class="username">%1$s</span> удалил файл <em>%3$s</em> из задачи <span class="issue_id">%2$s</span>';
$s_timeline_issue_note_created = '<span class="username">%1$s</span> прокомментировал задачу <span class="issue_id">%2$s</span>';
$s_timeline_issue_monitor = '<span class="username">%1$s</span> начал следить за задачей <span class="issue_id">%2$s</span>';
$s_timeline_issue_unmonitor = '<span class="username">%1$s</span> прекратил следить за задачей <span class="issue_id">%2$s</span>';
$s_timeline_issue_tagged = '<span class="username">%1$s</span> прикрепил к задаче <span class="issue_id">%2$s</span> метку <span class="tag_name">%3$s</span>';
$s_timeline_issue_untagged = '<span class="username">%1$s</span> удалил метку <span class="tag_name">%3$s</span> из задачи <span class="issue_id">%2$s</span>';
$s_timeline_issue_resolved = '<span class="username">%1$s</span> отметил задачу <span class="issue_id">%2$s</span> решенной';
$s_timeline_issue_closed = '<span class="username">%1$s</span> закрыл задачу <span class="issue_id">%2$s</span>';
$s_timeline_issue_reopened = '<span class="username">%1$s</span> переоткрыл задачу <span class="issue_id">%2$s</span>';
$s_timeline_issue_assigned = '<span class="username">%1$s</span> назначил <span class="username">%3$s</span> на задачу <span class="issue_id">%2$s</span>';
$s_timeline_issue_assigned_to_self = '<span class="username">%1$s</span> назначил себе задачу <span class="issue_id">%2$s</span>';
$s_timeline_issue_unassigned = '<span class="username">%1$s</span> отменил назначение задачи <span class="issue_id">%2$s</span>';
$s_timeline_no_activity = 'Действий за этот период не обнаружено.';
$s_timeline_title = 'Активность';
$s_timeline_more = 'Показать ещё...';
$s_missing_error_string = 'Отсутствует строка ошибки: %1$s';
$MANTIS_ERROR[ERROR_GENERIC] = 'При выполнении действия произошла ошибка. Возможно, об этом стоит сообщить вашему местному администратору.';
$MANTIS_ERROR[ERROR_SQL] = 'Обнаружена ошибка SQL .';
$MANTIS_ERROR[ERROR_REPORT] = 'Во введенной информации обнаружены ошибки.';
$MANTIS_ERROR[ERROR_NO_FILE_SPECIFIED] = 'Не указан файл.';
$MANTIS_ERROR[ERROR_FILE_DISALLOWED] = 'Этот тип файлов запрещён.';
$MANTIS_ERROR[ERROR_NO_DIRECTORY] = 'Каталог не существует. Проверьте настройки проекта.';
$MANTIS_ERROR[ERROR_DUPLICATE_PROJECT] = 'Проект с таким названием уже существует.';
$MANTIS_ERROR[ERROR_EMPTY_FIELD] = 'Обязательное поле \'%1$s\' не заполнено. Пожалуйста, проверьте правильность заполнения.';
$MANTIS_ERROR[ERROR_INVALID_FIELD_VALUE] = 'Неверное значение для \'%1$s\'';
$MANTIS_ERROR[ERROR_PROTECTED_ACCOUNT] = 'Эта учётная запись защищена. Вы не можете выполнить запрашиваемое действие, пока защита применена к учётной записи.';
$MANTIS_ERROR[ERROR_ACCESS_DENIED] = 'Доступ запрещён.';
$MANTIS_ERROR[ERROR_UPLOAD_FAILURE] = 'Загрузка файла не удалась. Файл недоступен для MantisBT. Проверьте настройки проекта.';
$MANTIS_ERROR[ERROR_FILE_TOO_BIG] = 'Загрузка файла не удалась. Скорее всего, размер файла был больше, чем разрешенный в данной конфигурации PHP.';
$MANTIS_ERROR[ERROR_FILE_NAME_TOO_LONG] = 'Имя файла %1$s слишком длинное.';
$MANTIS_ERROR[ERROR_GPC_VAR_NOT_FOUND] = 'Не найден параметр "%1$s", обязательный для этой страницы.';
$MANTIS_ERROR[ERROR_USER_NAME_NOT_UNIQUE] = 'Это регистрационное имя пользователя уже задействовано. Пожалуйста, вернитесь назад и выберите другое.';
$MANTIS_ERROR[ERROR_USER_EMAIL_NOT_UNIQUE] = 'Этот адрес электронной почты уже используется. Пожалуйста, вернитесь назад и выберите другой.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_NOT_FOUND] = 'Параметр конфигурации \'%1$s\' не найден.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_CANT_BE_SET_IN_DB] = 'Параметр конфигурации \'%1$s\' нельзя устанавливать в базе данных. Его следует установить в config_inc.php.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_BAD_SYNTAX] = 'Не удалось установить настройки параметра «%1$s»: %2$s';
$MANTIS_ERROR[ERROR_LANG_STRING_NOT_FOUND] = 'Строка «%1$s» не найдена.';
$MANTIS_ERROR[ERROR_BUGNOTE_NOT_FOUND] = 'Комментарий не найден.';
$MANTIS_ERROR[ERROR_DB_FIELD_NOT_FOUND] = 'Поле базы данных «%1$s» не найдено.';
$MANTIS_ERROR[ERROR_HANDLER_ACCESS_TOO_LOW] = 'У ответственного недостаточно прав для обработки задач с таким состоянием.';
$MANTIS_ERROR[ERROR_PROJECT_HIERARCHY_DISABLED] = 'Иерархия проектов (подпроекты) отключена.';
$MANTIS_ERROR[ERROR_PROJECT_NOT_FOUND] = 'Проект "%1$s" не найден.';
$MANTIS_ERROR[ERROR_PROJECT_NAME_NOT_UNIQUE] = 'Проект с таким названием уже существует. Пожалуйста, вернитесь назад и введите другое название.';
$MANTIS_ERROR[ERROR_PROJECT_NAME_INVALID] = 'Недопустимое название проекта. Названия проектов не должны быть пустыми.';
$MANTIS_ERROR[ERROR_PROJECT_RECURSIVE_HIERARCHY] = 'Это действие зациклит иерархию подпроектов.';
$MANTIS_ERROR[ERROR_PROJECT_SUBPROJECT_DUPLICATE] = 'Проект «%1$s» уже является подпроектом «%2$s».';
$MANTIS_ERROR[ERROR_PROJECT_SUBPROJECT_NOT_FOUND] = 'Проект «%1$s» не является подпроектом «%2$s».';
$MANTIS_ERROR[ERROR_USER_BY_NAME_NOT_FOUND] = 'Пользователь с именем "%1$s" не найден.';
$MANTIS_ERROR[ERROR_USER_BY_ID_NOT_FOUND] = 'Пользователь с id "%1$d" не найден.';
$MANTIS_ERROR[ERROR_USER_BY_EMAIL_NOT_FOUND] = 'Пользователь с электронной почтой "%1$s" не найден.';
$MANTIS_ERROR[ERROR_USER_BY_REALNAME_NOT_FOUND] = 'Пользователь с настоящим именем "%1$s" не найден.';
$MANTIS_ERROR[ERROR_AUTH_INVALID_COOKIE] = 'Параметры входа, сохранённые в вашем браузере, недопустимы. Может быть, ваша учётная запись удалена?';
$MANTIS_ERROR[ERROR_USER_PREFS_NOT_FOUND] = 'Не удаётся найти настройки для этого пользователя.';
$MANTIS_ERROR[ERROR_NEWS_NOT_FOUND] = 'Новость на найдена.';
$MANTIS_ERROR[ERROR_USER_CREATE_PASSWORD_MISMATCH] = 'Пароли не совпадают.';
$MANTIS_ERROR[ERROR_USER_CURRENT_PASSWORD_MISMATCH] = 'Текущий пароль неверен.';
$MANTIS_ERROR[ERROR_GPC_ARRAY_EXPECTED] = 'Для %1$s ожидался массив, а получена строка.';
$MANTIS_ERROR[ERROR_GPC_ARRAY_UNEXPECTED] = 'Для %1$s ожидалась строка, а получен массив.';
$MANTIS_ERROR[ERROR_GPC_NOT_NUMBER] = 'Для %1$s ожидается число.';
$MANTIS_ERROR[ERROR_BUG_NOT_FOUND] = 'Задача %1$d не найдена.';
$MANTIS_ERROR[ERROR_FILTER_NOT_FOUND] = 'Фильтр %1$s не найден.';
$MANTIS_ERROR[ERROR_EMAIL_INVALID] = 'Недопустимый адрес электронной почты.';
$MANTIS_ERROR[ERROR_EMAIL_DISPOSABLE] = 'Не допускается использование одноразовых адресов электронной почты.';
$MANTIS_ERROR[ERROR_USER_PROFILE_NOT_FOUND] = 'Профиль не найден.';
$MANTIS_ERROR[ERROR_FILE_NOT_ALLOWED] = 'Загрузка файлов этого типа запрещена.';
$MANTIS_ERROR[ERROR_FILE_DUPLICATE] = 'Это дубликат файла. Пожалуйста, сначала удалите первый файл.';
$MANTIS_ERROR[ERROR_FILE_INVALID_UPLOAD_PATH] = 'Недопустимый путь для загрузки файлов.  Каталог не существует или недоступен для записи веб-сервером.';
$MANTIS_ERROR[ERROR_FILE_NO_UPLOAD_FAILURE] = 'Файл не загружен. Пожалуйста, перед запуском загрузки вернитесь и выберите файл.';
$MANTIS_ERROR[ERROR_FILE_MOVE_FAILED] = 'Загружаемый файл не удаётся переместить в каталог для файлов. Каталог не существует или недоступен для записи веб-сервером.';
$MANTIS_ERROR[ERROR_FILE_NOT_FOUND] = 'Вложение с идентификатором «%1$d» не найдено.';
$MANTIS_ERROR[ERROR_BUG_DUPLICATE_SELF] = 'Нельзя cделать задачу дублем самой себя.';
$MANTIS_ERROR[ERROR_BUG_REVISION_NOT_FOUND] = 'Редакция задачи не найдена.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_NOT_FOUND] = 'Настраиваемое поле не найдено.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_NAME_NOT_UNIQUE] = 'Это дублирующееся имя.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_NAME_INVALID] = 'Недопустимое имя настраиваемого поля «%1$s»: использование запятых запрещено. См. раздел «Локализация имен настраиваемых полей» в Руководстве администратора в качестве временного решения.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_IN_USE] = 'Это поле ещё используется как минимум в одном проекте.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_INVALID_VALUE] = 'Недопустимое значение поля "%1$s".';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_INVALID_DEFINITION] = 'Недопустимое определение настраиваемого поля.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_INVALID_PROPERTY] = 'Недопустимое свойство настраиваемого поля (%1$s).';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_NOT_LINKED_TO_PROJECT] = 'Настраиваемое поле "%1$s" (id %2$s) не связано с текущим активным проектом.';
$MANTIS_ERROR[ERROR_LDAP_AUTH_FAILED] = 'LDAP авторизация не удалась.';
$MANTIS_ERROR[ERROR_LDAP_SERVER_CONNECT_FAILED] = 'Не удалось соединиться с LDAP-сервером.';
$MANTIS_ERROR[ERROR_LDAP_UPDATE_FAILED] = 'Обновление записи в LDAP не удалось.';
$MANTIS_ERROR[ERROR_LDAP_USER_NOT_FOUND] = 'Сведения о пользователе на LDAP-сервере не найдены.';
$MANTIS_ERROR[ERROR_LDAP_UNABLE_TO_SET_MIN_TLS] = 'Не удалось установить минимальную версию TLS на LDAP-сервере.';
$MANTIS_ERROR[ERROR_LDAP_UNABLE_TO_STARTTLS] = 'Не удаётся запустить StartTLS на LDAP-сервере.';
$MANTIS_ERROR[ERROR_DB_CONNECT_FAILED] = 'Соединение с базой данных не удалось. Получена ошибка - #%1$d: %2$s.';
$MANTIS_ERROR[ERROR_DB_QUERY_FAILED] = 'Запрос к базе данных не удался. Получена ошибка - #%1$d: %2$s, запрос: %3$s.';
$MANTIS_ERROR[ERROR_DB_SELECT_FAILED] = 'Выборка из базы данных данных не удалась. От БД получена ошибка #%1$d: %2$s.';
$MANTIS_ERROR[ERROR_DB_IDENTIFIER_TOO_LONG] = 'Идентификатор в базе данных "%1$s" слишком длинный. Попытайтесь уменьшить длину g_db_table_prefix/suffix';
$MANTIS_ERROR[ERROR_CATEGORY_DUPLICATE] = 'Категория с этим названием уже существует.';
$MANTIS_ERROR[ERROR_NO_COPY_ACTION] = 'Действие по копированию не определено.';
$MANTIS_ERROR[ERROR_CATEGORY_NOT_FOUND] = 'Категория не найдена.';
$MANTIS_ERROR[ERROR_CATEGORY_NOT_FOUND_FOR_PROJECT] = 'В проекте "%2$s" категория "%1$s" не обнаружена.';
$MANTIS_ERROR[ERROR_CATEGORY_CANNOT_DELETE_DEFAULT] = 'Эту категорию нельзя удалить, так как она определена как "Категория для перемещений по умолчанию".';
$MANTIS_ERROR[ERROR_CATEGORY_CANNOT_DELETE_HAS_ISSUES] = 'Категория "%1$s" не может быть удалена, потому что она связана с одним или более вопросами.';
$MANTIS_ERROR[ERROR_VERSION_DUPLICATE] = 'Версия с таким названием уже существует.';
$MANTIS_ERROR[ERROR_VERSION_NOT_FOUND] = 'Версия \'%1$s\' не найдена.';
$MANTIS_ERROR[ERROR_USER_NAME_INVALID] = 'Недопустимое регистрационное имя пользователя. В именах могут содержаться только латинские буквы, цифры, пробелы, дефисы, точки, знаки плюса и подчеркивания.';
$MANTIS_ERROR[ERROR_USER_REAL_NAME_INVALID] = 'Настоящее имя пользователя недопустимо.';
$MANTIS_ERROR[ERROR_USER_DOES_NOT_HAVE_REQ_ACCESS] = 'У пользователя нет нужного уровня доступа.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_NOT_ENABLED] = 'Поддержка оплаты не включена.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_NOT_FOUND] = 'Оплата %1$d не найдена.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_AMOUNT_TOO_LOW] = 'Сумма оплаты (%1$s) меньше необходимого минимума (%2$s).';
$MANTIS_ERROR[ERROR_SPONSORSHIP_HANDLER_ACCESS_LEVEL_TOO_LOW] = 'Уровень доступа ответственного недостаточен для обработки оплачиваемых задач.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_ASSIGNER_ACCESS_LEVEL_TOO_LOW] = 'Доступ запрещён: для назначения оплачиваемых задач нужен более высокий уровень доступа.';
$MANTIS_ERROR[ERROR_SPONSORSHIP_SPONSOR_NO_EMAIL] = 'У плательщика нет адреса электронной почты. Пожалуйста, обновите свой профиль.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_INVALID] = 'Параметру конфигурации "%1$s" присвоено недопустимое значение "%2$s".';
$MANTIS_ERROR[ERROR_BUG_READ_ONLY_ACTION_DENIED] = 'Действие невозможно, потому что задача \'%1$d\' доступна только для чтения.';
$MANTIS_ERROR[ERROR_BUG_RESOLVE_DEPENDANTS_BLOCKING] = 'Эта задача не может быть решена, пока все зависимые задачи не решены. Если вы не видите зависимых задач, запросите доступ у администратора.';
$MANTIS_ERROR[ERROR_RELATIONSHIP_NOT_FOUND] = 'Связь не найдена.';
$MANTIS_ERROR[ERROR_RELATIONSHIP_ACCESS_LEVEL_TO_DEST_BUG_TOO_LOW] = 'Доступ запрещён: для задачи %1$d нужен больший уровень доступа.';
$MANTIS_ERROR[ERROR_RELATIONSHIP_SAME_BUG] = 'Задачу нельзя связать с собой.';
$MANTIS_ERROR[ERROR_SIGNUP_NOT_MATCHING_CAPTCHA] = 'Хэш-код подтверждения не совпадает. Пожалуйста, повторите попытку.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_NOT_ENABLED] = 'Возможность восстановления пароля недоступна.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_NO_EMAIL_SPECIFIED] = 'Для сброса пароля вы должны предоставить адрес электронной почты.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_NOT_MATCHING_DATA] = 'Указанные сведения не совпадают ни с одной существующей учётной записью!';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_CONFIRM_HASH_INVALID] = 'Адрес для подтверждения недопустим или уже использован. Пожалуйста, повторите регистрацию.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_MAX_IN_PROGRESS_ATTEMPTS_REACHED] = 'Достигнуто предельное число незавершенных запросов. Пожалуйста, свяжитесь с администратором';
$MANTIS_ERROR[ERROR_USER_CHANGE_LAST_ADMIN] = 'Вы не можете удалить или понизить в ранге последнюю административную учётную запись. Чтобы выполнить запрошенное действие, вам необходимо сначала создать другую административную учётную запись.';
$MANTIS_ERROR[ERROR_PAGE_REDIRECTION] = 'Ошибка перенаправления страницы, убедитесь, что за пределами блока PHP (&lt;?php ?&gt;) в config_inc.php или настроенных файлах custom_*.php нет пробелов.';
$MANTIS_ERROR[ERROR_TAG_NOT_FOUND] = 'Не удалось найти метку "%1$s".';
$MANTIS_ERROR[ERROR_TAG_DUPLICATE] = 'Метка с именем "%1$s" уже существует.';
$MANTIS_ERROR[ERROR_TAG_NAME_INVALID] = 'Имя метки «%1$s» является недопустимым.';
$MANTIS_ERROR[ERROR_TAG_NOT_ATTACHED] = 'Эта метка не связана с этой задачей.';
$MANTIS_ERROR[ERROR_TAG_ALREADY_ATTACHED] = 'Эта метка уже связана c этой задачеq.';
$MANTIS_ERROR[ERROR_TOKEN_NOT_FOUND] = 'Признак не может быть найден.';
$MANTIS_ERROR[ERROR_EVENT_UNDECLARED] = 'Событие "%1$s" ещё не было объявлено.';
$MANTIS_ERROR[ERROR_PLUGIN_NOT_REGISTERED] = 'Плагин "%1$s" не зарегистрирован.';
$MANTIS_ERROR[ERROR_PLUGIN_NOT_LOADED] = 'Плагин "%1$s" не загружен, проверьте, пожалуйста, все зависимости.';
$MANTIS_ERROR[ERROR_PLUGIN_ALREADY_INSTALLED] = 'Плагин "%1$s" уже установлен.';
$MANTIS_ERROR[ERROR_PLUGIN_CLASS_NOT_FOUND] = 'В "%2$s" Класс не определен в модуль "%1$s".';
$MANTIS_ERROR[ERROR_PLUGIN_PAGE_NOT_FOUND] = 'Страница "%2$s" плагина "%1$s" не найдена.';
$MANTIS_ERROR[ERROR_PLUGIN_FILE_NOT_FOUND] = 'Файл "%2$s" плагина "%1$s" не найден.';
$MANTIS_ERROR[ERROR_PLUGIN_INSTALL_FAILED] = 'Не удалось установить плагин: %1$s.';
$MANTIS_ERROR[ERROR_PLUGIN_UPGRADE_FAILED] = 'Обновление схемы плагина привело к сбою в блоке #%1$s.';
$MANTIS_ERROR[ERROR_PLUGIN_UPGRADE_NEEDED] = 'Для открытия страницы необходимо обновить плагин «%1$s».';
$MANTIS_ERROR[ERROR_PLUGIN_INVALID_PAGE] = 'Формат страницы плагина "%1$s" не верен. Формат должен быть такого вида "Plugin[/path/to]/page".';
$MANTIS_ERROR[ERROR_PLUGIN_INVALID_FILE] = 'Формат файла плагина "%1$s" не верен. Формат должен быть такого вида "Plugin[/path/to]/file[.ext]".';
$MANTIS_ERROR[ERROR_PLUGIN_GENERIC] = 'Во время выполнения плагина "%2$s" возникла неизвестная ошибка "%1$s".';
$MANTIS_ERROR[ERROR_COLUMNS_DUPLICATE] = 'Поле "%1$s" содержит дублирующийся столбец "%2$s".';
$MANTIS_ERROR[ERROR_COLUMNS_INVALID] = 'Поле «%1$s» содержит недопустимое поле «%2$s».';
$MANTIS_ERROR[ERROR_SESSION_VAR_NOT_FOUND] = 'Сессионный атрибут "%1$s" не найден.';
$MANTIS_ERROR[ERROR_SESSION_NOT_VALID] = 'Ваша сессия стала недействительной.';
$MANTIS_ERROR[ERROR_FORM_TOKEN_INVALID] = 'Некорректный маркер безопасности. Возможно, Вы случайно послали форму дважды?';
$MANTIS_ERROR[ERROR_CRYPTO_MASTER_SALT_INVALID] = 'По соображениям безопасности MantisBT не будет работать, пока не установлено корректное значение для $g_crypto_master_salt в файле config_inc.php или если оно меньше 16 символов.';
$MANTIS_ERROR[ERROR_INVALID_REQUEST_METHOD] = 'Этим методом страница получена быть не может.';
$MANTIS_ERROR[ERROR_INVALID_SORT_FIELD] = 'Недопустимое поле для сортировки.';
$MANTIS_ERROR[ERROR_INVALID_DATE_FORMAT] = 'Неправильный формат даты.';
$MANTIS_ERROR[ERROR_INVALID_RESOLUTION] = 'Решение "%1$s" не допускается для состояния "%2$s".';
$MANTIS_ERROR[ERROR_UPDATING_TIMEZONE] = 'Невозможно изменить часовой пояс.';
$MANTIS_ERROR[ERROR_DEPRECATED_SUPERSEDED] = 'Отключенный функционал: "%1$s", используется "%2$s".';
$MANTIS_ERROR[ERROR_DISPLAY_USER_ERROR_INLINE] = 'Внимание: Система настроена для отображения встроенных ошибок MantisBT (E_USER_ERROR). Выполнение программы будет продолжено. Это может привести к проблемам с целостностью данных системы.';
$MANTIS_ERROR[ERROR_TYPE_MISMATCH] = 'Несоответствие типов данных. Включите отображение подробных сообщений об ошибках для получения дополнительной информации.';
$MANTIS_ERROR[ERROR_BUG_CONFLICTING_EDIT] = 'Эта задача был обновлена другим участником, пожалуйста, вернитесь к этой задаче и подтвердите своё изменение ещё раз.';
$MANTIS_ERROR[ERROR_SPAM_SUSPECTED] = 'Вы достигли разрешенного предела активности на %d событий в течение последних более %d сек. Ваши действия были заблокированы, чтобы избежать спама. Пожалуйста, повторите попытку позже.';
$MANTIS_ERROR[ERROR_FIELD_TOO_LONG] = 'Поле "%1$s" должно быть не длиннее %2$d символов.';
$MANTIS_ERROR[ERROR_API_TOKEN_NAME_NOT_UNIQUE] = 'Имя API token "%s" уже используется. Пожалуйста, вернитесь и выберите другое.';
$MANTIS_ERROR[ERROR_LOGFILE_NOT_WRITABLE] = 'Файл, указанный в переменной $g_log_destination "%s", недоступен для записи.';
$MANTIS_ERROR[ERROR_MONITOR_ACCESS_TOO_LOW] = 'Добавленный пользователь не имеет достаточных прав доступа для отслеживания этой задачи.';
$MANTIS_ERROR[ERROR_USER_TOKEN_NOT_FOUND] = 'Токен пользовательского API с идентификатором «%d» не найден.';
$s_dropzone_default_message = 'Добавьте файлы перетащив их или выделив и вставив.';
$s_dropzone_fallback_message = 'Ваш браузер не поддерживает загрузку файлов через перетаскивание.';
$s_dropzone_file_too_big = 'Файл слишком большой ({{filesize}} MiB). Максимальный размер файла: {{maxFilesize}} MiB.';
$s_dropzone_invalid_file_type = 'Вы не можете загружать файлы с этим расширением.';
$s_dropzone_response_error = 'Сервер ответил с кодом {{statusCode}}.';
$s_dropzone_cancel_upload = 'Отменить загрузку';
$s_dropzone_cancel_upload_confirmation = 'Вы уверены, что хотите отменить эту загрузку?';
$s_dropzone_remove_file = 'Удалить файл';
$s_dropzone_max_files_exceeded = 'Вы не можете загрузить больше файлов.';
$s_dropzone_not_supported = 'Dropzone.js не поддерживает старые браузеры!';
$s_dropzone_multiple_files_too_big = 'Размер следующих файлов превышает максимально разрешённый ({{maxFilesize}} MiB:{{files}})';
$s_dropzone_multiple_filenames_too_long = 'Следующие имена файлов слишком длинные (не более {{maxFilenameLength}} символов):{{files}}';
$s_save = 'Сохранить';
$s_reset = 'Сбросить';
$s_persist = 'Запомнить';
$s_load = 'Загрузить';
$s_apply_changes = 'Применить изменения';
$s_undo = 'Отменить';
$s_edit = 'Изменить';
$s_submit = 'Отправить';
$s_update = 'Изменить';
$s_delete = 'Удалить';
$s_make_default = 'Использовать по умолчанию';
$s_clear_default = 'Очистить по умолчанию';
$s_print = 'Печать';
$s_jump = 'Перейти';
$s_change = 'Изменить';
$s_go_back = 'Назад';
$s_proceed = 'Продолжить';
$s_move = 'Переместить';
$s_close = 'Закрыть';
$s_add = 'Добавить';
$s_login = 'Вход';
