<?php
/** MantisBT - a php based bugtracking system
 *
 * Copyright (C) 2000 - 2002  Kenzaburo Ito - <EMAIL>
 * Copyright (C) 2002 - 2016  MantisBT Team - <EMAIL>
 *
 * MantisBT is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * MantisBT is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with MantisBT.  If not, see <http://www.gnu.org/licenses/>.
 *
 * **********************************************************************
 * ** This file contains translations stored in translatewiki.net.     **
 * ** See https://translatewiki.net/wiki/Project:About for information **
 * ** on copyright/license for translatewiki.net translations.         **
 * **********************************************************************
 * **                                                                  **
 * **                      DO NOT UPDATE MANUALLY                      **
 * **                                                                  **
 * ** To improve a translation please visit https://translatewiki.net  **
 * ** Detailed instructions on how to create or update translations at **
 * ** http://www.mantisbt.org/wiki/doku.php/mantisbt:translationshowto **
 * **********************************************************************
 */
/** Georgian (ქართული)
 * 
 * See the qqq 'language' for message documentation incl. usage of parameters
 * To improve a translation please visit https://translatewiki.net
 *
 * @ingroup Language
 * @file
 *
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */

$s_actiongroup_menu_copy = 'კოპირება';
$s_actiongroup_menu_assign = 'მიმაგრება';
$s_actiongroup_menu_resolve = 'მოგვარებული';
$s_actiongroup_menu_update_priority = 'პრიორიტეტის განახლება';
$s_actiongroup_menu_update_status = 'სტატუსის განახლება';
$s_actiongroup_menu_update_severity = 'სიმძიმის განახლება';
$s_actiongroup_menu_update_view_status = 'ხედვის სტატუსის განახლება';
$s_actiongroup_menu_update_category = 'კატეგორიის განახლება';
$s_actiongroup_menu_update_field = 'განაახლე %1$s';
$s_actiongroup_menu_update_product_version = 'პროდუქტის ვერსიის განახლება';
$s_actiongroup_menu_update_target_version = 'სამიზნე ვერსიის განახლება';
$s_actiongroup_menu_update_fixed_in_version = 'განახლება გასწორებულია ვერსიაში';
$s_actiongroup_menu_update_due_date = 'დედლაინის განახლება';
$s_actiongroup_menu_add_note = 'შენიშვნის დამატება';
$s_actiongroup_menu_attach_tags = 'საკვანძო სიტყვების მიმაგრება';
$s_actiongroup_bugs = 'არჩეული მოთხოვნები';
$s_actiongroup_error_issue_is_readonly = 'მოთხოვნა არის მხოლოდ ნახვის უფლებით.';
$s_all_projects = 'ყველ პროექტი';
$s_move_bugs = 'მოთხოვნის გადატანა';
$s_operation_successful = 'ოპერაცია წარმატებით დასრულდა.';
$s_operation_warnings = 'ოპერაცია დასრულდა გაფრთხილებებით.';
$s_operation_failed = 'ოპერაცია წარუმატებლად დასრულდა';
$s_date_order = 'თარიღით სორტირება';
$s_print_all_bug_page_link = 'ანგარიშის დაბეჭდვა';
$s_csv_export = 'CSV ფაილში ექსპორტი';
$s_excel_export = 'Excel ფაილში ექსპორტი';
$s_login_anonymously = 'ანონიმად შესვლა';
$s_anonymous = 'ანონიმი';
$s_jump_to_bugnotes = 'კომენტრარებზე გადასხვლა';
$s_jump_to_history = 'ისტორიაზე გადასვლა';
$s_display_history = 'ისტორიის ჩვენება';
$s_public_project_msg = 'ეს პროექტი არის საჯარო. აღნიშნულ პროექტზე ყველა მომხმარებელს აქვს დაშვება.';
$s_private_project_msg = 'ეს არის პრივატული პროექტი. მხოლოდ ადმინისტრატორებს და პროექტზე დამატებულ ლმომხმარებლებს აქვთ დაშვება.';
$s_access_level_project = 'პროექტის დაშვების დონე';
$s_view_submitted_bug_link = 'დადასტურებული მოთხოვნის ნახვა %1$s';
$s_assigned_projects = 'მიმაგრებული პროექტები';
$s_assigned_projects_label = 'მიმაგრებული პროექტები';
$s_unassigned_projects_label = 'მიუმაგრებელი პროექტები';
$s_copy_users = 'მომხმარებლების კომპირება';
$s_copy_categories_from = 'კატეგორიების კოპირება (დან)';
$s_copy_categories_to = 'კატეგორიების კოპირება (ში)';
$s_copy_versions_from = 'ვერსიის დაკოპირება (დან)';
$s_copy_versions_to = 'ვერსიის დაკოპირება (ში)';
$s_copy_users_from = 'მომხმარებლების კოპირება (დან)';
$s_copy_users_to = 'მომხმარებლების კომპირება (ში)';
$s_bug_history = 'მოთხოვნების ისტორია';
$s_field = 'ველი';
$s_old_value = 'ძველი მნიშვნელობა';
$s_new_value = 'ახალი მნიშვნელობა';
$s_date_modified = 'შეცვლის თარიღი';
$s_bugnote = 'შენიშვნა';
$s_bugnote_view_state = 'კომენტარის ნახვის სტატუსი';
$s_bug_monitor = 'მოთხოვნა მონიტორინგდება';
$s_bug_end_monitor = 'მოთხოვნის მონიტორინგი დასრულდა';
$s_announcement = 'ანონსი';
$s_bugnote_link_title = 'პირდაპირი ბმული კომენტარზე';
$s_delete_bugnote_button = 'შენიშვნის წაშლა';
$s_delete_bugnote_sure_msg = 'დარწმუნებული ხართ, რომ გსურთ ამ კომენტარის წაშლა?';
$s_bug_relationships = 'ურთიერთობები';
$s_empty_password_sure_msg = 'მომხმარებელს მინიჭებული აქვს ცარიელი პაროლი. დატწმუნებული ხართ თქვენს მოქმედებაში?';
$s_empty_password_button = 'ცარილელი პაროლის გამოყენება';
$s_reauthenticate_title = 'ავტორიზირება';
$s_reauthenticate_message = 'თქვენ იმყოფებით დაცულ გვერდზე და თქვენს სესიას ვადა გაუვიდა. გაგრძელებისთვის გთხოვთ გაიაროთ ავტორიზაცია.';
$s_no_category = '(კატეგორიის გარეშე)';
$s_global_categories = 'გლობალური კატეგორიები';
$s_duplicate_of = 'დუბლიკატი';
$s_has_duplicate = 'აქვს დუბლიკატი';
$s_related_to = 'დაკავშირებულია';
$s_dependant_on = 'მშობელი';
$s_blocks = 'შვილი';
$s_new_bug = 'ახალი მოთხოვნა';
$s_bugnote_added = 'შენიშვნა დამატებულია';
$s_bugnote_edited = 'კომენტარი დარედაქტირდა';
$s_bugnote_deleted = 'კომენტარი წაიშალა';
$s_summary_updated = 'შეჯამება განახლდა';
$s_description_updated = 'აღწერილობა განახლდა';
$s_additional_information_updated = 'დამატებითი ინფორმაცია განახლდა';
$s_steps_to_reproduce_updated = 'რეპროდუცირების ნაბიჯები განახლდა';
$s_file_added = 'ფაილი დაემატა';
$s_file_deleted = 'ფაილი წაიშალა';
$s_bug_deleted = 'მოთხოვნა წაიშალა';
$s_make_private = 'გახდეს პრივატული';
$s_make_public = 'გახდეს საჯარო';
$s_create_new_project_link = 'ახალი პროექტის შექმნა';
$s_opensearch_id_short = '%s იდენტიფიკატორი';
$s_opensearch_text_short = '%s ტექსტი';
$s_opensearch_text_description = '%s: სრული ტექსტის ძებნა';
$s_select_option = '(მონიშვნა)';
$s_bug_actiongroup_access = 'თქვენ არ გაქვთ შესაბამისი დაშვება რომ განახორციელოთ აღნიშნული ქმედება.';
$s_bug_actiongroup_status = 'აღნიშნულ სტატუსზე მოთხოვნის ცვლილება შეუძლებელია';
$s_bug_actiongroup_category = 'აღნიშნული მოთხოვნის ცვლიება შეუძლებელია მოთხოვნილ კატეგორიაზე';
$s_close_bugs_conf_msg = 'დარწმუნებული ხართ რომ გსურთ მოთხოვნის დახურვა?';
$s_delete_bugs_conf_msg = 'დარწმუნებული ხართ რომ გსურთ მოთხოვნის წაშლა?';
$s_move_bugs_conf_msg = 'მოთხოვნის გადატანა';
$s_copy_bugs_conf_msg = 'მოთხოვნის კოპირება';
$s_assign_bugs_conf_msg = 'მოთხოვნის მიმაგრება';
$s_resolve_bugs_conf_msg = 'მოთხოვნის გადაჭრის არჩევა';
$s_priority_bugs_conf_msg = 'მოთხოვნის პრიორიტეტის არჩევა';
$s_status_bugs_conf_msg = 'მოთხოვნის სტატუსის არჩევა';
$s_view_status_bugs_conf_msg = 'მოთხოვნის სტატუსის არჩევა';
$s_category_bugs_conf_msg = 'მოთხოვნის კატეგორიის არჩევა';
$s_due_date_bugs_conf_msg = 'დედლაინის განახლება';
$s_close_group_bugs_button = 'მოთხოვნის დახურვა';
$s_delete_group_bugs_button = 'მოთხოვნების წაშლა';
$s_move_group_bugs_button = 'მოთხოვნის გადატანა';
$s_copy_group_bugs_button = 'მოთხოვნების კოპირება';
$s_assign_group_bugs_button = 'მოთხოვნის მიმაგება';
$s_resolve_group_bugs_button = 'მოთხოვნების გადაწყვეტა';
$s_priority_group_bugs_button = 'პრიორიტეტის განახლება';
$s_status_group_bugs_button = 'სტატუსის განახლება';
$s_category_group_bugs_button = 'კატეგორიის განახლება';
$s_view_status_group_bugs_button = 'ხედვის სტატუსის განახლება';
$s_product_version_group_bugs_button = 'პროდუქტის ვერსიის განახლება';
$s_due_date_group_bugs_button = 'დედლაინის განახლება';
$s_update_severity_button = 'სიმძიმის განახლება';
$s_hide_button = 'აჩვენე მხოლოდ მონიშნულები';
$s_printing_preferences_title = 'აირჩიეთ ველები ბეჭდვისთვის';
$s_printing_options_link = 'ბეჭდვის ოპციები';
$s_bugnote_date = 'შენიშვნის თარიღი';
$s_bugnote_description = 'შენიშვნის აღწერა';
$s_login_error = 'მომხმარებელი შესაძლოა დაბლოკილია ან გათიშული ან მომხმარებლის სახელი/პაროლი არასწორია.';
$s_logged_in_as = 'შესული ხართ როგორც';
$s_prefix_for_deleted_users = 'მომხმარებელი';
$s_administrator = 'ადმინისტრატორი';
$s_myself = 'მე თვითონ';
$s_default_access_level = 'სტანდარტული დაშვების დონე';
$s_issue_status_percentage = 'მოთხოვნის სტატუსის პროცენტი';
$s_access_levels_enum_string = '10:მნახველი,25:რეპორტერი,40:გამნახლებელი,55:დეველოპერი,70:მენეჯერი,90:ადმინისტრატორი';
$s_no_access = 'დაშვება არ არის';
$s_project_status_enum_string = '10:დეველოპმეტი,30:რელიზი,50:სტაბილური,70:მოძველებული';
$s_project_view_state_enum_string = '10:საჯარო,50:კერძო';
$s_view_state_enum_string = '10:საჯარო,50:კერძო';
$s_priority_enum_string = '10:გარეშე,20:დაბალი,30:საშუალო,40:მაღალი,50:სასწრაფო,60:ძალიან სასწრაფო';
$s_severity_enum_string = '10:ფუნქცია,20:ტრივიალური,30:ტექსტი,40:tweak,50:უმნიშვნელო,60:მნიშვნელოვანი,70:ავარიული,80:ბლოკი';
$s_reproducibility_enum_string = '10:ყოველთვის,30:ხანდახან,50:ზოგჯერ,70:არ მიცდია,90:შეუძლებელია რეპროდუცირება,100:N/A';
$s_status_enum_string = '10:ახალი,20:უკუკავშირი,30:აღიარებული,40:დადასტურებული,50:მიმაგრებული,80:გადაწყვეტილი,90:დახურული';
$s_resolution_enum_string = '10:გახსნილი,20:გასწირებული,30:ხელახლა გახსნილი,40:შეუძლებელია რეპროდუცირება,50:შეუძლებელია გასწორება,60:დუბლიკატი,70:არ საჭიროებს ცვლილებას,80:შეჩერებული,90:შეუძლებელია გასწორება';
$s_new_account_subject = 'მომხმარებლის რეგისტრაცია';
$s_new_account_greeting = 'მადლობა რეგისტრაციისთვის. თქვენი მომხმარებლის სახელია "%1$s". რეგისტრაციის დასრულებისთვის მიმართეთ შემდეგ ბმულს და მიუთითეთ სასურველი პაროლი:';
$s_new_account_username = 'მომხმარებლის სახელი:';
$s_new_account_message = 'თუ თქვენ არ მოგითხოვიათ რეგისტრაცია დააიგნორეთ აღნიშნული მესიჯი';
$s_new_account_do_not_reply = 'არ უპასუხოთ აღნიშნულ წერილს';
$s_new_account_email = 'ელ. ფოსტა:';
$s_new_account_signup_msg = 'შემდეგი მომხმარებლის ანგარიში შეიქმნა:';
$s_reset_request_msg = 'ვიღაცამ (შესაძლოა თქვენ) მოითხოვა პაროლის ცვლილება ელ. ფოსტის საშუალებით. თუ თქვენ არ მოფითხოვიათ პაროლის ცვლილება დააიგნორეთ აღნიშნული შეტყობინება. თუ ცვლილება თქვენი მოთხოვნილია მიყევით აღნიშნულ ბმულს პაროლის შესაცვლელად:';
$s_email_notification_title_for_status_bug_new = 'აღნიშNული მოთხოვნა არის კვლავ სტატუსით ახალი';
$s_email_notification_title_for_status_bug_assigned = 'აღნიშნული მოთხოვნა მიენიჭა შესასრულებლად.';
$s_email_notification_title_for_status_bug_resolved = 'აღნიშნული მოთხოვნა გადაიჭრა';
$s_email_notification_title_for_status_bug_closed = 'აღნიშნული მოთხოვნა დაიხურა';
$s_email_notification_title_for_action_bug_assigned = 'აღნიშნული მოთხოვნა მიენიჭა შესასრულებლად.';
$s_email_notification_title_for_action_bug_unassigned = 'აღნიშნული მოთხოვნა მოეხსნა შემსრულებელს.';
$s_email_notification_title_for_action_bug_reopened = 'აღნიშნული მოთხოვნა ხელახლა გაიხსნა.';
$s_email_notification_title_for_action_bug_deleted = 'აღნიშნული მოთხოვნა წაიშალა';
$s_email_notification_title_for_action_bug_updated = 'აღნიშნული მოთხოვნა განახლდა.';
$s_email_notification_title_for_action_sponsorship_added = 'აღნიშნული მოთხოვნა დასპონსორდა.';
$s_email_notification_title_for_action_bugnote_submitted = 'მოთხოვნას დაემატა კომენტარი.';
$s_email_notification_title_for_action_duplicate_of_relationship_added = 'აღნიშნულ მოთხოვნას მიენიჭა %1$s მოთხოვნის დუბლიკატის სტატუსი.';
$s_email_notification_title_for_action_dependant_on_relationship_added = 'აღნიშნულ მოთხოვნას მიენიჭა %1$s მოთხოვნის მშობლის სტატუსი.';
$s_email_notification_title_for_action_blocks_relationship_added = 'აღნიშნულ მოთხოვნას მიენიჭა %1$s მოთხოვნის შვილის სტატუსი.';
$s_email_notification_title_for_action_has_duplicate_relationship_deleted = 'მოთხოვნა %1$s წაიშალა როგორც დუბლიკატი აღნიშნული მოთხოვნის.';
$s_email_notification_title_for_action_relationship_child_resolved = 'დაკავშირებული მოთხოვნა %1$s გადაიჭრა.';
$s_email_notification_title_for_action_relationship_child_closed = 'დაკავშირებული მოთხოვნა %1$s დაიხურა.';
$s_email_notification_title_for_action_monitor = 'მოთხოვნა %1$s მონიტორინგდება %2$s მიერ.';
$s_email_reporter = 'შემტყობინებელი:';
$s_email_handler = 'მიმაგრებულია';
$s_email_project = 'პროექტი';
$s_email_bug = 'მოთხოვნის ID';
$s_email_category = 'კატეგორია';
$s_email_severity = 'სიმძიმე';
$s_email_priority = 'პრიორიტეტი';
$s_email_status = 'სტატუსი';
$s_email_resolution = 'გადაწყვეტა';
$s_email_duplicate = 'დუბლიკატი';
$s_email_fixed_in_version = 'გასწორებულია ვერსიაში';
$s_email_target_version = 'სამიზნე ვერსია';
$s_email_date_submitted = 'გასწორების თარიღი';
$s_email_last_modified = 'ბოლოს შეცვლილი';
$s_email_summary = 'შეჯამება';
$s_email_description = 'აღწერილობა';
$s_email_additional_information = 'დამატებითი ინფორმაცია';
$s_email_steps_to_reproduce = 'რეპროდუცირების ნაბიჯები';
$s_email_tag = 'საკვანძო სიტყვები';
$s_email_due_date = 'დასრულების თარიღი';
$s_account_protected_msg = 'დაცული მომხმარებლის ანგარიში. შეუძლებელია პარამეტრების შეცვლა';
$s_account_removed_msg = 'თქვენი მომხმარებლის ანგარიში წაიშალა';
$s_confirm_delete_msg = 'დარწმუნებული ხართ, რომ გსურთ თქვენი მომხმარებლის წაშლა?';
$s_delete_account_button = 'ანგარიშის წაშლა';
$s_manage_profiles_link = 'პროფილები';
$s_change_preferences_link = 'კონფიგურაცია';
$s_edit_account_title = 'ანგარიშის რედაქტირება';
$s_username = 'მომხმარებლის სახელი';
$s_username_label = 'მომხმარებლის სახელი';
$s_realname = 'ნამდვილი სახელი';
$s_realname_label = 'ნამდვილი სახელი';
$s_email = 'ელექტრონული ფოსტა';
$s_email_label = 'ელექტრონული ფოსტა';
$s_password = 'პაროლი';
$s_new_password = 'ახალი პაროლი';
$s_no_password_change = 'პაროლი კონტროლდება სხვა სისტემის მიერ, შეუძლებელია პაროლის ცვლილება.';
$s_confirm_password = 'დაადასტურეთ პაროლი';
$s_current_password = 'მიმდინარე პაროლი';
$s_access_level = 'დაშვების დონე';
$s_access_level_label = 'დაშვების დონე';
$s_update_user_button = 'მომხმარებლის განახლება';
$s_verify_warning = 'თქვენი მომხმარებლის ანგარიშის ინფორმაცია წარმატებით დადასტურდა.';
$s_verify_change_password = 'სისტემაში ხელახლა შესვლის უფლების მიღებისთვის უნდა მიეთითოს პაროლი';
$s_api_tokens_link = 'API Tokens';
$s_api_token_create_button = 'API Token შექმნა';
$s_api_token_name = 'Token სახელი';
$s_api_token_revoke_button = 'განბლოკვა';
$s_api_token_never_used = 'არასდროს გამოყენებული';
$s_last_used = 'ბოლოს გამოყენებული';
$s_default_project = 'ნაგულისხმევი პროექტი';
$s_refresh_delay = 'განახლების დაყოვნება';
$s_minutes = 'წუთი';
$s_seconds = 'წამი';
$s_bugnote_order_asc = 'ზრდადობით';
$s_bugnote_order_desc = 'კლებადობით';
$s_email_on_new = 'ახლის შემთხვევაში მეილზე გაგზავნა';
$s_email_on_resolved = 'მოთხოვნის გადაწყვეტისას შეტყობინების ელ.ფოსტაზე გაგზავნა';
$s_email_on_closed = 'წაშლისას შეტყობინების ელ.ფოსტაზე გაგზავნა';
$s_email_on_reopened = 'ხელახლა გახსნისას შეტყობინების ელ.ფოსტაზე გაგზავნა';
$s_email_on_bugnote_added = 'კომენტარის დამატებისას ელ.ფოსტაზე გაგზავნა';
$s_email_on_status_change = 'სტატუსის ცვლილებისას შეტყობინების ელ.ფოსტაზე გაგზავნა';
$s_email_on_priority_change = 'პრიორიტეტის ცვლილებისას ელ.ფოსტაზე გაგზავნა';
$s_language = 'ენა';
$s_timezone = 'დროის სარტყელი';
$s_select_profile = 'პროფილის არჩევა';
$s_edit_profile = 'პროფილის რედაქტირება';
$s_delete_profile = 'პროფილის წაშლა';
$s_update_sponsorship_button = 'გადახდის სტატუსის შეცვლა';
$s_own_sponsored = 'ჩემს მიერ დასპონსორებული მოთხოვნები:';
$s_amount = 'თანხა';
$s_sponsor_show = 'ყველას ჩვენება';
$s_account_updated_msg = 'თქვენი მომხმარებლის ანგარიში წარმატებით განახლდა...';
$s_email_updated = 'ელ. ფოსტიც მისამართი წარმატებით განახლდა';
$s_realname_updated = 'ნამდვილი სახელი წარმატებით განახლდა';
$s_password_updated = 'პაროლი წარმატებით განახლდა';
$s_new_bug_title = 'ახალი მოთხოვნა';
$s_feedback_bug_title = 'მოთხოვნაზე უკუკავშირის მოთხოვნა';
$s_confirmed_bug_title = 'მოთხოვნის დადასტურება';
$s_assigned_bug_title = 'მოთხოვნის მიმაგება';
$s_new_bug_button = 'ახალი მოთხოვნა';
$s_confirmed_bug_button = 'მოთხოვნის დადასტურება';
$s_assigned_bug_button = 'მოთხოვნის მიმაგება';
$s_bug_close_msg = 'მოთხოვნა დაიხურა';
$s_close_immediately = 'დაუყუვნებლივ დახურვა';
$s_closed_bug_title = 'მოთხოვნის დახურვა';
$s_bug_deleted_msg = 'მოთხოვნა წაიშალა...';
$s_delete_bug_sure_msg = 'დარწმუნებული ხართ რომ გსურთ მოთხოვნის წაშლა?';
$s_monitor_bug_button = 'მონიტორინგი';
$s_unmonitor_bug_button = 'მონიტორინგის რედაქტირება';
$s_upload_file = 'ფაილის ატვირთვა';
$s_upload_files = 'ფაილების ატვირთვა';
$s_select_file = 'ფაილის არჩევა';
$s_select_files = 'აირჩიეთ ფაილი';
$s_upload_file_button = 'ფაილების ატვირთვა';
$s_upload_files_button = 'ფაილების ატვირთვა';
$s_max_file_size_info = 'მაქსიმალური ზომა: %1$s %2$s';
$s_bug_reopened_msg = 'მოთხოვნა ხელახლა გაიხსნა';
$s_bugnote_add_reopen_button = 'კომენტარის დამატება და მოთხოვნის ხელახლა გახსნა';
$s_resolved_bug_title = 'მოთხოვნის გადაწყვეტა';
$s_resolved_bug_button = 'მოთხოვნის გადაწყვეტა';
$s_bug_resolved_msg = 'მოთხოვნა გადაიჭრა. მიუთითეთ კომენტარი...';
$s_bugnote_add_resolve_button = 'შენიშვნის დამატება';
$s_from = 'დან';
$s_to = 'ვის';
$s_bug_reminder = 'შეხსენება გაგზავნა';
$s_reminder_sent_to = 'შეხსენება გაიგზავნა';
$s_reminder_sent_none = 'შეხსენების გაგზავნის გაგზავნა შეუძლებელია';
$s_bug_send_button = 'გაგზავნა';
$s_reminder = 'შეხსენება';
$s_bug_updated_msg = 'მოთხოვნა წარმატებით განახლდა...';
$s_back_to_bug_link = 'მოთხოვნაზე დაბრუნება';
$s_id = 'იდენტიფიკატორი';
$s_category = 'კატეგორია';
$s_severity = 'სიმძიმე';
$s_reproducibility = 'რეპროდუცირებლობა';
$s_date_submitted = 'გასწორების თარიღი';
$s_last_update = 'ბოლო განახლება';
$s_reporter = 'მოთხოვნის გამომგზავნი';
$s_assigned_to = 'მიმაგრებულია';
$s_priority = 'პრიორიტეტი';
$s_resolution = 'გადაწყვეტა';
$s_status = 'სტატუსი';
$s_duplicate_id = 'დუბლიკატი';
$s_os = 'ოპერავიული სისტემა';
$s_platform = 'პლატფორმა';
$s_os_build = 'ოპერაციული სისტემის ვერსია';
$s_projection = 'პროექცია';
$s_product_version = 'პროდუქტის ვერსია';
$s_fixed_in_version = 'გასწორებულია ვერსიაში';
$s_target_version = 'სამიზნე ვერსია';
$s_votes = 'მიცემული ხმები';
$s_summary = 'შეჯამება';
$s_description = 'აღწერილობა';
$s_steps_to_reproduce = 'რეპროდუცირების ნაბიჯები';
$s_update_information_button = 'განახლების ინფორმაცია';
$s_profile = 'პროფილი';
$s_updating_bug_simple_title = 'მოთხოვნის ინფორმაციის განახლება';
$s_revision = 'რევიზია';
$s_revision_drop = 'ჩამოყრა';
$s_all_revisions = 'ყველა რედაქცია';
$s_back_to_issue = 'მოთხოვნაზე დაბრუნება';
$s_activities_title = 'აქტივობები';
$s_bugnote_attached_files = 'მიმაგრებული ფაილები:';
$s_bugnote_deleted_msg = 'შენიშვნა წარმატებით წაიშალა...';
$s_bug_notes_title = 'შენიშვნები';
$s_edit_bugnote_title = 'შენიშვნის რედაქტირება';
$s_no_bugnotes_msg = 'აღნიშნულ მოთხოვნაზე კომენტარები არ არის.';
$s_add_bugnote_title = 'შენიშვნის დამატება';
$s_add_bugnote_button = 'შენიშვნის დამატება';
$s_closed_bug_button = 'მოთხოვნის დახურვა';
$s_bugnote_updated_msg = 'შენიშვნა წარმატებით განახლდა...';
$s_last_edited = 'ბოლოს დარედაქტირდა';
$s_hide_content = 'კონტენტის დამალვა';
$s_show_content = 'კონტენტის ჩვენება';
$s_memory_usage = 'გამოყენებული მეხიერება: %1$s KiB';
$s_log_page_number = 'ნომერი';
$s_log_page_time = 'შესრულების დრო';
$s_log_page_event = 'ღონისძიება';
$s_please_report = 'გთხოვთ შეატყობინოთ %1$s.';
$s_login_title = 'შესვლა';
$s_save_login = 'დამიმახსოვრე';
$s_secure_session = 'დაცული სესია';
$s_choose_project = 'აირჩიე პროექტი';
$s_signup_link = 'ახალი მომხმარებლის რეგისტრირება';
$s_lost_password_link = 'დაგავიწყდათ პაროლი?';
$s_username_or_email = 'მომხმარებლის სახელი ან ელ. ფოსტის მისამართი';
$s_enter_password = 'შეიყვანეთ პაროლი \'%s\'-თვის';
$s_select_project_button = 'აირჩიეთ პროექტი';
$s_lost_password_title = 'პაროლის ჩამოყრა';
$s_lost_password_done_title = 'პაროლის მესიჯი გაიგზავნა';
$s_lost_password_subject = 'პაროლის ჩამოყრა';
$s_open_and_assigned_to_me_label = 'გადაუჭრელი და ჩემზე მომაგრებული:';
$s_open_and_reported_to_me_label = 'გახსნილი და ჩემს მიერ მოთხოვნილი:';
$s_newer_news_link = 'უახლესი ამბები';
$s_older_news_link = 'ძველი ამბები';
$s_archives = 'დაარქივებული';
$s_rss = 'RSS';
$s_site_information = 'გვერდის ინფორმაცია';
$s_mantis_version = 'MantisBT ვერსია';
$s_schema_version = 'სქემის ვერსია';
$s_site_path = 'Site Path';
$s_core_path = 'Core Path';
$s_plugin_path = 'Plugin Path';
$s_created_user_part1 = 'შექმნილი მომხმარებელი';
$s_create_new_account_title = 'ანგარიშის შექმნა';
$s_verify_password = 'პაროლის ვერიფიკაცია';
$s_enabled = 'ჩართული';
$s_enabled_label = 'ჩართული';
$s_protected = 'დაცულია';
$s_protected_label = 'დაცული';
$s_create_user_button = 'მომხმარებლის შექმნა';
$s_hide_disabled = 'გაუქმებულების დამალვა';
$s_filter_button = 'ფილტრის გამოყენება';
$s_default_filter = 'ნაგულისხმევი ფილტრი';
$s_create_short_link = 'მოკლე ბმულის შექმნა';
$s_manage_users_link = 'მომხმარებლების მართვა';
$s_manage_projects_link = 'პროექტის მენეჯმენტი';
$s_manage_global_profiles_link = 'გლობალური პროფილების მენეჯმენტი';
$s_manage_config_link = 'კონფიგურაციის მენეჯმენტი';
$s_manage_email_config = 'შეტყობინების ელ.ფოსტაზე გაგზავნა';
$s_create_new_account_link = 'ანგარიშის შექმნა';
$s_projects_link = 'პროექტები';
$s_documentation_link = 'დოკუმენტაცია';
$s_new_accounts_title = 'ახალი ანგარიში';
$s_1_week_title = '1 კვირა';
$s_never_logged_in_title = 'არასდროს შემოსულები';
$s_hide_inactive = 'დამალე არააქტიური';
$s_show_disabled = 'გაუქმებულების ჩვენება';
$s_manage_accounts_title = 'მომხმარებლების მენეჯმენტი';
$s_date_created = 'შექმნის თარიღი';
$s_last_visit = 'ბოლო ვიზიტი';
$s_last_visit_label = 'ბოლო ვიზიტი:';
$s_edit_user_link = 'მომხმარებლის რედაქტირება';
$s_issue_reporter = 'მომხმარებელი რომელმაც გამოაგზავნა მოთხოვნა';
$s_issue_handler = 'მომხმარებელი რომელიც ამუშავებს მოთხოვნას';
$s_users_added_bugnote = 'მომხმარებელი რომელმაც დაამატა მოთხოვნას კომენტარი';
$s_category_assigned_to = 'კატეგორიის მფლობელი';
$s_email_notify_users = 'ნებისმიერი მომხმარებელი დაშვების დონით';
$s_change_configuration = 'კონფიგურაციის შენახვა';
$s_message = 'შეტყობინება';
$s_notify_actions_change_access = 'ვის შეუძლია შეტყობინებების შეცვლა:';
$s_revert_to_system = 'ყველა პროექტის სეთინგების წაშლა';
$s_non_existent = 'არ არსებული';
$s_current_status = 'მიმდინარე სტატუსი';
$s_next_status = 'შემდეგი სტატუსი';
$s_workflow = 'სამუშაო ნაკადი';
$s_status_level = 'სტატუსი';
$s_alter_level = 'ვის შეუძლია მნიშვნელობის ცვლივლება';
$s_comment = 'კომენტარის ვალიდაცია';
$s_access_levels = 'დაშვების დონე';
$s_access_change = 'სტატუსის ცვლილებისთვის მინიმალური დაშვების დონე';
$s_desc_bug_submit_status = 'სტატუსი, რომლისთვისაც მინიჭებულია ახალი მოთხოვნა';
$s_workflow_change_access_label = 'ვის შეუძლია ვორკფლოუს შეცვლა:';
$s_access_change_access_label = 'ვის შეუძლია დაშვების დონის შეცვლა:';
$s_assign_issue = 'დავალების მიმაგრება';
$s_edit_others_bugnotes = 'სხვისი შენიშვნების რედაქტირება';
$s_edit_own_bugnotes = 'საკუთარი შენიშვნების კორექტირება';
$s_delete_others_bugnotes = 'სხვისი შენიშვნების წაშლა';
$s_delete_own_bugnotes = 'საკუთარი შენიშვნების წაშლა';
$s_change_view_state_own_bugnotes = 'საკუთარი შენიშვნების ხედვის სტატუსის ცვლილება';
$s_limit_access = 'რეპორტერის თავის მოთხოვნებთან დაშვების შეზღუდვა';
$s_submit_status = 'სტატუსი, რომლისთვისაც მინიჭებულია ახალი მოთხოვნა';
$s_config_delete_sure = 'დარწმუნებული ხართ, რომ გსურთ პარამეტრების წაშლა:';
$s_delete_config_button = 'პარამეტრების წაშლა';
$s_configuration_report = 'კონფიგურაციის რეპორტი';
$s_database_configuration = 'მონაცემთა ბაზის კონფიგურაცია';
$s_configuration_option = 'კონფიგურაციის პარამეტრები';
$s_configuration_option_type = 'ტიპი';
$s_configuration_option_value = 'მნიშვნელობა';
$s_all_users = 'ყველა მომხმარებელი';
$s_set_configuration_option = 'კონფიგურაციის პარამეტრების მინიჭება';
$s_delete_config_sure_msg = 'დარწმუნებული ხართ რომ გსურთ წაშალოთ კონფიგურაციის პარამეტრები?';
$s_set_configuration_option_action_create = 'კონფიგურაციის პარამეტრების შექმნა';
$s_set_configuration_option_action_edit = 'კონფიგურაციის პარამეტრების რედაქტირება';
$s_set_configuration_option_action_clone = 'კონფიგურაციის პარამეტრების კლონირება';
$s_plugin = 'Plugin';
$s_plugins_installed = 'ინსტალირებული Plugin-ები';
$s_plugins_available = 'შესაძლებელი Plugin-ები';
$s_plugin_description = 'აღწერილობა';
$s_plugin_author = 'ავტორი: %1$s';
$s_plugin_url = 'ვებ-გვერდი:';
$s_plugin_priority = 'პრიორიტეტი';
$s_plugin_protected = 'დაცულია';
$s_plugin_actions = 'ქმედებები';
$s_plugin_install = 'ინსტალაცია';
$s_plugin_upgrade = 'განახლება';
$s_plugin_uninstall = 'დეინსტალაცია';
$s_plugin_key_upgrade = 'საჭიროებს განახლებას';
$s_project_added_msg = 'პროექტი წარმატებით დაემატა...';
$s_category_added_msg = 'კატეგორია წარმატებით დაემატა...';
$s_category_deleted_msg = 'კატეგორია წარმატებით წაიშალა...';
$s_category_delete_confirm_msg = 'დარწმუნებული ხართ, რომ გინდათ წაშალოთ "%1$s" კატეგორია?';
$s_delete_category_button = 'კატეგორიის წაშლა';
$s_edit_project_category_title = 'პროექტის კატეგორიის რედაქტირება';
$s_update_category_button = 'კატეგორიის განახლება';
$s_category_updated_msg = 'კატეგორია წარმატებით განახლდა...';
$s_add_subproject_title = 'ქვეპროექტის დამატება';
$s_project_deleted_msg = 'პროექტი წარმატებით წაიშალა';
$s_project_delete_msg = 'დარწმუნებული ხართ რომ გსურთ პროექტის და მასზე მიმაგრებული მოთხოვნების წაშლა?';
$s_project_delete_button = 'პროექტის წაშლა';
$s_edit_project_title = 'პროექტის რედაქტირება';
$s_project_name = 'პროექტის სახელი';
$s_project_name_label = 'პროექტის სახელი:';
$s_view_status = 'ხედვის სტატუსი';
$s_public = 'საჯარო';
$s_private = 'პირადი';
$s_update_project_button = 'პროექტის განახლება';
$s_delete_project_button = 'პროექტის წაშლა';
$s_copy_from = 'კოპირება (დან)';
$s_copy_to = 'კოპირება (ში)';
$s_categories_and_version_title = 'კატეგორიები და ვერსიები';
$s_categories = 'კატეგორიები';
$s_add_category_button = 'კატეგორიის დამატება';
$s_add_and_edit_category_button = 'ლატეგორიის დამატება და რედაქტირება';
$s_versions = 'ვერსიები';
$s_add_version_button = 'ვერსიის დამატება';
$s_add_and_edit_version_button = 'ვერსიის დამატება რედაქტირება';
$s_actions = 'ქმედებები';
$s_version = 'ვერსია';
$s_version_label = 'ვერსია:';
$s_timestamp = 'თარიღი/დრო';
$s_subprojects = 'ქვეპროექტები';
$s_add_subproject = 'ქვეპროექტის დამატება';
$s_create_new_subproject_link = 'ახალი ქვეპროექტის შექმნა';
$s_unlink_link = 'კავშირის მოხსნა';
$s_show_global_users = 'აჩვენე გლობალური დაშვების მქონე მომხმარებლები';
$s_hide_global_users = 'დამალე გლობალური დაშვების მქონე მომხმარებლები';
$s_add_project_title = 'პროექტის დამატება';
$s_upload_file_path = 'ატვირთული ფაილის მისამართი';
$s_add_project_button = 'პროექტის დამატება';
$s_projects_title = 'პროექტები';
$s_projects_title_label = 'პროექტები';
$s_name = 'სახელი';
$s_project_updated_msg = 'პროექტი წარმატებით განახლდა...';
$s_version_added_msg = 'ვერსია წარმატებით დაემატა...';
$s_version_deleted_msg = 'ვერსია წარმატებით წაიშალა...';
$s_version_delete_sure = 'ნამდვილად გსურთ ამ ვერსიის წაშლა?';
$s_delete_version_button = 'ვერსიის წაშლა';
$s_edit_project_version_title = 'პროექტის ვერსიის რედაქტირება';
$s_update_version_button = 'ვერსიის განახლება';
$s_version_updated_msg = 'ვერსია წარმატებით განახლდა...';
$s_account_delete_protected_msg = 'მომხმარებლის ანგარიში დაცულია. შეუძლებელია წაშლა.';
$s_account_deleted_msg = 'მომხმარებლის ანგარიში წაიშალა...';
$s_delete_account_sure_msg = 'დარწმუნებული ხართ, რომ გსურთ ამ მომხმარებლის წაშლა?';
$s_notify_user = 'შეატყობინე მომხარებელს აღნიშნული ცვლილება';
$s_confirm_account_pruning = 'დარწმუნებული ხართ რომ გსურთ ძველი მომხმარებლის ანგარიშების წაშლა, რომლებიც არასდროს შესულან სისტემაში?';
$s_edit_user_title = 'მომხმარებლის რედაქტირება';
$s_account_unlock_button = 'მომხმარებლის ანგარიშზე ბლოკის მოხსნა';
$s_reset_password_button = 'პაროლის აღდგენა';
$s_delete_user_button = 'მომხმარებლის წაშლა';
$s_impersonate_user_button = 'მომხმარებლის ჩატვირთვა';
$s_show_all_users = 'ყველა';
$s_users_unused = 'გამოუყენებელი';
$s_users_new = 'ახალი';
$s_account_unlock_msg = 'მომხმარებლის ანგარიშს ბლოკი მოიხსნა.';
$s_manage_user_updated_msg = 'მომხმარებლის ანგარიში წარმატებით განახლდა...';
$s_email_user_updated_subject = 'მომხმარებლის ანგარიში განახლდა';
$s_email_user_updated_msg = 'თქვენი მომხმარებლის ანგარიში განახლდა ადმინისტრატორის მიერ. ახსნიშნული განახლებების სია მოცემულია ქვემოთ. თქვენ შეგიძლიათ განაახლოთ თქვენი მომხმარებლის ანგარიშის პარამეტრები ნებისმიერ დროს შემდეგი ბმულის გამოყენებით:';
$s_main_link = 'მთავარი';
$s_view_bugs_link = 'მოთხოვნის ნახვა';
$s_report_bug_link = 'მოთხოვნის გაგზავნა';
$s_changelog_link = 'ჟურნალიზაციის შეცვლა';
$s_summary_link = 'შეჯამება';
$s_account_link = 'ჩემი ანგარიში';
$s_users_link = 'მომხმარებლები';
$s_manage_link = 'მართვა';
$s_edit_news_link = 'ახალი ამბების რედაქტირება';
$s_docs_link = 'დოკუმენტები';
$s_logout_link = 'გასვლა';
$s_my_view_link = 'მთავარი';
$s_invite_users = 'მომხმარებლის მოწვევა';
$s_my_view_title_unassigned = 'მოხსნილი';
$s_my_view_title_recent_mod = 'უახლესი შეცვლილები';
$s_my_view_title_reported = 'ჩემს მიერ გაგზავნილი მოთხოვნები';
$s_my_view_title_assigned = 'ჩემზე მიმაგრაბული გადაუჭრელი მოთხოვნები';
$s_my_view_title_resolved = 'მოგვარებული';
$s_my_view_title_monitored = 'ჩემს მიერ დამონიტორინგებული';
$s_delete_news_sure_msg = 'დარწმუნებული ხართ, რომ გსურთ ამ კომენტარის წაშლა?';
$s_edit_news_title = 'ახალი ამბების რედაქტირება';
$s_headline = 'სათაური';
$s_body = 'ტექსტი';
$s_update_news_button = 'ახალი ამბების განახლება';
$s_add_news_title = 'ახალი ამბების დამატება';
$s_edit_or_delete_news_title = 'ახალი ამბების რედაქტირება/წაშლა';
$s_delete_post = 'შეტყობინების წაშლა';
$s_select_post = 'შეტყობინების მონიშვნა';
$s_back_link = 'უკან';
$s_file_uploaded_msg = 'ფაილის ატვირთვა წარმატებით დასრულდა.';
$s_upload_file_title = 'ფაილის ატვირთვა';
$s_title = 'სათაური';
$s_project_file_deleted_msg = 'პროექტის ფაილი წაიშალა.';
$s_confirm_file_delete_msg = 'დარწმუნებული ხართ, რომ გსურთ ამ ფაილის წაშლა?';
$s_filename = 'ფაილის სახელი';
$s_filename_label = 'ფაილის სახელი:';
$s_file_update_button = 'ფაილის განახლება';
$s_file_delete_button = 'ფაილის წაშლა';
$s_project_documentation_title = 'პროექტის დოკუმენტაცია';
$s_user_documentation = 'მომხმარებლის დოკუმენტაცია';
$s_project_documentation = 'პროექტის დოკუმენტაცია';
$s_add_file = 'ფაილის დამატება';
$s_project_document_updated = 'პროექტი წარმატებით განახლდა.';
$s_project_user_added_msg = 'მოხმარებელი წარმატებით დაემატა პროექტს.';
$s_project_removed_user_msg = 'მომხმარებელი წარმატებით წაიშალა პროექტიდან.';
$s_remove_user_sure_msg = 'დარწმუნებული ხართ, რომ გსურთ ამ მომხმარებლის წაშლა?';
$s_remove_user_button = 'მომხმარებლის წაშლა';
$s_remove_all_users_sure_msg = 'დარწმუნებული ხართ რომ გსურთ პროექტიდან ყველა მომხმარებლის წაშლა?';
$s_remove_all_users_button = 'ყველა მომხმარებლის წაშლა';
$s_add_user_title = 'პროექტში მომხმარებლის დამატება';
$s_add_user_button = 'მომხმარებლის დამატება';
$s_project_selection_title = 'პროექტის არჩევა';
$s_remove_link = 'წაშლა';
$s_remove_all_link = 'ყველას წაშლა';
$s_updated_user_msg = 'მომხმარებლის ანგარიში წარმატებით განახლდა.';
$s_must_enter_category = 'თვენ უნდა აირჩიოთ კატეგორია.';
$s_must_enter_severity = 'უნდა აირჩიოთ სიმძიმე.';
$s_must_enter_reproducibility = 'უნდა აირჩიოთ რეპროდუცირებულობა.';
$s_must_enter_summary = 'უნდა მიუთითოთ შემაჯამებელი ტექსტი.';
$s_must_enter_description = 'თქვენ უნდა დაამატოთ აღწერილობა.';
$s_report_more_bugs = 'დამატებითი მოთხოვნების გაგზავნა';
$s_simple_report_link = 'მარტივი რეპორტი';
$s_enter_report_details_title = 'მოთხოვნის დეტალების დამატება';
$s_required = 'სავალდებულოა';
$s_select_category = 'კატეგორიის არჩევა';
$s_select_severity = 'სიმძიმის არჩევა';
$s_assign_to = 'მიმაგრებულია';
$s_additional_information = 'დამატებითი ინფორმაცია';
$s_selected_project = 'არჩეული პროექტი';
$s_valid_project_msg = 'უნდა აირჩიოთ ვალიდური პროექტი';
$s_signup_captcha_refresh = 'ახალი კოდის გენერირება';
$s_signup_title = 'ავტორიზება';
$s_signup_button = 'ავტორიზება';
$s_edit_site_settings_title = 'ვებ გვერდის პარამეტრების რედაქტირება';
$s_save_settings_button = 'პარამეტრების შენახვა';
$s_system_info_link = 'სისტემის ინფორმაცია';
$s_summary_title = 'შეჯამება';
$s_by_project = 'პროექტები მიხედვით';
$s_by_status = 'სტატუსის მიხედვით';
$s_by_date = 'დღეების მიხედვით';
$s_by_severity = 'გადაწყვეტის მიხედვით';
$s_by_category = 'კატეგორიის მიხედვით';
$s_by_priority = 'პრიორიტეტის მიხედვით';
$s_by_reporter = 'მოთხოვნის გამომგზავნის მიხედვით';
$s_percentage_fixed = '% გადაჭრილი';
$s_percentage_errors = '% მცდარი';
$s_errors = 'მცდარი';
$s_opened = 'გახსნილი';
$s_resolved = 'მოგვარებული';
$s_total = 'სულ';
$s_balance = 'ბალანსი';
$s_most_active = 'ყველაზე აქტიური';
$s_score = 'შეფასება';
$s_days = 'დღე';
$s_longest_open_bug = 'დიდი ხნის გადაუჭრელი მოთხოვნა';
$s_longest_open = 'ყველაზე დიდი ხნით გახსნილი';
$s_average_time = 'საშუალო დრო';
$s_total_time = 'საერთო დრო';
$s_orct = '(გახსნილი/გადაწყვეტილი/დახურული/სულ)';
$s_any = 'ნებისმიერი';
$s_all = 'ყველა';
$s_show = 'ჩვენება';
$s_viewing_bugs_title = 'მოთხოვნის ნახვა';
$s_updated = 'განახლდა';
$s_sort = 'სორტირება';
$s_issue_id = 'მოთხოვნა #';
$s_recently_visited = 'ხშირად ნანახი';
$s_filter_match_type = 'დამთხვევის ტიპი';
$s_filter_match_all = 'ყველა პირობა';
$s_filter_match_any = 'ნებისმიერი პირობა';
$s_none = 'არცერთი';
$s_current = 'მიმდინარე';
$s_search = 'ძიება';
$s_view_prev_link = 'წინას ნახვა';
$s_view_next_link = 'შემდეგის ნახვა';
$s_prev = 'წინა';
$s_next = 'შემდეგი';
$s_first = 'პირველი';
$s_last = 'ბოლო';
$s_start_date_label = 'დაწყების თარიღი:';
$s_end_date_label = 'დასრულების თარიღი:';
$s_use_last_updated_date_filters = 'ბოლო განახლების თარიღის მიხედვით ფილტრაცია';
$s_yes = 'დიახ';
$s_no = 'არა';
$s_open_filters = 'ფილტრის შეცვლა';
$s_ok = 'დიახ';
$s_select_all = 'ყველაფრის მონიშვნა';
$s_use_query = 'მომხმარებლის ფილტრი';
$s_delete_query = 'ფილტრის წაშლა';
$s_save_query = 'არსებული ფილტრის შენახვა';
$s_reset_query = 'ფილტრის წაშლა';
$s_query_name = 'ფილტრის სახელი';
$s_query_name_label = 'ფილტრის სახელი';
$s_query_store_error = 'ფილტრის დამახსოვრებისას დაფიქსირდა შეცდომა.';
$s_open_queries = 'ფილტრების მენეჯმენტი';
$s_query_delete_msg = 'დარწმუნებული ხართ, რომ გსურთ ამ ფილტრის წაშლა?';
$s_edit_filter = 'ფილტრის რედაქტირება';
$s_owner = 'მფლობელი';
$s_update_filter = 'ფილტრის განახლება';
$s_current_project = 'მიმდინარე პროექტი';
$s_stored_project = 'შენახული პროექტი';
$s_manage_filter_page_title = 'ფილტრების მენეჯმენტი';
$s_manage_filter_edit_page_title = 'ფილტრის რედაქტირება';
$s_apply_filter_button = 'გამოყენება';
$s_bug_assign_to_button = 'მიმაგრებულია:';
$s_bug_status_to_button = 'სტატუსის შეცვლა:';
$s_reopen_bug_button = 'ხელახლა გახსნა';
$s_attached_files = 'მიმაგრებული ფაილები';
$s_publish = 'გამოქვეყნება';
$s_bug_view_title = 'მოთხოვნის დეტალების ნახვა';
$s_monitoring_user_list = 'მომხმარებლების სია';
$s_custom_field = 'ველი';
$s_custom_field_label = 'ველი:';
$s_custom_field_name = 'სახელი';
$s_custom_field_project_count = 'პროექტების რაოდენობა';
$s_custom_field_type = 'ტიპი';
$s_custom_field_possible_values = 'შესაძლო მნიშვნელობები';
$s_custom_field_default_value = 'ნაგულისხმევი მნიშვნელობა';
$s_custom_field_access_level_r = 'წაკითხვის უფლება';
$s_custom_field_access_level_rw = 'ჩაწერის უფლება';
$s_custom_field_length_min = 'მინიმალური სიგრძე';
$s_custom_field_length_max = 'მაქსიმალური სიგრძე';
$s_custom_field_filter_by = 'ფილტრში დამატება';
$s_custom_field_require_report = 'აუცილებელია რეპორტისთვის';
$s_custom_field_sequence = 'თანმიმდევრობა';
$s_custom_field_sequence_label = 'თანმიმდევრობა';
$s_field_delete_button = 'ველის წაშლა';
$s_field_remove_button = 'ველის წაშლა';
$s_hide_status = 'სტატუსის დამალვა';
$s_filter_closed = 'დახურული';
$s_filter_resolved = 'მოგვარებული';
$s_hide_closed = 'დახურულების დამალვა';
$s_hide_resolved = 'გადაჭრილი მოთხოვნების დამალვა';
$s_and_above = 'ზევით დამატება';
$s_advanced_filters = 'გაფართოებული ფილტრები';
$s_simple_filters = 'მარტივი ფილტრები';
$s_attachments = 'მიმაგრებული ფაილი(ები)';
$s_bytes = 'bytes';
$s_kib = 'KiB';
$s_attachment_count = 'მიმაგრებული ფაილების რაოდენობა';
$s_view_attachments_for_issue = '#%2$d მოთხოვნის მიბმული ფაილების %1$d ნახვა';
$s_sponsors = '%1$d სპონსორები';
$s_changelog = 'ჟურნალიზაციის შეცვლა';
$s_http_auth_realm = 'MantisBT ავტორიზება';
$s_bug = 'მოთხოვნა';
$s_bugs = 'მოთხოვნები';
$s_add_new_relationship = 'ახალი კავშირი';
$s_this_bug = 'მიმდინარე მოთხოვნა';
$s_relationship_added = 'კავშირი დამატებულია';
$s_relationship_deleted = 'კავშირი წაშლილია';
$s_no_relationship = 'კავშირი არ არის';
$s_relationship_replaced = 'კავშირი შეცვლილია';
$s_replace_relationship_button = 'ჩანაცვლება';
$s_delete_relationship_sure_msg = 'დარწმუნებული ხართ, რომ გსურთ ამ კავშირის წაშლა?';
$s_create_child_bug_button = 'კლონირება';
$s_bug_cloned_to = 'მოთხოვნა დაკლონირდა';
$s_bug_created_from = 'მოთხოვნა დაგენერირდა (დან)';
$s_copy_attachments_from_parent = 'მიბმული ფაილების კოპირება';
$s_vertical = 'ვერტიკალური';
$s_horizontal = 'ჰორიზონტალური';
$s_view_issue = 'მოთხოვნის ნახვა';
$s_issues = 'მოთხოვნები';
$s_update_issue = 'მოთხოვნის განახლება';
$s_move_issue = 'მოთხოვნის გადატანა';
$s_delete_issue = 'მოთხოვნის წაშლა';
$s_reopen_issue = 'მოთხოვნის თავიდან გახსნა';
$s_view_private_issues = 'კერძო მოთხოვნების ნახვა';
$s_update_issue_status = 'მოთხოვნის სტატუსის განახლება';
$s_notes = 'შენიშვნები';
$s_add_notes = 'შენიშვნის დამატება';
$s_news = 'სიახლეები';
$s_view_private_news = 'პირადული სიახლეების ნახვა';
$s_manage_news = 'ახალი ამბების მართვა';
$s_view_list_of_attachments = 'მიბმული ფაილების სიის ჩვენება';
$s_download_attachments = 'მიბმული ფაილების ჩამოტვირთვა';
$s_delete_attachments = 'მიმაგრებული ფაილების წაშლა';
$s_delete_attachment_sure_msg = 'დარწმუნებული ხართ, რომ გსურთ მიბმული ფაილის წაშლა?';
$s_upload_issue_attachments = 'მოთხოვნის მიბმული ფაილების ატვირთვა';
$s_filters = 'ფილტრები';
$s_save_filters = 'ფილტრის შენახვა';
$s_use_saved_filters = 'შენახული ფილტრების გამოყენება';
$s_create_project = 'პროექტის შექმნა';
$s_delete_project = 'პროექტის წაშლა';
$s_manage_project = 'პროექტის მენეჯმენტი';
$s_project_documents = 'პროექტის დოკუმენტაცია';
$s_others = 'სხვა';
$s_send_reminders = 'შეხსენების გაგზავნა';
$s_add_profiles = 'პროფილების დამატება';
$s_email_notification = 'შეტყობინების ელ.ფოსტაზე გაგზავნა';
$s_status_changed_to = 'სტატუსი შეიცვალა';
$s_email_on_deleted = 'წაშლისას შეტყობინების ელ.ფოსტაზე გაგზავნა';
$s_loading = 'იტვირთება...';
$s_between_date = 'შორის';
$s_before_date = 'მანამდე';
$s_after_date = 'შემდეგ';
$s_from_date = 'გამომგზავნი';
$s_wiki = 'Wiki';
$s_tag_name = 'სახელი';
$s_tag_creator = 'შემქმნელი';
$s_tag_created = 'შექმნის თარიღი';
$s_tag_updated = 'ბოლო განახლება';
$s_tag_statistics = 'მომხმარებლის სტატისტიკა';
$s_tag_delete_button = 'ტეგის წაშლა';
$s_tag_delete_message = 'დარწმუნებული ხართ, რომ გსურთ ამ ტეგის წაშლა?';
$s_tag_existing = 'არსებული დასათაურებები:';
$s_tag_attach = 'მიმაგრება';
$s_tag_attach_long = 'საკვანძო სიტყვების მიმაგრება';
$s_show_all_tags = 'ყველა';
$s_time_tracking_cost = 'ღირებულება';
$s_time_tracking_stopwatch_start = 'დაწყება';
$s_time_tracking_stopwatch_stop = 'შეჩერება';
$s_time_tracking_stopwatch_reset = 'გაუქმება';
$s_access_denied = 'დაშვება აკრძალულია.';
$s_manage_columns_config = 'სვეტების მენეჯმენტი';
$s_all_columns_title = 'არსებული სვეტები';
$s_csv_columns_title = 'CSV სვეტები';
$s_view_issues_columns_title = 'მოთხოვნის სვეტების ჩვენება';
$s_print_issues_columns_title = 'მოთხოვნის ველების ბეჭდვა';
$s_excel_columns_title = 'ექსელის სვეტები';
$s_due_date = 'დასრულების თარიღი';
$s_view_account_title = 'ინფორმაცია მომხმარებელზე';
$s_manage_user = 'მომხმარებლების მართვა';
$s_php_version = 'PHP ვერსია';
$s_adodb_version = 'ADOdb ვერსია';
$s_database_driver = 'მონაცემთა ბაზის დრაივერი';
$s_database_version_description = 'მონაცემთა ბაზის ვერსია, აღწერა';
$s_month_january = 'იანვარი';
$s_month_february = 'თებერვალი';
$s_month_march = 'მარტი';
$s_month_april = 'აპრილი';
$s_month_may = 'მაისი';
$s_month_june = 'ივნისი';
$s_month_july = 'ივლისი';
$s_month_august = 'აგვისტო';
$s_month_september = 'სექტემბერი';
$s_month_october = 'ოქტომბერი';
$s_month_november = 'ნოემბერი';
$s_month_december = 'დეკემბერი';
$s_timeline_no_activity = 'დროის ინტერვალში აქტიურობა არ ფიქსირდება.';
$s_timeline_more = 'დამატებითი მოვლენები ...';
$MANTIS_ERROR[ERROR_SQL] = 'SQL შეცდომა.';
$MANTIS_ERROR[ERROR_NO_DIRECTORY] = 'დირექტორია არ არსებობს. გთხოვთ იხილოთ პროექტის პარამეტრები.';
$MANTIS_ERROR[ERROR_DUPLICATE_PROJECT] = 'ასეთი სახელწოდების პროექტი უკვე არსებობს.';
$MANTIS_ERROR[ERROR_INVALID_FIELD_VALUE] = 'ასასწორი მნიშვნელობა \'%1$s\'-თვის';
$MANTIS_ERROR[ERROR_ACCESS_DENIED] = 'დაშვება აკრძალულია.';
$MANTIS_ERROR[ERROR_CONFIG_OPT_NOT_FOUND] = 'კონფიგურაციის პარამეტრი "%1$s" ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_BUGNOTE_NOT_FOUND] = 'შენიშვნა ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_DB_FIELD_NOT_FOUND] = 'მონაცემთა ბაზის ველი "%1$s" ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_PROJECT_NOT_FOUND] = 'პროექტი "%1$s" ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_PROJECT_NAME_INVALID] = 'პროექტის სახელი არაკორექტულია. პროექტის სახელი არ შეიძლება იყოს ცარიელი.';
$MANTIS_ERROR[ERROR_USER_BY_NAME_NOT_FOUND] = 'მომხმარებელი სახელით "%1$s" ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_USER_BY_ID_NOT_FOUND] = 'მომხმარებელი უნიკალური იდენტიფიკატორით "%1$d" ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_USER_BY_EMAIL_NOT_FOUND] = 'მომხმარებელი ელ.ფოსტის მისამართით "%1$s" ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_USER_BY_REALNAME_NOT_FOUND] = 'მომხმარებელი სახელით "%1$s" ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_USER_CREATE_PASSWORD_MISMATCH] = 'პაროლი არ ემთხვევა';
$MANTIS_ERROR[ERROR_USER_CURRENT_PASSWORD_MISMATCH] = 'მიმდინარე პაროლი სწორია.';
$MANTIS_ERROR[ERROR_BUG_NOT_FOUND] = 'მოთხოვნა %1$d ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_FILTER_NOT_FOUND] = 'ფილტრი %1$s ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_EMAIL_INVALID] = 'არასწორი ელექტრონული ფოსტის მისამართი';
$MANTIS_ERROR[ERROR_USER_PROFILE_NOT_FOUND] = 'პროფილი ვერ მოიძებნა';
$MANTIS_ERROR[ERROR_FILE_NOT_ALLOWED] = 'აღნიშნული ფაილის ტიპის ატვირთვა აკრძალულია.';
$MANTIS_ERROR[ERROR_FILE_DUPLICATE] = 'ეს არის დუბლირებული ფაილი. გთხოვთ წაშალოთ ძველი.';
$MANTIS_ERROR[ERROR_FILE_NOT_FOUND] = 'მიბმული ფაილი უნიკალური იდენტიფიკატორით "%1$d" ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_NAME_NOT_UNIQUE] = 'ეს არის დუბლირებული სახელი.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_IN_USE] = 'არანაკლებ ერთი პროექტისა კვლავ იყენებს ამ ველს.';
$MANTIS_ERROR[ERROR_CUSTOM_FIELD_INVALID_VALUE] = 'არასწორი მნიშვნელობა "%1$s" ველის.';
$MANTIS_ERROR[ERROR_LDAP_AUTH_FAILED] = 'LDAP ავტორიხაცია ვერ განხორციელდა.';
$MANTIS_ERROR[ERROR_LDAP_SERVER_CONNECT_FAILED] = 'LDAP სერვერთან კავშირი ვერ განხორციელდა.';
$MANTIS_ERROR[ERROR_DB_CONNECT_FAILED] = 'მონაცემთა ბაზასთან კავშირი ვერ მოხერდა. მონ.ბაზიდან მიღებული შეცდომა #%1$d: %2$s.';
$MANTIS_ERROR[ERROR_CATEGORY_DUPLICATE] = 'კატეგორია აღნიშნული სახელით უკვე არსებობს.';
$MANTIS_ERROR[ERROR_CATEGORY_NOT_FOUND] = 'კატეგორია ვერ მოიძებნა';
$MANTIS_ERROR[ERROR_VERSION_DUPLICATE] = 'ასეთი სახელწოდების ვერსია უკვე არსებობს.';
$MANTIS_ERROR[ERROR_VERSION_NOT_FOUND] = 'ვერსია "%1$s" ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_USER_NAME_INVALID] = 'მომხმარებლის სახელი არასწორია. მომხმარებლის სახელი უნდა შეიცავდეს მხოლოდ ლათინურ სიმბოლოებს, ციფრებს, ტირეს, წერტილს.';
$MANTIS_ERROR[ERROR_USER_REAL_NAME_INVALID] = 'მომხმარებლის რეალური სახელი არ არის ვალიდური';
$MANTIS_ERROR[ERROR_USER_DOES_NOT_HAVE_REQ_ACCESS] = 'მომხმარებელს არ აქვს მოთხოვნილი დაშვების დონე.';
$MANTIS_ERROR[ERROR_RELATIONSHIP_NOT_FOUND] = 'კავშირი ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_LOST_PASSWORD_NO_EMAIL_SPECIFIED] = 'უნდა მიუთითოთ ელ.ფოსტის მისამართი პაროლის ჩამოყრისთვის.';
$MANTIS_ERROR[ERROR_TAG_NOT_FOUND] = 'სახკვანძო სიტყვა ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_TAG_DUPLICATE] = 'საკვანძო სიტყვა უკვე არსებობს.';
$MANTIS_ERROR[ERROR_TAG_NAME_INVALID] = 'საკვანძო სიტყვის სახელი არაკორექტულია.';
$MANTIS_ERROR[ERROR_TOKEN_NOT_FOUND] = 'Token ვერ მოიძებნა.';
$MANTIS_ERROR[ERROR_INVALID_DATE_FORMAT] = 'არასწორი თარიღის ფორმატი';
$MANTIS_ERROR[ERROR_UPDATING_TIMEZONE] = 'შეუძლებელია დროის სარტყელის შეცვლა.';
$s_dropzone_default_message = 'ჩააგდეთ ასატვირთი ფაილები (ან დააქლიქეთ)';
$s_dropzone_invalid_file_type = 'შეუძლებელია ამ ტიპის ფაილების ატვირთვა.';
$s_dropzone_cancel_upload = 'ატვირთვის გაუქმება';
$s_dropzone_cancel_upload_confirmation = 'დაწმუნებული ხართ რომ გსურთ ატვირთვის გაუქმება?';
$s_dropzone_remove_file = 'ფაილის წაშლა';
$s_dropzone_max_files_exceeded = 'შეუძლებელია დამატებითი ფაილების ატვირთვა';
$s_edit = 'რედაქტირება';
$s_update = 'განახლება';
$s_delete = 'წაშლა';
$s_make_default = 'გახდეს ძირითადი';
$s_print = 'ბეჭდვა';
$s_jump = 'გადასვლა';
$s_change = 'შეცვლა';
$s_go_back = 'უკან';
$s_proceed = 'გაგრძელება';
$s_move = 'გადატანა';
$s_close = 'დახურვა';
$s_add = 'დამატება';
$s_login = 'შესვლა';
