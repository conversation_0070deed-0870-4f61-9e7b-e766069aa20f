Admin Guide
===========


Reference for Administrators
----------------------------


[IMAGE]


MantisBT Development Team

<<EMAIL>>

Copyright © 2016 MantisBT team. This material may only be distributed
subject to the terms and conditions set forth in the GNU Free
Documentation License (GFDL), V1.2 or later (the latest version is
presently available at http://www.gnu.org/licenses/fdl.txt).

Abstract

This book is targeted at MantisBT administrators, and documents the
installation, upgrade, configuration, customization and administration
tasks required to operate the software.

------------------------------------------------------------------------



Chapter 1. About MantisBT
=========================


1.1. What is MantisBT?
----------------------

MantisBT is a web based bug tracking system that was first made available
to the public in November 2000. Over time it has matured and gained a lot
of popularity, and now it has become one of the most popular open source
bug/issue tracking systems. MantisBT is developed in PHP, with support to
multiple database backends including MySQL, MS SQL and PostgreSQL.

MantisBT, as a PHP script, can run on any operating system that is
supported by PHP and has support for one of the DBMSes that are
supported. MantisBT is known to run fine on Windows, Linux, macOS and a
variety of Unix operating systems.


1.2. Who should read this manual?
---------------------------------

This manual is targeted for the person responsible for evaluating,
installing and maintaining MantisBT in a company. Typically we refer to
this person as the MantisBT administrator.


1.3. License
------------

MantisBT is released under the terms of GNU General Public License (GPL).
MantisBT is free to use and modify. It is free to redistribute as long as
you abide by the distribution terms of the GPL.


1.4. How to get it?
-------------------

MantisBT is available in several Linux distributions including: Debian,
Ubuntu, Fedora, Gentoo, Frugalware and others. Hence, if you are running
Linux, start by checking if your distribution has a package for MantisBT.
If not, or if the package is not up-to-date with the latest MantisBT
version, then you may want to download it directly from here.

For Windows, macOS and other operating systems, use the link provided
above to download MantisBT. The download is compressed in tar.gz or zip
format. Both formats can be unpacked using tools like 7-Zip (in case of
Windows).

Note that at any point in time there are typically two "latest" MantisBT
releases that are available for download. The latest production release
(stable), and the latest development release which can be an alpha or a
release candidate. It is not recommended to use development releases in
production specially if it is still in the alpha stage unless the
administrator is familiar with PHP and is able to troubleshoot and fix
any issues that may arise.


1.5. About the Name
-------------------

When initially seeking to name this project Ken ran into a problem every
programmer encounters. What is a good name? It has to be descriptive,
unique, and not too verbose. Additionally having multiple meanings would
be a nice touch. Quickly ruled out were php*Something* names which,
incidentally, although popular, do not seem to be condoned by the PHP
Group developers. Drawing inspiration from Open Source projects like
Apache, Mozilla, Gnome, and so forth resulted in two eventual choices:
Dragonfly and Mantis. Dragonfly was already the name of a webmail
package. So the name became Mantis.

Praying Mantis are insects that feed primarily on other insects and bugs.
They are extremely desirable in agriculture as they devour insects that
feed on crops. They are also extremely elegant looking creatures. So, we
have a name that is fairly distinctive and descriptive in multiple ways.
The BT suffix stands for "Bug Tracker" and distinguishes this project
from general usage of the word Mantis. However, over time the project was
typically referred to as Mantis.


1.6. History
------------

Kenzaburo Ito and a friend originally created a bug tracker as an
internal tool for their pet project. A search for good, free packages
came up with nothing suitable so they wrote their own. After a rewrite
and cleanup it was made available to the public via the GNU General
Public License (GPL). The GPL was chosen partly because of his belief
that development tools should be cheap or free. In 2002, Ken was joined
by Jeroen Latour, Victor Boctor and Julian Fitzell to be the
administrators and the core development team of MantisBT. This marks a
new era in MantisBT lifetime where it is now a team project.


1.7. Support
------------

There are plenty of resources to help answer support queries. Following
are the main ones:

  * 

    Forums - The forums are one of the most popular destinations for
    getting MantisBT support. Start off by searching the forums for your
    questions, if not found, then go ahead and submit a question.

  * 

    Mailing lists - Several lists are available, each of them with its
    own, specific purpose. Note that posting messages is restricted to
    subscribers so you will have to register before you can send
    messages; however, there are public archives available if you're only
    interested in reading.

  * 

    Gitter is a browser-based, on-line chat that has mainly replaced the
    team's use of IRC. In the main chat room, you can have a live
    discussion with the developers and other MantisBT users. Gitter
    supports all modern browsers and also offers Android and iOS-based
    clients, as well as an IRC bridge.

  * 

    IRC - The IRC channel not very active anymore, as the developers have
    moved on to using Gitter for live discussions; nevertheless, the
    channel is still open. There are many free IRC clients: XChat (for
    Linux), HexChat, IceChat amongst others. You can also use Web Chat to
    connect to IRC via your web browser, which may also be useful when
    you're behind a firewall that blocks the IRC port. The IRC channel
    logs are archived and made available on the MantisBT web site.

  * 

    Wiki - The MantisBT Wiki has information related to "How To
    (recipes)", FAQ, feature requirements, plugins etc.

  * 

    Search - A good way for locating an answer to your question or
    finding more information about a topic is to search across all
    MantisBT website and the Internet via your favorite search engine,
    e.g. Google or Bing.


Note

Support questions should not be sent directly to MantisBT developers or
through the MantisBT website's contact pages.

Also, our bug tracker is reserved for reporting issues with the software,
and must not be used for support requests.


1.8. MantisBT News
------------------

There are several ways to keep up to date with MantisBT news. These
include:

  * 

    We send release announcements and important updates to users
    registered on our official bugtracker. To get onto our mailing list,
    users will have to signup there and verify their email address. This
    same account can also be used to report, monitor, and comment on
    issues relating to MantisBT.

  * 

    MantisBT Blog is used to communicate announcements about new
    releases, topics relating to MantisBT, etc. Users are encouraged to
    subscribe to the RSS feed to know when new posts are posted there.

  * 

    Twitter is used to notify users about up-to-date details about what
    is happening with MantisBT development. Twitter users are encouraged
    to follow "@mantisbt".


1.9. Versioning
---------------

Our release numbering convention follows the guidelines of Semantic
Versioning. Given a version number Major.Minor.Patch and an optional
Suffix (eg. 1.3.0-rc.1):

  * 

    Major - Indicates a very large change in the core package. Rewrites
    or major milestones. API changes which are not backwards-compatible.

  * 

    Minor - Introduction of new features or significant changes in
    functionality, in a backwards-compatible manner.

  * 

    Patch - Bug fixes, maintenance and security releases.

  * 

    Suffix - Optional, indicates a development release.

      * 

        aN or alpha.N for alpha releases,

      * 

        bN or beta.N for beta releases, or

      * 

        rcN or rc.N for release candidates.

    Absence of suffix indicates a stable release.



Chapter 2. Installation
=======================

This chapter explains how to install or upgrade MantisBT.


2.1. Overview
-------------

The table below contains a high-level overview of the processes. Refer to
the corresponding sections for details.

New Installation

Upgrade

  1. 

    Section 2.2, “System Requirements”

  2. 

    Section 2.3, “Pre-installation / upgrade tasks”

  3. 

    Section 2.4, “New Installation”

  4. 

    Section 2.6, “Configure your installation”

  5. 

    Section 2.7, “Post-installation and upgrade tasks”

  6. 

    Section 2.8, “Post-installation tasks”

  1. 

    Section 2.3, “Pre-installation / upgrade tasks”

  2. 

    Section 2.10, “Backups”

  3. 

    Put the site down for maintenance

  4. 

    Section 2.5, “Upgrading”

  5. 

    Section 2.7, “Post-installation and upgrade tasks”

  6. 

    Section 2.9, “Post-upgrade tasks”


2.2. System Requirements
------------------------


2.2.1. Server Hardware Requirements

MantisBT has modest hardware requirements. It requires a computer that is
able to run the server software (see Section 2.2.2, “Server Software
Requirements”).

  * 

    Server type

    The server can be a shared public web server or a dedicated
    co-located box.

  * 

    CPU and Memory

    As for any web application, you should size your server based on the
    traffic on the site.

  * 

    Disk

    The application code is less than 50 MiB.

    The amount of disk space required for the database will vary
    depending on the RDBMS and the volume of data, the main driving
    factor being the expected number and size of attachments.


2.2.2. Server Software Requirements

All of the required software is free for commercial and non-commercial
use (open source). Please refer to the table in Section *******,
“Versions compatibility table” for minimum and recommended versions.

  * 

    Operating System

    MantisBT runs on Windows, macOS, Linux, Solaris, the BSDs, and just
    about anything that supports the required server software.

  * 

    Web Server

    MantisBT is mainly tested with Microsoft IIS and Apache. However, it
    is expected to work with any recent web server software.

    File Extensions: MantisBT uses only .php files. If your webserver is
    configured for other extensions (e.g. .PHP3, .PHTML) then you will
    have to request the administrator to add support for .PHP files. This
    should be a trivial modification. Further details can be found in the
    PHP documentation

  * 

    PHP

    The web server must support PHP. It can be installed as CGI or any
    other integration technology.

  * 

    PHP extensions

    MantisBT is designed to work in as many environments as possible.
    Hence the required extensions are minimal and many of them are
    optional affecting only one feature.

    Mandatory extensions

            * 

              The extension for the RDBMS being used ( mysqli with
              mysqlnd, pgsql, oci8, sqlsrv )

            * 

              mbstring - Required for Unicode (UTF-8) support.

            * 

              ctype, filter, hash, json, session - Required to run
              MantisBT in general. These are bundled with PHP, and
              enabled by default. Note that hash is a core extension
              since PHP 7.4.0, and json is a core extension since PHP
              8.0.0.

    Optional extensions

            * 

              Curl - required for the Twitter integration feature

            * 

              GD - required for the captcha feature

            * 

              Fileinfo - required for file attachments and most of the
              plugins

              Without this extension, file attachment previews and
              downloads do not work as MantisBT won't be able to send the
              Content-Type header to a browser requesting an attachment.

            * 

              LDAP - required for LDAP or Active Directory authentication
              (see Section 8.2, “LDAP and Microsoft Active Directory”).

            * 

              SOAP - required to use the SOAP API (see Section 5.38,
              “API”).

            * 

              zlib - required to enable output compression (see Section
              5.26, “Speed Optimisation”).


    Note

    You can check which PHP modules are installed by running php -m on
    the command line, or by using the php_info() function in a PHP
    script.

  * 

    Database

    MantisBT requires a database to store its data. The supported RDBMS
    are:

      * 

        MySQL (or one of its forks, e.g. MariaDB)

      * 

        PostgreSQL

    Experimental support is also available for

      * 

        Microsoft SQL Server

      * 

        Oracle

    Experimental support means that manual intervention by a skilled
    Database Administrator may be required to complete the installation,
    and/or that there may be known issues or limitations when using the
    software. Please refer to our Issue tracker, filtering on categories
    db mssql and db oracle to find out more about those.


    Note

    Please note that the MantisBT development team mainly works with
    MySQL, so testing for other drivers is not as extensive as we mainly
    rely on community contributions to improve support and fix issues
    with other RDBMS.

    We therefore recommend MySQL to store your database.

*******. Versions compatibility table

Category

Package

Minimum Version

Recommended

Comments

RDBMS

MySQL

5.5.35

5.6 or later

PHP extension: mysqli with MySQL Native driver (mysqlnd)

MariaDB

5.5.35

10.4 or later

PHP extension: mysqli

PostgreSQL

9.2

11.20 or later

PHP extension: pgsql

MS SQL Server

2012

2019 or later

PHP extension: sqlsrv

Oracle

11gR2

19c or later

PHP extension: oci8

PHP

PHP

7.2.5

8.0 or later

See above for PHP extensions

Web Server

Apache

2.2.x

2.4.x

lighttpd

1.4.x

1.4.x

nginx

1.10.x

1.16.x or later

IIS

7.5

10

Windows Server 2016 or later

Our minimum requirements are generally based on availability of support
for the underlying software by their respective vendors. In some cases,
we do require a specific version because we rely on a feature that is not
available in older releases.


Warning

Running MantisBT with versions of the software components lower than the
minimum requirements listed above is not supported.


2.2.3. Client Requirements

MantisBT should run on all recent browsers in the market, including but
not limited to:

  * 

    Firefox

  * 

    Edge

  * 

    Chrome

  * 

    Safari

  * 

    Opera


Note

Support for Internet Explorer 11 ended with release 2.22.0.


2.3. Pre-installation / upgrade tasks
-------------------------------------

These tasks cover the download and deployment of MantisBT, and should be
performed prior to any new installation or upgrade.

  1. 

    Download MantisBT (see Section 1.4, “How to get it?”)

  2. 

    Transfer the downloaded file to your webserver

    This can be done using whatever method you like best (ftp, scp, etc).
    You will need to telnet/ssh into the server machine for the next
    steps.

  3. 

    Extract the release

    It is highly recommended to maintain a separate directory for each
    release. This not only avoids mismatch between versions, (files may
    have been added or removed) but also provides an easy path to
    downgrade your installation, should you need to.

    The usual command is (1 step):

    tar -xzf     filename.tar.gz

    OR (2 steps):

    gunzip     filename.tar.gz    tar -xf     filename.tar

    Other file archiving tools such as 7-Zip should also be able to
    handle decompression of the archive.

    The extraction process should create a new directory like
    mantisbt-1.3.x

  4. 

    Rename the directory

    For new installations, you may want to rename the directory just
    created to something simpler, e.g. mantisbt

    mv mantisbt-1.3.x mantisbt  


2.4. New Installation
---------------------

This chapter explains how to perform a new installation of MantisBT.

Start by checking Section 2.2, “System Requirements” and installing the
appropriate version of required software.

Once that is done, execute the installation script. From your web
browser, access

https://yoursite/mantisbt/admin/install.php

The installation procedure will go through the following steps:

  1. 

    The script checks basic parameters for the web server

  2. 

    Provide required information for the installation

      * 

        database type

      * 

        database server hostname

      * 

        user and password

        Required privileges: SELECT, INSERT, UPDATE, and DELETE

      * 

        high-privileged database account

        Additional privileges required: INDEX, CREATE, ALTER, and DROP

        If this account is not specified, the database user will be used.

  3. 

    Click the Install/Upgrade Database button

  4. 

    The script creates the database and tables.

    The default Administrator user account is created at this stage, to
    allow the initial login and setup of MantisBT.

  5. 

    The script attempts to write a basic config_inc.php file to define
    the database connection parameters.

    This operation may fail if the web server's user account does not
    have write permissions to the directory (which is recommended for
    obvious security reasons). In this case, you will have to manually
    create the file and copy/paste the contents from the page.

  6. 

    The script perform post installation checks on the system.

    Review and correct any errors.


2.5. Upgrading
--------------

This chapter explains how to upgrade an existing MantisBT installation.

Start by Performing the steps described in Section 2.3, “Pre-installation
/ upgrade tasks” above.

  1. 

    Put the site down for maintenance

    cp mantis_offline.php.sample mantis_offline.php

    This will prevent users from using the system while the upgrade is in
    progress.

  2. 

    Always Backup your code, data and config files before upgrading !

    This includes your Mantis directory, your attachments, and your
    database. Refer to Section 2.10, “Backups” for details.

  3. 

    Copy the configuration files

    To preserve your system settings, you should copy the files listed
    below to subdirectory config of the new installation.

      * 

        config_inc.php,

      * 

        custom_strings_inc.php,

      * 

        custom_constants_inc.php and

      * 

        custom_functions_inc.php.


    Note

    The above list is not exhaustive. You might also have to copy other
    custom files specific to your installation such as logo, favicon,
    css, etc.

  4. 

    Copy third party plugins

    To maintain system functionality, you should copy any additional
    plugins in the plugins subdirectory.

    For example on Unix, you could use the following command; it will
    copy all installed plugins (in local subdirectories or symlinked),
    excluding bundled ones.

    cd /path/to/mantisbt-OLD/plugins
    find -maxdepth 1 ! -path . -type d -o -type l |
        grep -Pv "(Gravatar|MantisCoreFormatting|MantisGraph|XmlImportExport)" |
        xargs -Idirs cp -r dirs /path/to/mantisbt-NEW/plugins


    Warning

    Make sure that you do not overwrite any of the bundled plugins as per
    the list below, with an older version.

      * 

        Avatars via Gravatar (Gravatar)

      * 

        MantisBT Formatting (MantisCoreFormatting)

      * 

        Mantis Graphs (MantisGraph)

      * 

        Import/Export issues (XmlImportExport)

  5. 

    Execute the upgrade script. From your web browser, access

    https://yoursite/mantisbt-NEW/admin/install.php

    where mantisbt-NEW is the name of the directory where the new release
    was extracted

  6. 

    Provide required information for the upgrade

      * 

        high-privileged database account

        Additional privileges required: INDEX, CREATE, ALTER, and DROP

        If this account is not specified, the database user will be used.

  7. 

    Click the Install/Upgrade Database button

  8. 

    At the end of the upgrade, review and correct any warnings or errors.


Upgrading large databases

When processing large databases from versions older than 1.2, the upgrade
script may fail during the conversion of date fields, leaving the system
in an inconsistent (i.e. partially updated) state.

In this case, you should simply restart the upgrade process, which will
resume where it left off. Note that you may have to repeat this several
times, until normal completion.

Reference: MantisBT issue 12735.


2.6. Configure your installation
--------------------------------

There are many settings that you can adjust to configure and customize
MantisBT. Refer to Chapter 5, Configuration, as well as the
config_defaults_inc.php file for in depth explanations of the available
options. Check out also Chapter 7, Customizing MantisBT for further
options to personalize your installation.

This step is normally only required for new installations, but when
upgrading you may want to review and possibly customize any new
configuration options.

Open or create the file config_inc.php in subfolder config in an editor
and add or modify any values as required. These will override the default
values.

You may want to use the provided config_inc.php.sample file as a starting
point.


Warning

you should never edit the config_defaults_inc.php file directly, as it
could cause issues with future upgrades. Always store your custom
configuration in your own config_inc.php file.


Warning

The MantisBT configuration files (config_inc.php as well as
custom_strings_inc.php, custom_constants_inc.php,
custom_functions_inc.php, etc.) should always be saved as UTF-8 without
BOM. Failure to do so may lead to unexpected display issues.


2.7. Post-installation and upgrade tasks
----------------------------------------

Instructions in this section are common to both new installations and
upgrades, and should be applied after completing either process.

  1. 

    Test your configuration

    Load up admin/check/index.php to validate whether everything is setup
    correctly, and take corrective action as needed.

  2. 

    Delete the admin folder

    Once you have confirmed that the install or upgrade process was
    successful, you should delete this directory

    rm -r admin

    For security reasons, the scripts within this directory should not be
    freely accessible on a live MantisBT site, particularly one which is
    accessible via the Internet, as they can allow unauthorized people
    (e.g. hackers) to gain technical knowledge about the system, as well
    as perform administrative tasks.


    Warning

    Omitting this important step will leave your MantisBT instance
    exposed to several potentially severe attacks, e.g. issue #23173 (if
    mysqli.allow_local_infile is enabled in php.ini).


2.8. Post-installation tasks
----------------------------

Instructions in this section should only be applied after a new
installation

  1. 

    Login to your bugtracker

    Use the default Administrator account. The id and password are
    administrator / root.

  2. 

    Create a new Administrator account

    Go to Manage > Users and create a new account with 'administrator'
    access level.

  3. 

    Disable or delete the default Administrator account

  4. 

    Create a new Project

    Go to Manage > Projects and create a new project


2.9. Post-upgrade tasks
-----------------------

Instructions in this section should only be applied after upgrading an
existing installation.

  1. 

    Test the new release

    Perform any additional testing as appropriate to ensure the new
    version does not introduce any regressions.

  2. 

    Switch the site to the new version

    The commands below should be executed from the web root (or wherever
    the mantisbt scripts are installed) and assume that the "live"
    directory (old version) is named mantisbt and the new release
    directory is mantisbt-1.3.x.

    mv mantisbt mantisbt-old
    mv mantisbt-1.3.x mantisbt  

  3. 

    Put the site back on line

    rm mantis_offline.php

    This should be the final step in the upgrade process, as it will let
    users login again.


2.10. Backups
-------------

It is strongly recommended to backup your MantisBT database on a regular
basis. The method to perform this operation depends on which RDBMS you
use.

Backups are a complex subject, and the specificities of implementing and
handling them for each RDBMS are beyond the scope of this document. For
your convenience, the section below provides a simple method to backup
MySQL databases.

You should also consider implementing backups of your MantisBT code
(which includes your configs and possibly customization), as well as
issue attachments (if stored on disk) and project documents.


Warning

You should always backup your system (code and database) before upgrading
!


2.10.1. MySQL Backups

MySQL databases are easy to backup using the mysqldump command:

mysqldump -u<username> -p<password> <database name> > <output file>

To restore a backup you will need to have a clean database. Then run:

mysql -u<username> -p<password> <database name> < <input file>

You can also perform both of these tasks using phpMyAdmin

A good idea is to make a backup script and run it regularly through cron
or a task scheduler. Using the current date in the filename can prevent
overwriting and make cataloguing easier.

References and useful links:

  * 

    mysqldump documentation

  * 

    Percona XtraBackup

  * 

    AutoMySQLBackup script


2.11. Uninstall
---------------

It is recommended that you make a backup in case you wish to use your
data in the future. See Section 2.10, “Backups” for details.

To uninstall MantisBT:

  * 

    Delete the MantisBT directory and all files and subdirectories.

  * 

    Drop all MantisBT tables from the database, these can be identified
    by the configured prefix for the installation. The default prefix is
    'mantis'.

  * 

    Remove any customizations or additions that you may have made.

If you have the permissions to create/drop databases and you have a
specific database for MantisBT that does not contain any other data, you
can drop the whole database.



Chapter 3. User Management
==========================


3.1. Creating User Accounts
---------------------------

In MantisBT, there is no limit on the number of user accounts that can be
created. Typically, installations with thousands of users tend to have a
limited number of users that have access level above REPORTER.

By default users with ADMINISTRATOR access level have access to create
new user accounts. The steps to do that are:

  * 

    Click "Manage" on Main Menu.

  * 

    Click "Users" (if not selected by default).

  * 

    Click "Create New Account" button just below the alphabet key.

  * 

    Enter user name, email address, global access level (more details
    about access levels later). Other fields are optional.

  * 

    Click "Create Users".

Creating a user triggers the following actions:

  * 

    Creating a user in the database.

  * 

    If email notifications ($g_enable_email_notification) is set to ON,
    then the user will receive an email allowing them to activate their
    account and set their password. Otherwise, the account will be
    created with a blank password.

  * 

    If email notifications ($g_enable_email_notification) is set to ON,
    users with access level of $g_notify_new_user_created_threshold_min
    and above will get a notification that a user account has been
    created. Information about the user like user name, email address, IP
    address are included in the email notification.

When the 'Protected' flag is set on a user account, it indicates that the
account is a shared account (e.g. demo account) and hence users logged
using such account will not be allowed to change account preferences and
profile information.

The anonymous user account specified with the $g_anonymous_account option
will always be treated as a protected user account. When you are creating
the anonymous user account, the 'Protected' flag is essentially ignored
because the anonymous user is always treated as a protected user.


3.2. Enabling/Disabling User Accounts
-------------------------------------

The recommended way of retiring user accounts is to disable them.
Scenarios where this is useful is when a person leaves the team and it is
necessary to retire their account.

Once an account is disabled the following will be enforced:

  * 

    All currently active sessions for the account will be invalidated
    (i.e. automatically logged out).

  * 

    It will no longer be possible login using this account.

  * 

    No further email notifications will be sent to the account once it is
    disabled.

  * 

    The user account will not show anymore in lists like "assign to",
    "send reminder to", etc.

The disabling process is totally reversible. Hence, the account can be
re-enabled and all the account history will remain intact. For example,
the user will still have issues reported by them, assigned to them,
monitored by them, etc.


3.3. Deleting User Accounts
---------------------------

Another way to retire user accounts is by deleting them. This approach is
only recommended for accounts that have not been active (i.e. haven't
reported issues). Once the account is deleted, any issues or actions
associated with such account, will be associated with user123 (where 123
is the code of the account that was deleted). Note that associated issues
or actions are not deleted.

As far as the underlying database, after the deletion of a user, records
with the user id as a foreign key will have a value that no longer exists
in the users table. Hence, any tools that operate directly on the
database must take this into consideration.

By default administrators are the only users who can delete user
accounts. They can delete accounts by clicking Manage, Users, locating
the user to be deleted and opening it details page, then clicking on the
"Delete User" button which deletes the user.

Note that "Deleting Users" is not a reversible process. Hence, if it is
required to re-add the user account, it is not possible to recreate the
user account so that it gets the same ID and hence retains its history.
However, manually creating a record in the users table with the same id,
can possibly do that. However, this approach is not recommended or
supported.


3.4. User Signup
----------------

For open source and freeware projects, it is very common to setup
MantisBT so that users can signup for an account and get a REPORTER
access by default (configurable by the
$g_default_new_account_access_level configuration option). The signup
process can be enabled / disabled using the $g_allow_signup configuration
option, which is enabled by default.

If user signup is enabled, then it is required that
$g_send_reset_password is ON as well, and the e-mail settings properly
configured (see Section 5.8, “Email”).

If email notifications ($g_enable_email_notification) is set to ON, users
with access level of $g_notify_new_user_created_threshold_min and above
will get a notification that a user account has been created. Information
about the user like user name, email address, IP address are included in
the email notification.


3.5. Forgot Password and Reset Password
---------------------------------------

It is pretty common for users to forget their password. MantisBT provides
two ways to handle such scenario: "Forgot Password" and "Reset Password".

"Forgot Password" is a self service scenario where users go to the login
page, figure out they don't remember their password, and then click the
"Lost your password?" link. Users are then asked for their user name and
email address. If correct, then they are sent an email with a link which
allows them to login to MantisBT and change their password.

"Reset Password" scenario is where a user reports to the administrator
that they are not able to login into MantisBT anymore. This can be due to
forgetting their password and possibly user name or email address that
they used when signing up. The administrator then goes to Manage, Users,
locates the user account and opens its details. Under the user account
details, there is a "Reset Password" button which the administrator can
click to reset the password and trigger an email to the user to allow
them to get into MantisBT and set their password. In the case where email
notifications are disabled, resetting password will set the password to
an empty string.


3.6. Impersonating a user
-------------------------

Administrators are able to impersonate users in order to reproduce an
issue reported by a user, test their access making sure they can access
the expected projects/issues/fields, or to create API tokens for service
accounts that are used to grant other systems limited access to MantisBT.


3.7. Changing Password
----------------------

Users are able to change their own passwords (unless their account is
"protected"). This can be done by clicking on "My Account", and then
typing the new password in the "Password" and "Confirm Password" fields,
then clicking "Update User". Changing the password automatically
invalidates all logged in sessions and hence the user will be required to
re-login. Invalidating existing sessions is very useful in the case where
a user going onto a computer, logs into MantisBT and leaves the computer
without logging out. By changing the password from another computer, the
session on the original computer automatically becomes invalidated.


3.8. Pruning User Accounts
--------------------------

The pruning function allows deleting of user accounts for accounts that
have been created more than a week ago, and they never logged in. This is
particularly useful for users who signed up with an invalid email or with
a typo in their email address address.

The account pruning can be done by administrators by going to "Manage",
"Users", and clicking the "Prune Accounts" button inside the "Never
Logged In" box.


3.9. Authorization and Access Levels
------------------------------------

MantisBT uses access levels to define what a user can do. Each user
account has a global or default access level that is associated with it.
This access level is used as the access level for such users for all
actions associated with public projects as well as actions that are not
related to a specific project. Users with global access level less than
$g_private_project_threshold will not have access to private projects by
default.

The default access levels shipped with MantisBT out of the box are
VIEWER, REPORTER, UPDATER, DEVELOPER, MANAGER and ADMINISTRATOR. Each
features has several configuration options associated with it and
identifies the required access level to do certain actions. For example,
viewing an issue, reporting an issue, updating an issue, adding a note,
etc.

For example, in the case of reporting issues, the required access level
is configurable using the $g_report_bug_threshold configuration option
(which is defaulted to REPORTER). So for a user to be able to report an
issue against a public project, the user must have a project-specific or
a global access level that is greater than or equal to REPORTER. However,
in the case of reporting an issue against a private project, the user
must have project specific access level (that is explicitly granted
against the project) that is higher than REPORTER or have a global access
level that is higher than both $g_private_project_threshold and
$g_report_bug_threshold.

Note that project specific access levels override the global access
levels. For example, a user may have REPORTER as the global access level,
but have a MANAGER access level to a specific project. Or a user may have
MANAGER as the global access level by VIEWER access to a specific
project. Access levels can be overridden for both public and private
projects. However, overriding access level is not allowed for users with
global access ADMINISTRATOR.

Each feature typically has multiple access control configuration options
to define what access level can perform the operation. For example,
adding a note may require REPORTER access level, updating it note may
require DEVELOPER access level, unless the note was added by the same
user.

Such threshold configuration options can be set to a single access level,
which means users with such threshold and above are authorized to perform
the action. The other option is to specify an array of access levels
which indicates that users with the explicitly specific thresholds are
allowed to execute the actions.

It is also worth mentioning that the access levels are defined by the
$g_access_levels_enum_string configuration option, and it is possible to
customize such list. The default value for the available access levels is
'10:viewer, 25:reporter, 40:updater, 55:developer, 70:manager,
90:administrator'. The instructions about how to customize the list of
access levels will be covered in the customization section.


3.10. Auto Creation of Accounts on Login
----------------------------------------

If you are using a global user directory (LDAP, Active Directory), you
may want to configure MantisBT so users who already exists in the
directory will be automatically authenticated and added to MantisBT.

For example, a company may setup their MantisBT installation in a way,
where its staff members that are already registered in their LDAP
directory, should be allowed to login into MantisBT with the same user
name and password. Another option could be if MantisBT is integrated into
some content management system, where it is desired to have a single
registration and single sign-on experience.

In such scenarios, once a user logs in for the first time, a user account
is automatically created for them, although the password verification is
still done against LDAP or the main users repository.


3.11. User Preferences
----------------------

Users can fine tune the way MantisBT interacts with them by modifying
their user preferences to override the defaults set by the administrator;
If the administrator changes a default setting, it will not automatically
cascade in the users' preferences once they have been set, so it is the
users' responsibility to manage their own preferences.

The user preferences include the following:

  * 

    Default Project: A user can choose the default project that is
    selected when the user first logs in. This can be a specific project
    or "All Projects". For users that only work on one project, it would
    make sense to set such project as the default project (rather than
    "All Projects"). The active project is part of the filter applied on
    the issues listed in the "View Issues" page. Also any newly reported
    issues will be associated with the active project.

  * 

    Refresh Delay: The refresh delay is used to specify the number of
    seconds between auto-refreshes of the View Issues page.

  * 

    Redirect Delay: The redirect delay is the number of seconds to wait
    after displaying flash messages like "Issue created successfully",
    and before the user gets redirected to the next page.

  * 

    Notes Sort Order: The preference relating to how notes should be
    ordered when issue is viewed or in email notifications. Ascending
    order means that older notes are displayed first

  * 

    Email on XXX: If unticked, then the notifications related to the
    corresponding event would be disabled. User can also specify the
    minimum issue severity of for the email to be sent.

    Note that the preference is only used to disable notifications that
    as per the administrator's configuration, this user would have
    qualified to receive.

  * 

    Email Notes Limit: This preference can be used to limit the number of
    issue notes to be included in a email notifications. Specifying N
    here will cause only the latest N to be included. The value 0 means
    that all notes will be included.

  * 

    Language: The preferred language of the user. This language is used
    by the GUI and in email notifications. Note that MantisBT uses UTF-8
    for encoding the data, hence the user could for example use MantisBT
    with a Chinese interface, while logging issue data in German.


3.12. User Profiles
-------------------

A user profile describes an environment that used to run the software for
which issues are being tracked.

When reporting issues, users can elect to enter information like
platform, operating system and version manually, or they can choose from
a list of available profiles.

Each user has access to all the personal profiles they create, in
addition to global ones; Profile data includes "Platform", "Operating
System", "OS Version", and "Additional Description".

Global profiles are typically used by the administrator to define a set
of standard system settings used in their environment, which saves users
the trouble of having to define them individually. The access level
required to manage global profiles is configured by the
$g_manage_global_profile_threshold configuration option and defaults to
MANAGER.



Chapter 4. Issue Lifecycle and Workflow
=======================================


4.1. Issue Creation
-------------------

The life cycle of an issue starts with its creation. An issue can be
created via one of the following channels:

  * 

    MantisBT Web Interface - This is where a user logs into MantisBT and
    reports a new issue.

  * 

    SOAP API - Where an application automatically reports an issue into
    MantisBT using the SOAP API web services interfaces. For example, the
    nightly build script can automatically report an issue if the build
    fails.

  * 

    Email - This is not supported out of the box, but there are existing
    MantisBT patches that would listen to emails on pre-configured email
    addresses and adds them to the MantisBT database.

  * 

    Others - There can be several other ways to report issues. For
    example, applications / scripts that directly injects issues into
    MantisBT database (not recommended, except for one-off migration
    scripts), or PHP scripts that use the core MantisBT API to create new
    issues.


4.2. Issue Statuses
-------------------

An important part of issue tracking is to classify issues as per their
status. Each team may decide to have a different set of categorization
for the status of the issues, and hence, MantisBT provides the ability to
customize the list of statuses. MantisBT assumes that an issue can be in
one of three stages: opened, resolved and closed. Hence, the customized
statuses list will be mapped to these three stages. For example, MantisBT
comes out of the box with the following statuses: new, feedback,
acknowledged, confirmed, assigned, resolved and closed. In this case
"new" -> "assigned" map to opened, "resolved" means resolved and "closed"
means closed.

Following is the explanation of what the standard statuses that are
shipped with MantisBT means.

  * 

    New - This is the landing status for new issues. Issues stay in this
    status until they are assigned, acknowledged, confirmed or resolved.
    The next status can be "acknowledged", "confirmed", "assigned" or
    "resolved".

  * 

    Acknowledged - This status is used by the development team to reflect
    their agreement to the suggested feature request. Or to agree with
    what the reporter is suggesting in an issue report, although they
    didn't yet attempt to reproduce what the reporter is referring to.
    The next status is typically "assigned" or "confirmed".

  * 

    Confirmed - This status is typically used by the development team to
    mention that they agree with what the reporter is suggesting in the
    issue and that they have confirmed and reproduced the issue. The next
    status is typically "assigned".

  * 

    Assigned - This status is used to reflect that the issue has been
    assigned to one of the team members and that such team member is
    actively working on the issue. The next status is typically
    "resolved".

  * 

    Resolved - This status is used to reflect that the issue has been
    resolved. An issue can be resolved with one of many resolutions
    (customizable). For example, an issue can be resolved as "fixed",
    "duplicate", "won't fix", "no change required", etc. The next
    statuses are typically "closed" or in case of the issue being
    re-opened, then it would be "feedback".

  * 

    Closed - This status reflects that the issue is completely closed and
    no further actions are required on it. It also typically hides the
    issue from the View Issues page. Some teams use "closed" to reflect
    sign-off by the reporter and others use it to reflect the fact that
    the fix has been released to customers.


4.3. Workflow
-------------

Now that we have covered how an issue gets created, and what are the
different statuses during the life cycle of such issues, the next step is
to define the workflow. The workflow dictates the valid transitions
between statuses and the user access level required of the user who
triggers such transitions; in other words, how issues move from one
status to another and who is authorized to trigger such transitions.

MantisBT provides the ability for teams to define their own custom
workflow which works on top of their custom status (see Section 7.5,
“Customizing Status Values”).


4.3.1. Workflow Transitions

By default, there is no workflow defined, which means that all states are
accessible from any other, by anyone.

The "Manage > Configuration > Workflow Transitions" page allows users
with ADMINISTRATOR access level to do the following tasks:

  * 

    Define the valid next statuses for each status.

  * 

    Define the default next status for each status.

  * 

    Define the minimum access level required for a user to transition to
    each status.

  * 

    Define the default status for newly created issues.

  * 

    Define the status at which the issue is considered resolved. Any
    issues a status code greater than or equal to the specified status
    will be considered resolved.

  * 

    Define the status which is assigned to issues that are re-opened.

  * 

    Define the required access level to change the workflow.

Note that the scope of the applied change is dependent on the selected
project. If "All Projects" is selected, then the configuration is to be
used as the default for all projects, unless overridden by a specific
project. To configure for a specific project, switch to it via the
combobox at the top right corner of the screen.

The Global ("All Projects") workflow can also be defined in the
config_inc.php file, as per the following example.

$g_status_enum_workflow[NEW_]           ='30:acknowledged,20:feedback,40:confirmed,50:assigned,80:resolved';
$g_status_enum_workflow[FEEDBACK]       ='30:acknowledged,40:confirmed,50:assigned,80:resolved';
$g_status_enum_workflow[ACKNOWLEDGED]   ='40:confirmed,20:feedback,50:assigned,80:resolved';
$g_status_enum_workflow[CONFIRMED]      ='50:assigned,20:feedback,30:acknowledged,80:resolved';
$g_status_enum_workflow[ASSIGNED]       ='80:resolved,20:feedback,30:acknowledged,40:confirmed';
$g_status_enum_workflow[RESOLVED]       ='90:closed,20:feedback,50:assigned';
$g_status_enum_workflow[CLOSED]         ='20:feedback,50:assigned';


Note

The workflow needs to have a path from the statuses greater than or equal
to the 'resolved' state back to the 'feedback' state (see
$g_bug_resolved_status_threshold and $g_bug_feedback_status under Section
5.22, “Status Settings”), otherwise, the re-open operation won't work.


Note

The first item in each list denotes the default value for this status,
which will be pre-selected in the Change Status combobox in the View
Issues page.


4.3.2. Workflow Thresholds

The "Manage > Configuration > Workflow Thresholds" page allows users with
ADMINISTRATOR access level to define the thresholds required to do
certain actions. Following is a list of such actions and what they mean:

  * 

    Report an issue - The access levels that are allowed to report an
    issue.

  * 

    Update an issue - The access levels that are allowed to update the
    header information of an issue.

  * 

    Allow issue to be closed on resolved - The access levels that are
    allow to resolve and close an issue in one step.

  * 

    Allow reporter to close issue - Indicates if reporters should be
    allowed to close issues reported by them.

  * 

    Monitor an issue - The access levels required for a user to be able
    to monitor an issue. Once a user monitors an issue, the user will be
    included in all future email notifications relating to changes in the
    issue.

  * 

    Handle an issue - The access levels required for a user to be shown
    in the list of users that can handle an issue.

  * 

    Assign an issue - The access levels required for a user to be able to
    change the handler (i.e. assign / unassign) an issue.

  * 

    Move an issue - The access levels required for a user to be able to
    move an issue from one project to another. (TODO: are these access
    levels evaluated against source or destination project?).

  * 

    Delete an issue - The access levels required for a user to be able to
    delete an issue.

  * 

    Reopen an issue - The access levels required for a user to be able to
    re-open a resolved or closed issue.

  * 

    Allow Reporter to re-open Issue - Whether the reporter of an issue
    can re-open a resolved or closed issue, independent of their access
    level.

  * 

    Status to which a reopened issue is set - This is the status to which
    an issue is set after it is re-opened.

  * 

    Resolution to which a reopen issue is set - The resolution to set on
    issues that are reopened.

  * 

    Status where an issue is considered resolved - The status at which an
    issue is considered resolved.

  * 

    Status where an issue becomes readonly - Issues with such status and
    above are considered read-only. Read-only issues can only be modified
    by users with a configured access level. Read-only applies to the
    issue header information as well as other issue related information
    like relationships, attachments, notes, etc.

  * 

    Update readonly issues - The access levels required for a user to be
    able to modify a readonly issue.

  * 

    Update issue status - The access levels required for a user to be
    able to modify the status of an issue.

  * 

    View private issues - The access levels for a user to be able to view
    a private issue.

  * 

    Set view status (public vs. private) - The access level for a user to
    be able to set whether an issue is private or public, when reporting
    the issue. If the user reporting the issues doesn't have the required
    access, then the issue will be created with the default view state.

  * 

    Update view status (public vs private) - The access level required
    for a user to be able to update the view status (i.e. public vs.
    private).

  * 

    Show list of users monitoring issue - The access level required for a
    user to be able to view the list of users monitoring an issue.

  * 

    Set status on assignment of handler - The access levels required for
    a user to be able to re-assign an issue when changing its status.

  * 

    Status to set auto-assigned issues to - The status - This is the
    status that is set on issues that are auto assigned to users that are
    associated with the category that the issuer is reported under.

  * 

    Limit reporter's access to their own issues - When set, reporters are
    only allow to view issues that they have reported.

  * 

    Add notes - The access levels required for users to be able to add
    notes.

  * 

    Update notes - The access levels required for users to be able to
    update issue notes.

  * 

    Allow user to edit their own issue notes - A flag that indicates the
    ability for users to edit issue notes report by them.

  * 

    Delete note - The access levels required for a user to delete a note
    that they may or may not have reported themselves.

  * 

    View private notes - The access levels required for a user to be able
    to view private notes associated with an issue that they have access
    to view.

  * 

    View Change Log - The access levels required for a user to be able to
    view the change log.

  * 

    View Assigned To - The access levels required for a user to be able
    to know the handler of an issue that they have access to.

  * 

    View Issue History - The access levels required for a user to be able
    to view the history of changes of an issue.

  * 

    Send reminders - The access levels required for a user to be able to
    send reminders to other users relating to an issue that they have
    access to.



Chapter 5. Configuration
========================


5.1. Introduction
-----------------

MantisBT is highly customizable through the web interface and
configuration files. Configuration options can be set globally as well as
customized for a specific project or user (except for options listed in
$g_global_settings, see Section 5.5, “Configuration Settings”).

Configuration options can be set in config_inc.php and in the database
(using the various manage pages). Values stored in the database take
precedence over values defined in config_inc.php. The former can also be
viewed and updated on the Configuration Report page (Manage >
Configuration > Configuration Report).

To determine which value to use, MantisBT follows the list below,
sequentially searching for the specified configuration option until a
match is found.

  1. 

    database: current user, current project

  2. 

    database: current user, all projects

  3. 

    database: all users, current project

  4. 

    database: all users, all projects

  5. 

    config_inc.php

  6. 

    config_defaults_inc.php


5.2. Database
-------------


5.2.1. Base Database settings

These settings are required for the system to work, and are typically set
when installing MantisBT. They should be provided to you by your system
administrator or your hosting company.

$g_hostname

      Host name or connection string for Database server. The default
      value is localhost. For MySql, this should be hostname or
      hostname:port (e.g. localhost:3306).

$g_db_username

      User name to use for connecting to the database. The user needs to
      have read/write access to the MantisBT database. The default user
      name is "root".

$g_db_password

      Password for the specified user name. The default password is
      empty.

$g_database_name

      Name of database that contains MantisBT tables. The default name is
      'bugtracker'.

$g_db_type

      The supported database types are listed in the table below.

      The PHP extension corresponding to the selected type must be
      enabled (see also Section *******, “Versions compatibility table”).

      RDBMS

      db_type (ADOdb)

      PHP extension

      Comments

      MySQL

      mysqli

      mysqli

      default

      PostgreSQL

      pgsql

      pgsql

      MS SQL Server

      mssqlnative

      sqlsrv

      Oracle

      oci8

      oci8


5.2.2. Database table naming settings

MantisBT allows administrators to configure a prefix and a suffix for its
tables. This enables multiple MantisBT installation in the same database
or schema.


Warning

Use of long strings for these configuration options may cause issues on
RDBMS restricting the size of its identifiers, such as Oracle (which
imposed a maximum size of 30 characters until version 12.1; starting with
12cR2 this limit has been increased to 128).

To avoid this limitation, it is recommended that

  * 

    the prefix is set to blank or kept as short as possible (e.g. m).

  * 

    the suffix is set to blank.

  * 

    the plugin prefix is kept as short as possible (e.g. plg).

$g_db_table_prefix

      Specifies the prefix to be used for all table names. The default
      value is mantis.

      The given string is added with an underscore before the base table
      name, e.g. for the bug table, the actual table name with the
      default prefix would be mantis_bug.

$g_db_table_suffix

      Specifies the suffix to be appended to all table names. The default
      value is table.

      The given string is added with an underscore after the base table
      name, e.g. for the bug table, the actual table name with the
      default suffix would be bug_table.

$g_db_table_plugin_prefix

      Specifies the prefix to be used to differentiate tables belonging
      to a plugin's schema from MantisBT's own base tables. The default
      value is plugin.

      The given string is inserted with an underscore between the table
      prefix and the base table name, and the plugin basename is added
      after that, e.g. for a table named foo in the Example plugin, with
      default values for prefixes and suffix the physical table name
      would be mantis_plugin_Example_foo_table.


      Warning

      It is strongly recommended not to use an empty string here, as this
      could lead to problems, e.g. conflicts if a plugin's basename
      happens to match one of MantisBT's base tables.

$g_dsn

      Adodb Data Source Name This is an EXPERIMENTAL field. If the above
      database settings, do not provide enough flexibility, it is
      possible to specify a dsn for the database connection. NOTE: the
      installer does not yet fully support the use of dsn's


5.3. Path
---------

These path settings are important for proper linking within MantisBT. In
most scenarios the default values should work fine, and you should not
need to override them.

$g_path

      Full URL to your installation as seen from the web browser.

      This is what users type into the URL field, e.g.
      https://www.example.com/mantisbt/. Requires trailing `/`.

      If not set, MantisBT will default this to a working URL valid for
      most installations. However, in some cases (typically when an
      installation can be accessed by multiple URLs, e.g. internal vs
      external), it might be necessary to override the default.


      Warning

      The default is built based on headers from the HTTP request. This
      is a potential security risk, as the system will be exposed to Host
      Header injection attacks, so it is strongly recommended to
      initialize this in config_inc.php.

$g_short_path

      Short web path without the domain name. This requires the trailing
      '/'.

$g_absolute_path

      This is the absolute file system path to the MantisBT installation,
      it is defaulted to the directory where config_defaults_inc.php
      resides. Requires trailing '/' character (eg.
      '/usr/apache/htdocs/mantisbt/').

$g_core_path

      This is the path to the core directory of your installation. The
      default value is usually OK but it is recommended that you move the
      'core' directory out of your webroot. Requires trailing
      DIRECTORY_SEPARATOR character.

$g_class_path

      This is the path to the classes directory which is a sub-directory
      of core by default. The default value is typically OK. Requires
      trailing DIRECTORY_SEPARATOR. character.

$g_library_path

      This is the path to the library directory of your installation. The
      default value is usually OK but it is recommended that you move the
      'library' directory out of your webroot. Requires trailing
      DIRECTORY_SEPARATOR character.

$g_vendor_path

      Path to vendor folder for 3rd party libraries. Requires trailing
      DIRECTORY_SEPARATOR character.

$g_language_path

      This is the path to the language directory of your installation.
      The default value is usually OK but it is recommended that you move
      the 'language' directory out of your webroot. Requires trailing
      DIRECTORY_SEPARATOR character.

$g_manual_url

      This is the url to the MantisBT online manual. Requires trailing
      '/' character.


5.4. Webserver
--------------

$g_session_save_path

      Location where session files are stored. The default is false,
      meaning the session handler's default location will be used.

$g_session_validation

      Use Session validation (defaults to ON)


      Warning

      Disabling this could be a potential security risk !

$g_form_security_validation

      Form security validation, defaults to ON. This protects against
      Cross-Site Request Forgery. Some proxy servers may not correctly
      work with this option enabled because they cache pages incorrectly.


      Warning

      Disabling this option is a security risk, it is strongly
      recommended to leave it ON

$g_custom_headers

      An array of custom headers to be sent with each page.

      For example, to allow your MantisBT installation to be viewed in a
      frame in IE6 when the frameset is not at the same hostname as the
      MantisBT install, you need to add a P3P header. You could try
      something like

      $g_custom_headers = array( 'P3P: CP="CUR ADM"' );

      in your config file, but make sure to check that your policy
      actually matches with what you are promising. See MSDN for more
      information.

      Even though it is not recommended, you could also use this setting
      to disable previously sent headers. For example, assuming you
      didn't want to benefit from Content Security Policy (CSP), you
      could set:

      $g_custom_headers = array( 'Content-Security-Policy:' );


      Warning

      Disabling CSP is a security risk, it is strongly recommended that
      you leave it as Mantis defines it.

$g_logout_redirect_page

      Specify where the user should be sent after logging out.

$g_allow_browser_cache

      This will allow the browser to cache all pages. The upside will be
      better performance, but there may be cases where obsolete
      information is displayed. Note that this will be bypassed (and
      caching is allowed) for the bug report pages.

$g_allow_file_cache

      This will allow the browser to cache downloaded files. Without this
      set, there may be issues with IE receiving files, and launching
      support programs.


5.5. Configuration Settings
---------------------------

$g_global_settings

      This option contains the list of configuration options that are
      used to determine if it is allowed for a specific configuration
      option to be saved to or loaded from the database. Configuration
      options that are in the list are considered global only and hence
      are only configurable via the config_inc.php file and defaulted by
      config_defaults_inc.php file.

$g_public_config_names

      This option contains a list of configuration options that can be
      queried via SOAP API.


5.6. Security and Cryptography
------------------------------

Content Security Policy

      Amongst other things, MantisBT relies on Content Security Policy
      (CSP), which is a W3C candidate recommendation improving the
      system's security against cross-site scripting (XSS) and other,
      similar types of attacks. It is currently supported in recent
      versions of many browsers.


      Note

      CSP may cause issues in certain situations (e.g. during
      development), or when using plugins relying on externally hosted
      resources such as images or scripts.

      MantisBT currently does not provide any mechanism for plugins to
      notify the Core of 'safe' external domains. Because of that, even
      though it is not recommended for obvious security reasons, you may
      wish to disable CSP. You can do so by specifying a Custom Header in
      your config_inc.php file (see Section 5.4, “Webserver”).


      Warning

      Disabling Content Security Policy is a security risk !

$g_crypto_master_salt

      Master salt value used for cryptographic hashing throughout
      MantisBT. This value must be kept secret at all costs. You must
      generate a unique and random salt value for each installation of
      MantisBT you control. The minimum length of this string must be at
      least 16 characters.

      The value you select for this salt should be a long string
      generated using a secure random number generator. An example for
      Linux systems is:

      cat /dev/urandom | head -c 64 | base64

      Note that the number of bits of entropy per byte of output from
      /dev/urandom is not 8. If you're particularly paranoid and don't
      mind waiting a long time, you could use /dev/random to get much
      closer to 8 bits of entropy per byte. Moving the mouse (if
      possible) while generating entropy via /dev/random will greatly
      improve the speed at which /dev/random produces entropy.

      This setting is blank by default. MantisBT will not operate in this
      state. Hence you are forced to change the value of this
      configuration option.


      Warning

      This configuration option has a profound impact on the security of
      your MantisBT installation. Failure to set this configuration
      option correctly could lead to your MantisBT installation being
      compromised. Ensure that this value remains secret. Treat it with
      the same security that you'd treat the password to your MantisDB
      database.


5.7. Signup and Lost Password
-----------------------------

$g_allow_signup

      Allow users to signup for their own accounts.

      If ON (default), then $g_send_reset_password must be ON as well,
      and mail settings must be correctly configured (see Section 5.8,
      “Email”).

$g_max_failed_login_count

      Maximum number of failed login attempts before the user's account
      is locked. Once locked, it is required to reset the password (lost
      password). The counter is reset to zero after each successful
      login.

      Default is set to 5, in order to prevent brute force attacks
      attempting to gain access to end users accounts. Set to OFF to
      disable this feature and allow unlimited failed login attempts.

$g_notify_new_user_created_threshold_min

      The minimum global access level required to be notified when a new
      user registers via the "signup form". To pick specific access
      levels that are not necessarily at the higher end of access levels,
      use an array of access levels. Default is ADMINISTRATOR.

$g_send_reset_password

      If ON (default), users will be sent their password when their
      account is created or password reset (this requires mail settings
      to be correctly configured).

      If OFF, then the Administrator will have to provide a password when
      creating new accounts, and the password will be set to blank when
      reset.

$g_signup_use_captcha

      Use captcha image to validate subscription it requires GD library
      installed.

$g_system_font_folder

      Absolute path (with trailing slash!) to folder which contains your
      TrueType-Font files used for the Relationship Graphs, and the
      Workflow Graphs.

$g_lost_password_feature

      Setting to disable the 'lost your password' feature.

$g_max_lost_password_in_progress_count

      Max. simultaneous requests of 'lost password'. When this value is
      reached, it's no longer possible to request new password reset.
      Value resets to zero at each successfully login.


5.8. Email
----------

$g_webmaster_email

      The webmaster's e-mail address. This address is displayed in the
      bottom of all MantisBT pages. <EMAIL>

$g_from_email

      The email address to be used as the source of all emails sent by
      MantisBT. <EMAIL>

$g_from_name

      The sender name of all emails sent by MantisBT. Mantis Bug Tracker

$g_return_path_email

      Email address to receive bounced emails.

$g_enable_email_notification

      Set to ON to enable email notifications, OFF to disable them.
      Default is ON. Note that disabling email notifications has no
      effect on emails generated as part of the user signup process. When
      set to OFF, the password reset feature is disabled. Additionally,
      notifications of administrators updating accounts are not sent to
      users.

$g_email_notifications_verbose

      When enabled, the email notifications will include the full issue
      with a hint about the change type at the top, rather than using
      dedicated notifications that are focused on what changed. This
      change can be overridden in the database per user. Default is OFF.

$g_default_notify_flags

      Associates a default notification flag with each action, to control
      who should be notified. The default will be used if the action is
      not defined in $g_notify_flags or if the flag is not included in
      the specific action definition.

      The list of actions include: new, assigned, resolved, bugnote,
      reopened, closed, deleted, feedback.

      The default is:

      $g_default_notify_flags = array(
              'reporter'      => ON,
              'handler'       => ON,
              'monitor'       => ON,
              'bugnotes'      => ON,
              'category'      => ON,
              'explicit'      => ON,
              'threshold_min' => NOBODY,
              'threshold_max' => NOBODY
      );

      threshold_min and threshold_max are used to send messages to all
      members of the project whose status is

        * 

          greater than or equal to threshold_min, and

        * 

          less than or equal to threshold_max.

      Sending messages to everyone would set threshold_min to ANYBODY and
      threshold_max to NOBODY. To send to all DEVELOPERS and above, use
      DEVELOPER and NOBODY respectively.

$g_notify_flags

      Defines the specific notification flags when they are different
      from the defaults defined in $g_default_notify_flags.

      For example, the following code overrides the default by disabling
      notifications to bugnote authors and users monitoring the bug when
      submitting a new bug:

      $g_notify_flags['new'] = array(
              'bugnotes' => OFF,
              'monitor' => OFF,
      );

      See Section 7.4, “Email Notifications” for further examples of
      customizing the notification flags.

      Available actions include:

        * 

          new: a new bug has been added

        * 

          reopened: the bug has been reopened

        * 

          deleted: a bug has been deleted

        * 

          owner: the bug has been assigned a new owner

        * 

          bugnote: a bugnote has been added to a bug

        * 

          sponsor: the sponsorship for the bug has changed (added,
          deleted or updated)

        * 

          relation: a relationship for the bug has changed (added,
          deleted or updated)

        * 

          monitor: a user is added to the monitor list.

      In addition, an action can match the bug status in
      $g_status_enum_string. Note that spaces in the string are replaced
      with underscores ('_') when creating the action. Thus, using the
      defaults, 'feedback' would be a valid action.

$g_email_receive_own

      This defines whether users should receive emails for their own
      actions. This option is defaulted to OFF, hence, users do not
      receive email notification for their own actions. This can be a
      source for confusions for users upgrading from MantisBT 0.17.x
      versions, since in these versions users used to get notified of
      their own actions.

$g_validate_email

      Determines whether email addresses are validated.

      When ON (default), validation is performed using the pattern given
      by the HTML5 specification for email type form input elements. When
      OFF, validation is disabled.


      Note

      Regardless of how this option is set, validation is never performed
      when using LDAP email (i.e. when $g_use_ldap_email = ON, see
      Section 5.21.2, “LDAP authentication method parameters”), as we
      assume that it is handled by the directory.

$g_check_mx_record

      Set to OFF to disable email checking. Default is OFF.

$g_allow_blank_email

      If ON, allows the user to omit an email address field. If you allow
      users to create their own accounts, they must specify an email at
      that point, no matter what the value of this option is. Otherwise
      they wouldn't get their passwords.

      Administrators are able to bypass this check to enable them to
      create special accounts like anonymous access and other service
      accounts that don't need notifications.

$g_email_login_enabled

      Enable support for logging in by email and password, in addition to
      username and password. This will only work as long as there is a
      single user with the specified email address and the email address
      is not blank. The default value is OFF.

$g_email_ensure_unique

      When enabled, the uniqueness of email addresses will be enforced
      for new users as well as updates to existing ones. Default is ON.


      Warning

      When this setting changes from OFF to ON (which will de facto occur
      when upgrading to MantisBT 1.3.0 or later from an older version),
      there could be existing user accounts sharing the same email
      address.

      It important that such duplicates are identified and fixed, to
      avoid unexpected and unpredictable behavior when looking up users
      with their email address, as the system expects them to be unique.

      To facilitate this task, the Administration Checks will detect
      duplicate email addresses and identify the related user accounts. A
      warning will also be displayed in the Manage Users page (see
      Section 6.8.1, “Users”) and when editing a user account whose email
      address is associated with one or more other accounts.

$g_limit_email_domains

      Only allow and send email to addresses in the given domain(s). This
      is useful as a security feature and it is also useful in cases like
      Sourceforge where its servers are limited to only sending emails to
      SourceForge email addresses in order to avoid spam.
      $g_limit_email_domains = array( 'users.sourceforge.net',
      'sourceforge.net' );

$g_show_user_email_threshold

      This specifies the access level that is needed to have user names
      hyperlinked with mailto: links. The default value is NOBODY, hence,
      even administrators won't have this feature enabled.

$g_show_user_realname_threshold

      This specifies the access level that is needed to see realnames on
      user view page. The default value is NOBODY, hence, even
      administrators won't have this feature enabled.

$g_phpMailer_method

      Select the method to send mail:

        * 

          PHPMAILER_METHOD_MAIL for use of mail() function,

        * 

          PHPMAILER_METHOD_SENDMAIL for sendmail (or postfix),

        * 

          PHPMAILER_METHOD_SMTP for SMTP,

      Default is PHPMAILER_METHOD_MAIL.

$g_smtp_host

      This option specifies the SMTP server to submit messages to. The
      SMTP server (MTA) then takes on the responsibility of delivering
      messages to their final destinations.

      To use the local SMTP (if available) set this to 'localhost',
      otherwise use the fully qualified domain name of the remote SMTP
      server.

      It can be either a single hostname, or multiple semicolon-delimited
      hostnames. You can specify for each host a port other than the
      default, using format: hostname:port (e.g.
      "smtp1.example.com:25;smtp2.example.com").

      Hosts will be tried in the given order.


      Note

      This is only used with PHPMAILER_METHOD_SMTP (see
      $g_phpmailer_method).

      The default is 'localhost'.

$g_smtp_port

      The default SMTP port to use. This can be overridden individually
      for specific hosts. (see $g_smtp_host).

      Typical SMTP ports are 25 and 587.

      The default is 25.

$g_smtp_connection_mode

      Allow secure connection to the SMTP server. Valid values are:

        * 

          '' (empty string): No encryption. This is the default.

        * 

          ssl

        * 

          tls

$g_smtp_username

      SMTP Server Authentication user

      Allows the use of SMTP Authentication when using a remote SMTP
      host.


      Note

      must be set to '' (empty string) if the SMTP host does not require
      authentication.

      Default is ''.

$g_smtp_password

      This is the password that is used in SMTP Authentication. Not used
      when $g_smtp_username = ''

      Default is ''.

$g_email_retry_in_days

      Duration (in days) to retry failed emails before deleting them from
      queue. Default 7 days.

$g_email_send_using_cronjob

      Disables sending of emails as soon as an action is performed.
      Emails are instead queued and must be sent by running
      scripts/send_emails.php periodically. This script can only be
      executed from the CLI, not from the web interface, for security
      reasons.

      Enabling this option can help with performance problems if large
      numbers of emails are generated or mail delivery is slow by not
      delaying page execution when sending emails.

$g_email_separator1

      Default is str_pad('', 70, '='); This means 70 equal signs.

$g_email_separator2

      Default is str_pad('', 70, '-'); This means 70 minus signs.

$g_email_padding_length

      Default is 28.

MantisBT uses flags and a threshold system to generate emails on events.
For each new event, email is sent to:

  * 

    the reporter, qualified by the notify flag 'reporter' below

  * 

    the handler (or Assigned to), qualified by the notify flag 'handler'
    below

  * 

    anyone monitoring the bug, qualified by the notify flag 'monitor'
    below

  * 

    anyone who has ever added a bugnote the bug, qualified by the notify
    flag 'bugnotes' below

  * 

    anyone assigned to the project whose access level is greater than or
    equal to the notify flag 'threshold_min' and less than or equal to
    the notify flag 'threshold_max' below

From this list, those recipients who meet the following criteria are
eliminated:

  * 

    the originator of the change, if $g_email_receive_own is OFF

  * 

    the recipient either no longer exists, or is disabled

  * 

    the recipient has turned their email_on_<new status> preference OFF

  * 

    the recipient has no email address entered


5.8.1. DKIM signature

In order to setup DomainKeys Identified Mail (DKIM) Signatures (as
defined in RFC 6376), you need to enable the feature (see
$g_email_dkim_enable), and provide at least:

  * 

    Domain (see $g_email_dkim_domain),

  * 

    Private key or key file path (see $g_email_dkim_private_key_file_path
    and $g_email_dkim_private_key_string),

  * 

    Selector (see $g_email_dkim_selector),

  * 

    Identity (see $g_email_dkim_identity).

$g_email_dkim_enable

      Enables DomainKeys Identified Mail (DKIM).

      The default is OFF.

$g_email_dkim_domain

      Defines the domain for DKIM Signatures.

      This is typically same as the host part of the $g_from_email. For
      example example.com.

$g_email_dkim_private_key_file_path

      Path to the private domain key to be used for DKIM Signatures.

      If the key is specified in $g_email_dkim_private_key_string this
      setting will not be used.

$g_email_dkim_private_key_string

      Private domain key to be used for DKIM Signatures.

      This string should contain private key for signing. Leave empty
      string if you wish to load the key from the file defined with
      $g_email_dkim_private_key_file_path.

$g_email_dkim_selector

      Selector to be used for DKIM Signatures.

      If your domain is example.com, typically DNS TXT field should have:
      host: mail.example._domainkey, value: v=DKIM1; t=s; n=core; k=rsa;
      p=[public key]. In this case selector should be mail.example

$g_email_dkim_passphrase

      Private DKIM domain key password.

      Leave empty string if your private key does not have password

$g_email_dkim_identity

      Identity to be used for DomainKeys Identified Mail (DKIM)
      Signatures.

      This is usually the same as $g_from_email. For example,
      <EMAIL>


5.8.2. S/MIME signature

This sections describes the necessary settings to enable S/MIME signature
for outgoing MantisBT e-mails.

$g_email_smime_enable

      Enables S/MIME signature.

      Defaults to OFF.

$g_email_smime_cert_file

      Path to the S/MIME certificate.

      The file must contain a PEM-encoded certificate.

$g_email_smime_key_file

      Path to the S/MIME private key file.

      The file must contain a PEM-encoded private key matching the S/MIME
      certificate.

$g_email_smime_key_password

      Password for the S/MIME private key.

      Leave blank if the private key is not protected by a passphrase.

$g_email_smime_extracerts_file

      Optional path to S/MIME extra certificates.

      The file must contain one (or more) PEM-encoded certificates, which
      will be included in the signature to help the recipient verify the
      certificate specified in $g_email_smime_cert_file ("CA Chain").


Note

MantisBT expects the S/MIME certificates and the private key files to be
in PEM format. If you have a PKCS12 encrypted certificate (typically with
a .pfx or .p12 extension), you may use the following openssl commands to
extract and convert the individual elements:

  * 

    Certificate

    openssl pkcs12 -in cert.pfx -clcerts -nokeys -out cert.crt  

  * 

    Extra certificates ("CA chain")

    openssl pkcs12 -in cert.pfx -cacerts -nokeys -out ca-chain.crt  

  * 

    Private key (-passout specifies the private key's password)

    openssl pkcs12 -in cert.pfx -nocerts -out cert.key -passout pass:  

If the input file is protected, openssl will ask for the password;
alternatively, you can specify it on the command-line with the -passin
option, e.g. -passin pass:PASSWORD


5.9. Version
------------

$g_show_version

      Display MantisBT Version number to users in the page footer.

      This is more of a cosmetic setting and should NOT be considered as
      a security measure to avoid disclosure of version information to
      users. Default is OFF.


      Note

      When the REST API is enabled (see Section 5.38, “API”), accessing
      an endpoint will always return the version number in the
      X-Mantis-Version header, even if the request fails.

$g_version_suffix

      String appended to the MantisBT version when displayed to the user.
      Default is ''.

$g_copyright_statement

      Custom copyright and licensing statement shown at the footer of
      each page.

      Can contain HTML elements that are valid children of the <address>
      element. This string is treated as raw HTML and thus you must use
      &amp; instead of &. Default is ''.


5.10. Language
--------------

$g_default_language

      This is the language used by default in MantisBT. This may be set
      to 'auto' where MantisBT will try to determine the language from
      the browser.

$g_language_choices_arr

      This is to be set to an array of languages that are available for
      users to choose from. The default value includes all languages
      supported by MantisBT. The administrator can limit the languages
      available for users to choose from by overriding this value. For
      example, to support English, French and German include the
      following code:

      $g_language_choices_arr = array( 'english', 'french', 'german' );

      Of course, administrators can also add their own languages by
      translating the strings and creating their own language files. You
      are encouraged to share any translation work that you do with the
      MantisBT team. This will ensure that the newly created language
      file is maintained with future MantisBT releases.All language files
      reside in the lang/ folder. They are all named according to the
      following pattern: strings_<language>.txt.

$g_language_auto_map

      Browser language mapping for 'auto' language selection

$g_fallback_language

      This is the language used if MantisBT cannot determine the language
      from the browser. It defaults to 'english'.As of 0.19.0, this may
      be set to 'auto' where MantisBT will try to determine the language
      from the browser.


Note

If a string does not exist in the active language, the English string is
used instead.


5.11. Display
-------------

$g_font_family

      Name of the google font family for the browser to use. For all
      available fonts, see: fonts.google.com .

$g_font_family_choices

      Google font family list offered to the user to chose from. Font
      files are fetched from google servers.

$g_font_family_choices_local

      This is a small subset of $g_font_family_choices in which font
      files are part of MantisBT installation.

$g_window_title

      This is the browser window title (<TITLE> tag).

$g_search_title

      This is used as prefix to describe Browser Search entries, and must
      be short enough so that when inserted into the
      'opensearch_XXX_short' language string, the resulting text is 16
      characters or less, to be compliant with the limit for the
      ShortName element as defined in the OpenSearch specification .

      Defaults to the value of $g_window_title.

$g_admin_checks

      Check for admin directory, database upgrades, etc. It defaults to
      ON.

$g_favicon_image

      Path to the favorites icon relative to MantisBT root folder This
      icon should be of image/x-icon MIME type, and its size 16x16
      pixels. It is also used to decorate OpenSearch Browser search
      entries. (default 'images/favicon.ico').

$g_logo_image

      Path to the logo image relative to MantisBT root folder (default
      'images/mantis_logo.gif').

$g_logo_url

      The default URL to be associated with the logo. By default this is
      set to $g_default_home_page (which defaults to My View page).
      Clicking on the logo from any page in the bug tracker will navigate
      to the URL specified in this configuration option.

$g_show_project_menu_bar

      This option specifies whether to add menu at the top of the page
      which includes links to all the projects. The default value is OFF.

$g_show_assigned_names

      When a bug is assigned then replace the word "assigned" with the
      name of the developer in parenthesis. Default is ON.

$g_show_priority_text

      Specifies whether to show priority as text (ON) or icon (OFF) in
      the view all bugs page. Default is OFF (icon).

$g_priority_significant_threshold

      Define the priority level at which a bug becomes significant.
      Significant bugs are displayed with emphasis. Set this value to -1
      to disable the feature. The default value is HIGH.

$g_severity_significant_threshold

      Define the severity level at which a bug becomes significant.
      Significant bugs are displayed with emphasis. Set this value to -1
      to disable the feature. The default value is MAJOR.

$g_view_issues_page_columns

      This configuration option is used to set the columns to be included
      in the View Issues page, and the order in which they will be
      displayed.

      This can be overridden using Manage > Manage Configuration > Manage
      Columns; users can also configure their own columns using My
      Account > Manage Columns.

      The list of all available columns (i.e. the names to choose from)
      can be retrieved from the above-mentioned pages. In addition to
      standard column names, that will also include:

        * 

          Custom Fields: the column name will be the Custom Field's name
          prefixed with custom_, e.g. xyz should be included as
          custom_xyz.

        * 

          Plugin-specific columns (prefixed with the Plugin's basename)

      If one of the columns specified here is not accessible to the
      logged-in user or corresponds to a disabled feature, then it will
      automatically be removed from the list at runtime. The same
      configuration may therefore show a different set of columns
      depending on the logged in user, the currently selected project and
      enabled features.

      For example, the eta column will only be shown if usage of the ETA
      field is enabled (see $g_enable_eta in Section 5.35, “Field
      Visibility”), and the custom_xyz column will be removed if the xyz
      Custom Field is not available in the current Project.

      By default the following columns are selected: selection, edit,
      priority, id, bugnotes_count, attachment_count, category_id,
      severity, status, last_updated, summary.

$g_print_issues_page_columns

      This configuration option is used to set the columns to be included
      in the Print Issues page, and the order in which they will be
      displayed.

      See $g_view_issues_page_columns for details.

      By default the following columns are selected: selection, priority,
      id, bugnotes_count, attachment_count, category_id, severity,
      status, last_updated, summary.

$g_csv_columns

      This configuration option is used to set the columns to be included
      in CSV exports, and the order in which they will be displayed.

      See $g_view_issues_page_columns for details.

      By default the following columns are selected: id, project_id,
      reporter_id, handler_id, priority, severity, reproducibility,
      version, build, projection, category_id, date_submitted, eta, os,
      os_build, platform, view_state, last_updated, summary, status,
      resolution, fixed_in_version.

$g_excel_columns

      This configuration option is used to set the columns to be included
      in Excel exports, and the order in which they will be displayed.

      See $g_view_issues_page_columns for details.

      By default the following columns are selected: id, project_id,
      reporter_id, handler_id, priority, severity, reproducibility,
      version, build, projection, category_id, date_submitted, eta, os,
      os_build, platform, view_state, last_updated, summary, status,
      resolution, fixed_in_version.

$g_show_bug_project_links

      Show project links when in All Projects mode. Default is ON.

$g_filter_position

      Position of the filter box, can be: POSITION_* (POSITION_TOP,
      POSITION_BOTTOM, or POSITION_NONE for none). Default is
      FILTER_POSITION_TOP.

$g_action_button_position

      Position of action buttons when viewing issues. Can be:
      POSITION_TOP, POSITION_BOTTOM, or POSITION_BOTH. Default is
      POSITION_BOTTOM.

$g_show_product_version

      This controls display of the product version in the report, view,
      update and print issue pages. This flag also applies to other
      product version related fields like product build, fixed in
      version, and target version. Valid values are ON, OFF, and AUTO. ON
      for always displayed, AUTO for displayed when project has versions
      defined, and OFF for always OFF. The default value is AUTO.

$g_show_version_dates_threshold

      The access level threshold at which users will see the date of
      release for product versions. Dates will be shown next to the
      product version, target version and fixed in version fields. Set
      this threshold to NOBODY to disable the feature. Default value is
      NOBODY.

$g_show_realname

      This control will replace the user's userid with their realname. If
      it is set to ON, and the real name field has been populated, the
      replacement will occur. It defaults to OFF.

$g_sort_by_last_name

      Sorting for names in dropdown lists. If turned on, "Jane Doe" will
      be sorted with the "D"s. It defaults to OFF.

$g_show_avatar

      Show the users' avatar

      In addition to enabling this configuration option it is necessary
      to install an avatar plugin like the Gravatar plugin which is
      bundled out of the box.

$g_show_avatar_threshold

      The threshold of users for which MantisBT should show the avatar
      (default DEVELOPER). Note that the threshold is related to the user
      for whom the avatar is being shown, rather than the user who is
      currently logged in.

$g_show_changelog_dates

      Show release dates on changelog. It defaults to ON.

$g_show_roadmap_dates

      Show release dates on roadmap. It defaults to ON.

$g_status_colors

      Status color codes, using the Tango color palette.

$g_display_bug_padding

      The padding level when displaying bug ids. The bug id will be
      padded with 0's up to the size given.

$g_display_bugnote_padding

      The padding level when displaying bugnote ids. The bugnote id will
      be padded with 0's up to the size given.


5.12. Time
----------

$g_cookie_time_length

      Time for long lived cookie to live in seconds. It is also used as
      the default for permanent logins if $g_allow_permanent_cookie is
      enabled and selected. Default is 1 year.

$g_allow_permanent_cookie

      Allow users to opt for a 'permanent' cookie when logging in.
      Controls the display of the 'Remember my login in this browser'
      checkbox on the login page. See $g_cookie_time_length.

$g_wait_time

      Time to delay between page redirects (in seconds). Users can
      override this setting in their user preferences. Default is 2
      seconds.

$g_long_process_timeout

      This timeout is used by pages which does time consuming operations
      like upgrading the database. The default value of 0 disables
      timeout. Note that this timeout is specified in seconds.


5.13. Date
----------

These variables control how the date is displayed. The default is ISO
8601 formatting.

Please refer to the PHP manual for details on available formatting
options.

$g_short_date_format

      This format is used in the bug listing pages (eg: View Bugs).
      Default is Y-m-d.

$g_normal_date_format

      This format is used in the view/update bug pages, bug notes, manage
      section, and news section. Default is Y-m-d H:i.

$g_complete_date_format

      This format is used on the top of each page (current time) and the
      emails that are sent out. Default is Y-m-d H:i T.

$g_datetime_picker_format

      This format is used with the datetime picker widget. Default is
      Y-MM-DD HH:mm.


      Note

      The formatting convention for the DateTime picker is different from
      the one used for the other date settings described above; see
      Moment.js documentation for details.


      Warning

      This format needs to match the one defined in $g_normal_date_format.
      Inconsistencies between these two settings, e.g. using different
      date ordering (DMY, MDY or YMD) or displaying the month as a number
      vs a word or abbreviation, may result in unexpected behavior such
      as an invalid interpretation of the date by the DateTime picker
      widget, or errors trying to save a modified date.


5.14. Time Zone
---------------

$g_default_timezone

      Default timezone to use in MantisBT. This configuration is normally
      initialized when installing Mantis. It should be set to one of the
      values specified in the List of Supported Timezones.

      If this config is left blank, the timezone will be initialized by
      calling function date_default_timezone_get(), which will fall back
      to UTC if unable to determine the timezone.

      Correct configuration of this variable can be confirmed by running
      the administration checks. Users can override the default timezone
      under user their preferences.


5.15. News
----------

These options are used to control the query that selects the news entries
to be displayed.

$g_news_enabled

      Indicates whether the news feature should be enabled or disabled.
      The default is OFF. The news feature is deprecated in favor of
      being moved to a plugin.

$g_news_limit_method

      Limit the news entry that are displayed by number of entries
      (BY_LIMIT) or by date (BY_DATE). The default is BY_LIMIT.

$g_news_view_limit

      The limit for the number of news entries to be displayed. This
      option is only used if $g_news_limit_method is set to BY_LIMIT.

$g_news_view_limit_days

      Specifies the number of dates after which the news are not
      displayed. This option is only used if $g_news_limit_method is set
      to BY_DATE.

$g_private_news_threshold

      Specifies the access level required to view private news. The
      default is DEVELOPER.


5.16. Default Preferences
-------------------------

$g_default_new_account_access_level

      This is the default access level users are given when their account
      is created by email. The default access level is REPORTER. Look in
      constant_inc.php for other values.

$g_default_project_view_status

      The default viewing status for new projects (VS_PUBLIC or
      VS_PRIVATE). The default is VS_PUBLIC.

$g_default_bug_description

      Default value for bug description field used on bug report page.
      Default is empty description.

$g_default_bug_additional_info

      Default value for bug additional info field used on bug report
      page. Default is empty.

$g_default_bug_steps_to_reproduce

      Default value for bug steps to reproduce field used on bug report
      page. Default is empty.

$g_default_bug_view_status

      The default viewing status for the new bug (VS_PUBLIC or
      VS_PRIVATE). The default is VS_PUBLIC.

$g_default_bugnote_view_status

      The default viewing status for the new bugnote (VS_PUBLIC or
      VS_PRIVATE). The default is VS_PUBLIC.

$g_timeline_view_threshold

      Threshold for viewing timeline information. Use NOBODY to turn it
      off. If the timeline is turned off, the other widgets are displayed
      in a two column view. The default is VIEWER.

$g_default_reminder_view_status

      The default viewing status for the new reminders (VS_PUBLIC or
      VS_PRIVATE). The default is VS_PUBLIC.

$g_reminder_receive_threshold

      The minimum access level for a user to show up in the reminder user
      picker. Note that this is the access level for the project for
      which the issue belongs. The default is DEVELOPER.

$g_default_bug_resolution

      The resolution for a newly created issue. The default is OPEN. Look
      in constant_inc.php for other values.

$g_default_bug_severity

      The severity for a newly created issue. The default is MINOR. Look
      in constant_inc.php for other values.

$g_default_bug_priority

      The priority for a newly created issue. The default is NORMAL. Look
      in constant_inc.php for other values.

$g_default_bug_reproducibility

      The reproducibility for a newly created issue. The default is
      REPRODUCIBILITY_HAVENOTTRIED. Look in constant_inc.php for other
      values.

$g_default_bug_projection

      The projection for a newly created issue. The default is
      PROJECTION_NONE. Look in constant_inc.php for other values.

$g_default_bug_eta

      The ETA for a newly created issue. The default is ETA_NONE. Look in
      constant_inc.php for other values.

$g_default_category_for_moves

      Default global category to be used when an issue is moved from a
      project to another that doesn't have a category with a matching
      name. The default is 1 which is the "General" category that is
      created in the default database.

$g_default_limit_view

      Number of bugs to show in the View Bugs page. The default value is
      50.

$g_default_show_changed

      Highlight bugs that have changed during the last N hours. The
      default value is 6.

$g_hide_status_default

      Controls which issues will be displayed in the View Issues page.
      Default value is CLOSED, implying that all issues at "closed" or
      higher state will not be shown.

$g_min_refresh_delay

      This is the delay between automatic refreshes of the View Issues
      page in minutes. Make sure refresh delay in user preferences isn't
      too short. If a users set their preferences to be lower then it is
      bumped back up to this minimum value. The default value is 10
      minutes.

These settings are used as the default values for preferences for new
users. Each user can override these settings through the user preferences
form. Default language is set to default site language
($g_default_language).

$g_default_refresh_delay

      Default page refresh delay (in minutes). This is for the bug
      listing pages. Default value is 30 minutes.

$g_default_redirect_delay

      Default delay before a user is redirected to a page after being
      prompted by a message (eg: operational successful). Default value
      is 2 seconds.

$g_default_bugnote_order

      This controls the time order in which bug notes are displayed. It
      can be either ASC (oldest first, the default) or DESC (newest
      first).

$g_default_email_on_new, $g_default_email_on_assigned,
$g_default_email_on_feedback, $g_default_email_on_resolved,
$g_default_email_on_closed

      Default user preferences to enable receiving emails when a bug is
      set to the corresponding status. This option only has an effect if
      users have the required access level to receive such emails.
      Default value is ON.

$g_default_email_on_reopened

      Default user preferences to enable receiving emails when bugs are
      re-opened. Default value is ON.

$g_default_email_on_bugnote

      Default user preferences to enable receiving emails when bugnotes
      are added to bugs. Default value is ON.

$g_default_email_on_status

      Default user preferences to enable receiving emails when status is
      changed. Default is OFF.

$g_default_email_on_priority

      Default user preferences to enable receiving emails when priority
      is changed. Default is OFF.

$g_default_email_on_new_minimum_severity,
$g_default_email_on_assigned_minimum_severity,
$g_default_email_on_feedback_minimum_severity,
$g_default_email_on_resolved_minimum_severity,
$g_default_email_on_closed_minimum_severity,
$g_default_email_on_reopened_minimum_severity,
$g_default_email_on_bugnote_minimum_severity

      Default user preferences to enable filtering based on issue
      severity. These correspond to the email_on_<status> settings.
      Default is 'any'.

$g_default_email_on_bugnote_minimum_severity

      Default user preference to enable filtering based on issue
      severity. These corresponds to the email_on_bugnote setting.
      Default is 'any'.

$g_default_email_on_status_minimum_severity

      Default user preference to enable filtering based on issue
      severity. These corresponds to the email_on_status settings.
      Default is 'any'.

$g_default_email_on_priority_minimum_severity

      Default user preferences to enable filtering based on issue
      severity. These corresponds to the email_on_priority settings.
      Default is 'any'.

$g_default_bug_relationship_clone

      Default relationship between a new bug and its parent when cloning
      it

$g_default_bug_relationship

      Default for new bug relationships

$g_show_sticky_issues

      TODO

$g_default_email_on_new

      TODO

$g_default_email_on_assigned

      TODO

$g_default_email_on_feedback

      TODO

$g_default_email_on_resolved

      TODO

$g_default_email_on_closed

      TODO

$g_default_email_on_new_minimum_severity

      TODO

$g_default_email_on_assigned_minimum_severity

      TODO

$g_default_email_on_feedback_minimum_severity

      TODO

$g_default_email_on_resolved_minimum_severity

      TODO

$g_default_email_on_closed_minimum_severity

      TODO

$g_default_email_on_reopened_minimum_severity

      TODO

$g_default_email_bugnote_limit

      TODO

See also: Section 7.4, “Email Notifications”


5.17. Summary
-------------

These are the settings that are used to configuration options related to
the Summary page. This page contains statistics about the bugs in
MantisBT.

$g_reporter_summary_limit

      Limit how many reporters to show in the summary page. This is
      useful when there are dozens or hundreds of reporters. The default
      value is 10.

$g_date_partitions

      An array of date lengths to count bugs by (in days) for the summary
      by date. The default is to count for 1, 2, 3, 7, 30, 60, 90, 180,
      and 365.

$g_summary_category_include_project

      Specifies whether category names should be preceded by project
      names (eg: [Project] Category) when the summary page is viewed for
      all projects. This is useful in the case where category names are
      common across projects. The default is OFF.

$g_view_summary_threshold

      Specifies the access level required to view the summary page.
      Default is MANAGER.

$g_severity_multipliers

      An array of multipliers which are used to determine the
      effectiveness of reporters based on the severity of bugs. Higher
      multipliers will result in an increase in reporter effectiveness.
      The default multipliers are:

      $g_severity_multipliers = array ( FEATURE => 1,
                                        TRIVIAL => 2,
                                        TEXT => 3,
                                        TWEAK => 2,
                                        MINOR => 5,
                                        MAJOR => 8,
                                        CRASH => 8,
                                        BLOCK => 10 );

      The keys of the array are severity constants from constant_inc.php
      or from custom_constants_inc.php if you have custom severities
      defined. The values are integers, typically in the range of 0 to
      10. If you would like for a severity to not count towards
      effectiveness, set the value to 0 for that severity.

$g_resolution_multipliers

      An array of multipliers which are used to determine the
      effectiveness of reporters based on the resolution of bugs. Higher
      multipliers will result in a decrease in reporter effectiveness.
      The only resolutions that need to be defined here are those which
      match or exceed $g_bug_resolution_not_fixed_threshold. The default
      multipliers are:

      $g_resolution_multipliers = array( UNABLE_TO_REPRODUCE => 2,
                                         NOT_FIXABLE => 1,
                                         DUPLICATE => 3,
                                         NOT_A_BUG => 5,
                                         SUSPENDED => 1,
                                         WONT_FIX => 1 );

      The keys of the array are resolution constants from
      constant_inc.php or from custom_constants_inc.php if you have
      custom resolutions defined. Resolutions not included here will be
      assumed to have a multiplier value of 0. The values are integers,
      typically in the range of 0 to 10. If you would like for a
      resolution to not count towards effectiveness, set the value to 0
      for that resolution or remove it from the array completely. Note
      that these resolution multipliers are stacked on top of the
      severity multipliers. Therefore by default, a user reporting many
      duplicate bugs at severity level BLOCK will be far worse off than a
      user reporting many duplicate bugs at severity level FEATURE.


5.18. Bugnote
-------------

$g_bugnote_order

      Order to use for sorting bugnotes by submit date. Possible values
      include ASC for ascending and DESC for descending order. The
      default value is ASC.


5.19. File Upload
-----------------

MantisBT allows users to upload file attachments and associate them with
bugs as well as projects. Bug attachments / project documents can be
uploaded to the webserver or database. When bugs are uploaded to the
webserver they are uploaded to the path that is configured in the project
properties. In case of problems getting the file upload feature to work,
check the following resources: PHP Manual .

$g_allow_file_upload

      Whether to allow/disallow uploading of attachments. Default value
      is ON.

$g_file_upload_method

      Specify the location for uploading attachments. In case of DISK
      methods you need to provide the webserver with write access rights
      to the configured upload path (configured in the project) and
      temporary upload path (used by PHP).

      Values: DISK or DATABASE

      Default: DATABASE

$g_dropzone_enabled

      Whether to enable/disable drag and drop zone for uploading of
      attachments. Default value is ON.

$g_file_upload_max_num

      Maximum number of files that can be uploaded simultaneously.
      Default value is 10.

$g_max_file_size

      Maximum file size that can be uploaded. Default value is about 5
      MiB. The maximum size is also affected by the PHP options
      post_max_size (default 8 MiB), upload_max_filesize (default 2 MiB)
      and memory_limit (default 128 MiB) specified in php.ini.

$g_allowed_files

      Authorized file types (whitelist).

      If $g_allowed_files is filled in, NO other file types will be
      allowed. If empty, any extensions not specifically excluded by
      $g_disallowed_files list will be authorized ($g_disallowed_files
      takes precedence over $g_allowed_files). Separate items by commas,
      e.g. 'bmp,gif,jpg,png,txt,zip'.

$g_disallowed_files

      Forbidden file types (blacklist).

      All file extensions in this list will be unauthorized. Separate
      items by commas, e.g. 'php,html,java,exe,pl,svg'.


      Warning

      SVG files are disabled by default, for security reasons. It is
      recommended to also disable all extensions that can be executed by
      your server.

$g_preview_attachments_inline_max_size

      This limit applies to previewing of image / text attachments. If
      the attachment size is smaller than the specified value, the
      attachment is previewed with the issue details. The previewing can
      be disabled by setting this configuration to 0. The default value
      is 256 * 1024 (256KB).

$g_preview_text_extensions

      An array of file extensions (not including dots) for text files
      that can be previewed inline.

$g_preview_image_extensions

      An array of file extensions (not including dots) for image files
      that can be previewed inline.

$g_fileinfo_magic_db_file

      Specify the filename of the magic database file. This is used by
      PHP to guess what the MIME type of a file is. Usually it is safe to
      leave this setting as the default (blank) as PHP is usually able to
      find this file by itself.

$g_file_download_xsendfile_enabled

      Enable support for sending files to users via a more efficient
      X-Sendfile method. HTTP server software supporting this technique
      includes Lighttpd, Cherokee, Apache with mod_xsendfile and nginx.
      You may need to set the proceeding
      file_download_xsendfile_header_name option to suit the server you
      are using.

$g_file_download_xsendfile_header_name

      The name of the X-Sendfile header to use. Each server tends to
      implement this functionality in a slightly different way and thus
      the naming conventions for the header differ between each server.
      Lighttpd from v1.5, Apache with mod_xsendfile and Cherokee web
      servers use X-Sendfile. nginx uses X-Accel-Redirect and Lighttpd
      v1.4 uses X-LIGHTTPD-send-file.

$g_attachments_file_permissions

      When using DISK for storing uploaded files, this setting control
      the access permissions they will have on the web server: with the
      default value (0400) files will be read-only, and accessible only
      by the user running the apache process (probably "apache" in Linux
      and "Administrator" in Windows). For more details on unix style
      permissions: http://www.perlfect.com/articles/chmod.shtml

$g_absolute_path_default_upload_folder

      Absolute path to the default upload folder. Requires trailing / or
      \.

$g_preview_max_width

      Specifies the maximum width for the auto-preview feature. If no
      maximum width should be imposed then it should be set to 0.

$g_preview_max_height

      Specifies the maximum height for the auto-preview feature. If no
      maximum height should be imposed then it should be set to 0.

$g_view_attachments_threshold

      Access level needed to view bugs attachments. View means to see the
      file names, sizes, and timestamps of the attachments.

$g_download_attachments_threshold

      Access level needed to download bug attachments.

$g_delete_attachments_threshold

      Access level needed to delete bug attachments.

$g_allow_view_own_attachments

      Allow users to view attachments uploaded by themselves even if
      their access level is below view_attachments_threshold.

$g_allow_download_own_attachments

      Allow users to download attachments uploaded by themselves even if
      their access level is below download_attachments_threshold.

$g_allow_delete_own_attachments

      Allow users to delete attachments uploaded by themselves even if
      their access level is below delete_attachments_threshold.


5.20. HTML
----------

$g_html_make_links

      This flag controls whether URLs and email addresses are
      automatically converted to clickable links. Additionally, for URL
      links, it determines where they open when clicked (target
      attribute) and their type.

      The options below can be combined using bitwise operators, though
      not all possible combinations make sense. The default is
      LINKS_SAME_WINDOW | LINKS_NOOPENER.

        * 

          OFF - do not convert URLs or emails

        * 

          LINKS_SAME_WINDOW - convert to links that open in current
          tab/window. NOTE: for backwards-compatibility, this is
          equivalent to ON.

        * 

          LINKS_NEW_WINDOW - convert to links that open in a new
          tab/window. Overrides LINKS_SAME_WINDOW.

        * 

          LINKS_NOOPENER - Links have the noopener type.

        * 

          LINKS_NOREFERRER - Links have the noreferrer type, i.e. they
          omit the Referer header. Implies LINKS_NOOPENER.

$g_html_valid_tags

      This is the list of HTML tags that are allowed.Do NOT include href
      or img tags here.Do NOT include tags that have parameters (eg. )The
      HTML code is allowed to enter the database as is. The
      $g_allow_href_tags does not have to be enabled to make URL links.
      The package will automatically hyperlink properly formatted URLs
      eg. https://blah.blah/ or mailto://<EMAIL>/

$g_bottom_include_page

      Specifies a file to be included at the bottom of each page. It can
      be used e.g. for company branding, to include Google Analytics
      script, etc.

$g_top_include_page

      Specifies a file to be included at the top of each page. It can be
      used e.g. for company branding.

      If a file is supplied, the logo specified by $g_logo_image (see
      Section 5.11, “Display”) will not be shown, and the include file
      will have to handle display of the logo. To do so you can use the
      html_print_logo() API function, which will display the logo with an
      URL link if one has been specified in $g_logo_url

      Example top include PHP file with logo and centered page title:

      <div id="banner" style="display: flex; align-items: center;">
              <div style="width: 10%;">
                      <?php html_print_logo(); ?>
              </div>
      
              <div class="center">
                      <span class="pagetitle">
                              <?php global $g_window_title; echo $g_window_title; ?>
                      </span>
              </div>
      
              <div style="width: 10%;">
              </div>
      </div>

$g_css_include_file

      Set this to point to the CSS file of your choice.

$g_css_rtl_include_file

      Set this to point to the RTL CSS file of your choice.

$g_cdn_enabled

      A flag that indicates whether to use CDN (content delivery
      networks) for loading javascript libraries and their associated
      CSS. This improves performance for loading MantisBT pages. This can
      be disabled if it is desired that MantisBT doesn't reach out
      outside corporate network. Default OFF.

$g_main_menu_custom_options

      This option will add custom options to the main menu. It is an
      array of arrays listing the caption, access level required, and the
      link to be executed. For example:

      $g_main_menu_custom_options = array(
          array( 
              'title'        => 'My Link',
              'access_level' => MANAGER,
              'url'          => 'my_link.php',
              'icon'         => 'fa-plug'
          ),
          array( 
              'title'        => 'My Link2',
              'access_level' => ADMINISTRATOR,
              'url'          => 'my_link2.php',
              'icon'         => 'fa-plug'
          )
      );

      Note that if the caption is found in custom_strings_inc.php (see
      Section 7.1, “Strings / Translations”), it will be replaced by the
      corresponding translated string. Options will only be added to the
      menu if the current logged in user has the appropriate access
      level.

      Use icons from Font Awesome. Add "fa-" prefix to icon name.

      Access level is an optional field, and no check will be done if it
      is not set. Icon is an optional field, and 'fa-plug' will be used
      if it is not set.

$g_html_valid_tags_single_line

      These are the valid html tags for single line fields (e.g. issue
      summary). do NOT include a or img tags here. do NOT include tags
      that require attributes.

$g_max_dropdown_length

      Maximum length of the description in a dropdown menu (for search)
      set to 0 to disable truncations

$g_wrap_in_preformatted_text

      This flag controls whether pre-formatted text (delimited by HTML
      pre tags is wrapped to a maximum linelength (defaults to 100 chars
      in strings_api). If turned off, the display may be wide when
      viewing the text.


5.21. Authentication
--------------------


5.21.1. Global authentication parameters

$g_login_method

      Specifies which method will be used to authenticate. It should be
      one of the following values (defaults to MD5):

        * 

          MD5 - user's password is stored as a hash in the database

        * 

          LDAP - authenticates against an LDAP (or Active Directory)
          server

        * 

          BASIC_AUTH

        * 

          HTTP_AUTH

      In addition, the following deprecated values are supported for
      backwards-compatibility, and should no longer be used:

        * 

          PLAIN - password is stored in plain, unencrypted text in the
          database

        * 

          CRYPT

        * 

          CRYPT_FULL_SALT

      Note: you may not be able to easily switch encryption methods, so
      this should be carefully chosen at install time. However, MantisBT
      will attempt to "fall back" to older methods if possible.

$g_reauthentication

      Determines whether MantisBT will require the user to
      re-authenticate before granting access to the Admin areas after
      timeout expiration. Defaults to ON

$g_reauthentication_expiry

      Duration of the reauthentication timeout, in seconds. Defaults to 5
      minutes.


5.21.2. LDAP authentication method parameters

The parameters below are only used if $g_login_method (see Section
5.21.1, “Global authentication parameters” above) is set to LDAP.

$g_ldap_server

      Specifies the LDAP or Active Directory server to connect to.

      This must be a full LDAP URI (protocol://hostname:port)

        * 

          Protocol must be either:

            * 

              ldap - unencrypted or opportunistic TLS (STARTTLS)

            * 

              ldaps - TLS encryption

        * 

          Port number is optional, and defaults to 389.

          If this doesn't work, try using one of the following standard
          port numbers: 636 (ldaps); for Active Directory Global Catalog
          forest-wide search, use 3268 (ldap) or 3269 (ldaps).

      Examples of valid URI:

      ldap://ldap.example.com/
      ldaps://ldap.example.com:3269/


      Note

      Multiple servers can be specified as a space-separated list.

$g_ldap_use_starttls

      Determines whether the connection will attempt an opportunistic
      upgrade to a TLS connection (STARTTLS).

      Defaults to ON.


      Warning

      For security, a failure aborts the entire connection, so make sure
      your server supports StartTLS if this setting is ON, and use the
      ldap:// scheme (not ldaps://).

$g_ldap_tls_protocol_min

      An integer indicating the minimum version of the TLS protocol to
      allow. This maps to the LDAP_OPT_X_TLS_PROTOCOL_MIN LDAP library
      option.

      For example, LDAP_OPT_X_TLS_PROTOCOL_TLS1_2.

      Defaults to OFF (protocol version not set).


      Note

      Requires PHP 7.1 or later.


      Warning

      For security, a failure aborts the entire connection.

$g_ldap_root_dn

      The root distinguished name for LDAP searches. For example,
      dc=example, dc=com.

$g_ldap_organization

      LDAP search filter for the organization. For example,
      (organizationname=*Traffic). Defaults to '' (empty string).

$g_ldap_protocol_version

      The LDAP Protocol Version to use (2, 3 or 0). This maps to the
      LDAP_OPT_PROTOCOL_VERSION ldap library option.

      Defaults to 3.


      Note

      If 0, then the protocol version is not set, and you get whatever
      default the underlying LDAP library uses.

      In almost all cases you should use 3. LDAPv3 was introduced back in
      1997, and LDAPv2 was deprecated in 2003 by RFC3494.

$g_ldap_network_timeout

      Duration of the timeout for TCP connection to the LDAP server (in
      seconds). This maps to LDAP_OPT_NETWORK_TIMEOUT ldap library
      option. Defaults to 0 (infinite).

      Set this to a low value when the hostname defined in $g_ldap_server
      resolves to multiple IP addresses, allowing rapid failover to the
      next available LDAP server.

$g_ldap_follow_referrals

      Determines whether the LDAP library automatically follows referrals
      returned by LDAP servers or not. This maps to LDAP_OPT_REFERRALS
      ldap library option. Defaults to ON.

      For Active Directory, this should be set to OFF. If you have only
      one LDAP server, setting to this to OFF is advisable to prevent any
      man-in-the-middle attacks.

$g_ldap_bind_dn

      The distinguished name of the service account to use for binding to
      the LDAP server. For example,
      cn=ldap,ou=Administrators,dc=example,dc=com. Leave empty for
      anonymous binding.

$g_ldap_bind_passwd

      The password for the service account used to establish the
      connection to the LDAP server. For anonymous binding, leave empty.

$g_ldap_uid_field

      The LDAP field for username. Defaults to uid.

      For Active Directory, set to sAMAccountName.

$g_ldap_email_field

      The LDAP field for e-mail address. Defaults to mail.

$g_ldap_realname_field

      The LDAP field for the user's real name (i.e. common name).
      Defaults to cn.

$g_use_ldap_realname

      Use the realname specified in LDAP (ON) rather than the one stored
      in the database (OFF). Defaults to OFF.


      Note

      MantisBT will update the database with the data retrieved from LDAP
      when ON.

$g_use_ldap_email

      Use the email address specified in LDAP (ON) rather than the one
      stored in the database (OFF). Defaults to OFF.


      Note

      MantisBT will update the database with the data retrieved from LDAP
      when ON.

$g_ldap_simulation_file_path

      This configuration option allows replacing the ldap server with a
      comma-delimited text file for development or testing purposes.

      The LDAP simulation file format is as follows:

        * 

          No headers

        * 

          One line per user

        * 

          Each line has 4 comma-delimited fields

            * 

              username

            * 

              realname

            * 

              e-mail

            * 

              password

        * 

          Any extra fields are ignored


      Warning

      On production systems, this option should be set to '' (This is the
      default).


5.22. Status Settings
---------------------

$g_bug_submit_status

      Status to assign to the bug when submitted. Default value is NEW_.

$g_bug_assigned_status

      Status to assign to the bug when assigned. Default value is
      ASSIGNED.

$g_bug_reopen_status

      Status to assign to the bug when reopened. Default value is
      FEEDBACK.

$g_bug_feedback_status

      Status to assign to the bug when feedback is required from the
      issue reporter. Once the reporter adds a note the status moves back
      from feedback to $g_bug_assigned_status or $g_bug_submit_status
      based on whether the bug assigned or not.

$g_reassign_on_feedback

      When a note is added to a bug currently in $g_bug_feedback_status,
      and the note author is the bug's reporter, this option will
      automatically set the bug status to $g_bug_submit_status or
      $g_bug_assigned_status if the bug is assigned to a developer.
      Default value is ON.

$g_bug_duplicate_resolution

      Default resolution to assign to a bug when it is resolved as being
      a duplicate of another issue. Default value is DUPLICATE.

$g_bug_reopen_resolution

      Resolution to assign to the bug when reopened. Default value is
      REOPENED.

$g_auto_set_status_to_assigned

      Automatically set status to $g_bug_assigned_status whenever a bug
      is assigned to a person. Installations where assigned status is to
      be used when the defect is in progress, rather than just put in a
      person's queue should set it to OFF. Default is ON. For the status
      change to be effective, these conditions must be met:

        * 

          Bug has no handler, and a new handler is selected

        * 

          The assignment is not part of a explicit status change

        * 

          Current bug status is lower than defined "assigned" status

        * 

          "Assigned" status is reachable by workflow configuration

      If the conditions are not met, the assignment is still made, but
      status will not be modified.

$g_bug_resolved_status_threshold

      Bug is resolved, ready to be closed or reopened. In some custom
      installations a bug maybe considered as resolved when it is moved
      to a custom (FIXED OR TESTED) status.

$g_bug_resolution_fixed_threshold

      Threshold resolution which denotes that a bug has been resolved and
      successfully fixed by developers. Resolutions above and including
      this threshold and below $g_bug_resolution_not_fixed_threshold are
      considered to be resolved successfully. Default value is FIXED.

$g_bug_resolution_not_fixed_threshold

      Threshold resolution which denotes that a bug has been resolved
      without being successfully fixed by developers. Resolutions above
      this threshold are considered to be resolved in an unsuccessful
      way. Default value is UNABLE_TO_REPRODUCE.

$g_bug_readonly_status_threshold $g_update_readonly_bug_threshold

      Bug becomes readonly if its status is >=
      $g_bug_readonly_status_threshold. The bug becomes read/write again
      if re-opened and its status becomes less than this threshold. The
      default is RESOLVED. Once the bug becomes readonly, a user with an
      access level greater than or equal to
      $g_update_readonly_bug_threshold can still edit the bug.

$g_status_enum_workflow

      'status_enum_workflow' defines the workflow, and reflects a simple
      2-dimensional matrix. For each existing status, you define which
      statuses you can go to from that status, e.g. from NEW_ you might
      list statuses '10:new,20:feedback,30:acknowledged' but not higher
      ones.The default is no workflow, where all states are accessible
      from any others.

$g_report_bug_threshold

      This is the access level required to open a bug. The default is
      REPORTER.

$g_update_bug_threshold

      This is the access level generally required to update the content
      of a bug. The default is UPDATER.

$g_handle_bug_threshold

      This is the access level generally required to be access level
      needed to be listed in the assign to field. The default is
      DEVELOPER. If a more restrictive setting can be determined from
      $g_set_status_threshold, it will be used.

$g_update_bug_status_threshold $g_set_status_threshold

      These settings control the access level required to promote a bug
      to a new status once the bug is opened.$g_set_status_threshold is
      an array indexed by the status value that allows a distinct setting
      for each status. It defaults to blank.If the appropriate status is
      not defined above, $g_update_bug_status_threshold is used instead.
      The default is DEVELOPER.

$g_bugnote_user_edit_threshold

      Threshold at which a user can edit his/her own bugnotes. The
      default value is equal to the configuration setting
      $g_update_bugnote_threshold.

$g_bugnote_user_delete_threshold

      Threshold at which a user can delete his/her own bugnotes. The
      default value is equal to the configuration setting
      $g_delete_bugnote_threshold.

$g_bugnote_user_change_view_state_threshold

      Threshold at which a user can change the view status of his/her own
      bugnotes. The default value is equal to the configuration setting
      $g_change_view_status_threshold.

$g_allow_reporter_reopen

      If set, the bug reporter is allowed to reopen their own bugs once
      resolved, regardless of their access level. This allows the
      reporter to disagree with the resolution. The default is ON.

$g_allow_parent_of_unresolved_to_close

      If set, no check is performed on the status of a bug's children,
      which allows the parent to be closed whether or not the children
      have been resolved. The default is OFF.

$g_bug_readonly_status_threshold

      Bug becomes readonly if its status is >= this status. The bug
      becomes read/write again if re-opened and its status becomes less
      than this threshold.

$g_bug_closed_status_threshold

      Bug is closed. In some custom installations a bug may be considered
      as closed when it is moved to a custom (COMPLETED or IMPLEMENTED)
      status.

See also: Section 7.5, “Customizing Status Values”


5.23. Filters
-------------

$g_filter_by_custom_fields

      Show custom fields in the filter dialog and use these in filtering.
      Defaults to ON.

$g_filter_custom_fields_per_row

      The number of filter fields to display per row. The default is 8.

$g_view_filters = SIMPLE_DEFAULT;

      Controls the display of the filter pages. Possible values are:

        * 

          SIMPLE_ONLY - only allow use of simple view

        * 

          ADVANCED_ONLY - only allow use of advanced view (allows
          multiple value selections)

        * 

          SIMPLE_DEFAULT - defaults to simple view, but shows a link for
          advanced

        * 

          ADVANCED_DEFAULT - defaults to advanced view, but shows a link
          for simple

$g_use_dynamic_filters = ON;

      This switch enables the use of AJAX to dynamically load and create
      filter form controls upon request. This method will reduce the
      amount of data that needs to be transferred upon each page load
      dealing with filters and thus will result in speed improvements and
      bandwidth reduction.

$g_create_permalink_threshold

      The threshold required for users to be able to create permalinks
      (default DEVELOPER). To turn this feature off use NOBODY.

$g_create_short_url

      The service to use to create a short URL. The %s will be replaced
      by the long URL. By default https://www.tinyurl service is used to
      shorten URLs.

$g_view_filters

      Controls the display of the filter pages.

$g_use_dynamic_filters

      This switch enables the use of AJAX to dynamically load and create
      filter form controls upon request. This method will reduce the
      amount of data that needs to be transferred upon each page load
      dealing with filters and thus will result in speed improvements and
      bandwidth reduction.


5.24. Misc
----------

$g_user_login_valid_regex

      The regular expression to use when validating new user login names.
      The default regular expression allows a-z, A-Z, 0-9, +, -, dot,
      space and underscore. If you change this, you may want to update
      the ERROR_USER_NAME_INVALID string in the language files to explain
      the rules you are using on your site.

      See Wikipedia for more details about regular expressions. For
      testing regular expressions, use Rubular.

$g_monitor_bug_threshold

      Access level needed to monitor issues. The default value is
      REPORTER.

$g_show_monitor_list_threshold

      Access level needed to show the list of users monitoring an issue.
      The default value is DEVELOPER.

$g_monitor_add_others_bug_threshold

      Access level needed to add other users to the list of users
      monitoring an issue. The default value is DEVELOPER.

      This setting should not be lower than
      $g_show_monitor_list_threshold.

$g_monitor_delete_others_bug_threshold

      Access level needed to delete other users from the list of users
      monitoring an issue. The default value is DEVELOPER.

      This setting should not be lower than
      $g_show_monitor_list_threshold.

$g_print_reports_threshold

      Grants users access to the Print Reports functionality (Word/HTML)
      from the View Issues page. The default value is UPDATER.

$g_export_issues_threshold

      Access level required to export issues to CSV and Excel formats
      from the View Issues page. The default value is VIEWER.

$g_allow_reporter_close

      Allow reporters to close the bugs they reported.

$g_delete_bug_threshold

      Allow the specified access level and above to delete bugs.

$g_bug_move_access_level

      Allow the specified access level and above to move bugs between
      projects.

$g_allow_account_delete

      Allow users to delete their own accounts.

$g_allow_anonymous_login

      Enable anonymous access to Mantis. You must also specify
      $g_anonymous_account as the account which anonymous users will
      browse Mantis with. The default setting is OFF.

$g_anonymous_account

      Define the account which anonymous users will assume when using
      Mantis. This account is considered by Mantis to be protected from
      modification. In other words, this account can only be modified by
      users with an access level equal to or higher than
      $g_manage_user_threshold. Anonymous users will not be able to
      adjust preferences or change account settings like normal users
      can.

      You will need to create a new account to use for this
      $g_anonymous_account setting. When creating the account you should
      specify a password, email address and so forth in the same way
      you'd create any other account. It is suggested that the access
      level for this account be set to VIEWER or some other read only
      level.

      The anonymous user account will not receive standard notifications
      and can not monitor issues.

      The default setting is blank/undefined. You only need to define
      this setting when $g_allow_anonymous_login is set to ON.

$g_bug_link_tag

      If a number follows this tag it will create a link to a bug.
      Default is '#'.

        * 

          '#': a link would be #45

        * 

          'bug:' a link would be bug:98

$g_bugnote_link_tag

      If a number follows this tag it will create a link to a bug note.
      Default is '~'.

        * 

          '~': a link would be ~45

        * 

          'bugnote:' a link would be bugnote:98

$g_enable_project_documentation

      Specifies whether to enable support for project documents or not.
      Default is OFF. This feature is deprecated and is expected to be
      moved to a plugin in the future.

$g_admin_site_threshold

      Threshold at which a user is considered to be a site administrator.
      These users have the highest level of access to your Mantis
      installation. This access level is required to change key Mantis
      settings (such as server paths) and perform other administrative
      duties. You may need to change this value from the default of
      ADMINISTRATOR if you have defined a new access level to replace the
      default ADMINISTRATOR level in constant_inc.php.


      Warning

      This is a potentially dangerous configuration option. Users at or
      above this threshold value will have permission to all aspects of
      Mantis including the admin/ directory. With this access level,
      users can damage your installation of Mantis, destroy your database
      or have elevated access to your server.

      DO NOT CHANGE THIS VALUE UNLESS YOU ABSOLUTELY KNOW WHAT YOU'RE
      DOING. BE VERY CAREFUL WITH CHANGING THIS CONFIGURATION VALUE FROM
      THE DEFAULT SETTING.

$g_manage_configuration_threshold

      The threshold required for users to be able to manage configuration
      of a project. This includes workflow, email notifications, columns
      to view, and others. Default is MANAGER.

$g_view_configuration_threshold

      Threshold for users to view the raw system configurations as stored
      in the database. The default value is ADMINISTRATOR.

$g_set_configuration_threshold

      Threshold for users to set the system configurations generically
      via MantisBT web interface. The default value is ADMINISTRATOR.


      Warning

      Users who have access to set configuration via the interface MUST
      be trusted. This is due to the fact that these users can leverage
      the interface to inject PHP code into the system, which is a
      potential security risk.

$g_csv_separator

      The separator to use for CSV exports. The default value is the
      comma (,).

$g_csv_injection_protection

      When this setting is ON (default), any data that could be
      interpreted as a formula by a spreadsheet program such as Excel
      (i.e. starting with =, @, - or +), will be prefixed with a tab
      character (\t) in order to prevent CSV injection.

      Sometimes this may not be appropriate (e.g. if the CSV needs to be
      consumed programmatically). In that case,
      $g_csv_injection_protection can be set to OFF, resulting in raw
      data to be exported.


      Warning

      Setting this to OFF is a security risk. An attacker could upload a
      crafted CSV file containing formulas that will be executed when
      opened with Excel, as described in this article.

$g_view_bug_threshold

      Access level needed to view bugs.

$g_update_bug_assign_threshold

      Access level needed to show the Assign To: button bug_view*_page or
      the Assigned list in bug_update*_page. This allows control over who
      can route bugs This defaults to $g_handle_bug_threshold.

$g_private_bugnote_threshold

      Access level needed to view private bugnotes.

$g_view_handler_threshold

      Access level needed to view handler.

$g_view_history_threshold

      Access level needed to view history.

$g_bug_reminder_threshold

      Access level needed to send a reminder from the bug view pages set
      to NOBODY to disable the feature.

$g_upload_project_file_threshold

      Access level needed to upload files to the project documentation
      section You can set this to NOBODY to prevent uploads to projects.

$g_upload_bug_file_threshold

      Access level needed to upload files to attach to a bug You can set
      this to NOBODY to prevent uploads to bugs but note that the
      reporter of the bug will still be able to upload unless you set
      $g_allow_reporter_upload or $g_allow_file_upload to OFF See also:
      $g_upload_project_file_threshold, $g_allow_file_upload,
      $g_allow_reporter_upload.

$g_add_bugnote_threshold

      Add bugnote threshold.

$g_update_bugnote_threshold

      Threshold at which a user can edit the bugnotes of other users.

$g_view_proj_doc_threshold

      Threshold needed to view project documentation Note: setting this
      to ANYBODY will let any user download attachments from private
      projects, regardless of their being a member of it.

$g_manage_site_threshold

      Site manager.

$g_manage_project_threshold

      Threshold needed to manage a project: edit project details (not to
      add/delete projects) ...etc.

$g_manage_news_threshold

      Threshold needed to add/delete/modify news.

$g_delete_project_threshold

      Threshold required to delete a project.

$g_create_project_threshold

      Threshold needed to create a new project.

$g_private_project_threshold

      Threshold needed to be automatically included in private projects.

$g_project_user_threshold

      Threshold needed to manage user access to a project.

$g_delete_bugnote_threshold

      Threshold at which a user can delete the bugnotes of other users.
      The default value is equal to the configuration setting
      $g_delete_bug_threshold.

$g_move_bug_threshold

      Move bug threshold.

$g_stored_query_use_threshold

      Threshold needed to be able to use stored queries.

$g_stored_query_create_threshold

      Threshold needed to be able to create stored queries.

$g_stored_query_create_shared_threshold

      Threshold needed to be able to create shared stored queries.

$g_update_readonly_bug_threshold

      Threshold needed to update readonly bugs. Readonly bugs are
      identified via $g_bug_readonly_status_threshold.

$g_view_changelog_threshold

      Threshold for viewing changelog.

$g_roadmap_view_threshold

      Threshold for viewing roadmap.

$g_roadmap_update_threshold

      Threshold for updating roadmap, target_version, etc.

$g_update_bug_status_threshold

      Status change thresholds.

$g_reopen_bug_threshold

      Access level needed to re-open bugs.

$g_report_issues_for_unreleased_versions_threshold

      Access level needed to assign bugs to unreleased product versions.

$g_set_bug_sticky_threshold

      Access level needed to set a bug sticky.

$g_set_status_threshold

      This array sets the access thresholds needed to enter each status
      listed. if a status is not listed, it falls back to
      $g_update_bug_status_threshold.

$g_allow_no_category

      Allow a bug to have no category.

$g_limit_view_unless_threshold

      Threshold at which a user can view all issues in the project (as
      allowed by other permissions). Not meeting this threshold means the
      user can only see the issues they reported, are handling or
      monitoring. A value of ANYBODY means that all users have full
      visibility (as default) This is a replacement for old option:
      $g_limit_reporters.

$g_allow_reporter_upload

      Reporter can upload Allow reporters to upload attachments to bugs
      they reported.

$g_bug_count_hyperlink_prefix

      Bug Count Linking This is the prefix to use when creating links to
      bug views from bug counts (eg. on the main page and the summary
      page). Default is a temporary filter.

$g_default_manage_tag_prefix

      Default tag prefix used to filter the list of tags in
      manage_tags_page.php. Change this to 'A' (or any other letter) if
      you have a lot of tags in the system and loading the manage tags
      page takes a long time.

$g_access_levels_enum_string

      Status from $g_status_index-1 to 79 are used for the onboard
      customization (if enabled) directly use MantisBT to edit them.

$g_project_status_enum_string

      TODO

$g_project_view_state_enum_string

      TODO

$g_view_state_enum_string

      TODO

$g_priority_enum_string

      TODO

$g_severity_enum_string

      TODO

$g_reproducibility_enum_string

      TODO

$g_status_enum_string

      TODO

$g_resolution_enum_string

      The values in this list are also used to define variables in the
      language files (e.g., $s_new_bug_title referenced in
      bug_change_status_page.php ). Embedded spaces are converted to
      underscores (e.g., "working on" references
      $s_working_on_bug_title). They are also expected to be English
      names for the states

$g_projection_enum_string

      TODO

$g_eta_enum_string

      TODO

$g_sponsorship_enum_string

      TODO

$g_custom_field_type_enum_string

      TODO

$g_file_type_icons

      Maps a file extension to a file type icon. These icons are printed
      next to project documents and bug attachments.

$g_file_download_content_type_overrides

      Content types which will be overridden when downloading files.

$g_status_icon_arr

      Icon associative arrays. Status to icon mapping.

$g_sort_icon_arr

      Sort direction to icon mapping.

$g_rss_enabled

      This flag enables or disables RSS syndication. In the case where
      RSS syndication is not used, it is recommended to set it to OFF.

$g_recently_visited_count

      This controls whether to show the most recently visited issues by
      the current user or not. If set to 0, this feature is disabled.
      Otherwise it is the maximum number of issues to keep in the
      recently visited list.

$g_tag_separator

      String that will separate tags as entered for input.

$g_tag_view_threshold

      Access level required to view tags attached to a bug.

$g_tag_attach_threshold

      Access level required to attach tags to a bug.

$g_tag_detach_threshold

      Access level required to detach tags from a bug.

$g_tag_detach_own_threshold

      Access level required to detach tags attached by the same user.

$g_tag_create_threshold

      Access level required to create new tags.

$g_tag_edit_threshold

      Access level required to edit tag names and descriptions.

$g_tag_edit_own_threshold

      Access level required to edit descriptions by the creating user.

$g_enable_profiles

      Enable Profiles.

$g_add_profile_threshold

      Add profile threshold.

$g_manage_global_profile_threshold

      Threshold needed to be able to create and modify global profiles.

$g_allow_freetext_in_profile_fields

      Allows the users to enter free text when reporting/updating issues
      for the profile related fields (i.e. platform, os, os build).

$g_plugins_enabled

      Enable/disable plugins.

$g_plugin_path

      Absolute path to plugin files.

$g_manage_plugin_threshold

      Threshold needed to manage plugins.

$g_plugin_mime_types

      A mapping of file extensions to mime types, used when serving
      resources from plugins.

$g_plugins_force_installed

      Force installation and protection of certain plugins. Note that
      this is not the preferred method of installing plugins, which
      should generally be done directly through the plugin management
      interface. However, this method will prevent users with admin
      access from uninstalling plugins through the plugin management
      interface.

      Entries in the array must be in the form of a key/value pair
      consisting of the plugin basename and priority.


5.25. Cookies
-------------

$g_cookie_path

      Specifies the path under which a cookie is visible.

      All scripts in this directory and its sub-directories will be able
      to access MantisBT cookies.

      Default value is '/'. It is recommended to set this to the actual
      MantisBT path.

$g_cookie_domain

      The domain that the MantisBT cookies are available to.

$g_cookie_samesite

      Specifies the SameSite attribute to use for the MantisBT cookies.

      Valid values are Strict, Lax (default) or None.

      If this setting is changed, users with a non-expired Session cookie
      (see $g_string_cookie below) may need to log out and log back in,
      to switch the cookie's secure attribute to the new value.


      Note

      While Strict provides stronger protection against CSRF attacks, it
      actually prevents the user's session from being recognized when
      clicking a link from a notification e-mail, causing MantisBT to
      start an anonymous session even if the user is already logged in.

$g_cookie_prefix

      Prefix for all MantisBT cookies

      This must be an identifier which does not include spaces or
      periods, and should be unique per MantisBT installation, especially
      if $g_cookie_path is not restricting the cookies' scope to the
      actual MantisBT directory.

      It applies to the cookies listed below. Their actual names are
      calculated by prepending the prefix, and it is not expected for the
      user to need to change these.

        * 

          $g_bug_list_cookie

        * 

          $g_collapse_settings_cookie

          Stores the open/closed state of the collapsible sections.

        * 

          $g_logout_cookie

        * 

          $g_manage_config_cookie

          Stores the filter criteria for the Manage Config Report page.

        * 

          $g_manage_users_cookie

          Stores the filter criteria for the Manage Users page.

        * 

          $g_project_cookie

        * 

          $g_string_cookie

        * 

          $g_view_all_cookie

$g_string_cookie

      TODO

$g_project_cookie

      TODO

$g_view_all_cookie

      TODO

$g_collapse_settings_cookie

      Collapse settings cookie. Stores the open/closed state of the
      collapsible sections.

$g_manage_users_cookie

      Stores the filter criteria for the Manage User page

$g_manage_config_cookie

      Stores the filter criteria for the Manage Config Report page

$g_logout_cookie

      TODO

$g_bug_list_cookie

      TODO


5.26. Speed Optimisation
------------------------

$g_compress_html

      This option is used to enable buffering/compression of HTML output
      if the user's browser supports it. Default value is ON. This option
      will be ignored in the following scenarios:

        * 

          php.ini has zlib.output_compression enabled.

        * 

          php.ini has output_handler set to a handler.

        * 

          zlib extension is not enabled. The Windows version of PHP has
          built-in support for this extension.

$g_use_persistent_connections

      Use persistent database connections, setting this to ON will open
      the database once per connection, rather than once per page. There
      might be some scalability issues here and that is why it is
      defaulted to OFF.


5.27. Reminders
---------------

Sending reminders is a feature where a user can notify / remind other
users about a bug. In the past, only selected users like the managers, or
developers would get notified about bugs. However, these people can not
invite other people (through MantisBT) to look at or monitor these bugs.

This feature is useful if the Manager needs to get feedback from testers
/ requirements team about a certain bug. It avoid needing this person to
do this manual outside MantisBT. It also records the history of such
reminders.

$g_store_reminders

      Specifies if reminders should be stored as bugnotes. The bugnote
      will still reflect that it is a reminder and list the names of
      users that got it. Default is ON.

$g_reminder_recipients_monitor_bug

      Specifies if users who receive reminders about a bug, should be
      automatically added to the monitor list of that bug. Default is ON.

$g_mentions_enabled

      Enables or disables the @ mentions feature. Default is ON. When a
      user is @ mentioned in an issue or a note, they receive an email
      notification to get their attention. Users can be @ mentioned using
      their username and not realname.

      This feature works with fields like summary, description,
      additional info, steps to reproduce and notes.

$g_mentions_tag

      The tag to use for prefixing mentions. Default is '@'.


5.28. Bug History
-----------------

Bug history is a feature where MantisBT tracks all modifications that are
made to bugs. These include everything starting from its creation, till
it is closed. For each change, the bug history will record the time
stamp, user who made the change, field that changed, old value, and new
value.

Independent of the these settings, MantisBT will always track the changes
to a bug and add them to its history.

$g_history_default_visible

      Make the bug history visible by default. If this option is not
      enabled, then the user will have to click on the Bug History link
      to see the bug history. Default is ON.

$g_history_order

      Show bug history entries in ascending or descending order. Default
      value is 'ASC'.

In this context, MantisBT records individual changes to text fields (Description,
Steps to Reproduce, Additional Information as well as Bug Notes). These
revisions are controlled by the following settings.

$g_bug_revision_view_threshold

      Access level required to view bug history revisions. Defaults to
      DEVELOPER.


      Note

      Users can always see revisions for the issues and bugnotes they
      reported.

$g_bug_revision_drop_threshold

      Access level required to drop bug history revisions. Defaults to
      MANAGER.


5.29. Sponsorship
-----------------

$g_enable_sponsorship

      enable/disable the whole issue sponsorship feature. The default os
      OFF.

$g_sponsorship_currency

      The currency string used for all sponsorships. The default is
      'US$'.

$g_minimum_sponsorship_amount

      The minimum sponsorship amount that can be entered. If the user
      enters a value less than this, an error will be flagged. The
      default is 5.

$g_view_sponsorship_total_threshold

      The access level threshold needed to view the total sponsorship for
      an issue by all users. The default is VIEWER.

$g_view_sponsorship_details_threshold

      The access level threshold needed to view the details of the
      sponsorship (i.e., who will donate what) for an issue by all users.
      The default is VIEWER.

$g_sponsor_threshold

      The access level threshold needed to allow user to sponsor issues.
      The default is REPORTER. Note that sponsoring user must have their
      email set in their profile.

$g_handle_sponsored_bugs_threshold

      The access level required to be able to handle sponsored issues.
      The default is DEVELOPER.

$g_assign_sponsored_bugs_threshold

      The access level required to be able to assign a sponsored issue to
      a user with access level greater or equal to
      'handle_sponsored_bugs_threshold'. The default is MANAGER.


5.30. Custom Fields
-------------------

$g_manage_custom_fields_threshold

      Access level needed to manage custom fields. The default is
      ADMINISTRATOR.

$g_custom_field_link_threshold

      Access level needed to link a custom field to a project. The
      default is MANAGER.

$g_custom_field_edit_after_create

      This flag determines whether to start editing a custom field
      immediately after creating it, or return to the definition list.
      The default is ON (edit the custom field after creating).


5.31. My View Settings
----------------------

$g_my_view_boxes

      This is an array of values defining the order that the boxes to be
      shown. A box that is not to be shown can have its value set to 0.
      The default is:

      $g_my_view_boxes = array(
              'assigned'      => '1',
              'unassigned'    => '2',
              'reported'      => '3',
              'resolved'      => '4',
              'recent_mod'    => '5',
              'monitored'     => '6',
              'feedback'      => '0',
              'verify'        => '0',
              'my_comments'   => '0'
      );

      If you want to change the definition, copy the default value and
      apply the changes.

$g_my_view_bug_count

      Number of bugs shown in each box. The default is 10.

$g_default_home_page

      Default page to transfer to after Login or Set Project. The default
      is 'my_view_page.php'. An alternative would be
      'view_all_bugs_page.php' or 'main_page.php'.

$g_logout_redirect_page

      Specify where the user should be sent after logging out.


5.32. Relationship Graphs
-------------------------

MantisBT can display a graphical representation of the relationships
between issues. Two types of interactive visualizations are available,
dependencies and a full relationships graph.


Important

This feature relies on the external dot and neato tools from the GraphViz
library, which must be installed separately.

Most Linux distributions have a GraphViz package available for easy
download and install.

Under Windows, the software needs to be installed manually. The following
post-installation steps may be required for proper operations:

  * 

    Update the system PATH to point to GraphViz's bin directory

  * 

    Initialize the graph engine by running dot -c from an Administrator
    command prompt.

$g_relationship_graph_enable

      This enables the relationship graphs feature where issues are
      represented by nodes and relationships as links between such nodes.
      Possible values are ON or OFF. Default is OFF.

$g_relationship_graph_fontname

      Font name and size, as required by Graphviz. If Graphviz fails to
      run for you, you are probably using a font name that gd PHP
      extension can't find. On Linux, try the name of the font file
      without the extension. The default value is 'Arial'.

$g_relationship_graph_fontsize

      Font size, default is 8.

$g_relationship_graph_orientation

      Default dependency orientation. If you have issues with lots of
      children or parents, leave as 'horizontal', otherwise, if you have
      lots of "chained" issue dependencies, change to 'vertical'. Default
      is 'horizontal'.

$g_relationship_graph_max_depth

      Max depth for relation graphs. This only affects relationship
      graphs, dependency graphs are drawn to the full depth. The default
      value is 2.

$g_relationship_graph_view_on_click

      If set to ON, clicking on an issue on the relationship graph will
      open the bug view page for that issue, otherwise, will navigate to
      the relationship graph for that issue.

$g_dot_tool

      The full path for the dot tool. The webserver must have execute
      permission to this program in order to generate relationship
      graphs. This configuration option is not relevant for Windows. The
      default value is '/usr/bin/dot'.

$g_neato_tool

      The full path for the neato tool. The webserver must have execute
      permission to this program in order to generate relationship
      graphs. This configuration option is not relevant for Windows. The
      default value is '/usr/bin/neato'.

$g_backward_year_count

      Number of years in the past that custom date fields will display in
      drop down boxes.

$g_forward_year_count

      Number of years in the future that custom date fields will display
      in drop down boxes.

$g_custom_group_actions

      This extensibility model allows developing new group custom
      actions. This can be implemented with a totally custom form and
      action pages or with a pre-implemented form and action page and
      call-outs to some functions. These functions are to be implemented
      in a predefined file whose name is based on the action name. For
      example, for an action to add a note, the action would be
      EXT_ADD_NOTE and the file implementing it would be
      bug_actiongroup_add_note_inc.php. See implementation of this file
      for details.


5.33. Wiki Integration
----------------------

$g_wiki_enable

      Set to ON to enable Wiki integration. Defaults to OFF.

$g_wiki_engine

      The following Wiki Engine values are supported:

        * 

          dokuwiki: DokuWiki

        * 

          mediawiki: MediaWiki

        * 

          twiki: TWiki

        * 

          wackowiki: WackoWiki

        * 

          wikka: WikkaWiki

        * 

          xwiki: XWiki

$g_wiki_root_namespace

      Wiki namespace to be used as root for all pages relating to this
      MantisBT installation.

$g_wiki_engine_url

      URL under which the wiki engine is hosted.

      Must be on the same server as MantisBT, requires a trailing '/'.

      If left empty (default), the URL is derived from the global
      MantisBT path ($g_path, see Section 5.3, “Path”), replacing the
      URL's path component by the wiki engine string (i.e. if $g_path =
      'http://example.com/mantis/' and $g_wiki_engine = 'dokuwiki', the
      wiki URL will be 'http://example.com/dokuwiki/').


5.34. Sub-Projects
------------------

$g_subprojects_enabled

      Whether sub-projects feature should be enabled. Before turning this
      flag OFF, make sure all sub-projects are moved to top level
      projects, otherwise they won't be accessible. The default value is
      ON.

$g_subprojects_inherit_versions

      Whether sub-projects should inherit versions from parent projects.
      For project X which is a sub-project of A and B, it will have
      versions from X, A and B. The default value is ON.

$g_subprojects_inherit_categories

      Whether sub-projects should inherit categories from parent
      projects. For project X which is a sub-project of A and B, it will
      have categories from X, A and B. The default value is ON.


5.35. Field Visibility
----------------------

$g_enable_eta

      Enable or disable usage of 'ETA' field. Default value is OFF.

$g_enable_projection

      Enable or disable usage of 'Projection' field. Default value is
      OFF.

$g_enable_product_build

      Enable or disable usage of 'Product Build' field. Default is OFF.

$g_bug_report_page_fields

      An array of optional fields to show on the bug report page.

      The following optional fields are allowed: additional_info,
      attachments, category_id, due_date, eta, handler, monitors, os,
      os_build, platform, priority, product_build, product_version,
      reproducibility, resolution, severity, status, steps_to_reproduce,
      tags, target_version, view_state.

      The summary and description fields are always shown and do not need
      to be listed in this option. Fields not listed above cannot be
      shown on the bug report page. Visibility of custom fields is
      handled via the Manage => Custom Fields administrator page.

      Note that monitors is not an actual field; adding it to the list
      will let authorized reporters (see monitor_add_others_bug_threshold
      in Section 5.24, “Misc”) select users to add to the issue's
      monitoring list. Monitors will only be notified of the submission
      if both their e-mail preferencess and the notify_flags
      configuration (see Section 5.8, “Email”) allows it, i.e.

      $g_notify_flags['new']['monitor'] = ON;

      This setting can be set on a per-project basis by using the Manage
      => Configuration administrator page.

$g_bug_view_page_fields

      An array of optional fields to show on the issue view page and
      other pages that include issue details.

      The following optional fields are allowed: additional_info,
      attachments, category_id, date_submitted, description, due_date,
      eta, fixed_in_version, handler, id, last_updated, os, os_build,
      platform, priority, product_build, product_version, project,
      projection, reporter, reproducibility, resolution, severity,
      status, steps_to_reproduce, summary, tags, target_version,
      view_state.

      Fields not listed above cannot be shown on the bug view page.
      Visibility of custom fields is handled via the Manage => Custom
      Fields administrator page.

      This setting can be set on a per-project basis by using the Manage
      => Configuration administrator page.

$g_bug_update_page_fields

      An array of optional fields to show on the bug update page.

      The following optional fields are allowed: additional_info,
      category_id, date_submitted, description, due_date, eta,
      fixed_in_version, handler, id, last_updated, os, os_build,
      platform, priority, product_build, product_version, project,
      projection, reporter, reproducibility, resolution, severity,
      status, steps_to_reproduce, summary, target_version, view_state.

      Fields not listed above cannot be shown on the bug update page.
      Visibility of custom fields is handled via the Manage => Custom
      Fields administrator page.

      This setting can be set on a per-project basis by using the Manage
      => Configuration administrator page.


5.36. System Logging and Debugging
----------------------------------

This section describes settings which can be used to troubleshoot
MantisBT operations as well as assist during development.

$g_show_timer

      Time page loads. The page execution timer shows at the bottom of
      each page.

      Default is OFF.

$g_show_memory_usage

      Show memory usage for each page load in the footer.

      Default is OFF.

$g_debug_email

      Used for debugging e-mail notifications. When it is '', the emails
      are sent normally. If set to an e-mail address, all messages are
      sent to it, with the original recipients (To, Cc, Bcc) included in
      the message body.

      Default is ''.

$g_show_queries_count

      Shows the total number/unique number of queries executed to serve
      the page.

      Default is OFF.

$g_display_errors

      Errors Display Method. Defines what errors are displayed and how.
      Available options are:

      DISPLAY_ERROR_HALT

            Stop and display the error message (including variables and
            backtrace if $g_show_detailed_errors is ON).

      DISPLAY_ERROR_INLINE

            Display a one line error and continue execution.

      DISPLAY_ERROR_NONE

            Suppress the error (no display). This is the default behavior
            for unspecified errors constants.

      The default settings are recommended for use in production, and
      will only display MantisBT fatal errors, suppressing output of all
      other error types.

      Recommended config_inc.php settings for developers:

      $g_display_errors = array(
              E_WARNING           => DISPLAY_ERROR_HALT,
              E_ALL               => DISPLAY_ERROR_INLINE,
      );


      Note

      The system automatically sets $g_display_errors to the above
      recommended development values when the server's name is localhost.

      Less intrusive settings, recommended for testing purposes:

      $g_display_errors = array(
              E_USER_WARNING => DISPLAY_ERROR_INLINE,
              E_WARNING      => DISPLAY_ERROR_INLINE,
      );


      Note

      E_USER_ERROR, E_RECOVERABLE_ERROR and E_ERROR will always be set to
      DISPLAY_ERROR_HALT internally, regardless of the actual configured
      value. This ensures that program execution stops, to prevent
      potential integrity issues and/or MantisBT from functioning
      incorrectly.

$g_show_detailed_errors

      Shows a list of variables and their values whenever an error is
      triggered. Only applies to error types configured to
      DISPLAY_ERROR_HALT in $g_display_errors.

      Default is OFF.


      Warning

      Setting this to ON is a potential security hazard, as it can expose
      sensitive information. Only enable this setting for debugging
      purposes when you really need it.

$g_stop_on_errors

      Debug messages. If this option is turned OFF, page redirects will
      function if a non-fatal error occurs. For debugging purposes, you
      can set this to ON so that any non-fatal error will prevent page
      redirection, allowing you to see the errors.

      Default is OFF.


      Note

      This should only be turned on when debugging.

$g_log_level

      The system logging interface is used to extract detailed debugging
      information for the MantisBT system. It can also serve as an audit
      trail for users' actions.

      This controls the type of logging information recorded. Refer to
      $g_log_destination for details on where to save the logs.

      The available log channels are:

      LOG_NONE

            Disable logging

      LOG_AJAX

            logs AJAX events

      LOG_DATABASE

            logs database events and executed SQL queries

      LOG_EMAIL

            logs issue id, message type and recipients for all emails
            sent

      LOG_EMAIL_VERBOSE

            Enables extra logging for troubleshooting internals of email
            queuing and sending.

      LOG_EMAIL_RECIPIENT

            logs the details of email recipient determination. Each user
            id is listed as well as why they are added, or deleted from
            the recipient list

      LOG_FILTERING

            logs filter operations

      LOG_LDAP

            logs the details of LDAP operations

      LOG_WEBSERVICE

            logs the details of Web Services operations (e.g. SOAP API)

      LOG_PLUGIN

            Enables logging from plugins.

      LOG_ALL

            combines all of the above log levels

      Default is LOG_NONE.


      Note

      Multiple log channels can be combined using PHP bitwise operators ,
      e.g.

      $g_log_level = LOG_EMAIL | LOG_EMAIL_RECIPIENT;

      or

      $g_log_level = LOG_ALL & ~LOG_DATABASE;

$g_log_destination

      Specifies where the log data goes. The following five options are
      available:

      ''    

            The empty string means default PHP error log settings

      'none'

            Don't output the logs, but would still trigger EVENT_LOG
            plugin event.

      'file'

            Log to a specific file, specified as an absolute path, e.g.
            'file:/var/log/mantis.log' (Unix) or
            'file:c:/temp/mantisbt.log' (Windows)


            Note

            This file must be writable by the web server running
            MantisBT.

      'page'

            Display log output at bottom of the page. See also
            $g_show_log_threshold to restrict who can see log data.

      Default is '' (empty string).

$g_show_log_threshold

      Indicates the access level required for a user to see the log
      output (if $g_log_destination is 'page').

      Default is ADMINISTRATOR.


      Note

      This threshold is compared against the user's global access level
      rather than the one from the currently active project.


5.37. Time Tracking
-------------------

$g_time_tracking_enabled

      Turns Time Tracking features ON or OFF - Default is OFF

$g_time_tracking_without_note

      Allow time tracking to be recorded without writing some text in the
      associated bugnote - Default is ON

$g_time_tracking_with_billing

      Adds calculation links to workout how much time has been spent
      between a particular time frame. Currently it will allow you to
      enter a cost/hour and will work out some billing information. This
      will become more extensive in the future. Currently it is more of a
      proof of concept.

$g_time_tracking_billing_rate

      Default billing rate per hour - Default is 0

$g_time_tracking_stopwatch

      Instead of a text field turning this option on places a stopwatch
      on the page with Start/Stop and Reset buttons next to it. A bit
      gimmicky, but who cares.

$g_time_tracking_view_threshold

      Access level required to view time tracking information - Default
      DEVELOPER.

$g_time_tracking_edit_threshold

      Access level required to add/edit time tracking information (If you
      give a user $g_time_tracking_edit_threshold you must give them
      $g_time_tracking_view_threshold as well) - Default DEVELOPER.

$g_time_tracking_reporting_threshold

      Access level required to run reports (not completed yet) - Default
      MANAGER.


5.38. API
---------

MantisBT exposes a webservice API which allows remote clients to interact
with MantisBT and perform many of the usual tasks, such as reporting
issues, running filtered searches and retrieving attachments.

The SOAP API is enabled by default and available at
/api/soap/mantisconnect.php below the MantisBT root. A WSDL file which
describes the web service is available at
/api/soap/mantisconnect.php?wsdl below the MantisBT root.

The REST API is enabled by default. A Swagger sandbox and documentation
for REST API is available at /api/rest/swagger/ below the MantisBT root.

The following options are used to control the behaviour of the MantisBT
SOAP API:

$g_webservice_rest_enabled

      Whether the REST API is enabled or not. Note that this flag only
      impacts API Token based auth. Hence, even if the API is disabled,
      it can still be used from the Web UI using cookie based
      authentication. Default ON.

$g_webservice_readonly_access_level_threshold

      Minimum global access level required to access webservice for
      readonly operations.

$g_webservice_readwrite_access_level_threshold

      Minimum global access level required to access webservice for
      read/write operations.

$g_webservice_admin_access_level_threshold

      Minimum global access level required to access the administrator
      webservices.

$g_webservice_specify_reporter_on_add_access_level_threshold

      Minimum project access level required for caller to be able to
      specify reporter when adding issues or issue notes. Defaults to
      DEVELOPER.

$g_webservice_priority_enum_default_when_not_found

      The following enum id is used when the webservices get enum labels
      that are not defined in the associated MantisBT installation. In
      this case, the enum id is set to the value specified by the
      corresponding configuration option.

$g_webservice_severity_enum_default_when_not_found

      The following enum id is used when the webservices get enum labels
      that are not defined in the associated MantisBT installation. In
      this case, the enum id is set to the value specified by the
      corresponding configuration option.

$g_webservice_status_enum_default_when_not_found

      The following enum id is used when the webservices get enum labels
      that are not defined in the associated MantisBT installation. In
      this case, the enum id is set to the value specified by the
      corresponding configuration option.

$g_webservice_resolution_enum_default_when_not_found

      The following enum id is used when the webservices get enum labels
      that are not defined in the associated MantisBT installation. In
      this case, the enum id is set to the value specified by the
      corresponding configuration option.

$g_webservice_projection_enum_default_when_not_found

      The following enum id is used when the webservices get enum labels
      that are not defined in the associated MantisBT installation. In
      this case, the enum id is set to the value specified by the
      corresponding configuration option.

$g_webservice_eta_enum_default_when_not_found

      The following enum id is used when the webservices get enum labels
      that are not defined in the associated MantisBT installation. In
      this case, the enum id is set to the value specified by the
      corresponding configuration option.

$g_webservice_error_when_version_not_found

      If ON and the supplied version is not found, then a SoapException
      will be raised.

$g_webservice_version_when_not_found

      Default version to be used if the specified version is not found
      and $g_webservice_error_when_version_not_found == OFF. (at the
      moment this value does not depend on the project).


5.38.1. Disabling the webservice API

If you wish to temporarily disable the webservice API it is sufficient to
set the specific access thresholds to NOBODY:

$g_webservice_readonly_access_level_threshold =
$g_webservice_readwrite_access_level_threshold =
$g_webservice_admin_access_level_threshold = NOBODY;

While the SOAP API will still be accessible, it will not allow users to
retrieve or modify data.


5.39. Anti-Spam Configuration
-----------------------------

$g_antispam_max_event_count

      Max number of events to allow for users with default access level
      (see $g_default_new_account_access_level) when signup is enabled.
      Use 0 for no limit. Default is 10.

$g_antispam_time_window_in_seconds

      Time window to enforce max events within. Default is 3600 seconds
      (1 hour).


5.40. Due Date
--------------

$g_due_date_update_threshold

      Threshold to update due date submitted. Default is NOBODY.

$g_due_date_view_threshold

      Threshold to see due date. Default is NOBODY.

$g_due_date_default

      Default due date value for newly submitted issues. A valid relative
      date format e.g. today or +2 days, or empty string for no due date
      set (default).

$g_due_date_warning_levels

      Due date warning levels. A variable number of Levels (defined as a
      number of seconds going backwards from the current timestamp,
      compared to an issue's due date) can be defined. Levels must be
      defined in ascending order.

        * 

          The first entry (array key 0) defines Overdue. Normally and by
          default, its value is 0, meaning that issues will be marked
          overdue as soon as their due date has passed. However, it is
          also possible to set it to a higher value to flag overdue
          issues earlier, or even use a negative value to allow a "grace
          period" after due date.

        * 

          Array keys 1 and 2 offer two levels of Due soon: orange and
          green. By default, only the first one is set, to 7 days.

      Out of the box, MantisBT allows for 3 warning levels. Additional
      ones may be defined, but in that case new due-N CSS rules (where N
      is the array's index) must be created otherwise the extra levels
      will not be highlighted in the UI.


5.41. User Management
---------------------

$g_impersonate_user_threshold

      The threshold for a user to be able to impersonate another user, or
      NOBODY to disable impersonation. Default ADMINISTRATOR.

$g_manage_user_threshold

      The threshold for a user to manage user accounts. Default
      ADMINISTRATOR.


5.42. View Page Settings
------------------------

$g_issue_activity_note_attachments_seconds_threshold

      If a user submits a note with an attachments (with the specified #
      of seconds) the attachment is linked to the note. Or 0 for
      disabling this feature.


5.43. Issues visibility
-----------------------

By default, all issues are visible to any user within a project. To limit
the visibility of issues there are several mechanisms.


5.43.1. Public/Private view status

A view status flag can be set, for an issue, to be either public or
private. Private issues are accesible by the user who created it, and by
those users that meet a threshold defined in $g_private_bug_threshold.

Refer to the following configuration options related to issue view status
configurations:

$g_private_bug_threshold

      The threshold for a user to be able to view any private issue
      within a project.

$g_set_view_status_threshold

      The threshold for a user to be able to set an issue to
      Private/Public.

$g_change_view_status_threshold

      The threshold for a user to be able to update the view status while
      updating an issue.


5.43.2. Limited view configuration

The $g_limit_view_unless_threshold option allows the administrator to
configure access limitations for users, letting them view only those
issues that they are involved with, i.e. if:

  * 

    They reported the issue,

  * 

    It is assigned to them,

  * 

    Or they are monitoring the issue.

This configuration option can be set individually for each project. It
defaults to ANYBODY, effectively disabling the limitation (i.e. users can
see all issues).

The value for this option is an access level threshold, so that those
users that meet that threshold have an unrestricted view of any issue in
the project. A user that doesn't meet this threshold, will have a
restricted view of only those issues in the conditions previously
described.

Note that this visibility does not override other restrictions as private
issues or pivate projects user assignments.


5.43.3. "Limit reporters" configuration (deprecated)

When the option $g_limit_reporters is enabled, users that are reporters
in a project, or lower access level, are only allowed to see the issues
they reported. Issues reported by other users are not accessible by them.

This option is only supported for ALL_PROJECTS, this means that it's a
global setting that affects all projects

Note that the definition of reporter in this context is the actual access
level for which a user is able to report issues, and is determined by
$g_report_bug_threshold. Additionally, that threshold can have different
values in each project. Being dependant on that threshold, the behaviour
of this option is not well defined when the reporting threshold is
configured as discrete values with gaps, instead of a simple threshold.
In that scenario, the visibilty is determined by the minimum access level
contained in the $g_report_bug_threshold access levels array.


Note

This option option is deprecated in favour of
$g_limit_view_unless_threshold. The new option will be available by
default on new installations, or after disabling $g_limit_reporters if
enabled in an existing instance.



Chapter 6. Page descriptions
============================


6.1. Login page
---------------

Just enter your username and password and hit the login button. There is
also a Save Login checkbox to have the package remember that you are
logged in between browser sessions. You will have to have cookies enabled
to login.If the account doesn't exist, the account is disabled, or the
password is incorrect then you will remain at the login page. An error
message will be displayed.The administrator may allow users to sign up
for their own accounts. If so, a link to Signup for your own account will
be available.The administrator may also have anonymous login allowed.
Anonymous users will be logged in under a common account.You will be
allowed to select a project to work in after logging in. You can make a
project your default selection from the Select Project screen or from
your Account Options.SignupHere you can signup for a new account. You
must supply a valid email address and select a unique username. Your
randomly generated password will be emailed to your email account. If
MantisBT is setup so that the email password is not to be emailed, newly
generated accounts will have an empty password.


6.2. Main page
--------------

This is the first page you see upon logging in. It shows you the latest
news updates for the bugtracker. This is a simple news module (based off
of work by Scott Roberts) and is to keep users abreast of changes in the
bugtracker or project. Some news postings are specific to projects and
others are global across the entire bugtracker. This is set at the time
of posting in the Edit News section.The number of news posts is
controlled by a global variable. When the number of posts is more than
the limit, a link to show "older news" is displayed at the bottom.
Similarly a "newer news" is displayed when you have clicked on "older
news".There is an Archives option at the bottom of the page to view all
listings.ArchivesA title/date/poster listing of ALL past news articles
will be listed here. Clicking on the link will bring up the specified
article. This listing will also only display items that are either global
or specific to the selected project.


6.3. View Issues page
---------------------

Here we can view the issue listings. The page has a set of viewing
filters at the top and the issues are listed below.FiltersThe filters
control the behavior of the issues list. The filters are saved between
browsing sessions but do not currently save sort order or direction.If
the number of issues exceeds the "Show" count in the filter a set of
navigation to go to "First", "Last", "Previous", "Next" and specific page
numbers are added.The Search field will look for simple keyword matches
in the summary, description, steps to reproduce, additional information,
issue id, or issue text id fields. It does not search through issue
notes. Issue List - The issues are listed in a table and the attributes
are listed in the following order: priority, id, number of issue notes,
category, severity, status, last updated, and summary. Each (except for
number of issue notes) can be clicked on to sort by that column. Clicking
again will reverse the direction of the sort. The default is to sort by
last modification time, where the last modified issue appears at the top.
The issue id is a link that leads to a more detailed report about the
issue. You can also add issue notes here. The number in the issue note
count column will be bold if an issue note has been added in the
specified time frame. The addition of an issue note will make the issue
note link of the issue appear in the unvisited state. The text in the
"Severity" column will be bold if the severity is major, crash, or block
and the issue not resolved. The text in the "Updated" column will be bold
if the issue has changed in the last "Changed(hrs)" field which is
specified in the viewing filters. Each table row is color coded according
to the issue status. The colors can be customised through MantisBT
configuration pages (see Chapter 5, Configuration for details).
Severities block - prevents further work/progress from being made crash -
crashes the application or blocking, major - major issue, minor - minor
issue, tweak - needs tweaking, text - error in the text, trivial - being
nit picky, feature - requesting new feature - Status new - new issue,
feedback - issue requires more information from reporter, acknowledged -
issue has been looked at but not confirmed or assigned, confirmed -
confirmed and reproducible (typically set by an Updater or other
Developer), assigned - assigned to a Developer, resolved - issue should
be fixed, waiting on confirmation of fix, closed - issue is closed,
Moving the mouse over the status text will show the resolution as a
title. This is rendered by some browsers as a bubble and in others as a
status line text.


6.4. Issue View page
--------------------

Here is the simple listing of the issue report. Most of the fields are
self-explanatory. "Assigned To" will contain the developer assigned to
handle the issue. Priority is fully functional but currently does nothing
of importance. Duplicate ID is used when an issue is a duplicate of
another. It links to the duplicate issue which allows users to read up on
the original issue report. Below the issue report is a set of buttons
that a user can select to work on the issue.

  * 

    Update Issue - brings up a page to edit all aspects of the issue

  * 

    Assign to - in conjunction with the dropdown list next top the
    button, this is a shortcut to change the assignment of an issue

  * 

    Change Status to - in conjunction with the dropdown list next top the
    button, this is a shortcut to change the status of an issue. Another
    page (Change Status) will be presented to allow the user to add notes
    or change relevant information

  * 

    Monitor / Unmonitor Issue - allows the user to monitor any additions
    to the issue by email

  * 

    Create Clone - create a copy of the current issue. This presents the
    user with a new issue reporting form with all of the information in
    the current issue filled in. Upon submission, a new issue, related to
    the current issue, will be created.

  * 

    Reopen Issue - Allows the user to re-open a resolved issue

  * 

    Move Issue - allows the user to move the issue to another project

  * 

    Delete Issue - Allows the user to delete the issue permanently. It is
    recommended against deleting issues unless the entry is frivolous.
    Instead issues should be set to resolved and an appropriate
    resolution category chosen.

A panel is provided to view and update the sponsorship of an
issue.Another panel is provided to view, delete and add relationships for
an issue. Issues can have a parent/child relationship, where the user is
warned about resolving a parent issue before all of the children are
resolved. A peer relationship is also possible.Below this, there may be a
form for uploading file attachments. The Administrator needs to configure
the bugtracker to handle file uploads. If uploading to disk is selected,
each project needs to set its own upload path. Issue notes are shown at
the bottom of the issue report. A panel to add issue notes is also shown.


6.5. Issue Change Status page
-----------------------------

This page is used to change the status of an issue. A user can add an
issue note to describe the reason for change.In addition, the following
fields may be displayed for update:

  * 

    Resolution and Duplicate ID - for issues being resolved or closed

  * 

    Issue Handler (Assigned to)

  * 

    any Custom Fields that are to be visible on update or resolution

  * 

    Fixed in Version - for issues being resolved

  * 

    Close Immediately - to immediately close a resolved issue


6.6. Issue Edit page
--------------------

The layout of this page resembles the Simple Issue View page, but here
you can update various issue fields. The Reporter, Category, Severity,
and Reproducibility fields are editable but shouldn't be unless there is
a gross mis-categorization.Also modifiable are the Assigned To, Priority,
Projection, ETA, Resolution, and Duplicate ID fields.As per version
0.18.0, the user can also add an issue note as part of an issue update.


6.7. My Account Page
--------------------

This page changes user alterable parameters for the system. These
selections are user specific. This allows the user to change their
password, username, real name and email address. It also reports the
user's access levels on the current project and default access level used
for public projects.


6.7.1. Preferences

This sets the following information:

  * 

    Default project

  * 

    whether the pages used for reporting, viewing, and updating are the
    simple or advanced views

  * 

    the delay in minutes between refreshes of the view all issues page

  * 

    the delay in seconds when redirecting from a confirmation page to the
    display page

  * 

    the time order in which notes will be sorted

  * 

    whether to filter email messages based on type of message and
    severity

  * 

    the number of notes to append to notification emails

  * 

    the default language for the system. The additional setting of "auto"
    will use the browser's default language for the system.


6.7.2. Profiles

Profiles are shortcuts to define the values for Platform, OS, and
version. This page allows you to define and edit personal shortcuts.


6.7.3. Manage Columns

Provides the ability to select the fields to be displayed in View Issues,
Print Issues, CSV and Excel exports. The changes apply to the currently
selected projects or All Projects for setting the defaults. It is also
possible to copy such settings from/to other projects.


6.7.4. API Tokens

Provides the ability to generate and revoke tokens that can be used by
applications and services to access MantisBT via its APIs. This page also
provides information about the creation and last used timestamps for such
tokens.


6.8. System Management Pages
----------------------------

A number of pages exist under the "Manage" link. These will only be
visible to those who have an appropriate access level.


6.8.1. Users

This page allow an administrator to manage the users in the system.It
essentially supplies a list of users defined in the system. The user
names are linked to a page where you can change the user's name, access
level, and projects to which they are assigned. You can also reset their
passwords through this page.At the top, there is also a list of new users
(who have created an account in the last week), and accounts where the
user has yet to log in.New users are created using the "Create User" link
above the list of existing users. Note that the username must be unique
in the system. Further, note that the user's real name (as displayed on
the screen) cannot match another user's user name.


6.8.2. Manage Projects Page

This page allows the user to manage the projects listed in the
system.Each project is listed along with a link to manage that specific
project. The specific project pages allow the user to change:

  * 

    the project name

  * 

    the project description

  * 

    its status

  * 

    whether the project is public or private. Private projects are only
    visible to users who are assigned to it or users who have the access
    level to automatically have access to private projects (eg:
    administrators).

  * 

    file directory used to store attachments for issues and documents
    associated with the project. This folder is located on the webserver,
    it can be absolute path or path relative to the main MantisBT folder.
    Note that this is only used if the files are stored on disk.

  * 

    common subprojects. These are other projects who can be considered a
    sub-project of this one. They can be shared amongst multiple
    projects. For example, a "documentation" project may be shared
    amongst several development projects.

  * 

    project categories. These are used to sub-divide the issues stored in
    the system.

  * 

    project versions. These are used to create ChangeLog reports and can
    be used to filter issues. They are used for both the Found In and
    Fixed In versions.

  * 

    Custom Fields linked to this project

  * 

    Users linked to this project. Here is the place where a user's access
    level may be upgraded or downgraded depending on their particular
    role in the project.


6.8.3. Manage Custom Fields

This page is the base point for managing custom fields. It lists the
custom fields defined in the system. There is also a place to enter a new
field name to create a new field.

The "Edit" links take you to a page where you can define the details of a
custom field. These include it's name, type, value, and display
information. On the edit page, the following information is defined to
control the custom field:

  * 

    name

  * 

    type

  * 

    Value constraints (Possible values, default value, regular
    expression, minimum length, maximum length).

  * 

    Access (who can read and write the field based on their access
    level).

  * 

    Display control (where the field will show up and must be filled in

All fields are compared in length to be greater than or equal to the
minimum length, and less than or equal to the minimum length, unless
these values are 0 in which case the check is skipped. All fields are
also compared against the regular expression; if the value matches, then
it is valid. For example, the expression ^-?([0-9])*$ can be used to
constrain an integer.

Please refer to Section 7.2, “Custom Fields” for further details about
Custom Fields and all the above-mentioned properties.


6.8.4. Global Profiles

This page allows the definition of global profiles accessible to all
users of the system. It is similar to the user definition of a profile
consisting of Platform, OS and Version.


6.8.5. Configuration

This set of pages control the configuration of the MantisBT system. Note
that the configuration items displayed may be on a project by project
basis.These pages serve two purposes. First, they will display the
settings for the particular aspects of the system. If authorized, they
will allow a user to change the parameters. They also have settings for
what access level is required to change these settings ON A PROJECT
basis. In general, this should be left alone, but administrators may want
to delegate some of these settings to managers.

*******. Workflow Thresholds

This page covers the adjustment of the settings for many of the workflow
related parameters. For most of these, the fields are self explanatory
and relate to a similarly named setting in the configuration file. At the
right of each row is a selector that allows the administrator to lower
the access level required to change the particular parameter.The values
changeable on this page are:

Issues.

Title

Variable

Description

Report an Issue

$g_report_bug_threshold

threshold to report an issue

Status to which a new issue is set

$g_bug_submit_status

status issue is set to when submitted

Update an Issue

$g_update_bug_threshold

threshold to update an issue

Allow Reporter to close an issue

$g_allow_reporter_close

allow reporter to close issues they reported

Monitor an issue

$g_monitor_bug_threshold

threshold to monitor an issue

Handle Issue

$g_handle_bug_threshold

threshold to handle (be assigned) an issue

Assign Issue

$g_update_bug_assign_threshold

threshold to be in the assign to list

Move Issue

$g_move_bug_threshold

threshold to move an issue to another project. This setting is for all
projects.

Delete Issue

$g_delete_bug_threshold

threshold to delete an issue

Reopen Issue

$g_reopen_bug_threshold

threshold to reopen an issue

Allow reporter to reopen Issue

$g_allow_reporter_reopen

allow reporter to reopen issues they reported

Status to which a reopened Issue is set

$g_bug_reopen_status

status issue is set to when reopened

Resolution to which a reopened Issue is set

$g_bug_reopen_resolution

resolution issue is set to when reopened

Status where an issue is considered resolved

$g_bug_resolved_status_threshold

status where bug is resolved

Status where an issue becomes read-only

$g_bug_readonly_status_threshold

status where bug is read-only (see update_readonly_bug_threshold)

Update readonly issue

$g_update_readonly_bug_threshold

threshold to update an issue marked as read-only

Update Issue Status

$g_update_bug_status_threshold

threshold to update an issue's status

View Private Issues

$g_private_bug_threshold

threshold to view a private issue

Set View Status

$g_set_view_status_threshold

threshold to set an issue to Private/Public

Update View Status

$g_change_view_status_threshold

threshold needed to update the view status while updating an issue or an
issue note

Show list of users monitoring issue

$g_show_monitor_list_threshold

threshold to see who is monitoring an issue

Add monitors to an issue

$g_monitor_add_others_bug_threshold

threshold to add users to the list of users monitoring an issue

Remove monitors from an issue

$g_monitor_delete_others_bug_threshold

threshold to remove users from the list of users monitoring an issue

Set status on assignment of handler

$g_auto_set_status_to_assigned

change status when an issue is assigned

Status to set auto-assigned issues to

$g_bug_assigned_status

status to use when an issue is auto-assigned

Limit reporter's access to their own issues (deprecated option)

$g_limit_reporters

reporters can see only issues they reported. This setting is for all
projects.

Limit access only to those issues reported, handled, or monitored by the
user

$g_limit_view_unless_threshold

threshold that, if not met, hides other users' issues.

Notes.

Title

Variable

Description

Add Notes

$g_add_bugnote_threshold

threshold to add an issue note

Update Others' Notes

$g_update_bugnote_threshold

threshold at which a user can edit issue notes created by other users

Update Own Notes

$g_bugnote_user_edit_threshold

threshold at which a user can edit issue notes created by themselves

Delete Others' Notes

$g_delete_bugnote_threshold

threshold at which a user can delete issue notes created by other users

Delete Own Notes

$g_bugnote_user_delete_threshold

threshold at which a user can delete issue notes created by themselves

View private notes

$g_private_bugnote_threshold

threshold to view a private issue note

Change view state of own notes

$g_bugnote_user_change_view_state_threshold

threshold at which a user can change the view state of issue notes
created by themselves

Others.

Title

Variable

Description

View Change Log

$g_view_changelog_threshold

threshold to view the changelog

View Roadmap

$g_roadmap_view_threshold

threshold to view the roadmap

View Summary

$g_view_summary_threshold

threshold to view the summary

View Assigned To

$g_view_handler_threshold

threshold to see who is handling an issue

View Issue History

$g_view_history_threshold

threshold to view the issue history

Send Reminders

$g_bug_reminder_threshold

threshold to send a reminder

*******. Workflow Transitions

This page covers the status workflow. For most of these, the fields are
self explanatory and relate to a similarly named setting in the
configuration file. At the right of each row is a selector that allows
the administrator to lower the access level required to change the
particular parameter.The values changeable on this page are:

Table 6.1. Issues

Title

Variable

Description

Status to which a new issue is set

$g_bug_submit_status

status issue is set to when submitted

Status where an issue is considered resolved

$g_bug_resolved_status_threshold

status where issue is resolved

Status to which a reopened Issue is set

$g_bug_reopen_status

status issue is set to when reopened


The matrix that follows has checkmarks where the transitions are allowed
from the status on the left edge to the status listed across the top.
This corresponds to the $g_enum_workflow array.At the bottom, there is a
list of access levels that are required to change the status to the value
listed across the top. This can be used, for instance, to restrict those
who can close an issue to a specific level, say a manager. This
corresponds to the $g_set_status_threshold array and the
$g_report_bug_threshold setting.

*******. Email Notifications

This page sets the system defaults for sending emails on issue related
events. MantisBT uses flags and a threshold system to generate emails on
events. For each new event, email is sent to:

  * 

    the reporter

  * 

    the handler (or Assigned to)

  * 

    anyone monitoring the issue

  * 

    anyone who has ever added a issue note the issue

  * 

    anyone assigned to the project whose access level matches a range

From this list, those recipients who meet the following criteria are
eliminated:

  * 

    the originator of the change, if $g_email_receive_own is OFF

  * 

    the recipient either no longer exists, or is disabled

  * 

    the recipient has turned their email_on_<new status> preference OFF

  * 

    the recipient has no email address entered

The matrix on this page selects who will receive messages for each of the
events listed down the left hand side. The first four columns correspond
to the first four points listed above. The next columns correspond to the
access levels defined. Note that because a minimum and maximum threshold
are used, a discontinuous selection is not allowed.


6.9. Monitor Issue
------------------

The monitor issues feature allows users to subscribe to certain issues
and hence get copied on all notification emails that are sent for these
issues.Depending on the configuration, sending a reminder to a user about
an issue can add this issue to the user's list of monitored issues. Users
who reported the issue or are assigned the issue typically don't need to
monitor the issue to get the notifications. This is because by default
they get notified on changes related to the issue anyway. However,
administrators can change the configuration to disable notifications to
reporters or handlers in specific scenarios.


6.10. Reopen Issue
------------------

Re-open issue button is visible in the issue view pages if the user has
the appropriate access level and the issue is resolved/closed. Re-opening
a issue will allow users to enter issue notes for the re-opening reason.
The issue will automatically be put into the Feedback status.


6.11. Delete Issue
------------------

The delete issues button appears on the issue view pages for the users
who have the appropriate access level. This allows you to delete an
existing issue. This should only be used on frivolous or test issues. A
confirmation screen will prompt you if you really want to delete the
issue. Updaters, Developers, Managers, and Administrators can remove
issues (you can also configure this).


6.12. Close Issue
-----------------

This is a button that appears on the issue view pages for users that are
authorized to close issues. Depending on the configuration, users may be
able to close issues without having to resolve them first, or may be able
to only close resolved issues. After the button is clicked, the user is
redirected to a page where an issue note maybe added.


6.13. Assign to Me
------------------

This button appears in the issue view pages in case of users with access
level that is equal to handle_bug_threshold or higher. When this button
is clicked the issue is assigned to the user.


6.14. Resolve Issue
-------------------

This option on the View Issues page allows you to resolve the issue. It
will lead you to a page where you can set the resolution state and a
duplicate id (if applicable). After choosing that the user can choose to
enter an issue note detailing the reason for the closure. The issue is
then set to the Resolved state. The reporter should check off on the
issue by using the Close Issue button.


6.15. News Syndication
----------------------

MantisBT supports news syndication using RSS v2.0 protocol. MantisBT also
supports authenticated news feeds for private projects or installations
where anonymous access is not enabled. Authenticated feeds takes a user
name and a key token that are used to authenticate the user and generate
the feed results in the context of the user's access rights (i.e. the
same as what the user would see if they were to logged into MantisBT).To
get access to the News RSS as anonymous user, visit the following page:
http://www.example.com/mantisbt/news_rss.php While a user is logged in,
the RSS links provided in the UI will always provide links to the
authenticated feeds, if no user is logged in (i.e. anonymous), then
anonymous links will be provided.



Chapter 7. Customizing MantisBT
===============================


7.1. Strings / Translations
---------------------------

All the strings used in MantisBT including error messages, as well as
those defined in plugins, can be customized or translated differently.
This is achieved by overriding them in the Custom Strings File (config/custom_strings_inc.php),
which is automatically detected and included by MantisBT code.

Defining custom strings in this file provides a simple upgrade path, and
avoids having to re-apply changes to modified core language files when
upgrading MantisBT to the next release.


Note

The standard MantisBT language strings are sometimes reused in different
contexts. If you are planning to override some strings to meet your
specific requirements, make sure to analyze where and how they are used
to avoid unexpected issues.


7.1.1. Custom Strings File Format

This is a regular PHP script, containing variable assignments and
optionally some control structures to conditionally define strings based
on specific criteria (see Section 7.2.5, “Localizing Custom Field Names”
for an example).

<?php
$s_CODE = STRING;
$MANTIS_ERROR[ERROR_NUMBER] = STRING;

Where

  * 

    CODE = language string code, as called by lang_get() function. Search
    in lang/strings_english.txt for existing codes.

  * 

    ERROR_NUMBER = error number or constant, see constant_inc.php.

  * 

    STRING = string value / translation.


Note

The custom_strings_inc.php file should only contain variable assignments
and basic PHP control structures. In particular, calling MantisBT core
functions in it is not recommended, as it could lead to unexpected
behavior and even errors depending on context.

If you must use API calls, then anything that expects an active database
connection needs to be protected, e.g.

<?php
if( db_is_connected() ) {
        if( helper_get_current_project() == 1 ) {
                $s_summary = 'Title';
        }
}


Warning

NEVER call lang_get_current() from the custom_strings_inc.php. Doing so
will reset the active_language, causing the code to return incorrect
translations if the default language is different from English. Always
use the $g_active_language global variable instead.


7.2. Custom Fields
------------------


7.2.1. Overview

Different teams typically like to capture different information as users
report issues, in some cases, the data required is even different from
one project to another. Hence, MantisBT provides the ability for managers
and administrators to define custom fields as way to extend MantisBT to
deal with information that is specific to their teams or their projects.
The aim is for this to keep MantisBT native fields to a minimum.
Following are some facts about the implementation of custom fields in
MantisBT:

  * 

    Custom fields are defined system wide.

  * 

    Custom fields can be linked to multiple projects.

  * 

    The sequence of displaying custom fields can be different per
    project.

  * 

    Custom fields must be defined by users with access level
    ADMINISTRATOR.

  * 

    Custom fields can be linked to projects by users with access level
    MANAGER or above (by default, this can be configurable).

  * 

    Number of custom fields is not restricted.

  * 

    Users can define filters that include custom fields.

  * 

    Custom fields can be included in View Issues, Print Issues, and CSV
    exports.

  * 

    Enumeration custom fields can have a set of static values or values
    that are calculated dynamically based on a custom function.


7.2.2. Custom Field Definition

The definition of a custom field includes the following logical
attributes:

  * 

    Caption variable name. This value is supplied to the lang_get() API;
    it is therefore mandatory to set this to a valid PHP identifier (i.e.
    only letters, numbers and underscores; no spaces) if you intend to
    translate the field label (see Section 7.2.5, “Localizing Custom
    Field Names”).


    Note

    If the specified variable is not found in the language files or in
    custom_strings_inc.php, then it will be displayed as-is.

  * 

    Custom field type, can be one of:

      * 

        string, for strings of up to 255 characters.

      * 

        numeric, for numerical integer values.

      * 

        float, for real (float / double) numbers.

      * 

        email, for storing email addresses.

      * 

        enumeration is used when a user selects one entry from a list.
        The user interface for this type is a combo-box.

      * 

        checkbox is like enumeration, but the options are shown as
        checkboxes and the user is allowed to tick more than one item.

        The default value and the possible value can contain multiple
        values like RED|YELLOW|BLUE.

      * 

        radio is like enumeration, but the list is shown as radio buttons
        and the user is only allowed to tick a single option.

        The possible values can be RED|YELLOW|BLUE, and default YELLOW.


        Note

        The default value can't contain multiple values.

      * 

        list is like enumeration but the list is shown as a list box
        where the user is only allowed to select one option.

        The possible values can be RED|YELLOW|BLUE, and default YELLOW.


        Note

        The default value can't contain multiple values.

      * 

        multi-selection list is like enumeration, but the list is shown
        as a list box where the user is allowed to select multiple
        options.

        The possible values can be RED|YELLOW|BLUE, and default RED|BLUE.


        Note

        Multiple values are allowed as default.

      * 

        date, for date values.

        The default value can be empty, a numeric UNIX timestamp, or a
        date in a valid format, including relative indications such as
        tomorrow, next week, last month, +3 days, last day of this month,
        etc.


        Note

        The legacy format where the dynamic date had to be wrapped in
        curly brackets (e.g. {tomorrow}) is still supported for
        backwards-compatibility, but no longer necessary. This is
        considered a deprecated feature, that will be removed in a future
        released of MantisBT.

  * 

    Possible values for the Custom Field (e.g. RED|YELLOW|BLUE). Use the
    pipe (|) character to separate the enumeration's values. It is
    possible for one of the values to be empty (e.g. |RED|YELLOW|BLUE,
    note the leading |).

    The set of values can also be calculated at runtime. For example,
    =versions would automatically resolve into all the versions defined
    for the current project. See Section 7.2.7, “Dynamic values for
    Enumeration Custom Fields” for more information.

  * 

    Default value - see details above for a sample default value for each
    type.

  * 

    Minimum/maximum length for the custom field value (use 0 to disable).
    Note that these metrics are not really relevant to custom fields that
    are based on an enumeration of possible values.

  * 

    Regular expression to use for validating user input (use PCRE syntax).

  * 

    Read Access level: Minimum access level for users to be able to see
    the value of the custom field.

  * 

    Write Access level: Minimum access level for users to be able to edit
    the value of the custom field.

  * 

    Display when reporting issues? - If this custom field should be shown
    on the Report Issue page.

  * 

    Display when updating issues? - If this custom field should be shown
    on the Update Issue page.

  * 

    Display when resolving issues? - If this custom field should be shown
    when resolving an issue. For example, a "root cause" custom field
    would make sense to set when resolving the issue.

  * 

    Display when closing issues? - If this custom field should be shown
    when closing an issue.

  * 

    Required on Report - If this custom field is a mandatory field on the
    Report Issue page.

  * 

    Required on Update - If this custom field is a mandatory field on the
    Update Issue page.

  * 

    Required on Resolve - If this custom field is a mandatory field when
    resolving an issue.

  * 

    Required on Close - If this custom field is a mandatory field when
    closing an issue.

If the value of a custom field for a certain defect is not found, the
default value is assumed.


7.2.3. Adding/Editing Custom Fields

  * 

    The logged in user needs $g_manage_custom_fields_threshold access
    level.

  * 

    Select "Manage" from the main menu.

  * 

    Select "Custom Fields" from the management menu.

  * 

    In case of edit, click on the name of an existing custom field to
    edit its information.

  * 

    In case of adding a new one, enter the name of the new custom field
    then click "New Custom Field".


Note

Added custom fields will not show up in any of the issues until the added
custom field is linked to the appropriate projects.


7.2.4. Linking/Unlinking/Ordering Existing Custom Fields in Projects

  * 

    The logged in user needs to have access level that is greater than or
    equal to $g_custom_field_link_threshold and
    $g_manage_project_threshold.

  * 

    Select "Manage" from the main menu.

  * 

    Select "Projects".

  * 

    Select the name of the project to manage.

  * 

    Scroll down to the "Custom Fields" box.

  * 

    Select the field to add from the list, then click "Add This Existing
    Custom Field".

  * 

    To change the order of the custom fields, edit the "Sequence" value
    and click update. Custom fields with smaller values are displayed
    first.

  * 

    To unlink a custom field, click on "Remove" link next to the field.
    Unlinking a custom field will not delete the values that are
    associated with the issues for this field. These values are only
    deleted if the custom field definition is removed (not unlinked!)
    from the database. This is useful if you decide to re-link the custom
    field. These values may also re-appear if issues are moved to another
    project which has this field linked.

Moving Issues. When an issue is moved from one project to another, custom
fields that are not defined for the new project are not deleted. These
fields will re-appear with their correct values if the issue is moved
back to the original project, or if these custom fields are linked to the
new project.


7.2.5. Localizing Custom Field Names

It is possible to localize the custom fields' labels. This can be done as
follows:

  1. 

    Define the custom field (see Section 7.2.2, “Custom Field Definition”),
    keeping in mind that its name must be a valid PHP identifier.

    As an example, we will use my_start_date for a custom field of type
    "Date", storing the date when work on an issue was initiated.

  2. 

    Set the localization strings

      * 

        In the MantisBT config directory, locate and edit
        custom_strings_inc.php (see Section 7.1, “Strings / Translations”),
        create it if it does not exist.

      * 

        Localize the custom field's label my_start_date by adding the
        following code

        <?php
        switch( $g_active_language ) {
                case 'french':
                        $s_my_start_date = 'Date de début';
                        break;
        
                default:
                        # Default language, as defined in config/config_inc.php
                        # ($g_default_language, English in this case)
                        $s_my_start_date = 'Start Date';
                        break;
        }      


Note

Had we decided to use start_date as the custom field's name, then it
would not have been necessary to modify custom_strings_inc.php (see
Section 7.1, “Strings / Translations”), since MantisBT would have used
the existing, already localized string from the standard language files.
To check for standard strings, inspect lang/strings_english.txt.


7.2.6. Dynamic default values

7.2.6.1. Dynamic defaults for Date fields

Custom fields of type date can be defaulted to either specific or
relative dates. Typically, relative dates is the scenario that makes
sense in most of the cases.

The format for specific dates is an integer which indicates the number of
seconds since the Unix Epoch (January 1 1970 00:00:00 UTC), which is the
format consumed by the PHP date() method.

The relative scenario expects default values like {tomorrow},
{yesterday}, {+2 days}, {-3 days}, {next week}, etc. The curly brackets
indicate that this is a logical value which is then evaluated using the
PHP strtotime() function.


7.2.7. Dynamic values for Enumeration Custom Fields

As discussed earlier, one of the possible types of a custom field is
"enumeration". This type of custom field allows the user to select one
value from a provided list of possible values. The standard way of
defining such custom fields is to provide a '|' separated list of
possible values. However, this approach has two limitations: the list is
static, and the maximum length of the list must be no longer than 255
characters. Hence, the need for the ability to construct the list of
possible values dynamically.

*******. Dynamic possible values included by default

MantisBT ships with some dynamic possible values, these include the
following:

  * 

    =categories a list of categories defined in the current project (or
    the project to which the issue belongs).

  * 

    =versions a list of all versions defined in the current project (or
    the project to which the issue belongs).

  * 

    =future_versions a list of all versions that belong to the current
    project with released flag set to false.

  * 

    =released_versions a list of all versions that belong to the current
    project with released flag set to true.


Note

The = before the list of options tells MantisBT that this is a dynamic
list, rather than a static one with a single option.

*******. Defining Custom Dynamic Possible Values

If the user selects =versions, the actual custom function that is
executed is custom_function_*_enum_versions(). The reason why the "enum_"
is not included is to have a fixed prefix for all custom functions used
for this purpose and protect against users using custom functions that
were not intended for this purpose.

For example, you would not want the user to use
custom_function_*_issue_delete_notify() which may be overridden by the
web master to delete associated data in other databases.

Following is a sample custom function that is used to populate a field
with the categories belonging to the currently selected project:

/**
 * Construct an enumeration for all categories for the current project.
 *
 * The enumeration will be empty if current project is ALL PROJECTS.
 * Enumerations format is: "abc|lmn|xyz"
 * To use this in a custom field type "=categories" in the possible values field.
 */
function custom_function_override_enum_categories() {
        $t_categories = category_get_all_rows( helper_get_current_project() );

        $t_enum = array();
        foreach( $t_categories as $t_category ) {
                $t_enum[] = $t_category['category'];
        }

        $t_possible_values = implode( '|', $t_enum );

        return $t_possible_values;
}


Note

  * 

    The custom function doesn't take any parameters.

  * 

    The custom function returns the possible values in the format
    (A|B|C).

  * 

    The custom function uses the current project.

  * 

    The custom function builds on top of the already existing APIs.

To define your own function mine, you will have to define it with the
following signature:

/**
 * Use this in a custom field type "=mine" in the possible values field.
 */
function custom_function_override_enum_mine() {
        # Populate $t_enum values as appropriate here
        $t_enum = array();

        $t_possible_values = implode( '|', $t_enum );

        return $t_possible_values;
}


Note

Notice the override in the function name. This is because this method is
defined by the MantisBT administrator and not part of the MantisBT
source. It is OK to override a method that doesn't exist.

As usual, when MantisBT is upgraded to future releases, the custom
functions will not be overwritten. The difference between the "default"
implementation and the "override" implementation is explained in more
details in Section 7.6, “Custom Functions”.


7.3. Enumerations
-----------------

Enumerations are used in MantisBT to represent a set of possible values
for an attribute. Enumerations are used for access levels, severities,
priorities, project statuses, project view state, reproducibility,
resolution, ETA, and projection. MantisBT provides the administrator with
the flexibility of altering the values in these enumerations. The rest of
this topic explains how enumerations work, and then how they can be
customised.

How do enumerations work? core/constant_inc.php defines the constants
that correspond to those in the enumeration. These are useful to refer to
these enumerations in the configs and the code.

define( 'VIEWER', 10 );
define( 'REPORTER', 25 );
define( 'UPDATER',  40 );
define( 'DEVELOPER', 55 );
define( 'MANAGER', 70 );
define( 'ADMINISTRATOR', 90 );

config_defaults_inc.php includes the defaults for the enumerations. The
configuration options that are defaulted here are used in specifying
which enumerations are active and should be used in MantisBT.

$g_access_levels_enum_string =
        '10:viewer,25:reporter,40:updater,55:developer,70:manager,90:administrator';


Note

The strings included in the enumerations here are just for documentation
purposes, they are not actually shown to the user (due to the need for
localisation). Hence, if an entry in this enumeration is not found in the
corresponding localised string (i.e. 70:manager), then it will be printed
to the user as @70@.

The Language Files (e.g. lang/strings_german.txt) provide the localised
strings (German in this case) for enumerations. But again, the master
list is the enumeration in the configs themselves, the ones in the
language files are just used for finding the localised equivalent for an
entry. Hence, if a user changes the config to have only two types of
users developers and administrators, then only those will be prompted to
the users even if the enumerations in the language files still includes
the full list.

$s_access_levels_enum_string =
        '10:Betrachter,25:Reporter,40:Updater,55:Entwickler,70:Manager,90:Administrator';

How can they be customised? Let say we want to remove access level
"Updater" and add access level "Senior Developer".

The file config/custom_constants_inc.php is supported for the exclusive
purpose of allowing administrators to define their own constants while
maintaining a simple upgrade path for future releases of MantisBT. Note
that this file is not distributed with MantisBT and you will need to
create it if you need such customisation. In our example, we need to
define a constant for the new access level.

define( 'SENIOR_DEVELOPER', 60 );

In config/config_inc.php

// Remove Updater and add Senior Developer
$g_access_levels_enum_string =
        '10:viewer,25:reporter,55:developer,60:senior_developer,70:manager,90:administrator';

// Give access to Senior developers to create/delete custom field.
$g_manage_custom_fields_threshold = SENIOR_DEVELOPER;

Update custom_strings_inc.php (see Section 7.1, “Strings / Translations”)

$s_access_levels_enum_string =
        '10:Betrachter,25:Reporter,40:Updater,55:Entwickler,60:Senior Developer,70:Manager,90:Administrator';


Note

We don't need to remove the Updater entry from the localisation file if
the current language is 'English'.

Conclusion. We have covered how enumerations work in general, and how to
customise one of them. If you are interested in customising other
enumerations, a good starting point would be to go to MantisBT Enum
Strings section in config_defaults_inc.php. This section defines all
enumerations that are used by MantisBT.


7.4. Email Notifications
------------------------

See Section 5.8, “Email” in the Configuration section.

Examples:

  * 

    Notify only managers of new issues.

    $g_notify_flags['new'] = array(
            'threshold_min' => MANAGER,
            'threshold_max' => MANAGER,
    );  

  * 

    Notify Developers and managers of all project events, except, exclude
    developers from the 'closed' events.

    $g_default_notify_flags = array(
            'threshold_min' => DEVELOPER,
            'threshold_max' => MANAGER,
    );
    $g_notify_flags['closed'] = array(
            'threshold_min' => MANAGER,
            'threshold_max' => MANAGER,
    );  

  * 

    Exclude those who contributed issue notes from getting messages about
    other changes in the issue.

    $g_default_notify_flags['bugnotes'] = OFF;  

  * 

    Exclude those monitoring issues from seeing the 'closed' message

    $g_notify_flags['closed']['monitor'] = OFF;  

  * 

    Only notify developers when issue notes are added.

    $g_notify_flags['bugnote'] = array(
            'threshold_min' => DEVELOPER,
            'threshold_max' => DEVELOPER,
    );  

  * 

    Notify managers of changes in sponsorship.

    $g_notify_flags['sponsor'] = array(
            'threshold_min' => MANAGER,
            'threshold_max' => MANAGER,
    );  

  * 

    Notify originator and managers of changes in ownership ("Assigned
    To:").

    $g_notify_flags['owner'] = array(
            'threshold_min' => MANAGER,
            'threshold_max' => MANAGER,
            'reporter'      => ON,
    );  

  * 

    I'm paranoid about mail. Only send information on issues to those
    involved in them. Don't send mail people already know about. Also
    send new issue notifications to managers so they can screen them.

    $g_email_receive_own = OFF;
    $g_default_notify_flags = array(
            'reporter'      => ON,
            'handler'       => ON,
            'monitor'       => ON,
            'bugnotes'      => ON,
            'category'      => ON,
            'threshold_min' => NOBODY,
            'threshold_max' => NOBODY
    );
    $g_notify_flags['new'] = array(
            'threshold_min' => MANAGER,
            'threshold_max' => MANAGER,
    );  

  * 

    How do I send all messages to an email logger.

    You will need to create a dummy user with the appropriate access
    level for the notices you want to log. Once this user is added to
    projects, they will receive mail using the appropriate rules.


7.5. Customizing Status Values
------------------------------

This section describes how to add a custom status.

  1. 

    Define a constant to map the new status to.

    In subfolder config, locate and edit file custom_constants_inc.php;
    (create it if it does not exist)

    <?php
            # Custom status code
            define( 'TESTING', 60 );  

  2. 

    Define the new status in the enumeration, as well as the
    corresponding color code.

    In subfolder config, edit your config_inc.php

    # Revised enum string with new 'testing' status
    $g_status_enum_string = '10:new,20:feedback,30:acknowledged,40:confirmed,50:assigned,    60:testing,    80:resolved,90:closed';
    
    # Status color additions
    $g_status_colors['    testing    '] = '#ACE7AE';

    Note that the key in the $g_status_colors array must be equal to the
    value defined for the new status code in $g_status_enum_string.

  3. 

    Define the required translation strings for the new status, for each
    language used in the installation.

      * 

        s_status_enum_string: status codes translation (refer to the
        original language strings for standard values)

      * 

        s_XXXX_bug_title: title displayed in the change status page

      * 

        s_XXXX_bug_button: label for the submit button in the change
        status page

      * 

        s_email_notification_title_for_status_bug_XXXX: title for
        notification e-mails

    where XXXX is the name of the new status as it was defined in
    g_status_enum_string above. If XXXX contains spaces, they should be
    replaced by underscores in the language strings names (e.g. for
    '35:pending user', use '$s_pending_user_bug_button')

    In the config subfolder, locate and edit custom_strings_inc.php (see
    Section 7.1, “Strings / Translations”), create it if it does not
    exist

    <?php
    # Translation for Custom Status Code:     testing    switch( $g_active_language ) {
    
            case 'french':
                    $s_status_enum_string = '10:nouveau,20:commentaire,30:accepté,40:confirmé,50:affecté,60:à tester,80:résolu,90:fermé';
    
                    $s_testing_bug_title = 'Mettre le bogue en test';
                    $s_testing_bug_button = 'A tester';
    
                    $s_email_notification_title_for_status_bug_testing = 'Le bogue suivant est prêt à être TESTE.';
                    break;
    
            default: # english
                    $s_status_enum_string = '10:new,20:feedback,30:acknowledged,40:confirmed,50:assigned,60:testing,80:resolved,90:closed';
    
                    $s_testing_bug_title = 'Mark issue Ready for Testing';
                    $s_testing_bug_button = 'Ready for Testing';
    
                    $s_email_notification_title_for_status_bug_testing = 'The following issue is ready for TESTING.';
                    break;
    }  

  4. 

    Add the new status to the workflow as required.

    This can either be done from the Manage Workflow Transitions page
    (see Section 4.3.1, “Workflow Transitions”) or by manually editing
    config_inc.php as per the example below:

    $g_status_enum_workflow[NEW_]         ='30:acknowledged,20:feedback,40:confirmed,50:assigned,80:resolved';
    $g_status_enum_workflow[FEEDBACK]     ='30:acknowledged,40:confirmed,50:assigned,80:resolved';
    $g_status_enum_workflow[ACKNOWLEDGED] ='40:confirmed,20:feedback,50:assigned,80:resolved';
    $g_status_enum_workflow[CONFIRMED]    ='50:assigned,20:feedback,30:acknowledged,80:resolved';
    $g_status_enum_workflow[ASSIGNED]     ='60:testing,20:feedback,30:acknowledged,40:confirmed,80:resolved';
    $g_status_enum_workflow[TESTING]      ='80:resolved,20:feedback,50:assigned';
    $g_status_enum_workflow[RESOLVED]     ='90:closed,20:feedback,50:assigned';
    $g_status_enum_workflow[CLOSED]       ='20:feedback,50:assigned';  

  5. 

    Check and update existing workflow configurations

    If you do not perform this step and have existing workflow
    definitions, it will not be possible to transition to and from your
    new status.

    Go to the Workflow Transitions page
    (manage_config_workflow_page.php), and update the workflow as
    appropriate. Make sure that you have picked the correct Project in
    the selection list).

    Hint: to identify whether you have any workflows that should be
    updated, open the Manage Configuration Report page
    (adm_config_report.php) and filter on 'All Users', [any] project and
    config option = 'status_enum_workflow'. All of the listed projects
    should be reviewed to eventually include transitions to and from the
    newly added states.


7.6. Custom Functions
---------------------

Custom functions are used to extend the functionality of MantisBT by
integrating user-written functions into the issue processing at strategic
places. This allows the system administrator to change the functionality
without touching MantisBT's core.

Default Custom Functions are defined in the API file
core/custom_function_api.php , and are named
custom_function_default_descriptive_name, where descriptive_name
describes the particular function. See Section 7.6.1, “Default Custom
Functions” for a description of the specific functions.

User versions of these functions (overrides) are named like
custom_function_override_descriptive_name, and placed in a file called
custom_functions_inc.php that must be saved in MantisBT's config
directory. In normal processing, the system will look for override
functions and execute them instead of the provided default functions.

The simplest way to create a custom function is to copy the default one
from the api to your override file (custom_functions_inc.php), and rename
it (i.e. replacing 'default' by 'override'). The specific functionality
you need can then be coded into the override function.


7.6.1. Default Custom Functions

Refer to core/custom_functions_api.php for further details.

Custom Function Name

Description

Return value

custom_function_default_auth_can_change_password()

Determines whether MantisBT can update the password

True if yes, False if not

custom_function_default_changelog_include_issue( $p_issue_id )

Determines whether the specified issue should be included in the
Changelog or not.

True to include, False to exclude

custom_function_default_changelog_print_issue( $p_issue_id,
$p_issue_level = 0 )

Prints one entry in the Changelog

None

custom_function_default_enum_categories()

Build a list of all categories for the current project

Enumeration, delimited by "|"

custom_function_default_enum_future_versions()

Build a list of all future versions for the current project

Enumeration, delimited by "|"

custom_function_default_enum_released_versions()

Build a list of all released versions for the current project

Enumeration, delimited by "|"

custom_function_default_enum_versions()

Build a list of all versions for the current project

Enumeration, delimited by "|"

custom_function_default_format_issue_summary( $p_issue_id, $p_context = 0
)

Format the bug summary

Formatted string

custom_function_default_get_columns_to_view( $p_columns_target =
COLUMNS_TARGET_VIEW_PAGE, $p_user_id = null )

Defines which columns should be displayed

Array of the column names

custom_function_default_issue_create_notify( $p_issue_id )

Notify after an issue has been created

In case of invalid data, this function should call trigger_error()

custom_function_default_issue_create_validate( $p_new_issue_data )

Validate field settings before creating an issue

In case of invalid data, this function should call trigger_error()

custom_function_default_issue_delete_notify( $p_issue_data )

Notify after an issue has been deleted

In case of invalid data, this function should call trigger_error()

custom_function_default_issue_delete_validate( $p_issue_id )

Validate field settings before deleting an issue

In case of invalid data, this function should call trigger_error()

custom_function_default_issue_update_notify( $p_issue_id )

Notify after an issue has been updated

In case of invalid data, this function should call trigger_error()

custom_function_default_issue_update_validate( $p_issue_id,
$p_new_issue_data, $p_bugnote_text )

Validate field issue data before updating

In case of invalid data, this function should call trigger_error()

custom_function_default_print_bug_view_page_custom_buttons( $p_bug_id )

Prints the custom buttons on the current view page

None

custom_function_default_print_column_title( $p_column, $p_columns_target
= COLUMNS_TARGET_VIEW_PAGE, array $p_sort_properties = null )

Print a column's title based on its name

None

custom_function_default_print_column_value( $p_column, $p_bug,
$p_columns_target = COLUMNS_TARGET_VIEW_PAGE )

Print a column's value based on its name

None

custom_function_default_roadmap_include_issue( $p_issue_id )

Determines whether the specified issue should be included in the Roadmap
or not.

True to include, False to exclude

custom_function_default_roadmap_print_issue( $p_issue_id, $p_issue_level
= 0 )

Prints one entry in the Roadmap

None


7.6.2. Example Custom Function Override

The following function is used to validate an issue before it is
resolved.

<?php

/**
 * Hook to validate Validate field settings before resolving
 * verify that the resolution is not set to OPEN
 * verify that the fixed in version is set (if versions of the product exist)
 */
function custom_function_override_issue_update_validate( $p_issue_id, $p_bug_data, $p_bugnote_text ) {
        if( $p_bug_data->status == RESOLVED ) {
                if( $p_bug_data->resolution == OPEN ) {
                        error_parameters( 'the resolution cannot be open to resolve the issue' );
                        trigger_error( ERROR_VALIDATE_FAILURE, ERROR );
                }
                $t_version_count = count( version_get_all_rows( $p_bug_data->project_id ) );
                if( ( $t_version_count > 0 ) && ( $p_bug_data->fixed_in_version == '' ) ) {
                        error_parameters( 'fixed in version must be set to resolve the issue' );
                        trigger_error( ERROR_VALIDATE_FAILURE, ERROR );
                }
        }
}

?>

The errors will also need to be defined, by modifying the following files

  * 

    custom_constants_inc.php

    define( 'ERROR_VALIDATE_FAILURE', 2000 );  

  * 

    custom_strings_inc.php (see Section 7.1, “Strings / Translations”)

    $MANTIS_ERROR['ERROR_VALIDATE_FAILURE'] = 'This change cannot be made because %s';  



Chapter 8. Authentication
=========================

MantisBT supports several authentication methods out of the box. In
addition, there is work in progress relating to supporting authentication
plug-ins. Once these are implemented, authentication against any protocol
or repository of user names and passwords will be possible without having
to touch MantisBT core code.

It is important to note that MantisBT does not yet support hybrid
authentication scenarios. For example, internal staff authenticating
against LDAP while customers authenticate against the MantisBT database
with MD5 hash.

See $g_login_method in Section 5.21.1, “Global authentication parameters”
for more details about how to configure MantisBT to use one of these
authentication techniques.


8.1. Standard Authentication
----------------------------

With Standard login method, MantisBT users are authenticated against
records in the MantisBT database, where the passwords are stored as a
hash.

Note: while technically unlimited, the password's length is arbitrarily
restricted to 1024 characters (PASSWORD_MAX_SIZE_BEFORE_HASH constant).

Values for $g_login_method:

  * 

    MD5 is the default method

  * 

    Support for additional methods could be added in the future


8.2. LDAP and Microsoft Active Directory
----------------------------------------

Value for $g_login_method: LDAP

Authentication is made against an LDAP or Active Directory server.

The LDAP parameters should be setup as explained in Section 5.21.2, “LDAP
authentication method parameters”.

An MD5 hash of the user's password will be stored in the database upon
successful login, allowing fall-back to Standard Authentication when the
LDAP server is not available.

The user's ID and password is checked against the Directory; if the
credentials are valid, then the user is allowed to login and their user
account in MantisBT is created automatically.


8.3. Basic Authentication
-------------------------

Value for $g_login_method: BASIC_AUTH

When MantisBT is configured to use basic auth, it automatically detects
the logged in user and checks if they are already registered in MantisBT,
if not, then a new account is automatically created for the username.

The password length is limited to the size of the underlying database
field (DB_FIELD_SIZE_PASSWORD constant), currently 32 characters.


8.4. HTTP Authentication
------------------------

Value for $g_login_method: HTTP_AUTH

TODO

The password length is limited to the size of the underlying database
field (DB_FIELD_SIZE_PASSWORD constant), currently 32 characters.


8.5. Deprecated authentication methods
--------------------------------------

The following methods of authentication are deprecated, and supported for
backwards-compatibility reasons only. It is strongly recommended to
update MantisBT installations relying on these to use Section 8.1,
“Standard Authentication” instead.

Deprecated values for $g_login_method:

  * 

    CRYPT

  * 

    CRYPT_FULL_SALT

  * 

    PLAIN

With CRYPT-based methods, the password's length is limited as per
Standard Authentication. With PLAIN, its size is restricted as for Basic
Authentication.



Chapter 9. Troubleshooting
==========================

This chapter provides the Administrator with additional information
related to Application Errors and common problems in MantisBT.

Useful additional reference information and support may also be found on
the MantisBT website, more specifically the Forums and the Bugtracker.


9.1. Application Errors
-----------------------

Additional information about common MantisBT errors.


9.1.1. Error 2800 - Invalid form security token

This error may only occur when Form Validation is enabled with
$g_form_security_validation = ON (see Section 5.4, “Webserver”). There
are several known cases that could trigger it:

  * 

    Multiple submissions of a form by clicking on the submit button
    several times (user error)

  * 

    Invalid or unauthorized submission of a form, e.g. by hand-crafting
    the URL (CSRF attack)

  * 

    Expired PHP session

In the first two instances, MantisBT's behavior is by design, and the
response as expected. For expired sessions however, the user is impacted
by system behavior, which could not only cause confusion, but also
potential loss of submitted form data. What happens is driven by several
php.ini configuration settings:

  * 

    The ratio session.gc_probability divided by session.gc_divisor, which
    determines the probability that the garbage collection process will
    start when a session is initialized.

  * 

    session.gc_maxlifetime which specifies (as the name does not
    indicate) the minimum validity of session data.

With PHP default values, sessions created more than 1440 seconds (24
minutes) ago have a 1% chance to be invalidated each time a new session
is initialized. This explains the seemingly random occurrence of this
error.

Unfortunately, this problem cannot be fixed without a major rework of the
way sessions and form security are handled in MantisBT.

As a workaround, the Administrator can

  * 

    Increase the value of session.gc_maxlifetime

  * 

    Set $g_form_security_validation = OFF. Note that for security
    reasons, it is strongly recommended not to do this.

Users may also install local tools to avoid loss of form data, such as
Typio Form Recovery Chrome extension, or Form History Control add-on for
Firefox and Chrome.

Further references and reading:

  * 

    MantisBT issues 12381, 12492, 13106, 13246

  * 

    MantisBT forums



Chapter 10. Project Management
==============================

This section covers the project management features of MantisBT. This
includes features like change log, roadmap, time tracking, reporting and
others.


10.1. Change Log
----------------

MantisBT doesn't just track the status of issues, it also relates issues
to versions. Each project can have several versions, which are marked
with attributes like released and obsolete. Users typically report issues
against released issues and developers typically fix issues in not
released versions. With every new release comes question like: what's
new? what has been fixed? Customers wonder if the new release is of
interest to them and whether they should take an upgrade. Well, the
change log is specifically tailored to answer these kind of questions.

In order for an issue to show up in the change log, it has to satisfy
certain criteria. The criteria is that the issue has to be resolved with
a 'fixed' resolution and has to have the 'fixed_in_version' field set.
Users sometimes wonder why resolved or closed issues don't show up in the
change log, and the answer is that the 'fixed_in_version' field is not
set. Without the 'fixed_in_version', it is not possible for MantisBT to
include the issues in the appropriate section of the changelog. Note that
it is possible to set the 'fixed_in_version' for multiple issues using
the 'Update Fixed in Version' group action on the View Issues page (just
below the issues list). This option is only available when the selected
project is not 'All Projects'. Once a version is marked as obsolete, it
is now longer included in the change log.

MantisBT also provides the ability to customize the criteria used for an
issue to be included in the change log. For example, for installations
that use a custom set of resolutions, it is possible to select multiple
resolutions as valid candidates for the change log. This can be done
using custom functions (see custom functions documentation for more
details). The custom function below overrides the MantisBT default
behavior to include issues with both FIXED and IMPLEMENTED (a custom
resolution) resolutions in the change log.

<?php
# --------------------
# Checks the provided bug and determines whether it should be included in the changelog
# or not.
# returns true: to include, false: to exclude.
function custom_function_override_changelog_include_issue( $p_issue_id ) {
    $t_issue = bug_get( $p_issue_id );

    return ( ( $t_issue->resolution == FIXED || $t_issue->resolution == IMPLEMENTED ) &&
        ( $t_issue->status >= config_get( 'bug_resolved_status_threshold' ) ) );
}

MantisBT also provides the ability to customize the details to include
from the issue and in what format. This can be done using the following
custom function.

<?php
# --------------------
# Prints one entry in the changelog.
function custom_function_override_changelog_print_issue( $p_issue_id, $p_issue_level = 0 ) {
    $t_bug = bug_get( $p_issue_id );

    if( $t_bug->category_id ) {
        $t_category_name = category_get_name( $t_bug->category_id );
    } else {
        $t_category_name = '';
    }

    $t_category = is_blank( $t_category_name ) ? '' : '&lt;b&gt;[' . $t_category_name . ']&lt;/b&gt; ';
    echo str_pad( '', $p_issue_level * 6, '&#160;' ), '- ', string_get_bug_view_link( $p_issue_id ), ': ', $t_category, string_display_line_links( $t_bug->summary );

    if( $t_bug->handler_id != 0 ) {
        echo ' (', prepare_user_name( $t_bug->handler_id ), ')';
    }

    echo ' - ', get_enum_element( 'status', $t_bug->status ), '.&lt;br /&gt;';
}

By combining both customization features, it is also possible to do more
advanced customization scenarios. For example, users can add a
'ChangelogSummary' custom field and include all issues that have such
field in the change log. Through customizing what information being
included for a qualifying issue, users can also include the
'ChangelogSummary' text rather than the native summary field.

In some cases, users know that they fixed an issue and that the fix will
be included in the next release, however, they don't know yet the name of
the release. In such case, the recommended approach is to always have a
version defined that corresponds to the next release, which is typically
called 'Next Release'. Once the release is cut and has a concrete name,
then 'Next Release' can be renamed to the appropriate name and a new
'Next Release' can then be created. For teams that manage releases from
multiple branches for the same project, then more than one next release
can be possible. For example, 'Next Dev Release' and 'Next Stable
Release'.

Another common requirement is to be able to link to the change log of a
specific project from the project's main website. There is a variety of
ways to do that:

  * 

    To link to the changelog of version "ver1" of project "myproject":

    http://www.example.com/mantisbt/changelog_page.php?project=myproject&version=ver1  

  * 

    To link to the changelog of all non-obsolete versions of project
    'myproject':

    http://www.example.com/mantisbt/changelog_page.php?project=myproject  

  * 

    To link to the changelog of project with id 1. The project id can be
    figured out by going to the management page for the project and
    getting the value of project_id field form the URL.

    http://www.example.com/mantisbt/changelog_page.php?project_id=1  

  * 

    To link to the changelog of version with id 1. The version id is
    unique across all projects and hence in this case it is not necessary
    to include the project id/name. The version id can be figured out by
    going to the manage project page and editing the required version.
    The version_id will be included in the URL.

    http://www.example.com/mantisbt/changelog_page.php?version_id=1  

Another approach is to go to the project page and from there users can
get to multiple other locations relating to the project include the
change log. This can be done by a URL like the following:

http://www.example.com/mantisbt/project_page.php?project_id=1

It is possible to customize the access level required for viewing the
change log page. This can be done using the $g_view_changelog_threshold
configuration option.


10.2. Roadmap
-------------

One of the very important scenarios in project management is where the
project managers (or team leads) triage the issues to set their
priorities, target version, and possibly assign the issues to specific
developers or take other actions on the issue. By setting the target
version of an issue to a version that is not yet released, the issue
shows up on the project roadmap, providing user with information about
when to expect the issues to be resolved. The roadmap page has a section
for each release showing information like planned issues, issues done and
percentage of issues completed. Issues that are fixed in a specific
version, but didn't have the target_version field set, will not show up
in the roadmap. This allows the ability to control the issues that are
significant enough to show in the roadmap, while all resolved fields can
be found in the change log. Note that it is possible to set the
'target_version' for multiple issues using the 'Update Target Version'
group action that is available through the View Issues page (below the
issues list). This option is only available when the current project is
not 'All Projects'. Although it is not a typical scenario, it is worth
mentioning that once a version is marked as obsolete, it is not included
in the roadmap.

Note that the roadmap only includes future versions, once a version is
marked as released, it no longer is included in the roadmap. For
information about such releases, the change log feature should be used.
For an issue to be shown on the roadmap, it has to have the target
version set. It does not matter whether the feature is resolved or not.
Resolved features will be decorated with a strikethrough and will be
counted as done.

MantisBT provides the ability to customize the criteria for issues to
show up on the roadmap. The default criteria is that the issue has to
belong to a version that is not yet released and that the issues is not a
duplicate. However, such criteria can be customized by using custom
functions as below.

<?php
# --------------------
# Checks the provided bug and determines whether it should be included in the roadmap or not.
# returns true: to include, false: to exclude.
function custom_function_override_roadmap_include_issue( $p_issue_id ) {
    return ( true );
}

It is also possible to customize the details included about an issues and
the presentation of such details. This can be done through the following
custom function:

<?php
# --------------------
# Prints one entry in the roadmap.
function custom_function_override_roadmap_print_issue( $p_issue_id, $p_issue_level = 0 ) {
    $t_bug = bug_get( $p_issue_id );

    if( bug_is_resolved( $p_issue_id ) ) {
        $t_strike_start = '&lt;strike&gt;';
        $t_strike_end = '&lt;/strike&gt;';
    } else {
        $t_strike_start = $t_strike_end = '';
    }

    if( $t_bug->category_id ) {
        $t_category_name = category_get_name( $t_bug->category_id );
    } else {
        $t_category_name = '';
    }

    $t_category = is_blank( $t_category_name ) ? '' : '&lt;b&gt;[' . $t_category_name . ']&lt;/b&gt; ';

    echo str_pad( '', $p_issue_level * 6, '&#160;' ), '- ', $t_strike_start, string_get_bug_view_link( $p_issue_id ), ': ', $t_category, string_display_line_links( $t_bug->summary );

    if( $t_bug->handler_id != 0 ) {
        echo ' (', prepare_user_name( $t_bug->handler_id ), ')';
    }

    echo ' - ', get_enum_element( 'status', $t_bug->status ), $t_strike_end, '.&lt;br /&gt;';
}

Some teams manage different branches for each of their projects (e.g.
development and maintenance branches). As part of triaging the issue,
they may decide that an issue should be targeted to multiple branches.
Hence, frequently the request comes up to be able to target a single
issue to multiple releases. The current MantisBT approach is that an
issues represents an implementation or a fix for an issue on a specific
branch. Since sometimes applying and verifying a fix to the two branches
does not happen at the same time and in some cases the approach for
fixing an issue is different based on the branch. Hence, the way to
manage such scenario is to have the main issue for the initial fix and
have related issues which capture the work relating to applying the fix
to other branches. The issues for porting the fix can contain any
discussions relating to progress, reflect the appropriate status and can
go through the standard workflow process independent of the original
issues.

Another common requirement is to be able to link to the roadmap of a
specific project from the project's main website. There is a variety of
ways to do that:

  * 

    To link to the roadmap of version "ver1" of project "myproject":

    http://www.example.com/mantisbt/roadmap_page.php?project=myproject&version=ver1  

  * 

    To link to the roadmap of all non-obsolete versions of project
    'myproject':

    http://www.example.com/mantisbt/roadmap_page.php?project=myproject  

  * 

    To link to the roadmap of project with id 1. The project id can be
    figured out by going to the management page for the project and
    getting the value of project_id field form the URL.

    http://www.example.com/mantisbt/roadmap_page.php?project_id=1  

  * 

    To link to the roadmap of version with id 1. The version id is unique
    across all projects and hence in this case it is not necessary to
    include the project id/name. The version id can be figured out by
    going to the manage project page and editing the required version.
    The version_id will be included in the URL.

    http://www.example.com/mantisbt/roadmap_page.php?version_id=1  

Another approach is to go to the project page and from there users can
get to multiple other locations relating to the project include the
roadmap. This can be done by a URL like the following:

http://www.example.com/mantisbt/project_page.php?project_id=1

The access level required to view and modify the roadmap can be
configured through $g_roadmap_view_threshold and
$g_roadmap_update_threshold respectively. Modifying the roadmap is the
ability to set the target versions for issues. Users who have such access
can set the target versions while reporting new issues or by updating
existing issues.


10.3. Time Tracking
-------------------

To activate the Time Tracking feature you have to set the configuration
option "time_tracking_enabled" to ON. To activating the Time Tracking you
can :

  * 

    Static solution : change the variable '$g_time_tracking_enabled' in
    the configuration file 'config_defaults_inc.php', this will change
    the configuration for all the MantisBT instance ;

  * 

    Dynamic and "project by project" solution : Use the administration
    page "Manage Configuration" and set the variable
    'time_tracking_enabled' to '1' for which user and which project of
    you choice.

All Time Tracking configuration options are described in the
configuration section off this guide.


10.4. Graphs
------------

Assigned to me: TODO

Release Delta: TODO

Category: TODO

Severity: TODO

Severity / Status: TODO

Daily Delta: TODO

Reported by Me: TODO


10.5. Summary Page
------------------

By Status: TODO

By Severity: TODO

By Category: TODO

Time Stats for Resolved Issues (days): TODO

Developer Status: TODO

Reporter by Resolution: TODO

Developer by Resolution: TODO

By Date: TODO

Most Active: TODO

Longest Open: TODO

By Resolution: TODO

By Priority: TODO

Reporter Status: TODO

Reporter Effectiveness: TODO



Chapter 11. Contributing to MantisBT
====================================


11.1. Talent and Time
---------------------

One of the greatest ways to contribute to MantisBT is to contribute your
talent and time. For MantisBT to keep growing we need such support in all
areas related to the software development cycle. This includes: business
analysts, developers, web designers, graphics designers, technical
writers, globalization developers, translators, testers, super users,
packagers and active users. If you would like to contribute in any of
these capacities please contact us through the "Contact Us" page.


11.2. Recommend MantisBT to Others
----------------------------------

It feels great when we get feedback from the user community about how
MantisBT boosted their productivity, and benefited their organization. A
lot of the feedback I get is via email, some on mailing lists, and some
on forums. I would encourage such users to blog about it, tell their
friends about MantisBT, and recommend MantisBT to other organizations.
MantisBT is driven by it's community, the greater the community, the
greater the ideas, the greater of a product it becomes.


11.3. Blog about MantisBT
-------------------------

If you have a blog, then talk about MantisBT, review it's features and
help us spread the word. A lot of users also like to blog about how they
customized MantisBT to fit their needs or to integrate with other tools
that they use in their work environment.


11.4. Integrate with MantisBT
-----------------------------

If you have a product that can be integrated with MantisBT to provide
value for MantisBT users, that would be a great place to contribute and
benefit both your project's and the MantisBT community.

A great example in this area are integrations with content management
systems (e.g. *Nuke, Xoops), project management (PHPProjekt), and
TestLink for Test Management. MantisBT can easily be integrated with
projects in any programming language whether it is hosted on the same
webserver or anywhere else in the world. This can be achieved through its
SOAP API and MantisConnect client libraries. MantisConnect comes with
client libraries and samples in languages like PHP, .NET, Java and Cocoa.



Appendix A. Revision History
============================

Revision History

Revision 2.26-0

Sun Oct 15 2023

Damien Regad

Release 2.26.0

Revision 2.25-0

Mon Mar 8 2021

Damien Regad

Release 2.25.0

Revision 2.24-1

Sun May 3 2020

Victor Boctor

Release 2.24.1

Revision 2.24-0

Sun Mar 15 2020

Victor Boctor

Release 2.24.0

Revision 2.23-0

Sun Dec 9 2019

Victor Boctor

Release 2.23.0

Revision 2.22-1

Thu Sep 26 2019

Victor Boctor

Release 2.22.1

Revision 2.22-0

Sun Aug 25 2019

Victor Boctor

Release 2.22.0

Revision 2.21-2

Mon Aug 19 2019

Victor Boctor

Release 2.21.2

Revision 2.21-1

Thu Jun 13 2019

Victor Boctor

Release 2.21.1

Revision 2.21-0

Sat Apr 20 2019

Victor Boctor

Release 2.21.0

Revision 2.20-0

Sat Mar 16 2019

Victor Boctor

Release 2.20.0

Revision 2.19-0

Wed Jan 2 2019

Victor Boctor

Release 2.19.0

Revision 2.18-0

Tue Oct 16 2018

Victor Boctor

Release 2.18.0

Revision 2.17-1

Mon Sep 24 2018

Victor Boctor

Release 2.17.1

Revision 2.17-0

Mon Sep 3 2018

Victor Boctor

Release 2.17.0

Revision 2.16-0

Sun Jul 29 2018

Victor Boctor

Release 2.16.0

Revision 2.15-0

Tue Jun 5 2018

Victor Boctor

Release 2.15.0

Revision 2.14-0

Sun Apr 29 2018

Victor Boctor

Release 2.14.0

Revision 2.13-1

Wed Apr 4 2018

Victor Boctor

Release 2.13.1

Revision 2.13-0

Sun Apr 1 2018

Victor Boctor

Release 2.13.0

Revision 2.12-0

Sat Mar 3 2018

Victor Boctor

Release 2.12.0

Revision 2.11-0

Tue Feb 6 2018

Victor Boctor

Release 2.11.0

Revision 2.10-0

Sat Dec 30 2017

Victor Boctor

Release 2.10.0

Revision 2.9-0

Sun Dec 3 2017

Victor Boctor

Release 2.9.0

Revision 2.8-0

Sat Oct 28 2017

Victor Boctor

Release 2.8.0

Revision 2.7-0

Sun Oct 8 2017

Victor Boctor

Release 2.7.0

Revision 2.6-0

Sun Sep 3 2017

Victor Boctor

Release 2.6.0

Revision 2.5-1

Sat Jun 17 2017

Victor Boctor

Release 2.5.1

Revision 2.5-0

Sun Jun 4 2017

Victor Boctor

Release 2.5.0

Revision 2.4-1

Sat May 20 2017

Victor Boctor

Release 2.4.1

Revision 2.4-0

Sun Apr 30 2017

Victor Boctor

Release 2.4.0

Revision 2.3-3

Sun Apr 30 2017

Victor Boctor

Release 2.3.2

Revision 2.3-2

Sun Apr 17 2017

Victor Boctor

Release 2.3.1

Revision 2.3-1

Fri Mar 31 2017

Victor Boctor

Release 2.3.0

Revision 2.2-3

Wed Mar 22 2017

Damien Regad

Release 2.2.2

Revision 2.2-2

Sun Mar 12 2017

Victor Boctor

Release 2.2.1

Revision 2.2-1

Sun Feb 26 2017

Victor Boctor

Release 2.2.0

Revision 2.1-2

Sun Feb 26 2017

Victor Boctor

Release 2.1.1

Revision 2.1-1

Tue Jan 31 2017

Victor Boctor

Release 2.1.0

Revision 2.0-2

Fri Dec 30 2016

Victor Boctor

Release 2.0.0

Revision 2.0-1

Sat Nov 26 2016

Damien Regad

Release 2.0.0-rc.2
