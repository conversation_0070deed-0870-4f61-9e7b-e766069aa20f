<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><title xmlns:d="http://docbook.org/ns/docbook">Admin Guide</title><link rel="stylesheet" type="text/css" href="Common_Content/css/default.css" /><link rel="stylesheet" media="print" href="Common_Content/css/print.css" type="text/css" /><meta xmlns:d="http://docbook.org/ns/docbook" name="generator" content="publican v4.3.2" /><meta xmlns:d="http://docbook.org/ns/docbook" name="package" content="MantisBT-Admin_Guide-2.0-en-US-2.26-0" /><meta name="description" content="This book is targeted at MantisBT administrators, and documents the installation, upgrade, configuration, customization and administration tasks required to operate the software." /></head><body class="desktop "><p id="title"><a class="left" href="https://mantisbt.org"><img alt="Product Site" src="Common_Content/images//image_left.png" /></a><a class="right" href="https://mantisbt.org/documentation.php"><img alt="Documentation Site" src="Common_Content/images//image_right.png" /></a></p><div xml:lang="en-US" class="book" lang="en-US"><div class="titlepage"><div><div class="producttitle"><span xmlns:d="http://docbook.org/ns/docbook" class="productname">MantisBT</span> <span xmlns:d="http://docbook.org/ns/docbook" class="productnumber">2.0</span></div><div><h1 class="title"><a id="idm1"></a>Admin Guide</h1></div><div><h2 class="subtitle">Reference for Administrators</h2></div><div><h3 class="corpauthor">
		<span class="inlinemediaobject"><img src="./images/mantis_logo.png" /></span>

	</h3></div><div><div xml:lang="en-US" class="authorgroup" lang="en-US"><div class="author"><h3 class="author"><span class="surname">MantisBT Development Team</span></h3><code class="email"><a class="email" href="mailto:<EMAIL>"><EMAIL></a></code></div></div></div><div><div class="legalnotice"><a id="idm13"></a><h1 class="legalnotice">Legal Notice</h1><div class="para">
		Copyright <span class="trademark"></span>© 2016 MantisBT team.  This material may only be distributed subject to the terms and conditions set forth in the GNU Free Documentation License (GFDL), V1.2 or later (the latest version is presently available at <a href="http://www.gnu.org/licenses/fdl.txt">http://www.gnu.org/licenses/fdl.txt</a>).
	</div></div></div><div><div class="abstract"><p class="title"><strong>Abstract</strong></p><div class="para">
			This book is targeted at MantisBT administrators, and documents the installation, upgrade, configuration, customization and administration tasks required to operate the software.
		</div></div></div></div></div><div class="toc"><dl class="toc"><dt><span class="chapter"><a href="#admin.about">1. About MantisBT</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.about.what">1.1. What is MantisBT?</a></span></dt><dt><span class="section"><a href="#admin.about.who">1.2. Who should read this manual?</a></span></dt><dt><span class="section"><a href="#admin.about.license">1.3. License</a></span></dt><dt><span class="section"><a href="#admin.about.download">1.4. How to get it?</a></span></dt><dt><span class="section"><a href="#admin.about.name">1.5. About the Name</a></span></dt><dt><span class="section"><a href="#admin.about.history">1.6. History</a></span></dt><dt><span class="section"><a href="#admin.about.support">1.7. Support</a></span></dt><dt><span class="section"><a href="#admin.about.news">1.8. MantisBT News</a></span></dt><dt><span class="section"><a href="#admin.about.versioning">1.9. Versioning</a></span></dt></dl></dd><dt><span class="chapter"><a href="#admin.install">2. Installation</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.install.overview">2.1. Overview</a></span></dt><dt><span class="section"><a href="#admin.install.requirements">2.2. System Requirements</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.install.requirements.hardware">2.2.1. Server Hardware Requirements</a></span></dt><dt><span class="section"><a href="#admin.install.requirements.software">2.2.2. Server Software Requirements</a></span></dt><dt><span class="section"><a href="#admin.install.requirements.client">2.2.3. Client Requirements</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.install.preinstall">2.3. Pre-installation / upgrade tasks</a></span></dt><dt><span class="section"><a href="#admin.install.new">2.4. New Installation</a></span></dt><dt><span class="section"><a href="#admin.install.upgrade">2.5. Upgrading</a></span></dt><dt><span class="section"><a href="#admin.install.config">2.6. Configure your installation</a></span></dt><dt><span class="section"><a href="#admin.install.postcommon">2.7. Post-installation and upgrade tasks</a></span></dt><dt><span class="section"><a href="#admin.install.postinstall">2.8. Post-installation tasks</a></span></dt><dt><span class="section"><a href="#admin.install.postupgrade">2.9. Post-upgrade tasks</a></span></dt><dt><span class="section"><a href="#admin.install.backups">2.10. Backups</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.install.backups.mysql">2.10.1. MySQL Backups</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.install.uninstall">2.11. Uninstall</a></span></dt></dl></dd><dt><span class="chapter"><a href="#admin.user">3. User Management</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.user.create">3.1. Creating User Accounts</a></span></dt><dt><span class="section"><a href="#admin.user.enable">3.2. Enabling/Disabling User Accounts</a></span></dt><dt><span class="section"><a href="#admin.user.delete">3.3. Deleting User Accounts</a></span></dt><dt><span class="section"><a href="#admin.user.signup">3.4. User Signup</a></span></dt><dt><span class="section"><a href="#admin.user.passwordreset">3.5. Forgot Password and Reset Password</a></span></dt><dt><span class="section"><a href="#admin.user.impersonation">3.6. Impersonating a user</a></span></dt><dt><span class="section"><a href="#admin.user.passwordchange">3.7. Changing Password</a></span></dt><dt><span class="section"><a href="#admin.user.pruning">3.8. Pruning User Accounts</a></span></dt><dt><span class="section"><a href="#admin.user.access">3.9. Authorization and Access Levels</a></span></dt><dt><span class="section"><a href="#admin.user.autocreate">3.10. Auto Creation of Accounts on Login</a></span></dt><dt><span class="section"><a href="#admin.user.prefs">3.11. User Preferences</a></span></dt><dt><span class="section"><a href="#admin.user.profiles">3.12. User Profiles</a></span></dt></dl></dd><dt><span class="chapter"><a href="#admin.lifecycle">4. Issue Lifecycle and Workflow</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.lifecycle.create">4.1. Issue Creation</a></span></dt><dt><span class="section"><a href="#admin.lifecycle.status">4.2. Issue Statuses</a></span></dt><dt><span class="section"><a href="#admin.lifecycle.workflow">4.3. Workflow</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.lifecycle.workflow.transitions">4.3.1. Workflow Transitions</a></span></dt><dt><span class="section"><a href="#admin.lifecycle.workflow.thresholds">4.3.2. Workflow Thresholds</a></span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="#admin.config">5. Configuration</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.config.intro">5.1. Introduction</a></span></dt><dt><span class="section"><a href="#admin.config.database">5.2. Database</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.config.database.mandatory">5.2.1. Base Database settings</a></span></dt><dt><span class="section"><a href="#admin.config.database.tablenaming">5.2.2. Database table naming settings</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.config.path">5.3. Path</a></span></dt><dt><span class="section"><a href="#admin.config.webserver">5.4. Webserver</a></span></dt><dt><span class="section"><a href="#admin.config.settings">5.5. Configuration Settings</a></span></dt><dt><span class="section"><a href="#admin.config.security">5.6. Security and Cryptography</a></span></dt><dt><span class="section"><a href="#admin.config.signup">5.7. Signup and Lost Password</a></span></dt><dt><span class="section"><a href="#admin.config.email">5.8. Email</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.config.email.dkim">5.8.1. DKIM signature</a></span></dt><dt><span class="section"><a href="#admin.config.email.smime">5.8.2. S/MIME signature</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.config.version">5.9. Version</a></span></dt><dt><span class="section"><a href="#admin.config.language">5.10. Language</a></span></dt><dt><span class="section"><a href="#admin.config.display">5.11. Display</a></span></dt><dt><span class="section"><a href="#admin.config.time">5.12. Time</a></span></dt><dt><span class="section"><a href="#admin.config.date">5.13. Date</a></span></dt><dt><span class="section"><a href="#admin.config.timezone">5.14. Time Zone</a></span></dt><dt><span class="section"><a href="#admin.config.news">5.15. News</a></span></dt><dt><span class="section"><a href="#admin.config.defaults">5.16. Default Preferences</a></span></dt><dt><span class="section"><a href="#admin.config.summary">5.17. Summary</a></span></dt><dt><span class="section"><a href="#admin.config.bugnote">5.18. Bugnote</a></span></dt><dt><span class="section"><a href="#admin.config.uploads">5.19. File Upload</a></span></dt><dt><span class="section"><a href="#admin.config.html">5.20. HTML</a></span></dt><dt><span class="section"><a href="#admin.config.auth">5.21. Authentication</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.config.auth.global">5.21.1. Global authentication parameters</a></span></dt><dt><span class="section"><a href="#admin.config.auth.ldap">5.21.2. LDAP authentication method parameters</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.config.status">5.22. Status Settings</a></span></dt><dt><span class="section"><a href="#admin.config.filters">5.23. Filters</a></span></dt><dt><span class="section"><a href="#admin.config.misc">5.24. Misc</a></span></dt><dt><span class="section"><a href="#admin.config.cookies">5.25. Cookies</a></span></dt><dt><span class="section"><a href="#admin.config.speed">5.26. Speed Optimisation</a></span></dt><dt><span class="section"><a href="#admin.config.reminders">5.27. Reminders</a></span></dt><dt><span class="section"><a href="#admin.config.bughistory">5.28. Bug History</a></span></dt><dt><span class="section"><a href="#admin.config.sponsorship">5.29. Sponsorship</a></span></dt><dt><span class="section"><a href="#admin.config.customfields">5.30. Custom Fields</a></span></dt><dt><span class="section"><a href="#admin.config.myview">5.31. My View Settings</a></span></dt><dt><span class="section"><a href="#admin.config.relationship">5.32. Relationship Graphs</a></span></dt><dt><span class="section"><a href="#admin.config.wiki">5.33. Wiki Integration</a></span></dt><dt><span class="section"><a href="#admin.config.subprojects">5.34. Sub-Projects</a></span></dt><dt><span class="section"><a href="#admin.config.fields">5.35. Field Visibility</a></span></dt><dt><span class="section"><a href="#admin.config.logging">5.36. System Logging and Debugging</a></span></dt><dt><span class="section"><a href="#admin.config.timetracking">5.37. Time Tracking</a></span></dt><dt><span class="section"><a href="#admin.config.api">5.38. API</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.config.api.disable">5.38.1. Disabling the webservice API</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.config.antispam">5.39. Anti-Spam Configuration</a></span></dt><dt><span class="section"><a href="#admin.config.duedate">5.40. Due Date</a></span></dt><dt><span class="section"><a href="#admin.config.users">5.41. User Management</a></span></dt><dt><span class="section"><a href="#admin.config.view">5.42. View Page Settings</a></span></dt><dt><span class="section"><a href="#admin.config.issues">5.43. Issues visibility</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.config.issues.private">5.43.1. Public/Private view status</a></span></dt><dt><span class="section"><a href="#admin.config.issues.limitedview">5.43.2. Limited view configuration</a></span></dt><dt><span class="section"><a href="#admin.config.issues.limitreporters">5.43.3. "Limit reporters" configuration (deprecated)</a></span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="#admin.pages">6. Page descriptions</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.pages.login">6.1. Login page</a></span></dt><dt><span class="section"><a href="#admin.pages.main">6.2. Main page</a></span></dt><dt><span class="section"><a href="#admin.pages.filter">6.3. View Issues page</a></span></dt><dt><span class="section"><a href="#admin.pages.issueview">6.4. Issue View page</a></span></dt><dt><span class="section"><a href="#admin.pages.issuestatus">6.5. Issue Change Status page</a></span></dt><dt><span class="section"><a href="#admin.pages.issueedit">6.6. Issue Edit page</a></span></dt><dt><span class="section"><a href="#admin.pages.account">6.7. My Account Page</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.pages.account.prefs">6.7.1. Preferences</a></span></dt><dt><span class="section"><a href="#admin.pages.account.profiles">6.7.2. Profiles</a></span></dt><dt><span class="section"><a href="#admin.pages.account.managecolumns">6.7.3. Manage Columns</a></span></dt><dt><span class="section"><a href="#admin.pages.account.apitokens">6.7.4. API Tokens</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.pages.manage">6.8. System Management Pages</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.pages.manage.users">6.8.1. Users</a></span></dt><dt><span class="section"><a href="#admin.pages.manage.projects">6.8.2. Manage Projects Page</a></span></dt><dt><span class="section"><a href="#admin.pages.manage.customfields">6.8.3. Manage Custom Fields</a></span></dt><dt><span class="section"><a href="#admin.pages.manage.profiles">6.8.4. Global Profiles</a></span></dt><dt><span class="section"><a href="#admin.pages.manage.config">6.8.5. Configuration</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.pages.monitor">6.9. Monitor Issue</a></span></dt><dt><span class="section"><a href="#admin.pages.reopen">6.10. Reopen Issue</a></span></dt><dt><span class="section"><a href="#admin.pages.delete">6.11. Delete Issue</a></span></dt><dt><span class="section"><a href="#admin.pages.close">6.12. Close Issue</a></span></dt><dt><span class="section"><a href="#admin.pages.assigntome">6.13. Assign to Me</a></span></dt><dt><span class="section"><a href="#admin.pages.resolve">6.14. Resolve Issue</a></span></dt><dt><span class="section"><a href="#admin.pages.news">6.15. News Syndication</a></span></dt></dl></dd><dt><span class="chapter"><a href="#admin.customize">7. Customizing MantisBT</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.customize.strings">7.1. Strings / Translations</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.customize.strings.format">7.1.1. Custom Strings File Format</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.customize.customfields">7.2. Custom Fields</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.customize.customfields.overview">7.2.1. Overview</a></span></dt><dt><span class="section"><a href="#admin.customize.customfields.definitions">7.2.2. Custom Field Definition</a></span></dt><dt><span class="section"><a href="#admin.customize.customfields.editing">7.2.3. Adding/Editing Custom Fields</a></span></dt><dt><span class="section"><a href="#admin.customize.customfields.linking">7.2.4. Linking/Unlinking/Ordering Existing Custom Fields in Projects</a></span></dt><dt><span class="section"><a href="#admin.customize.customfields.localize">7.2.5. Localizing Custom Field Names</a></span></dt><dt><span class="section"><a href="#admin.customize.customfields.defaults">7.2.6. Dynamic default values</a></span></dt><dt><span class="section"><a href="#admin.customize.customfields.dynamic">7.2.7. Dynamic values for Enumeration Custom Fields</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.customize.enums">7.3. Enumerations</a></span></dt><dt><span class="section"><a href="#admin.customize.email">7.4. Email Notifications</a></span></dt><dt><span class="section"><a href="#admin.customize.status">7.5. Customizing Status Values</a></span></dt><dt><span class="section"><a href="#admin.customize.customfuncs">7.6. Custom Functions</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.customize.customfuncs.defined">7.6.1. Default Custom Functions</a></span></dt><dt><span class="section"><a href="#admin.customize.customfuncs.example">7.6.2. Example Custom Function Override</a></span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="#admin.auth">8. Authentication</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.auth.standard">8.1. Standard Authentication</a></span></dt><dt><span class="section"><a href="#admin.auth.ldap">8.2. LDAP and Microsoft Active Directory</a></span></dt><dt><span class="section"><a href="#admin.auth.basic">8.3. Basic Authentication</a></span></dt><dt><span class="section"><a href="#admin.auth.http">8.4. HTTP Authentication</a></span></dt><dt><span class="section"><a href="#admin.auth.deprecated">8.5. Deprecated authentication methods</a></span></dt></dl></dd><dt><span class="chapter"><a href="#admin.troubleshooting">9. Troubleshooting</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.troubleshooting.errors">9.1. Application Errors</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.troubleshooting.errors.2800">9.1.1. Error 2800 - Invalid form security token</a></span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="#admin.project">10. Project Management</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.project.changelog">10.1. Change Log</a></span></dt><dt><span class="section"><a href="#admin.project.roadmap">10.2. Roadmap</a></span></dt><dt><span class="section"><a href="#admin.project.timetracking">10.3. Time Tracking</a></span></dt><dt><span class="section"><a href="#admin.project.graphs">10.4. Graphs</a></span></dt><dt><span class="section"><a href="#admin.project.summary">10.5. Summary Page</a></span></dt></dl></dd><dt><span class="chapter"><a href="#admin.contributing">11. Contributing to MantisBT</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.contributing.develop">11.1. Talent and Time</a></span></dt><dt><span class="section"><a href="#admin.contributing.share">11.2. Recommend MantisBT to Others</a></span></dt><dt><span class="section"><a href="#admin.contributing.blog">11.3. Blog about MantisBT</a></span></dt><dt><span class="section"><a href="#admin.contributing.integrate">11.4. Integrate with MantisBT</a></span></dt></dl></dd><dt><span class="appendix"><a href="#appe-Admin_Guide-Revision_History">A. Revision History</a></span></dt></dl></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.about">
      ⁠</a>Chapter 1. About MantisBT</h1></div></div></div><div class="toc"><dl class="toc"><dt><span class="section"><a href="#admin.about.what">1.1. What is MantisBT?</a></span></dt><dt><span class="section"><a href="#admin.about.who">1.2. Who should read this manual?</a></span></dt><dt><span class="section"><a href="#admin.about.license">1.3. License</a></span></dt><dt><span class="section"><a href="#admin.about.download">1.4. How to get it?</a></span></dt><dt><span class="section"><a href="#admin.about.name">1.5. About the Name</a></span></dt><dt><span class="section"><a href="#admin.about.history">1.6. History</a></span></dt><dt><span class="section"><a href="#admin.about.support">1.7. Support</a></span></dt><dt><span class="section"><a href="#admin.about.news">1.8. MantisBT News</a></span></dt><dt><span class="section"><a href="#admin.about.versioning">1.9. Versioning</a></span></dt></dl></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.what">
      ⁠</a>1.1. What is MantisBT?</h2></div></div></div><div class="para">
			MantisBT is a web based bug tracking system that was first made available to the public in November 2000. Over time it has matured and gained a lot of popularity, and now it has become one of the most popular open source bug/issue tracking systems. MantisBT is developed in PHP, with support to multiple database backends including MySQL, MS SQL and PostgreSQL.
		</div><div class="para">
			MantisBT, as a PHP script, can run on any operating system that is supported by PHP and has support for one of the DBMSes that are supported. MantisBT is known to run fine on Windows, Linux, macOS and a variety of Unix operating systems.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.who">
      ⁠</a>1.2. Who should read this manual?</h2></div></div></div><div class="para">
			This manual is targeted for the person responsible for evaluating, installing and maintaining MantisBT in a company. Typically we refer to this person as the MantisBT administrator.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.license">
      ⁠</a>1.3. License</h2></div></div></div><div class="para">
			MantisBT is released under the terms of <a href="https://www.gnu.org/copyleft/gpl.html">GNU General Public License (GPL)</a>. MantisBT is free to use and modify. It is free to redistribute as long as you abide by the distribution terms of the <a href="https://www.gnu.org/copyleft/gpl.html">GPL</a>.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.download">
      ⁠</a>1.4. How to get it?</h2></div></div></div><div class="para">
			MantisBT is available in several Linux distributions including: Debian, Ubuntu, Fedora, Gentoo, Frugalware and others. Hence, if you are running Linux, start by checking if your distribution has a package for MantisBT. If not, or if the package is not up-to-date with the latest MantisBT version, then you may want to download it directly from <a href="https://mantisbt.org/download.php">here</a>.
		</div><div class="para">
			For Windows, macOS and other operating systems, use the link provided above to download MantisBT. The download is compressed in tar.gz or zip format. Both formats can be unpacked using tools like <a href="https://www.7-zip.org/">7-Zip</a> (in case of Windows).
		</div><div class="para">
			Note that at any point in time there are typically two "latest" MantisBT releases that are available for download. The latest production release (stable), and the latest development release which can be an alpha or a release candidate. It is not recommended to use development releases in production specially if it is still in the alpha stage unless the administrator is familiar with PHP and is able to troubleshoot and fix any issues that may arise.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.name">
      ⁠</a>1.5. About the Name</h2></div></div></div><div class="para">
			When initially seeking to name this project Ken ran into a problem every programmer encounters. What is a good name? It has to be descriptive, unique, and not too verbose. Additionally having multiple meanings would be a nice touch. Quickly ruled out were php*Something* names which, incidentally, although popular, do not seem to be condoned by the PHP Group developers. Drawing inspiration from Open Source projects like Apache, Mozilla, Gnome, and so forth resulted in two eventual choices: Dragonfly and Mantis. Dragonfly was already the name of a webmail package. So the name became Mantis.
		</div><div class="para">
			Praying Mantis are insects that feed primarily on other insects and bugs. They are extremely desirable in agriculture as they devour insects that feed on crops. They are also extremely elegant looking creatures. So, we have a name that is fairly distinctive and descriptive in multiple ways. The BT suffix stands for "Bug Tracker" and distinguishes this project from general usage of the word Mantis. However, over time the project was typically referred to as Mantis.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.history">
      ⁠</a>1.6. History</h2></div></div></div><div class="para">
			Kenzaburo Ito and a friend originally created a bug tracker as an internal tool for their pet project. A search for good, free packages came up with nothing suitable so they wrote their own. After a rewrite and cleanup it was made available to the public via the GNU General Public License (GPL). The GPL was chosen partly because of his belief that development tools should be cheap or free. In 2002, Ken was joined by Jeroen Latour, Victor Boctor and Julian Fitzell to be the administrators and the core development team of MantisBT. This marks a new era in MantisBT lifetime where it is now a team project.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.support">
      ⁠</a>1.7. Support</h2></div></div></div><div class="para">
			There are plenty of resources to help answer support queries. Following are the main ones:
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
					<a href="https://mantisbt.org/forums/">Forums</a> - The forums are one of the most popular destinations for getting MantisBT support. Start off by searching the forums for your questions, if not found, then go ahead and submit a question.
				</div></li><li class="listitem"><div class="para">
					<a href="http://www.mantisbt.org/mailinglists.php">Mailing lists</a> - Several lists are available, each of them with its own, specific purpose. Note that posting messages is restricted to subscribers so you will have to register before you can send messages; however, there are public archives available if you're only interested in reading.
				</div></li><li class="listitem"><div class="para">
					<a href="https://gitter.im/mantisbt/mantisbt">Gitter</a> is a browser-based, on-line chat that has mainly replaced the team's use of IRC. In the main chat room, you can have a live discussion with the developers and other MantisBT users. Gitter supports all modern browsers and also offers Android and iOS-based clients, as well as an <a href="https://irc.gitter.im/">IRC bridge</a>.
				</div></li><li class="listitem"><div class="para">
					<a href="http://www.mantisbt.org/irc.php">IRC</a> - The IRC channel not very active anymore, as the developers have moved on to using Gitter for live discussions; nevertheless, the channel is still open. There are many free IRC clients: XChat (for Linux), <a href="http://hexchat.github.io/">HexChat</a>, <a href="http://www.icechat.net/">IceChat</a> amongst others. You can also use <a href="http://webchat.freenode.net/">Web Chat</a> to connect to IRC via your web browser, which may also be useful when you're behind a firewall that blocks the IRC port. The IRC channel logs are archived and made <a href="http://www.mantisbt.org/irclogs.php"> available on the MantisBT web site</a>.
				</div></li><li class="listitem"><div class="para">
					<a href="https://mantisbt.org/wiki/doku.php/mantisbt:start">Wiki</a> - The MantisBT Wiki has information related to "How To (recipes)", FAQ, feature requirements, plugins etc.
				</div></li><li class="listitem"><div class="para">
					Search - A good way for locating an answer to your question or finding more information about a topic is to search across all MantisBT website and the Internet via your favorite search engine, e.g. <a href="https://www.google.com">Google</a> or <a href="https://www.bing.com">Bing</a>.
				</div></li></ul></div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
				Support questions should not be sent directly to MantisBT developers or through the MantisBT website's contact pages.
			</div><div class="para">
				Also, our <a href="https://mantisbt.org/bugs/">bug tracker</a> is reserved for reporting issues with the software, and <span class="emphasis"><em>must not be used for support requests</em></span>.
			</div></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.news">
      ⁠</a>1.8. MantisBT News</h2></div></div></div><div class="para">
			There are several ways to keep up to date with MantisBT news. These include:
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
					We send release announcements and important updates to users registered on our <a href="https://mantisbt.org/bugs">official bugtracker</a>. To get onto our mailing list, users will have to signup there and verify their email address. This same account can also be used to report, monitor, and comment on issues relating to MantisBT.
				</div></li><li class="listitem"><div class="para">
					<a href="https://mantisbt.org/blog/">MantisBT Blog</a> is used to communicate announcements about new releases, topics relating to MantisBT, etc. Users are encouraged to subscribe to the RSS feed to know when new posts are posted there.
				</div></li><li class="listitem"><div class="para">
					<a href="https://twitter.com/mantisbt">Twitter</a> is used to notify users about up-to-date details about what is happening with MantisBT development. Twitter users are encouraged to follow "@mantisbt".
				</div></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.versioning">
      ⁠</a>1.9. Versioning</h2></div></div></div><div class="para">
			Our release numbering convention follows the guidelines of <a href="https://semver.org/">Semantic Versioning</a>. Given a version number <span class="emphasis"><em>Major.Minor.Patch</em></span> and an optional <span class="emphasis"><em>Suffix</em></span> (eg. 1.3.0-rc.1):
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
					Major - Indicates a very large change in the core package. Rewrites or major milestones. API changes which are not backwards-compatible.
				</div></li><li class="listitem"><div class="para">
					Minor - Introduction of new features or significant changes in functionality, in a backwards-compatible manner.
				</div></li><li class="listitem"><div class="para">
					Patch - Bug fixes, maintenance and security releases.
				</div></li><li class="listitem"><div class="para">
					Suffix - Optional, indicates a development release. 
					<div class="itemizedlist"><ul><li class="listitem"><div class="para">
								a<span class="emphasis"><em>N</em></span> or alpha.<span class="emphasis"><em>N</em></span> for alpha releases,
							</div></li><li class="listitem"><div class="para">
								b<span class="emphasis"><em>N</em></span> or beta.<span class="emphasis"><em>N</em></span> for beta releases, or
							</div></li><li class="listitem"><div class="para">
								rc<span class="emphasis"><em>N</em></span> or rc.<span class="emphasis"><em>N</em></span> for release candidates.
							</div></li></ul></div>
					 Absence of suffix indicates a stable release.
				</div></li></ul></div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.install">
      ⁠</a>Chapter 2. Installation</h1></div></div></div><div class="toc"><dl class="toc"><dt><span class="section"><a href="#admin.install.overview">2.1. Overview</a></span></dt><dt><span class="section"><a href="#admin.install.requirements">2.2. System Requirements</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.install.requirements.hardware">2.2.1. Server Hardware Requirements</a></span></dt><dt><span class="section"><a href="#admin.install.requirements.software">2.2.2. Server Software Requirements</a></span></dt><dt><span class="section"><a href="#admin.install.requirements.client">2.2.3. Client Requirements</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.install.preinstall">2.3. Pre-installation / upgrade tasks</a></span></dt><dt><span class="section"><a href="#admin.install.new">2.4. New Installation</a></span></dt><dt><span class="section"><a href="#admin.install.upgrade">2.5. Upgrading</a></span></dt><dt><span class="section"><a href="#admin.install.config">2.6. Configure your installation</a></span></dt><dt><span class="section"><a href="#admin.install.postcommon">2.7. Post-installation and upgrade tasks</a></span></dt><dt><span class="section"><a href="#admin.install.postinstall">2.8. Post-installation tasks</a></span></dt><dt><span class="section"><a href="#admin.install.postupgrade">2.9. Post-upgrade tasks</a></span></dt><dt><span class="section"><a href="#admin.install.backups">2.10. Backups</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.install.backups.mysql">2.10.1. MySQL Backups</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.install.uninstall">2.11. Uninstall</a></span></dt></dl></div><div class="para">
		This chapter explains how to install or upgrade MantisBT.
	</div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.overview">
      ⁠</a>2.1. Overview</h2></div></div></div><div class="para">
			The table below contains a high-level overview of the processes. Refer to the corresponding sections for details.
		</div><div class="informaltable"><table xmlns:d="http://docbook.org/ns/docbook" class="lt-4-cols lt-7-rows"><colgroup><col /><col /></colgroup><thead><tr><th>New Installation</th><th>Upgrade</th></tr></thead><tbody><tr><td> <div class="orderedlist"><ol><li class="listitem"><div class="para">
									<a class="xref" href="#admin.install.requirements">Section 2.2, “System Requirements”</a>
								</div></li><li class="listitem"><div class="para">
									<a class="xref" href="#admin.install.preinstall">Section 2.3, “Pre-installation / upgrade tasks”</a>
								</div></li><li class="listitem"><div class="para">
									<a class="xref" href="#admin.install.new">Section 2.4, “New Installation”</a>
								</div></li><li class="listitem"><div class="para">
									<a class="xref" href="#admin.install.config">Section 2.6, “Configure your installation”</a>
								</div></li><li class="listitem"><div class="para">
									<a class="xref" href="#admin.install.postcommon">Section 2.7, “Post-installation and upgrade tasks”</a>
								</div></li><li class="listitem"><div class="para">
									<a class="xref" href="#admin.install.postinstall">Section 2.8, “Post-installation tasks”</a>
								</div></li></ol></div>
						 </td><td> <div class="orderedlist"><ol><li class="listitem"><div class="para">
									<a class="xref" href="#admin.install.preinstall">Section 2.3, “Pre-installation / upgrade tasks”</a>
								</div></li><li class="listitem"><div class="para">
									<a class="xref" href="#admin.install.backups">Section 2.10, “Backups”</a>
								</div></li><li class="listitem"><div class="para">
									Put the site down for maintenance
								</div></li><li class="listitem"><div class="para">
									<a class="xref" href="#admin.install.upgrade">Section 2.5, “Upgrading”</a>
								</div></li><li class="listitem"><div class="para">
									<a class="xref" href="#admin.install.postcommon">Section 2.7, “Post-installation and upgrade tasks”</a>
								</div></li><li class="listitem"><div class="para">
									<a class="xref" href="#admin.install.postupgrade">Section 2.9, “Post-upgrade tasks”</a>
								</div></li></ol></div>
						 </td></tr></tbody></table></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.requirements">
      ⁠</a>2.2. System Requirements</h2></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.install.requirements.hardware">
      ⁠</a>2.2.1. Server Hardware Requirements</h3></div></div></div><div class="para">
				MantisBT has modest hardware requirements. It requires a computer that is able to run the server software (see <a class="xref" href="#admin.install.requirements.software">Section 2.2.2, “Server Software Requirements”</a>).
			</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						Server type
					</div><div class="para">
						The server can be a shared public web server or a dedicated co-located box.
					</div></li><li class="listitem"><div class="para">
						CPU and Memory
					</div><div class="para">
						As for any web application, you should size your server based on the traffic on the site.
					</div></li><li class="listitem"><div class="para">
						Disk
					</div><div class="para">
						The application code is less than 50 MiB.
					</div><div class="para">
						The amount of disk space required for the database will vary depending on the RDBMS and the volume of data, the main driving factor being the expected number and size of attachments.
					</div></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.install.requirements.software">
      ⁠</a>2.2.2. Server Software Requirements</h3></div></div></div><div class="para">
				All of the required software is free for commercial and non-commercial use (open source). Please refer to the table in <a class="xref" href="#admin.install.requirements.software.versions">Section *******, “Versions compatibility table”</a> for minimum and recommended versions.
			</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						Operating System
					</div><div class="para">
						MantisBT runs on Windows, macOS, Linux, Solaris, the BSDs, and just about anything that supports the required server software.
					</div></li><li class="listitem"><div class="para">
						Web Server
					</div><div class="para">
						MantisBT is mainly tested with <a href="https://docs.microsoft.com/en-us/iis">Microsoft IIS</a> and <a href="https://www.apache.org/">Apache</a>. However, it is expected to work with any recent web server software.
					</div><div class="para">
						File Extensions: MantisBT uses only <span class="emphasis"><em>.php</em></span> files. If your webserver is configured for other extensions (e.g. .PHP3, .PHTML) then you will have to request the administrator to add support for .PHP files. This should be a trivial modification. Further details can be found in the <a href="https://www.php.net/manual/en/install.php">PHP documentation</a>
					</div></li><li class="listitem"><div class="para">
						<a href="https://www.php.net/">PHP</a>
					</div><div class="para">
						The web server must support PHP. It can be installed as CGI or any other integration technology.
					</div></li><li class="listitem"><div class="para">
						PHP extensions
					</div><div class="para">
						MantisBT is designed to work in as many environments as possible. Hence the required extensions are minimal and many of them are optional affecting only one feature.
					</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">Mandatory extensions</span></dt><dd><div class="itemizedlist"><ul><li class="listitem"><div class="para">
											The extension for the RDBMS being used ( mysqli with mysqlnd, pgsql, oci8, sqlsrv )
										</div></li><li class="listitem"><div class="para">
											<span class="emphasis"><em>mbstring</em></span> - Required for Unicode (UTF-8) support.
										</div></li><li class="listitem"><div class="para">
											<span class="emphasis"><em> ctype, filter, hash, json, session </em></span> - Required to run MantisBT in general. These are bundled with PHP, and enabled by default. Note that <span class="emphasis"><em>hash</em></span> is a core extension since PHP 7.4.0, and <span class="emphasis"><em>json</em></span> is a core extension since PHP 8.0.0.
										</div></li></ul></div></dd><dt><span class="term">Optional extensions</span></dt><dd><div class="itemizedlist"><ul><li class="listitem"><div class="para">
											<span class="emphasis"><em>Curl</em></span> - required for the Twitter integration feature
										</div></li><li class="listitem"><div class="para">
											<span class="emphasis"><em>GD</em></span> - required for the captcha feature
										</div></li><li class="listitem"><div class="para">
											<span class="emphasis"><em>Fileinfo</em></span> - required for file attachments and most of the plugins
										</div><div class="para">
											Without this extension, file attachment previews and downloads do not work as MantisBT won't be able to send the Content-Type header to a browser requesting an attachment.
										</div></li><li class="listitem"><div class="para">
											<span class="emphasis"><em>LDAP</em></span> - required for LDAP or Active Directory authentication (see <a class="xref" href="#admin.auth.ldap">Section 8.2, “LDAP and Microsoft Active Directory”</a>).
										</div></li><li class="listitem"><div class="para">
											<span class="emphasis"><em>SOAP</em></span> - required to use the SOAP API (see <a class="xref" href="#admin.config.api">Section 5.38, “API”</a>).
										</div></li><li class="listitem"><div class="para">
											<span class="emphasis"><em>zlib</em></span> - required to enable output compression (see <a class="xref" href="#admin.config.speed">Section 5.26, “Speed Optimisation”</a>).
										</div></li></ul></div></dd></dl></div><div class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
							You can check which PHP modules are installed by running <code class="literal">php -m</code> on the command line, or by using the <code class="literal">php_info()</code> function in a PHP script.
						</div></div></div></li><li class="listitem"><div class="para">
						Database
					</div><div class="para">
						MantisBT requires a database to store its data. The supported RDBMS are:
					</div><div class="itemizedlist"><ul><li class="listitem"><div class="para">
								MySQL (or one of its forks, e.g. MariaDB)
							</div></li><li class="listitem"><div class="para">
								PostgreSQL
							</div></li></ul></div><div class="para">
						Experimental support is also available for
					</div><div class="itemizedlist"><ul><li class="listitem"><div class="para">
								Microsoft SQL Server
							</div></li><li class="listitem"><div class="para">
								Oracle
							</div></li></ul></div><div class="para">
						Experimental support means that manual intervention by a skilled Database Administrator may be required to complete the installation, and/or that there may be known issues or limitations when using the software. Please refer to our <a href="https://mantisbt.org/bugs/">Issue tracker</a>, filtering on categories <span class="emphasis"><em>db mssql</em></span> and <span class="emphasis"><em>db oracle</em></span> to find out more about those.
					</div><div class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
							Please note that the MantisBT development team mainly works with MySQL, so testing for other drivers is not as extensive as we mainly rely on community contributions to improve support and fix issues with other RDBMS.
						</div><div class="para">
							We therefore recommend MySQL to store your database.
						</div></div></div></li></ul></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.install.requirements.software.versions">
      ⁠</a>*******. Versions compatibility table</h4></div></div></div><div class="informaltable"><table xmlns:d="http://docbook.org/ns/docbook" class="gt-4-cols gt-7-rows"><colgroup><col /><col /><col /><col /><col /></colgroup><thead><tr><th>Category</th><th>Package</th><th>Minimum Version</th><th>Recommended</th><th>Comments</th></tr></thead><tbody><tr><td rowspan="5" valign="middle">RDBMS</td><td>MySQL</td><td>5.5.35</td><td>5.6 or later</td><td>PHP extension: mysqli with MySQL Native driver (mysqlnd) </td></tr><tr><td>MariaDB</td><td>5.5.35</td><td>10.4 or later</td><td>PHP extension: mysqli</td></tr><tr><td>PostgreSQL</td><td>9.2</td><td>11.20 or later</td><td>PHP extension: pgsql</td></tr><tr><td>MS SQL Server</td><td>2012</td><td>2019 or later</td><td>PHP extension: sqlsrv</td></tr><tr><td>Oracle</td><td>11gR2</td><td>19c or later</td><td>PHP extension: oci8</td></tr><tr><td>PHP</td><td>PHP</td><td>7.2.5</td><td>8.0 or later</td><td>See above for PHP extensions</td></tr><tr><td rowspan="4" valign="middle">Web Server</td><td>Apache</td><td>2.2.x</td><td>2.4.x</td><td> </td></tr><tr><td>lighttpd</td><td>1.4.x</td><td>1.4.x</td><td> </td></tr><tr><td>nginx</td><td>1.10.x</td><td>1.16.x or later</td><td> </td></tr><tr><td>IIS</td><td>7.5</td><td>10</td><td>Windows Server 2016 or later</td></tr></tbody></table></div><div class="para">
					Our minimum requirements are generally based on availability of support for the underlying software by their respective vendors. In some cases, we do require a specific version because we rely on a feature that is not available in older releases.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						Running MantisBT with versions of the software components lower than the minimum requirements listed above is not supported.
					</div></div></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.install.requirements.client">
      ⁠</a>2.2.3. Client Requirements</h3></div></div></div><div class="para">
				MantisBT should run on all recent browsers in the market, including but not limited to:
			</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						Firefox
					</div></li><li class="listitem"><div class="para">
						Edge
					</div></li><li class="listitem"><div class="para">
						Chrome
					</div></li><li class="listitem"><div class="para">
						Safari
					</div></li><li class="listitem"><div class="para">
						Opera
					</div></li></ul></div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
					Support for <span class="emphasis"><em>Internet Explorer 11</em></span> ended with release 2.22.0.
				</div></div></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.preinstall">
      ⁠</a>2.3. Pre-installation / upgrade tasks</h2></div></div></div><div class="para">
			These tasks cover the download and deployment of MantisBT, and should be performed prior to any new installation or upgrade.
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="orderedlist"><ol><li class="listitem"><div class="para">
					Download MantisBT (see <a class="xref" href="#admin.about.download">Section 1.4, “How to get it?”</a>)
				</div></li><li class="listitem"><div class="para">
					Transfer the downloaded file to your webserver
				</div><div class="para">
					This can be done using whatever method you like best (ftp, scp, etc). You will need to telnet/ssh into the server machine for the next steps.
				</div></li><li class="listitem"><div class="para">
					Extract the release
				</div><div class="para">
					It is highly recommended to maintain a separate directory for each release. This not only avoids mismatch between versions, (files may have been added or removed) but also provides an easy path to downgrade your installation, should you need to.
				</div><div class="para">
					The usual command is (1 step): 
<pre class="programlisting">tar -xzf <span class="emphasis"><em>filename.tar.gz</em></span></pre>
					 OR (2 steps): 
<pre class="programlisting">
gunzip <span class="emphasis"><em>filename.tar.gz</em></span>
tar -xf <span class="emphasis"><em>filename.tar</em></span></pre>
					 Other file archiving tools such as <a href="https://www.7-zip.org/">7-Zip</a> should also be able to handle decompression of the archive.
				</div><div class="para">
					The extraction process should create a new directory like <span class="emphasis"><em>mantisbt-1.3.x</em></span>
				</div></li><li class="listitem"><div class="para">
					Rename the directory
				</div><div class="para">
					For new installations, you may want to rename the directory just created to something simpler, e.g. <span class="emphasis"><em>mantisbt</em></span> 
<pre class="programlisting">mv mantisbt-1.3.x mantisbt</pre>

				</div></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.new">
      ⁠</a>2.4. New Installation</h2></div></div></div><div class="para">
			This chapter explains how to perform a new installation of MantisBT.
		</div><div class="para">
			Start by checking <a class="xref" href="#admin.install.requirements">Section 2.2, “System Requirements”</a> and installing the appropriate version of required software.
		</div><div class="para">
			Once that is done, execute the installation script. From your web browser, access 
<pre class="programlisting">https://yoursite/mantisbt/admin/install.php</pre>
			 The installation procedure will go through the following steps:
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="orderedlist"><ol><li class="listitem"><div class="para">
					The script checks basic parameters for the web server
				</div></li><li class="listitem"><div class="para">
					Provide required information for the installation
				</div><div class="itemizedlist"><ul><li class="listitem"><div class="para">
							database type
						</div></li><li class="listitem"><div class="para">
							database server hostname
						</div></li><li class="listitem"><div class="para">
							user and password
						</div><div class="para">
							Required privileges: SELECT, INSERT, UPDATE, and DELETE
						</div></li><li class="listitem"><div class="para">
							high-privileged database account
						</div><div class="para">
							Additional privileges required: INDEX, CREATE, ALTER, and DROP
						</div><div class="para">
							If this account is not specified, the database user will be used.
						</div></li></ul></div></li><li class="listitem"><div class="para">
					Click the <span class="emphasis"><em>Install/Upgrade Database</em></span> button
				</div></li><li class="listitem"><div class="para">
					The script creates the database and tables.
				</div><div class="para">
					The default Administrator user account is created at this stage, to allow the initial login and setup of MantisBT.
				</div></li><li class="listitem"><div class="para">
					The script attempts to write a basic <code class="filename">config_inc.php</code> file to define the database connection parameters.
				</div><div class="para">
					This operation may fail if the web server's user account does not have write permissions to the directory (which is recommended for obvious security reasons). In this case, you will have to manually create the file and copy/paste the contents from the page.
				</div></li><li class="listitem"><div class="para">
					The script perform post installation checks on the system.
				</div><div class="para">
					Review and correct any errors.
				</div></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.upgrade">
      ⁠</a>2.5. Upgrading</h2></div></div></div><div class="para">
			This chapter explains how to upgrade an existing MantisBT installation.
		</div><div class="para">
			Start by Performing the steps described in <a class="xref" href="#admin.install.preinstall">Section 2.3, “Pre-installation / upgrade tasks”</a> above.
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="orderedlist"><ol><li class="listitem"><div class="para">
					Put the site down for maintenance 
<pre class="programlisting">cp mantis_offline.php.sample mantis_offline.php
</pre>
					 This will prevent users from using the system while the upgrade is in progress.
				</div></li><li class="listitem"><div class="para">
					Always <span class="emphasis"><em>Backup your code, data and config files</em></span> before upgrading !
				</div><div class="para">
					This includes your Mantis directory, your attachments, and your database. Refer to <a class="xref" href="#admin.install.backups">Section 2.10, “Backups”</a> for details.
				</div></li><li class="listitem"><div class="para">
					Copy the configuration files
				</div><div class="para">
					To preserve your system settings, you should copy the files listed below to subdirectory <code class="filename">config</code> of the new installation.
				</div><div class="itemizedlist"><ul><li class="listitem"><div class="para">
							<code class="filename">config_inc.php</code>,
						</div></li><li class="listitem"><div class="para">
							<code class="filename">custom_strings_inc.php</code>,
						</div></li><li class="listitem"><div class="para">
							<code class="filename">custom_constants_inc.php</code> and
						</div></li><li class="listitem"><div class="para">
							<code class="filename">custom_functions_inc.php</code>.
						</div></li></ul></div><div class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						The above list is not exhaustive. You might also have to copy other custom files specific to your installation such as logo, favicon, css, etc.
					</div></div></div></li><li class="listitem"><div class="para">
					Copy third party plugins
				</div><div class="para">
					To maintain system functionality, you should copy any additional plugins in the <code class="filename">plugins</code> subdirectory.
				</div><div class="para">
					For example on Unix, you could use the following command; it will copy all installed plugins (in local subdirectories or symlinked), excluding bundled ones.
				</div><pre class="programlisting">
cd /path/to/mantisbt-OLD/plugins
find -maxdepth 1 ! -path . -type d -o -type l |
    grep -Pv "(Gravatar|MantisCoreFormatting|MantisGraph|XmlImportExport)" |
    xargs -Idirs cp -r dirs /path/to/mantisbt-NEW/plugins
</pre><div class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						Make sure that you <span class="emphasis"><em>do not overwrite any of the bundled plugins</em></span> as per the list below, with an older version.
					</div><div class="itemizedlist"><ul><li class="listitem"><div class="para">
								Avatars via Gravatar (<code class="filename">Gravatar</code>)
							</div></li><li class="listitem"><div class="para">
								MantisBT Formatting (<code class="filename">MantisCoreFormatting</code>)
							</div></li><li class="listitem"><div class="para">
								Mantis Graphs (<code class="filename">MantisGraph</code>)
							</div></li><li class="listitem"><div class="para">
								Import/Export issues (<code class="filename">XmlImportExport</code>)
							</div></li></ul></div></div></div></li><li class="listitem"><div class="para">
					Execute the upgrade script. From your web browser, access 
<pre class="programlisting">https://yoursite/mantisbt-NEW/admin/install.php</pre>
					 where <span class="emphasis"><em>mantisbt-NEW</em></span> is the name of the directory where the new release was extracted
				</div></li><li class="listitem"><div class="para">
					Provide required information for the upgrade
				</div><div class="itemizedlist"><ul><li class="listitem"><div class="para">
							high-privileged database account
						</div><div class="para">
							Additional privileges required: INDEX, CREATE, ALTER, and DROP
						</div><div class="para">
							If this account is not specified, the database user will be used.
						</div></li></ul></div></li><li class="listitem"><div class="para">
					Click the <span class="emphasis"><em>Install/Upgrade Database</em></span> button
				</div></li><li class="listitem"><div class="para">
					At the end of the upgrade, review and correct any warnings or errors.
				</div></li></ol></div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Upgrading large databases</strong></p></div><div class="admonition"><div class="para">
				When processing large databases from versions older than 1.2, the upgrade script may fail during the conversion of date fields, leaving the system in an inconsistent (i.e. partially updated) state.
			</div><div class="para">
				In this case, you should simply restart the upgrade process, which will resume where it left off. Note that you may have to repeat this several times, until normal completion.
			</div><div class="para">
				Reference: MantisBT issue <a href="https://mantisbt.org/bugs/view.php?id=12735">12735</a>.
			</div></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.config">
      ⁠</a>2.6. Configure your installation</h2></div></div></div><div class="para">
			There are many settings that you can adjust to configure and customize MantisBT. Refer to <a class="xref" href="#admin.config">Chapter 5, <em>Configuration</em></a>, as well as the <code class="filename">config_defaults_inc.php</code> file for in depth explanations of the available options. Check out also <a class="xref" href="#admin.customize">Chapter 7, <em>Customizing MantisBT</em></a> for further options to personalize your installation.
		</div><div class="para">
			This step is normally only required for new installations, but when upgrading you may want to review and possibly customize any new configuration options.
		</div><div class="para">
			Open or create the file <code class="filename">config_inc.php</code> in subfolder config in an editor and add or modify any values as required. These will override the default values.
		</div><div class="para">
			You may want to use the provided <code class="filename">config_inc.php.sample</code> file as a starting point.
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
				you should never edit the <code class="filename">config_defaults_inc.php</code> file directly, as it could cause issues with future upgrades. Always store your custom configuration in your own <code class="filename">config_inc.php</code> file.
			</div></div></div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
				The MantisBT configuration files (<code class="filename">config_inc.php</code> as well as <code class="filename">custom_strings_inc.php</code>, <code class="filename">custom_constants_inc.php</code>, <code class="filename">custom_functions_inc.php</code>, etc.) should always be saved as <span class="emphasis"><em>UTF-8 without BOM</em></span>. Failure to do so may lead to unexpected display issues.
			</div></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.postcommon">
      ⁠</a>2.7. Post-installation and upgrade tasks</h2></div></div></div><div class="para">
			Instructions in this section are common to both new installations and upgrades, and should be applied after completing either process.
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="orderedlist"><ol><li class="listitem"><div class="para">
					Test your configuration
				</div><div class="para">
					Load up <span class="emphasis"><em>admin/check/index.php</em></span> to validate whether everything is setup correctly, and take corrective action as needed.
				</div></li><li class="listitem"><div class="para">
					Delete the <span class="emphasis"><em>admin</em></span> folder
				</div><div class="para">
					Once you have confirmed that the install or upgrade process was successful, you should delete this directory 
<pre class="programlisting">rm -r admin</pre>

				</div><div class="para">
					For security reasons, the scripts within this directory should not be freely accessible on a live MantisBT site, particularly one which is accessible via the Internet, as they can allow unauthorized people (e.g. hackers) to gain technical knowledge about the system, as well as perform administrative tasks.
				</div><div class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						Omitting this important step will leave your MantisBT instance exposed to several potentially severe attacks, e.g. <a href="https://mantisbt.org/bugs/view.php?id=23173"> issue #23173</a> (if <a href="https://www.php.net/manual/en/mysqli.configuration.php#ini.mysqli.allow-local-infile"> mysqli.allow_local_infile</a> is enabled in php.ini).
					</div></div></div></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.postinstall">
      ⁠</a>2.8. Post-installation tasks</h2></div></div></div><div class="para">
			Instructions in this section should only be applied after a new installation
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="orderedlist"><ol><li class="listitem"><div class="para">
					Login to your bugtracker
				</div><div class="para">
					Use the default Administrator account. The id and password are <span class="emphasis"><em>administrator / root</em></span>.
				</div></li><li class="listitem"><div class="para">
					Create a new Administrator account
				</div><div class="para">
					Go to <span class="emphasis"><em>Manage &gt; Users</em></span> and create a new account with 'administrator' access level.
				</div></li><li class="listitem"><div class="para">
					Disable or delete the default Administrator account
				</div></li><li class="listitem"><div class="para">
					Create a new Project
				</div><div class="para">
					Go to <span class="emphasis"><em>Manage &gt; Projects</em></span> and create a new project
				</div></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.postupgrade">
      ⁠</a>2.9. Post-upgrade tasks</h2></div></div></div><div class="para">
			Instructions in this section should only be applied after upgrading an existing installation.
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="orderedlist"><ol><li class="listitem"><div class="para">
					Test the new release
				</div><div class="para">
					Perform any additional testing as appropriate to ensure the new version does not introduce any regressions.
				</div></li><li class="listitem"><div class="para">
					Switch the site to the new version
				</div><div class="para">
					The commands below should be executed from the web root (or wherever the mantisbt scripts are installed) and assume that the "live" directory (old version) is named <span class="emphasis"><em>mantisbt</em></span> and the new release directory is <span class="emphasis"><em>mantisbt-1.3.x</em></span>. 
<pre class="programlisting">
mv mantisbt mantisbt-old
mv mantisbt-1.3.x mantisbt
</pre>

				</div></li><li class="listitem"><div class="para">
					Put the site back on line 
<pre class="programlisting">rm mantis_offline.php</pre>
					 This should be the final step in the upgrade process, as it will let users login again.
				</div></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.backups">
      ⁠</a>2.10. Backups</h2></div></div></div><div class="para">
			It is strongly recommended to backup your MantisBT database on a regular basis. The method to perform this operation depends on which RDBMS you use.
		</div><div class="para">
			Backups are a complex subject, and the specificities of implementing and handling them for each RDBMS are beyond the scope of this document. For your convenience, the section below provides a simple method to backup MySQL databases.
		</div><div class="para">
			You should also consider implementing backups of your MantisBT code (which includes your configs and possibly customization), as well as issue attachments (if stored on disk) and project documents.
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
				You should always backup your system (code and database) before upgrading !
			</div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.install.backups.mysql">
      ⁠</a>2.10.1. MySQL Backups</h3></div></div></div><div class="para">
				MySQL databases are easy to backup using the <span class="emphasis"><em>mysqldump</em></span> command: 
<pre class="programlisting">
mysqldump -u&lt;username&gt; -p&lt;password&gt; &lt;database name&gt; &gt; &lt;output file&gt;
</pre>

			</div><div class="para">
				To restore a backup you will need to have a clean database. Then run: 
<pre class="programlisting">
mysql -u&lt;username&gt; -p&lt;password&gt; &lt;database name&gt; &lt; &lt;input file&gt;
</pre>

			</div><div class="para">
				You can also perform both of these tasks using <a href="https://www.phpmyadmin.net/">phpMyAdmin</a>
			</div><div class="para">
				A good idea is to make a backup script and run it regularly through cron or a task scheduler. Using the current date in the filename can prevent overwriting and make cataloguing easier.
			</div><div class="para">
				References and useful links: 
				<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							<a href="https://dev.mysql.com/doc/refman/8.0/en/mysqldump.html"> mysqldump documentation </a>
						</div></li><li class="listitem"><div class="para">
							<a href="https://www.percona.com/software/mysql-database/percona-xtrabackup"> Percona XtraBackup </a>
						</div></li><li class="listitem"><div class="para">
							<a href="https://sourceforge.net/projects/automysqlbackup/"> AutoMySQLBackup script </a>
						</div></li></ul></div>

			</div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.uninstall">
      ⁠</a>2.11. Uninstall</h2></div></div></div><div class="para">
			It is recommended that you make a backup in case you wish to use your data in the future. See <a class="xref" href="#admin.install.backups">Section 2.10, “Backups”</a> for details.
		</div><div class="para">
			To uninstall MantisBT: 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						Delete the MantisBT directory and all files and subdirectories.
					</div></li><li class="listitem"><div class="para">
						Drop all MantisBT tables from the database, these can be identified by the configured prefix for the installation. The default prefix is 'mantis'.
					</div></li><li class="listitem"><div class="para">
						Remove any customizations or additions that you may have made.
					</div></li></ul></div>
			 If you have the permissions to create/drop databases and you have a specific database for MantisBT that does not contain any other data, you can drop the whole database.
		</div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.user">
      ⁠</a>Chapter 3. User Management</h1></div></div></div><div class="toc"><dl class="toc"><dt><span class="section"><a href="#admin.user.create">3.1. Creating User Accounts</a></span></dt><dt><span class="section"><a href="#admin.user.enable">3.2. Enabling/Disabling User Accounts</a></span></dt><dt><span class="section"><a href="#admin.user.delete">3.3. Deleting User Accounts</a></span></dt><dt><span class="section"><a href="#admin.user.signup">3.4. User Signup</a></span></dt><dt><span class="section"><a href="#admin.user.passwordreset">3.5. Forgot Password and Reset Password</a></span></dt><dt><span class="section"><a href="#admin.user.impersonation">3.6. Impersonating a user</a></span></dt><dt><span class="section"><a href="#admin.user.passwordchange">3.7. Changing Password</a></span></dt><dt><span class="section"><a href="#admin.user.pruning">3.8. Pruning User Accounts</a></span></dt><dt><span class="section"><a href="#admin.user.access">3.9. Authorization and Access Levels</a></span></dt><dt><span class="section"><a href="#admin.user.autocreate">3.10. Auto Creation of Accounts on Login</a></span></dt><dt><span class="section"><a href="#admin.user.prefs">3.11. User Preferences</a></span></dt><dt><span class="section"><a href="#admin.user.profiles">3.12. User Profiles</a></span></dt></dl></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.create">
      ⁠</a>3.1. Creating User Accounts</h2></div></div></div><div class="para">
			In MantisBT, there is no limit on the number of user accounts that can be created. Typically, installations with thousands of users tend to have a limited number of users that have access level above REPORTER.
		</div><div class="para">
			By default users with ADMINISTRATOR access level have access to create new user accounts. The steps to do that are: 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						Click "Manage" on Main Menu.
					</div></li><li class="listitem"><div class="para">
						Click "Users" (if not selected by default).
					</div></li><li class="listitem"><div class="para">
						Click "Create New Account" button just below the alphabet key.
					</div></li><li class="listitem"><div class="para">
						Enter user name, email address, global access level (more details about access levels later). Other fields are optional.
					</div></li><li class="listitem"><div class="para">
						Click "Create Users".
					</div></li></ul></div>

		</div><div class="para">
			Creating a user triggers the following actions: 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						Creating a user in the database.
					</div></li><li class="listitem"><div class="para">
						If email notifications ($g_enable_email_notification) is set to ON, then the user will receive an email allowing them to activate their account and set their password. Otherwise, the account will be created with a blank password.
					</div></li><li class="listitem"><div class="para">
						If email notifications ($g_enable_email_notification) is set to ON, users with access level of $g_notify_new_user_created_threshold_min and above will get a notification that a user account has been created. Information about the user like user name, email address, IP address are included in the email notification.
					</div></li></ul></div>
		</div><div class="para">
			When the 'Protected' flag is set on a user account, it indicates that the account is a shared account (e.g. demo account) and hence users logged using such account will not be allowed to change account preferences and profile information.
		</div><div class="para">
			The anonymous user account specified with the $g_anonymous_account option will always be treated as a protected user account. When you are creating the anonymous user account, the 'Protected' flag is essentially ignored because the anonymous user is always treated as a protected user.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.enable">
      ⁠</a>3.2. Enabling/Disabling User Accounts</h2></div></div></div><div class="para">
			The recommended way of retiring user accounts is to disable them. Scenarios where this is useful is when a person leaves the team and it is necessary to retire their account.
		</div><div class="para">
			Once an account is disabled the following will be enforced: 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						All currently active sessions for the account will be invalidated (i.e. automatically logged out).
					</div></li><li class="listitem"><div class="para">
						It will no longer be possible login using this account.
					</div></li><li class="listitem"><div class="para">
						No further email notifications will be sent to the account once it is disabled.
					</div></li><li class="listitem"><div class="para">
						The user account will not show anymore in lists like "assign to", "send reminder to", etc.
					</div></li></ul></div>
		</div><div class="para">
			The disabling process is totally reversible. Hence, the account can be re-enabled and all the account history will remain intact. For example, the user will still have issues reported by them, assigned to them, monitored by them, etc.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.delete">
      ⁠</a>3.3. Deleting User Accounts</h2></div></div></div><div class="para">
			Another way to retire user accounts is by deleting them. This approach is only recommended for accounts that have not been active (i.e. haven't reported issues). Once the account is deleted, any issues or actions associated with such account, will be associated with user123 (where 123 is the code of the account that was deleted). Note that associated issues or actions are not deleted.
		</div><div class="para">
			As far as the underlying database, after the deletion of a user, records with the user id as a foreign key will have a value that no longer exists in the users table. Hence, any tools that operate directly on the database must take this into consideration.
		</div><div class="para">
			By default administrators are the only users who can delete user accounts. They can delete accounts by clicking Manage, Users, locating the user to be deleted and opening it details page, then clicking on the "Delete User" button which deletes the user.
		</div><div class="para">
			Note that "Deleting Users" is not a reversible process. Hence, if it is required to re-add the user account, it is not possible to recreate the user account so that it gets the same ID and hence retains its history. However, manually creating a record in the users table with the same id, can possibly do that. However, this approach is not recommended or supported.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.signup">
      ⁠</a>3.4. User Signup</h2></div></div></div><div class="para">
			For open source and freeware projects, it is very common to setup MantisBT so that users can signup for an account and get a REPORTER access by default (configurable by the $g_default_new_account_access_level configuration option). The signup process can be enabled / disabled using the $g_allow_signup configuration option, which is enabled by default.
		</div><div class="para">
			If user signup is enabled, then it is required that $g_send_reset_password is ON as well, and the e-mail settings properly configured (see <a class="xref" href="#admin.config.email">Section 5.8, “Email”</a>).
		</div><div class="para">
			If email notifications ($g_enable_email_notification) is set to ON, users with access level of $g_notify_new_user_created_threshold_min and above will get a notification that a user account has been created. Information about the user like user name, email address, IP address are included in the email notification.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.passwordreset">
      ⁠</a>3.5. Forgot Password and Reset Password</h2></div></div></div><div class="para">
			It is pretty common for users to forget their password. MantisBT provides two ways to handle such scenario: "Forgot Password" and "Reset Password".
		</div><div class="para">
			"Forgot Password" is a self service scenario where users go to the login page, figure out they don't remember their password, and then click the "Lost your password?" link. Users are then asked for their user name and email address. If correct, then they are sent an email with a link which allows them to login to MantisBT and change their password.
		</div><div class="para">
			"Reset Password" scenario is where a user reports to the administrator that they are not able to login into MantisBT anymore. This can be due to forgetting their password and possibly user name or email address that they used when signing up. The administrator then goes to Manage, Users, locates the user account and opens its details. Under the user account details, there is a "Reset Password" button which the administrator can click to reset the password and trigger an email to the user to allow them to get into MantisBT and set their password. In the case where email notifications are disabled, resetting password will set the password to an empty string.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.impersonation">
      ⁠</a>3.6. Impersonating a user</h2></div></div></div><div class="para">
			Administrators are able to impersonate users in order to reproduce an issue reported by a user, test their access making sure they can access the expected projects/issues/fields, or to create API tokens for service accounts that are used to grant other systems limited access to MantisBT.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.passwordchange">
      ⁠</a>3.7. Changing Password</h2></div></div></div><div class="para">
			Users are able to change their own passwords (unless their account is "protected"). This can be done by clicking on "My Account", and then typing the new password in the "Password" and "Confirm Password" fields, then clicking "Update User". Changing the password automatically invalidates all logged in sessions and hence the user will be required to re-login. Invalidating existing sessions is very useful in the case where a user going onto a computer, logs into MantisBT and leaves the computer without logging out. By changing the password from another computer, the session on the original computer automatically becomes invalidated.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.pruning">
      ⁠</a>3.8. Pruning User Accounts</h2></div></div></div><div class="para">
			The pruning function allows deleting of user accounts for accounts that have been created more than a week ago, and they never logged in. This is particularly useful for users who signed up with an invalid email or with a typo in their email address address.
		</div><div class="para">
			The account pruning can be done by administrators by going to "Manage", "Users", and clicking the "Prune Accounts" button inside the "Never Logged In" box.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.access">
      ⁠</a>3.9. Authorization and Access Levels</h2></div></div></div><div class="para">
			MantisBT uses access levels to define what a user can do. Each user account has a global or default access level that is associated with it. This access level is used as the access level for such users for all actions associated with public projects as well as actions that are not related to a specific project. Users with global access level less than $g_private_project_threshold will not have access to private projects by default.
		</div><div class="para">
			The default access levels shipped with MantisBT out of the box are VIEWER, REPORTER, UPDATER, DEVELOPER, MANAGER and ADMINISTRATOR. Each features has several configuration options associated with it and identifies the required access level to do certain actions. For example, viewing an issue, reporting an issue, updating an issue, adding a note, etc.
		</div><div class="para">
			For example, in the case of reporting issues, the required access level is configurable using the $g_report_bug_threshold configuration option (which is defaulted to REPORTER). So for a user to be able to report an issue against a public project, the user must have a project-specific or a global access level that is greater than or equal to REPORTER. However, in the case of reporting an issue against a private project, the user must have project specific access level (that is explicitly granted against the project) that is higher than REPORTER or have a global access level that is higher than both $g_private_project_threshold and $g_report_bug_threshold.
		</div><div class="para">
			Note that project specific access levels override the global access levels. For example, a user may have REPORTER as the global access level, but have a MANAGER access level to a specific project. Or a user may have MANAGER as the global access level by VIEWER access to a specific project. Access levels can be overridden for both public and private projects. However, overriding access level is not allowed for users with global access ADMINISTRATOR.
		</div><div class="para">
			Each feature typically has multiple access control configuration options to define what access level can perform the operation. For example, adding a note may require REPORTER access level, updating it note may require DEVELOPER access level, unless the note was added by the same user.
		</div><div class="para">
			Such threshold configuration options can be set to a single access level, which means users with such threshold and above are authorized to perform the action. The other option is to specify an array of access levels which indicates that users with the explicitly specific thresholds are allowed to execute the actions.
		</div><div class="para">
			It is also worth mentioning that the access levels are defined by the $g_access_levels_enum_string configuration option, and it is possible to customize such list. The default value for the available access levels is '10:viewer, 25:reporter, 40:updater, 55:developer, 70:manager, 90:administrator'. The instructions about how to customize the list of access levels will be covered in the customization section.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.autocreate">
      ⁠</a>3.10. Auto Creation of Accounts on Login</h2></div></div></div><div class="para">
			If you are using a global user directory (LDAP, Active Directory), you may want to configure MantisBT so users who already exists in the directory will be automatically authenticated and added to MantisBT.
		</div><div class="para">
			For example, a company may setup their MantisBT installation in a way, where its staff members that are already registered in their LDAP directory, should be allowed to login into MantisBT with the same user name and password. Another option could be if MantisBT is integrated into some content management system, where it is desired to have a single registration and single sign-on experience.
		</div><div class="para">
			In such scenarios, once a user logs in for the first time, a user account is automatically created for them, although the password verification is still done against LDAP or the main users repository.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.prefs">
      ⁠</a>3.11. User Preferences</h2></div></div></div><div class="para">
			Users can fine tune the way MantisBT interacts with them by modifying their user preferences to override the defaults set by the administrator; If the administrator changes a default setting, it will not automatically cascade in the users' preferences once they have been set, so it is the users' responsibility to manage their own preferences.
		</div><div class="para">
			The user preferences include the following:
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
					<span class="emphasis"><em>Default Project</em></span>: A user can choose the default project that is selected when the user first logs in. This can be a specific project or "All Projects". For users that only work on one project, it would make sense to set such project as the default project (rather than "All Projects"). The active project is part of the filter applied on the issues listed in the "View Issues" page. Also any newly reported issues will be associated with the active project.
				</div></li><li class="listitem"><div class="para">
					<span class="emphasis"><em>Refresh Delay</em></span>: The refresh delay is used to specify the number of seconds between auto-refreshes of the View Issues page.
				</div></li><li class="listitem"><div class="para">
					<span class="emphasis"><em>Redirect Delay</em></span>: The redirect delay is the number of seconds to wait after displaying flash messages like "Issue created successfully", and before the user gets redirected to the next page.
				</div></li><li class="listitem"><div class="para">
					<span class="emphasis"><em>Notes Sort Order</em></span>: The preference relating to how notes should be ordered when issue is viewed or in email notifications. Ascending order means that older notes are displayed first
				</div></li><li class="listitem"><div class="para">
					<span class="emphasis"><em>Email on XXX</em></span>: If unticked, then the notifications related to the corresponding event would be disabled. User can also specify the minimum issue severity of for the email to be sent.
				</div><div class="para">
					Note that the preference is only used to disable notifications that as per the administrator's configuration, this user would have qualified to receive.
				</div></li><li class="listitem"><div class="para">
					<span class="emphasis"><em>Email Notes Limit</em></span>: This preference can be used to limit the number of issue notes to be included in a email notifications. Specifying N here will cause only the latest N to be included. The value 0 means that all notes will be included.
				</div></li><li class="listitem"><div class="para">
					<span class="emphasis"><em>Language</em></span>: The preferred language of the user. This language is used by the GUI and in email notifications. Note that MantisBT uses UTF-8 for encoding the data, hence the user could for example use MantisBT with a Chinese interface, while logging issue data in German.
				</div></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.profiles">
      ⁠</a>3.12. User Profiles</h2></div></div></div><div class="para">
			A user profile describes an environment that used to run the software for which issues are being tracked.
		</div><div class="para">
			When reporting issues, users can elect to enter information like platform, operating system and version manually, or they can choose from a list of available profiles.
		</div><div class="para">
			Each user has access to all the personal profiles they create, in addition to global ones; Profile data includes "Platform", "Operating System", "OS Version", and "Additional Description".
		</div><div class="para">
			Global profiles are typically used by the administrator to define a set of standard system settings used in their environment, which saves users the trouble of having to define them individually. The access level required to manage global profiles is configured by the $g_manage_global_profile_threshold configuration option and defaults to MANAGER.
		</div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.lifecycle">
      ⁠</a>Chapter 4. Issue Lifecycle and Workflow</h1></div></div></div><div class="toc"><dl class="toc"><dt><span class="section"><a href="#admin.lifecycle.create">4.1. Issue Creation</a></span></dt><dt><span class="section"><a href="#admin.lifecycle.status">4.2. Issue Statuses</a></span></dt><dt><span class="section"><a href="#admin.lifecycle.workflow">4.3. Workflow</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.lifecycle.workflow.transitions">4.3.1. Workflow Transitions</a></span></dt><dt><span class="section"><a href="#admin.lifecycle.workflow.thresholds">4.3.2. Workflow Thresholds</a></span></dt></dl></dd></dl></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.lifecycle.create">
      ⁠</a>4.1. Issue Creation</h2></div></div></div><div class="para">
			The life cycle of an issue starts with its creation. An issue can be created via one of the following channels: 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						MantisBT Web Interface - This is where a user logs into MantisBT and reports a new issue.
					</div></li><li class="listitem"><div class="para">
						SOAP API - Where an application automatically reports an issue into MantisBT using the SOAP API web services interfaces. For example, the nightly build script can automatically report an issue if the build fails.
					</div></li><li class="listitem"><div class="para">
						Email - This is not supported out of the box, but there are existing MantisBT patches that would listen to emails on pre-configured email addresses and adds them to the MantisBT database.
					</div></li><li class="listitem"><div class="para">
						Others - There can be several other ways to report issues. For example, applications / scripts that directly injects issues into MantisBT database (not recommended, except for one-off migration scripts), or PHP scripts that use the core MantisBT API to create new issues.
					</div></li></ul></div>

		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.lifecycle.status">
      ⁠</a>4.2. Issue Statuses</h2></div></div></div><div class="para">
			An important part of issue tracking is to classify issues as per their status. Each team may decide to have a different set of categorization for the status of the issues, and hence, MantisBT provides the ability to customize the list of statuses. MantisBT assumes that an issue can be in one of three stages: opened, resolved and closed. Hence, the customized statuses list will be mapped to these three stages. For example, MantisBT comes out of the box with the following statuses: new, feedback, acknowledged, confirmed, assigned, resolved and closed. In this case "new" -&gt; "assigned" map to opened, "resolved" means resolved and "closed" means closed.
		</div><div class="para">
			Following is the explanation of what the standard statuses that are shipped with MantisBT means. 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						New - This is the landing status for new issues. Issues stay in this status until they are assigned, acknowledged, confirmed or resolved. The next status can be "acknowledged", "confirmed", "assigned" or "resolved".
					</div></li><li class="listitem"><div class="para">
						Acknowledged - This status is used by the development team to reflect their agreement to the suggested feature request. Or to agree with what the reporter is suggesting in an issue report, although they didn't yet attempt to reproduce what the reporter is referring to. The next status is typically "assigned" or "confirmed".
					</div></li><li class="listitem"><div class="para">
						Confirmed - This status is typically used by the development team to mention that they agree with what the reporter is suggesting in the issue and that they have confirmed and reproduced the issue. The next status is typically "assigned".
					</div></li><li class="listitem"><div class="para">
						Assigned - This status is used to reflect that the issue has been assigned to one of the team members and that such team member is actively working on the issue. The next status is typically "resolved".
					</div></li><li class="listitem"><div class="para">
						Resolved - This status is used to reflect that the issue has been resolved. An issue can be resolved with one of many resolutions (customizable). For example, an issue can be resolved as "fixed", "duplicate", "won't fix", "no change required", etc. The next statuses are typically "closed" or in case of the issue being re-opened, then it would be "feedback".
					</div></li><li class="listitem"><div class="para">
						Closed - This status reflects that the issue is completely closed and no further actions are required on it. It also typically hides the issue from the View Issues page. Some teams use "closed" to reflect sign-off by the reporter and others use it to reflect the fact that the fix has been released to customers.
					</div></li></ul></div>

		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.lifecycle.workflow">
      ⁠</a>4.3. Workflow</h2></div></div></div><div class="para">
			Now that we have covered how an issue gets created, and what are the different statuses during the life cycle of such issues, the next step is to define the workflow. The workflow dictates the valid transitions between statuses and the user access level required of the user who triggers such transitions; in other words, how issues move from one status to another and who is authorized to trigger such transitions.
		</div><div class="para">
			MantisBT provides the ability for teams to define their own custom workflow which works on top of their custom status (see <a class="xref" href="#admin.customize.status">Section 7.5, “Customizing Status Values”</a>).
		</div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.lifecycle.workflow.transitions">
      ⁠</a>4.3.1. Workflow Transitions</h3></div></div></div><div class="para">
				By default, there is no workflow defined, which means that all states are accessible from any other, by anyone.
			</div><div class="para">
				The "Manage &gt; Configuration &gt; Workflow Transitions" page allows users with ADMINISTRATOR access level to do the following tasks: 
				<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							Define the valid next statuses for each status.
						</div></li><li class="listitem"><div class="para">
							Define the default next status for each status.
						</div></li><li class="listitem"><div class="para">
							Define the minimum access level required for a user to transition to each status.
						</div></li><li class="listitem"><div class="para">
							Define the default status for newly created issues.
						</div></li><li class="listitem"><div class="para">
							Define the status at which the issue is considered resolved. Any issues a status code greater than or equal to the specified status will be considered resolved.
						</div></li><li class="listitem"><div class="para">
							Define the status which is assigned to issues that are re-opened.
						</div></li><li class="listitem"><div class="para">
							Define the required access level to change the workflow.
						</div></li></ul></div>

			</div><div class="para">
				Note that the scope of the applied change is dependent on the selected project. If "All Projects" is selected, then the configuration is to be used as the default for all projects, unless overridden by a specific project. To configure for a specific project, switch to it via the combobox at the top right corner of the screen.
			</div><div class="para">
				The Global ("All Projects") workflow can also be defined in the <span class="emphasis"><em>config_inc.php</em></span> file, as per the following example. 
<pre class="programlisting">
$g_status_enum_workflow[NEW_]           ='30:acknowledged,20:feedback,40:confirmed,50:assigned,80:resolved';
$g_status_enum_workflow[FEEDBACK]       ='30:acknowledged,40:confirmed,50:assigned,80:resolved';
$g_status_enum_workflow[ACKNOWLEDGED]   ='40:confirmed,20:feedback,50:assigned,80:resolved';
$g_status_enum_workflow[CONFIRMED]      ='50:assigned,20:feedback,30:acknowledged,80:resolved';
$g_status_enum_workflow[ASSIGNED]       ='80:resolved,20:feedback,30:acknowledged,40:confirmed';
$g_status_enum_workflow[RESOLVED]       ='90:closed,20:feedback,50:assigned';
$g_status_enum_workflow[CLOSED]         ='20:feedback,50:assigned';
</pre>

			</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
					The workflow needs to have a path from the statuses greater than or equal to the 'resolved' state back to the 'feedback' state (see <span class="emphasis"><em>$g_bug_resolved_status_threshold</em></span> and <span class="emphasis"><em>$g_bug_feedback_status</em></span> under <a class="xref" href="#admin.config.status">Section 5.22, “Status Settings”</a>), otherwise, the re-open operation won't work.
				</div></div></div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
					The first item in each list denotes the default value for this status, which will be pre-selected in the Change Status combobox in the View Issues page.
				</div></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.lifecycle.workflow.thresholds">
      ⁠</a>4.3.2. Workflow Thresholds</h3></div></div></div><div class="para">
				The "Manage &gt; Configuration &gt; Workflow Thresholds" page allows users with ADMINISTRATOR access level to define the thresholds required to do certain actions. Following is a list of such actions and what they mean:
			</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						Report an issue - The access levels that are allowed to report an issue.
					</div></li><li class="listitem"><div class="para">
						Update an issue - The access levels that are allowed to update the header information of an issue.
					</div></li><li class="listitem"><div class="para">
						Allow issue to be closed on resolved - The access levels that are allow to resolve and close an issue in one step.
					</div></li><li class="listitem"><div class="para">
						Allow reporter to close issue - Indicates if reporters should be allowed to close issues reported by them.
					</div></li><li class="listitem"><div class="para">
						Monitor an issue - The access levels required for a user to be able to monitor an issue. Once a user monitors an issue, the user will be included in all future email notifications relating to changes in the issue.
					</div></li><li class="listitem"><div class="para">
						Handle an issue - The access levels required for a user to be shown in the list of users that can handle an issue.
					</div></li><li class="listitem"><div class="para">
						Assign an issue - The access levels required for a user to be able to change the handler (i.e. assign / unassign) an issue.
					</div></li><li class="listitem"><div class="para">
						Move an issue - The access levels required for a user to be able to move an issue from one project to another. (TODO: are these access levels evaluated against source or destination project?).
					</div></li><li class="listitem"><div class="para">
						Delete an issue - The access levels required for a user to be able to delete an issue.
					</div></li><li class="listitem"><div class="para">
						Reopen an issue - The access levels required for a user to be able to re-open a resolved or closed issue.
					</div></li><li class="listitem"><div class="para">
						Allow Reporter to re-open Issue - Whether the reporter of an issue can re-open a resolved or closed issue, independent of their access level.
					</div></li><li class="listitem"><div class="para">
						Status to which a reopened issue is set - This is the status to which an issue is set after it is re-opened.
					</div></li><li class="listitem"><div class="para">
						Resolution to which a reopen issue is set - The resolution to set on issues that are reopened.
					</div></li><li class="listitem"><div class="para">
						Status where an issue is considered resolved - The status at which an issue is considered resolved.
					</div></li><li class="listitem"><div class="para">
						Status where an issue becomes readonly - Issues with such status and above are considered read-only. Read-only issues can only be modified by users with a configured access level. Read-only applies to the issue header information as well as other issue related information like relationships, attachments, notes, etc.
					</div></li><li class="listitem"><div class="para">
						Update readonly issues - The access levels required for a user to be able to modify a readonly issue.
					</div></li><li class="listitem"><div class="para">
						Update issue status - The access levels required for a user to be able to modify the status of an issue.
					</div></li><li class="listitem"><div class="para">
						View private issues - The access levels for a user to be able to view a private issue.
					</div></li><li class="listitem"><div class="para">
						Set view status (public vs. private) - The access level for a user to be able to set whether an issue is private or public, when reporting the issue. If the user reporting the issues doesn't have the required access, then the issue will be created with the default view state.
					</div></li><li class="listitem"><div class="para">
						Update view status (public vs private) - The access level required for a user to be able to update the view status (i.e. public vs. private).
					</div></li><li class="listitem"><div class="para">
						Show list of users monitoring issue - The access level required for a user to be able to view the list of users monitoring an issue.
					</div></li><li class="listitem"><div class="para">
						Set status on assignment of handler - The access levels required for a user to be able to re-assign an issue when changing its status.
					</div></li><li class="listitem"><div class="para">
						Status to set auto-assigned issues to - The status - This is the status that is set on issues that are auto assigned to users that are associated with the category that the issuer is reported under.
					</div></li><li class="listitem"><div class="para">
						Limit reporter's access to their own issues - When set, reporters are only allow to view issues that they have reported.
					</div></li><li class="listitem"><div class="para">
						Add notes - The access levels required for users to be able to add notes.
					</div></li><li class="listitem"><div class="para">
						Update notes - The access levels required for users to be able to update issue notes.
					</div></li><li class="listitem"><div class="para">
						Allow user to edit their own issue notes - A flag that indicates the ability for users to edit issue notes report by them.
					</div></li><li class="listitem"><div class="para">
						Delete note - The access levels required for a user to delete a note that they may or may not have reported themselves.
					</div></li><li class="listitem"><div class="para">
						View private notes - The access levels required for a user to be able to view private notes associated with an issue that they have access to view.
					</div></li><li class="listitem"><div class="para">
						View Change Log - The access levels required for a user to be able to view the change log.
					</div></li><li class="listitem"><div class="para">
						View Assigned To - The access levels required for a user to be able to know the handler of an issue that they have access to.
					</div></li><li class="listitem"><div class="para">
						View Issue History - The access levels required for a user to be able to view the history of changes of an issue.
					</div></li><li class="listitem"><div class="para">
						Send reminders - The access levels required for a user to be able to send reminders to other users relating to an issue that they have access to.
					</div></li></ul></div></div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.config">
      ⁠</a>Chapter 5. Configuration</h1></div></div></div><div class="toc"><dl class="toc"><dt><span class="section"><a href="#admin.config.intro">5.1. Introduction</a></span></dt><dt><span class="section"><a href="#admin.config.database">5.2. Database</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.config.database.mandatory">5.2.1. Base Database settings</a></span></dt><dt><span class="section"><a href="#admin.config.database.tablenaming">5.2.2. Database table naming settings</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.config.path">5.3. Path</a></span></dt><dt><span class="section"><a href="#admin.config.webserver">5.4. Webserver</a></span></dt><dt><span class="section"><a href="#admin.config.settings">5.5. Configuration Settings</a></span></dt><dt><span class="section"><a href="#admin.config.security">5.6. Security and Cryptography</a></span></dt><dt><span class="section"><a href="#admin.config.signup">5.7. Signup and Lost Password</a></span></dt><dt><span class="section"><a href="#admin.config.email">5.8. Email</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.config.email.dkim">5.8.1. DKIM signature</a></span></dt><dt><span class="section"><a href="#admin.config.email.smime">5.8.2. S/MIME signature</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.config.version">5.9. Version</a></span></dt><dt><span class="section"><a href="#admin.config.language">5.10. Language</a></span></dt><dt><span class="section"><a href="#admin.config.display">5.11. Display</a></span></dt><dt><span class="section"><a href="#admin.config.time">5.12. Time</a></span></dt><dt><span class="section"><a href="#admin.config.date">5.13. Date</a></span></dt><dt><span class="section"><a href="#admin.config.timezone">5.14. Time Zone</a></span></dt><dt><span class="section"><a href="#admin.config.news">5.15. News</a></span></dt><dt><span class="section"><a href="#admin.config.defaults">5.16. Default Preferences</a></span></dt><dt><span class="section"><a href="#admin.config.summary">5.17. Summary</a></span></dt><dt><span class="section"><a href="#admin.config.bugnote">5.18. Bugnote</a></span></dt><dt><span class="section"><a href="#admin.config.uploads">5.19. File Upload</a></span></dt><dt><span class="section"><a href="#admin.config.html">5.20. HTML</a></span></dt><dt><span class="section"><a href="#admin.config.auth">5.21. Authentication</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.config.auth.global">5.21.1. Global authentication parameters</a></span></dt><dt><span class="section"><a href="#admin.config.auth.ldap">5.21.2. LDAP authentication method parameters</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.config.status">5.22. Status Settings</a></span></dt><dt><span class="section"><a href="#admin.config.filters">5.23. Filters</a></span></dt><dt><span class="section"><a href="#admin.config.misc">5.24. Misc</a></span></dt><dt><span class="section"><a href="#admin.config.cookies">5.25. Cookies</a></span></dt><dt><span class="section"><a href="#admin.config.speed">5.26. Speed Optimisation</a></span></dt><dt><span class="section"><a href="#admin.config.reminders">5.27. Reminders</a></span></dt><dt><span class="section"><a href="#admin.config.bughistory">5.28. Bug History</a></span></dt><dt><span class="section"><a href="#admin.config.sponsorship">5.29. Sponsorship</a></span></dt><dt><span class="section"><a href="#admin.config.customfields">5.30. Custom Fields</a></span></dt><dt><span class="section"><a href="#admin.config.myview">5.31. My View Settings</a></span></dt><dt><span class="section"><a href="#admin.config.relationship">5.32. Relationship Graphs</a></span></dt><dt><span class="section"><a href="#admin.config.wiki">5.33. Wiki Integration</a></span></dt><dt><span class="section"><a href="#admin.config.subprojects">5.34. Sub-Projects</a></span></dt><dt><span class="section"><a href="#admin.config.fields">5.35. Field Visibility</a></span></dt><dt><span class="section"><a href="#admin.config.logging">5.36. System Logging and Debugging</a></span></dt><dt><span class="section"><a href="#admin.config.timetracking">5.37. Time Tracking</a></span></dt><dt><span class="section"><a href="#admin.config.api">5.38. API</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.config.api.disable">5.38.1. Disabling the webservice API</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.config.antispam">5.39. Anti-Spam Configuration</a></span></dt><dt><span class="section"><a href="#admin.config.duedate">5.40. Due Date</a></span></dt><dt><span class="section"><a href="#admin.config.users">5.41. User Management</a></span></dt><dt><span class="section"><a href="#admin.config.view">5.42. View Page Settings</a></span></dt><dt><span class="section"><a href="#admin.config.issues">5.43. Issues visibility</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.config.issues.private">5.43.1. Public/Private view status</a></span></dt><dt><span class="section"><a href="#admin.config.issues.limitedview">5.43.2. Limited view configuration</a></span></dt><dt><span class="section"><a href="#admin.config.issues.limitreporters">5.43.3. "Limit reporters" configuration (deprecated)</a></span></dt></dl></dd></dl></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.intro">
      ⁠</a>5.1. Introduction</h2></div></div></div><div class="para">
			MantisBT is highly customizable through the web interface and configuration files. Configuration options can be set globally as well as customized for a specific project or user (except for options listed in <span class="emphasis"><em>$g_global_settings</em></span>, see <a class="xref" href="#admin.config.settings">Section 5.5, “Configuration Settings”</a>).
		</div><div class="para">
			Configuration options can be set in <span class="emphasis"><em>config_inc.php</em></span> and in the <span class="emphasis"><em>database</em></span> (using the various manage pages). Values stored in the database take precedence over values defined in <span class="emphasis"><em>config_inc.php</em></span>. The former can also be viewed and updated on the <span class="emphasis"><em>Configuration Report</em></span> page (Manage &gt; Configuration &gt; Configuration Report).
		</div><div class="para">
			To determine which value to use, MantisBT follows the list below, sequentially searching for the specified configuration option until a match is found.
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="orderedlist"><ol><li class="listitem"><div class="para">
					<span class="emphasis"><em>database</em></span>: current user, current project
				</div></li><li class="listitem"><div class="para">
					<span class="emphasis"><em>database</em></span>: current user, all projects
				</div></li><li class="listitem"><div class="para">
					<span class="emphasis"><em>database</em></span>: all users, current project
				</div></li><li class="listitem"><div class="para">
					<span class="emphasis"><em>database</em></span>: all users, all projects
				</div></li><li class="listitem"><div class="para">
					<span class="emphasis"><em>config_inc.php</em></span>
				</div></li><li class="listitem"><div class="para">
					<span class="emphasis"><em>config_defaults_inc.php</em></span>
				</div></li></ol></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.database">
      ⁠</a>5.2. Database</h2></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.database.mandatory">
      ⁠</a>5.2.1. Base Database settings</h3></div></div></div><div class="para">
			These settings are required for the system to work, and are typically set when installing MantisBT. They should be provided to you by your system administrator or your hosting company.
		</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_hostname</span></dt><dd><div class="para">
						Host name or connection string for Database server. The default value is localhost. For MySql, this should be hostname or hostname:port (e.g. localhost:3306).
					</div></dd><dt><span class="term">$g_db_username</span></dt><dd><div class="para">
						User name to use for connecting to the database. The user needs to have read/write access to the MantisBT database. The default user name is "root".
					</div></dd><dt><span class="term">$g_db_password</span></dt><dd><div class="para">
						Password for the specified user name. The default password is empty.
					</div></dd><dt><span class="term">$g_database_name</span></dt><dd><div class="para">
						Name of database that contains MantisBT tables. The default name is 'bugtracker'.
					</div></dd><dt><span class="term">$g_db_type</span></dt><dd><div class="para">
						The supported database types are listed in the table below.
					</div><div class="para">
						The PHP extension corresponding to the selected type must be enabled (see also <a class="xref" href="#admin.install.requirements.software.versions">Section *******, “Versions compatibility table”</a>).
					</div><div class="informaltable"><table xmlns:d="http://docbook.org/ns/docbook" class="gt-4-cols lt-7-rows"><colgroup><col /><col /><col /><col /></colgroup><thead><tr><th>RDBMS</th><th>db_type (ADOdb)</th><th>PHP extension</th><th>Comments</th></tr></thead><tbody><tr><td>MySQL</td><td>mysqli</td><td>mysqli</td><td>default</td></tr><tr><td>PostgreSQL</td><td>pgsql</td><td>pgsql</td><td> </td></tr><tr><td>MS SQL Server</td><td>mssqlnative</td><td>sqlsrv</td><td> </td></tr><tr><td>Oracle</td><td>oci8</td><td>oci8</td><td> </td></tr></tbody></table></div></dd></dl></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.database.tablenaming">
      ⁠</a>5.2.2. Database table naming settings</h3></div></div></div><div class="para">
			MantisBT allows administrators to configure a prefix and a suffix for its tables. This enables multiple MantisBT installation in the same database or schema.
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
				Use of long strings for these configuration options may cause issues on RDBMS restricting the size of its identifiers, such as Oracle (which imposed a maximum size of 30 characters until version 12.1; starting with 12cR2 this <a href="https://docs.oracle.com/en/database/oracle/oracle-database/12.2/sqlrf/Database-Object-Names-and-Qualifiers.html"> limit has been increased to 128</a>).
			</div><div class="para">
				To avoid this limitation, it is recommended that
			</div><div class="itemizedlist"><ul><li class="listitem"><div class="para">
						the <span class="emphasis"><em>prefix</em></span> is set to blank or kept as short as possible (e.g. <code class="literal">m</code>).
					</div></li><li class="listitem"><div class="para">
						the <span class="emphasis"><em>suffix</em></span> is set to blank.
					</div></li><li class="listitem"><div class="para">
						the <span class="emphasis"><em>plugin prefix</em></span> is kept as short as possible (e.g. <code class="literal">plg</code>).
					</div></li></ul></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_db_table_prefix</span></dt><dd><div class="para">
						Specifies the prefix to be used for all table names. The default value is <code class="literal">mantis</code>.
					</div><div class="para">
						The given string is added with an underscore before the base table name, e.g. for the <code class="literal">bug</code> table, the actual table name with the default prefix would be <code class="literal">mantis_bug</code>.
					</div></dd><dt><span class="term">$g_db_table_suffix</span></dt><dd><div class="para">
						Specifies the suffix to be appended to all table names. The default value is <code class="literal">table</code>.
					</div><div class="para">
						The given string is added with an underscore after the base table name, e.g. for the <code class="literal">bug</code> table, the actual table name with the default suffix would be <code class="literal">bug_table</code>.
					</div></dd><dt><span class="term">$g_db_table_plugin_prefix</span></dt><dd><div class="para">
						Specifies the prefix to be used to differentiate tables belonging to a plugin's schema from MantisBT's own base tables. The default value is <code class="literal">plugin</code>.
					</div><div class="para">
						The given string is inserted with an underscore between the table prefix and the base table name, and the plugin <span class="emphasis"><em>basename</em></span> is added after that, e.g. for a table named <code class="literal">foo</code> in the <code class="literal">Example</code> plugin, with default values for prefixes and suffix the physical table name would be <code class="literal">mantis_plugin_Example_foo_table</code>.
					</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
							It is strongly recommended <span class="emphasis"><em>not to use an empty string</em></span> here, as this could lead to problems, e.g. conflicts if a plugin's basename happens to match one of MantisBT's base tables.
						</div></div></div></dd><dt><span class="term">$g_dsn</span></dt><dd><div class="para">
						Adodb Data Source Name This is an EXPERIMENTAL field. If the above database settings, do not provide enough flexibility, it is possible to specify a dsn for the database connection. NOTE: the installer does not yet fully support the use of dsn's
					</div></dd></dl></div></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.path">
      ⁠</a>5.3. Path</h2></div></div></div><div class="para">
		These path settings are important for proper linking within MantisBT. In most scenarios the default values should work fine, and you should not need to override them.
	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_path</span></dt><dd><div class="para">
					Full URL to your installation as seen from the web browser.
				</div><div class="para">
					This is what users type into the URL field, e.g. <code class="literal">https://www.example.com/mantisbt/</code>. Requires trailing `/`.
				</div><div class="para">
					If not set, MantisBT will default this to a working URL valid for most installations. However, in some cases (typically when an installation can be accessed by multiple URLs, e.g. internal vs external), it might be necessary to override the default.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						The default is built based on headers from the HTTP request. This is a potential security risk, as the system will be exposed to <a href="https://owasp.org/www-project-web-security-testing-guide/stable/4-Web_Application_Security_Testing/07-Input_Validation_Testing/17-Testing_for_Host_Header_Injection">Host Header injection</a> attacks, so it is strongly recommended to initialize this in config_inc.php.
					</div></div></div></dd><dt><span class="term">$g_short_path</span></dt><dd><div class="para">
					Short web path without the domain name. This requires the trailing '/'.
				</div></dd><dt><span class="term">$g_absolute_path</span></dt><dd><div class="para">
					This is the absolute file system path to the MantisBT installation, it is defaulted to the directory where config_defaults_inc.php resides. Requires trailing '/' character (eg. '/usr/apache/htdocs/mantisbt/').
				</div></dd><dt><span class="term">$g_core_path</span></dt><dd><div class="para">
					This is the path to the core directory of your installation. The default value is usually OK but it is recommended that you move the 'core' directory out of your webroot. Requires trailing DIRECTORY_SEPARATOR character.
				</div></dd><dt><span class="term">$g_class_path</span></dt><dd><div class="para">
					This is the path to the classes directory which is a sub-directory of core by default. The default value is typically OK. Requires trailing DIRECTORY_SEPARATOR. character.
				</div></dd><dt><span class="term">$g_library_path</span></dt><dd><div class="para">
					This is the path to the library directory of your installation. The default value is usually OK but it is recommended that you move the 'library' directory out of your webroot. Requires trailing DIRECTORY_SEPARATOR character.
				</div></dd><dt><span class="term">$g_vendor_path</span></dt><dd><div class="para">
					Path to vendor folder for 3rd party libraries. Requires trailing DIRECTORY_SEPARATOR character.
				</div></dd><dt><span class="term">$g_language_path</span></dt><dd><div class="para">
					This is the path to the language directory of your installation. The default value is usually OK but it is recommended that you move the 'language' directory out of your webroot. Requires trailing DIRECTORY_SEPARATOR character.
				</div></dd><dt><span class="term">$g_manual_url</span></dt><dd><div class="para">
					This is the url to the MantisBT online manual. Requires trailing '/' character.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.webserver">
      ⁠</a>5.4. Webserver</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_session_save_path</span></dt><dd><div class="para">
					Location where session files are stored. The default is <span class="emphasis"><em>false</em></span>, meaning the session handler's default location will be used.
				</div></dd><dt><span class="term">$g_session_validation</span></dt><dd><div class="para">
					Use Session validation (defaults to <span class="emphasis"><em>ON</em></span>)
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						Disabling this could be a potential security risk !
					</div></div></div></dd><dt><span class="term">$g_form_security_validation</span></dt><dd><div class="para">
					Form security validation, defaults to <span class="emphasis"><em>ON</em></span>. This protects against <a href="https://en.wikipedia.org/wiki/Cross-site_request_forgery"> Cross-Site Request Forgery</a>. Some proxy servers may not correctly work with this option enabled because they cache pages incorrectly.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						Disabling this option is a security risk, it is strongly recommended to leave it ON
					</div></div></div></dd><dt><span class="term">$g_custom_headers</span></dt><dd><div class="para">
					An array of custom headers to be sent with each page.
				</div><div class="para">
					For example, to allow your MantisBT installation to be viewed in a frame in IE6 when the frameset is not at the same hostname as the MantisBT install, you need to add a P3P header. You could try something like 
<pre class="programlisting">
$g_custom_headers = array( 'P3P: CP="CUR ADM"' );
</pre>
					 in your config file, but make sure to check that your policy actually matches with what you are promising. See <a href="http://msdn.microsoft.com/en-us/library/ms537343.aspx"> MSDN</a> for more information.
				</div><div class="para">
					Even though it is not recommended, you could also use this setting to disable previously sent headers. For example, assuming you didn't want to benefit from Content Security Policy (CSP), you could set: 
<pre class="programlisting">
$g_custom_headers = array( 'Content-Security-Policy:' );
</pre>

				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						Disabling CSP is a security risk, it is strongly recommended that you leave it as Mantis defines it.
					</div></div></div></dd><dt><span class="term">$g_logout_redirect_page</span></dt><dd><div class="para">
					Specify where the user should be sent after logging out.
				</div></dd><dt><span class="term">$g_allow_browser_cache</span></dt><dd><div class="para">
					This will allow the browser to cache all pages. The upside will be better performance, but there may be cases where obsolete information is displayed. Note that this will be bypassed (and caching is allowed) for the bug report pages.
				</div></dd><dt><span class="term">$g_allow_file_cache</span></dt><dd><div class="para">
					This will allow the browser to cache downloaded files. Without this set, there may be issues with IE receiving files, and launching support programs.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.settings">
      ⁠</a>5.5. Configuration Settings</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_global_settings</span></dt><dd><div class="para">
					This option contains the list of configuration options that are used to determine if it is allowed for a specific configuration option to be saved to or loaded from the database. Configuration options that are in the list are considered global only and hence are only configurable via the config_inc.php file and defaulted by config_defaults_inc.php file.
				</div></dd><dt><span class="term">$g_public_config_names</span></dt><dd><div class="para">
					This option contains a list of configuration options that can be queried via SOAP API.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.security">
      ⁠</a>5.6. Security and Cryptography</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">Content Security Policy</span></dt><dd><div class="para">
					Amongst other things, MantisBT relies on <a href="https://en.wikipedia.org/wiki/Content_Security_Policy"> Content Security Policy</a> (CSP), which is a <a href="https://www.w3.org/TR/CSP/"> W3C candidate recommendation</a> improving the system's security against <a href="https://en.wikipedia.org/wiki/Cross-site_scripting"> cross-site scripting (XSS)</a> and other, similar types of attacks. It is currently supported in <a href="https://caniuse.com/#feat=contentsecuritypolicy"> recent versions of many browsers</a>.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						CSP may cause issues in certain situations (e.g. during development), or when using plugins relying on externally hosted resources such as images or scripts.
					</div></div></div><div class="para">
					MantisBT currently does not provide any mechanism for plugins to notify the Core of 'safe' external domains. Because of that, even though it is not recommended for obvious security reasons, you may wish to disable CSP. You can do so by specifying a <span class="emphasis"><em>Custom Header</em></span> in your <code class="literal">config_inc.php</code> file (see <a class="xref" href="#admin.config.webserver">Section 5.4, “Webserver”</a>).
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						Disabling Content Security Policy is a security risk !
					</div></div></div></dd><dt><span class="term">$g_crypto_master_salt</span></dt><dd><div class="para">
					Master salt value used for cryptographic hashing throughout MantisBT. This value must be kept secret at all costs. You must generate a unique and random salt value for each installation of MantisBT you control. The minimum length of this string must be at least 16 characters.
				</div><div class="para">
					The value you select for this salt should be a long string generated using a secure random number generator. An example for Linux systems is:
				</div><pre class="programlisting">
cat /dev/urandom | head -c 64 | base64
</pre><div class="para">
					Note that the number of bits of entropy per byte of output from /dev/urandom is not 8. If you're particularly paranoid and don't mind waiting a long time, you could use /dev/random to get much closer to 8 bits of entropy per byte. Moving the mouse (if possible) while generating entropy via /dev/random will greatly improve the speed at which /dev/random produces entropy.
				</div><div class="para">
					This setting is blank by default. MantisBT will not operate in this state. Hence you are forced to change the value of this configuration option.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						This configuration option has a profound impact on the security of your MantisBT installation. Failure to set this configuration option correctly could lead to your MantisBT installation being compromised. Ensure that this value remains secret. Treat it with the same security that you'd treat the password to your MantisDB database.
					</div></div></div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.signup">
      ⁠</a>5.7. Signup and Lost Password</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_allow_signup</span></dt><dd><div class="para">
					Allow users to signup for their own accounts.
				</div><div class="para">
					If ON (default), then $g_send_reset_password must be ON as well, and mail settings must be correctly configured (see <a class="xref" href="#admin.config.email">Section 5.8, “Email”</a>).
				</div></dd><dt><span class="term">$g_max_failed_login_count</span></dt><dd><div class="para">
					Maximum number of failed login attempts before the user's account is locked. Once locked, it is required to reset the password (lost password). The counter is reset to zero after each successful login.
				</div><div class="para">
					Default is set to 5, in order to prevent brute force attacks attempting to gain access to end users accounts. Set to OFF to disable this feature and allow unlimited failed login attempts.
				</div></dd><dt><span class="term">$g_notify_new_user_created_threshold_min</span></dt><dd><div class="para">
					The minimum global access level required to be notified when a new user registers via the "signup form". To pick specific access levels that are not necessarily at the higher end of access levels, use an array of access levels. Default is ADMINISTRATOR.
				</div></dd><dt><span class="term">$g_send_reset_password</span></dt><dd><div class="para">
					If ON (default), users will be sent their password when their account is created or password reset (this requires mail settings to be correctly configured).
				</div><div class="para">
					If OFF, then the Administrator will have to provide a password when creating new accounts, and the password will be set to blank when reset.
				</div></dd><dt><span class="term">$g_signup_use_captcha</span></dt><dd><div class="para">
					Use captcha image to validate subscription it requires GD library installed.
				</div></dd><dt><span class="term">$g_system_font_folder</span></dt><dd><div class="para">
					Absolute path (with trailing slash!) to folder which contains your TrueType-Font files used for the Relationship Graphs, and the Workflow Graphs.
				</div></dd><dt><span class="term">$g_lost_password_feature</span></dt><dd><div class="para">
					Setting to disable the 'lost your password' feature.
				</div></dd><dt><span class="term">$g_max_lost_password_in_progress_count</span></dt><dd><div class="para">
					Max. simultaneous requests of 'lost password'. When this value is reached, it's no longer possible to request new password reset. Value resets to zero at each successfully login.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.email">
      ⁠</a>5.8. Email</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_webmaster_email</span></dt><dd><div class="para">
					The webmaster's e-mail address. This address is displayed in the bottom of all MantisBT pages. <EMAIL>
				</div></dd><dt><span class="term">$g_from_email</span></dt><dd><div class="para">
					The email address to be used as the source of all emails sent by MantisBT. <EMAIL>
				</div></dd><dt><span class="term">$g_from_name</span></dt><dd><div class="para">
					The sender name of all emails sent by MantisBT. Mantis Bug Tracker
				</div></dd><dt><span class="term">$g_return_path_email</span></dt><dd><div class="para">
					Email address to receive bounced emails.
				</div></dd><dt><span class="term">$g_enable_email_notification</span></dt><dd><div class="para">
					Set to ON to enable email notifications, OFF to disable them. Default is ON. Note that disabling email notifications has no effect on emails generated as part of the user signup process. When set to OFF, the password reset feature is disabled. Additionally, notifications of administrators updating accounts are not sent to users.
				</div></dd><dt><span class="term">$g_email_notifications_verbose</span></dt><dd><div class="para">
					When enabled, the email notifications will include the full issue with a hint about the change type at the top, rather than using dedicated notifications that are focused on what changed. This change can be overridden in the database per user. Default is OFF.
				</div></dd><dt><span class="term">$g_default_notify_flags</span></dt><dd><div class="para">
					Associates a default notification flag with each action, to control who should be notified. The default will be used if the action is not defined in <span class="emphasis"><em>$g_notify_flags</em></span> or if the flag is not included in the specific action definition.
				</div><div class="para">
					The list of actions include: <span class="emphasis"><em>new</em></span>, <span class="emphasis"><em>assigned</em></span>, <span class="emphasis"><em>resolved</em></span>, <span class="emphasis"><em>bugnote</em></span>, <span class="emphasis"><em>reopened</em></span>, <span class="emphasis"><em>closed</em></span>, <span class="emphasis"><em>deleted</em></span>, <span class="emphasis"><em>feedback</em></span>.
				</div><div class="para">
					The default is: 
<pre class="programlisting">
$g_default_notify_flags = array(
	'reporter'      =&gt; ON,
	'handler'       =&gt; ON,
	'monitor'       =&gt; ON,
	'bugnotes'      =&gt; ON,
	'category'      =&gt; ON,
	'explicit'      =&gt; ON,
	'threshold_min' =&gt; NOBODY,
	'threshold_max' =&gt; NOBODY
);
</pre>
					 <span class="emphasis"><em>threshold_min</em></span> and <span class="emphasis"><em>threshold_max</em></span> are used to send messages to all members of the project whose status is 
					<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								greater than or equal to <span class="emphasis"><em>threshold_min</em></span>, and
							</div></li><li class="listitem"><div class="para">
								less than or equal to <span class="emphasis"><em>threshold_max</em></span>.
							</div></li></ul></div>

				</div><div class="para">
					Sending messages to everyone would set <span class="emphasis"><em>threshold_min</em></span> to ANYBODY and <span class="emphasis"><em>threshold_max</em></span> to NOBODY. To send to all DEVELOPERS and above, use DEVELOPER and NOBODY respectively.
				</div></dd><dt><span class="term">$g_notify_flags</span></dt><dd><div class="para">
					Defines the specific notification flags when they are different from the defaults defined in <span class="emphasis"><em>$g_default_notify_flags</em></span>.
				</div><div class="para">
					For example, the following code overrides the default by disabling notifications to bugnote authors and users monitoring the bug when submitting a new bug: 
<pre class="programlisting">
$g_notify_flags['new'] = array(
	'bugnotes' =&gt; OFF,
	'monitor' =&gt; OFF,
);
</pre>
					 See <a class="xref" href="#admin.customize.email">Section 7.4, “Email Notifications”</a> for further examples of customizing the notification flags.
				</div><div class="para">
					Available actions include: 
					<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								<span class="emphasis"><em>new</em></span>: a new bug has been added
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>reopened</em></span>: the bug has been reopened
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>deleted</em></span>: a bug has been deleted
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>owner</em></span>: the bug has been assigned a new owner
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>bugnote</em></span>: a bugnote has been added to a bug
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>sponsor</em></span>: the sponsorship for the bug has changed (added, deleted or updated)
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>relation</em></span>: a relationship for the bug has changed (added, deleted or updated)
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>monitor</em></span>: a user is added to the monitor list.
							</div></li></ul></div>
					 In addition, an action can match the bug status in <span class="emphasis"><em>$g_status_enum_string</em></span>. Note that spaces in the string are replaced with underscores ('_') when creating the action. Thus, using the defaults, 'feedback' would be a valid action.
				</div></dd><dt><span class="term">$g_email_receive_own</span></dt><dd><div class="para">
					This defines whether users should receive emails for their own actions. This option is defaulted to OFF, hence, users do not receive email notification for their own actions. This can be a source for confusions for users upgrading from MantisBT 0.17.x versions, since in these versions users used to get notified of their own actions.
				</div></dd><dt><span class="term">$g_validate_email</span></dt><dd><div class="para">
					Determines whether email addresses are validated.
				</div><div class="para">
					When ON (default), validation is performed using the pattern given by the <a href="https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address"> HTML5 specification for <span class="emphasis"><em>email</em></span> type form input elements</a>. When OFF, validation is disabled.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						Regardless of how this option is set, validation is never performed when using LDAP email (i.e. when $g_use_ldap_email = ON, see <a class="xref" href="#admin.config.auth.ldap">Section 5.21.2, “LDAP authentication method parameters”</a>), as we assume that it is handled by the directory.
					</div></div></div></dd><dt><span class="term">$g_check_mx_record</span></dt><dd><div class="para">
					Set to OFF to disable email checking. Default is OFF.
				</div></dd><dt><span class="term">$g_allow_blank_email</span></dt><dd><div class="para">
					If ON, allows the user to omit an email address field. If you allow users to create their own accounts, they must specify an email at that point, no matter what the value of this option is. Otherwise they wouldn't get their passwords.
				</div><div class="para">
					Administrators are able to bypass this check to enable them to create special accounts like anonymous access and other service accounts that don't need notifications.
				</div></dd><dt><span class="term">$g_email_login_enabled</span></dt><dd><div class="para">
					Enable support for logging in by email and password, in addition to username and password. This will only work as long as there is a single user with the specified email address and the email address is not blank. The default value is OFF.
				</div></dd><dt><span class="term">$g_email_ensure_unique</span></dt><dd><div class="para">
					When enabled, the uniqueness of email addresses will be enforced for new users as well as updates to existing ones. Default is ON.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						When this setting changes from OFF to ON (which will de facto occur when upgrading to MantisBT 1.3.0 or later from an older version), there could be existing user accounts sharing the same email address.
					</div><div class="para">
						It important that such duplicates are identified and fixed, to avoid unexpected and unpredictable behavior when looking up users with their email address, as the system expects them to be unique.
					</div><div class="para">
						To facilitate this task, the <span class="emphasis"><em>Administration Checks</em></span> will detect duplicate email addresses and identify the related user accounts. A warning will also be displayed in the Manage Users page (see <a class="xref" href="#admin.pages.manage.users">Section 6.8.1, “Users”</a>) and when editing a user account whose email address is associated with one or more other accounts.
					</div></div></div></dd><dt><span class="term">$g_limit_email_domains</span></dt><dd><div class="para">
					Only allow and send email to addresses in the given domain(s). This is useful as a security feature and it is also useful in cases like Sourceforge where its servers are limited to only sending emails to SourceForge email addresses in order to avoid spam. $g_limit_email_domains = array( 'users.sourceforge.net', 'sourceforge.net' );
				</div></dd><dt><span class="term">$g_show_user_email_threshold</span></dt><dd><div class="para">
					This specifies the access level that is needed to have user names hyperlinked with mailto: links. The default value is NOBODY, hence, even administrators won't have this feature enabled.
				</div></dd><dt><span class="term">$g_show_user_realname_threshold</span></dt><dd><div class="para">
					This specifies the access level that is needed to see realnames on user view page. The default value is NOBODY, hence, even administrators won't have this feature enabled.
				</div></dd><dt><span class="term">$g_phpMailer_method</span></dt><dd><div class="para">
					Select the method to send mail: 
					<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								<span class="emphasis"><em>PHPMAILER_METHOD_MAIL</em></span> for use of mail() function,
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>PHPMAILER_METHOD_SENDMAIL</em></span> for sendmail (or postfix),
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>PHPMAILER_METHOD_SMTP</em></span> for SMTP,
							</div></li></ul></div>
					 Default is PHPMAILER_METHOD_MAIL.
				</div></dd><dt><span class="term">$g_smtp_host</span></dt><dd><div class="para">
					This option specifies the SMTP server to submit messages to. The SMTP server (MTA) then takes on the responsibility of delivering messages to their final destinations.
				</div><div class="para">
					To use the local SMTP (if available) set this to 'localhost', otherwise use the fully qualified domain name of the remote SMTP server.
				</div><div class="para">
					It can be either a single hostname, or multiple semicolon-delimited hostnames. You can specify for each host a port other than the default, using format: <span class="emphasis"><em>hostname:port</em></span> (e.g. "smtp1.example.com:25;smtp2.example.com").
				</div><div class="para">
					Hosts will be tried in the given order.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						This is only used with <span class="emphasis"><em>PHPMAILER_METHOD_SMTP</em></span> (see $g_phpmailer_method).
					</div></div></div><div class="para">
					The default is 'localhost'.
				</div></dd><dt><span class="term">$g_smtp_port</span></dt><dd><div class="para">
					The default SMTP port to use. This can be overridden individually for specific hosts. (see $g_smtp_host).
				</div><div class="para">
					Typical SMTP ports are 25 and 587.
				</div><div class="para">
					The default is 25.
				</div></dd><dt><span class="term">$g_smtp_connection_mode</span></dt><dd><div class="para">
					Allow secure connection to the SMTP server. Valid values are: 
					<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								<span class="emphasis"><em>'' (empty string)</em></span>: No encryption. This is the default.
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>ssl</em></span>
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>tls</em></span>
							</div></li></ul></div>

				</div></dd><dt><span class="term">$g_smtp_username</span></dt><dd><div class="para">
					SMTP Server Authentication user
				</div><div class="para">
					Allows the use of SMTP Authentication when using a remote SMTP host.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						must be set to '' (empty string) if the SMTP host does not require authentication.
					</div></div></div><div class="para">
					Default is ''.
				</div></dd><dt><span class="term">$g_smtp_password</span></dt><dd><div class="para">
					This is the password that is used in SMTP Authentication. Not used when $g_smtp_username = ''
				</div><div class="para">
					Default is ''.
				</div></dd><dt><span class="term">$g_email_retry_in_days</span></dt><dd><div class="para">
					Duration (in days) to retry failed emails before deleting them from queue. Default 7 days.
				</div></dd><dt><span class="term">$g_email_send_using_cronjob</span></dt><dd><div class="para">
					Disables sending of emails as soon as an action is performed. Emails are instead queued and must be sent by running scripts/send_emails.php periodically. This script can only be executed from the CLI, not from the web interface, for security reasons.
				</div><div class="para">
					Enabling this option can help with performance problems if large numbers of emails are generated or mail delivery is slow by not delaying page execution when sending emails.
				</div></dd><dt><span class="term">$g_email_separator1</span></dt><dd><div class="para">
					Default is str_pad('', 70, '='); This means 70 equal signs.
				</div></dd><dt><span class="term">$g_email_separator2</span></dt><dd><div class="para">
					Default is str_pad('', 70, '-'); This means 70 minus signs.
				</div></dd><dt><span class="term">$g_email_padding_length</span></dt><dd><div class="para">
					Default is 28.
				</div></dd></dl></div><div class="para">
		MantisBT uses flags and a threshold system to generate emails on events. For each new event, email is sent to: 
		<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
					the reporter, qualified by the notify flag 'reporter' below
				</div></li><li class="listitem"><div class="para">
					the handler (or Assigned to), qualified by the notify flag 'handler' below
				</div></li><li class="listitem"><div class="para">
					anyone monitoring the bug, qualified by the notify flag 'monitor' below
				</div></li><li class="listitem"><div class="para">
					anyone who has ever added a bugnote the bug, qualified by the notify flag 'bugnotes' below
				</div></li><li class="listitem"><div class="para">
					anyone assigned to the project whose access level is greater than or equal to the notify flag 'threshold_min' and less than or equal to the notify flag 'threshold_max' below
				</div></li></ul></div>

	</div><div class="para">
		From this list, those recipients who meet the following criteria are eliminated: 
		<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
					the originator of the change, if $g_email_receive_own is OFF
				</div></li><li class="listitem"><div class="para">
					the recipient either no longer exists, or is disabled
				</div></li><li class="listitem"><div class="para">
					the recipient has turned their email_on_&lt;new status&gt; preference OFF
				</div></li><li class="listitem"><div class="para">
					the recipient has no email address entered
				</div></li></ul></div>

	</div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.email.dkim">
      ⁠</a>5.8.1. DKIM signature</h3></div></div></div><div class="para">
			In order to setup <a href="https://en.wikipedia.org/wiki/DomainKeys_Identified_Mail">DomainKeys Identified Mail (DKIM) Signatures</a> (as defined in <a href="https://tools.ietf.org/html/rfc6376">RFC 6376</a>), you need to enable the feature (see <span class="emphasis"><em>$g_email_dkim_enable</em></span>), and provide at least:
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
					Domain (see <span class="emphasis"><em>$g_email_dkim_domain</em></span>),
				</div></li><li class="listitem"><div class="para">
					Private key or key file path (see <span class="emphasis"><em>$g_email_dkim_private_key_file_path</em></span> and <span class="emphasis"><em>$g_email_dkim_private_key_string</em></span>),
				</div></li><li class="listitem"><div class="para">
					Selector (see <span class="emphasis"><em>$g_email_dkim_selector</em></span>),
				</div></li><li class="listitem"><div class="para">
					Identity (see <span class="emphasis"><em>$g_email_dkim_identity</em></span>).
				</div></li></ul></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_email_dkim_enable</span></dt><dd><div class="para">
						Enables DomainKeys Identified Mail (DKIM).
					</div><div class="para">
						The default is OFF.
					</div></dd><dt><span class="term">$g_email_dkim_domain</span></dt><dd><div class="para">
						Defines the domain for DKIM Signatures.
					</div><div class="para">
						This is typically same as the host part of the $g_from_email. For example <span class="emphasis"><em>example.com</em></span>.
					</div></dd><dt><span class="term">$g_email_dkim_private_key_file_path</span></dt><dd><div class="para">
						Path to the private domain key to be used for DKIM Signatures.
					</div><div class="para">
						If the key is specified in $g_email_dkim_private_key_string this setting will not be used.
					</div></dd><dt><span class="term">$g_email_dkim_private_key_string</span></dt><dd><div class="para">
						Private domain key to be used for DKIM Signatures.
					</div><div class="para">
						This string should contain private key for signing. Leave empty string if you wish to load the key from the file defined with $g_email_dkim_private_key_file_path.
					</div></dd><dt><span class="term">$g_email_dkim_selector</span></dt><dd><div class="para">
						Selector to be used for DKIM Signatures.
					</div><div class="para">
						If your domain is example.com, typically DNS TXT field should have: <span class="emphasis"><em>host: mail.example._domainkey</em></span>, <span class="emphasis"><em>value: v=DKIM1; t=s; n=core; k=rsa; p=[public key]</em></span>. In this case selector should be mail.example
					</div></dd><dt><span class="term">$g_email_dkim_passphrase</span></dt><dd><div class="para">
						Private DKIM domain key password.
					</div><div class="para">
						Leave empty string if your private key does not have password
					</div></dd><dt><span class="term">$g_email_dkim_identity</span></dt><dd><div class="para">
						Identity to be used for DomainKeys Identified Mail (DKIM) Signatures.
					</div><div class="para">
						This is usually the same as <span class="emphasis"><em>$g_from_email</em></span>. For example, <EMAIL>
					</div></dd></dl></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.email.smime">
      ⁠</a>5.8.2. S/MIME signature</h3></div></div></div><div class="para">
			This sections describes the necessary settings to enable <a href="https://en.wikipedia.org/wiki/S/MIME">S/MIME</a> signature for outgoing MantisBT e-mails.
		</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_email_smime_enable</span></dt><dd><div class="para">
						Enables S/MIME signature.
					</div><div class="para">
						Defaults to OFF.
					</div></dd><dt><span class="term">$g_email_smime_cert_file</span></dt><dd><div class="para">
						Path to the S/MIME certificate.
					</div><div class="para">
						The file must contain a <a href="https://en.wikipedia.org/wiki/Privacy-Enhanced_Mail">PEM-encoded</a> certificate.
					</div></dd><dt><span class="term">$g_email_smime_key_file</span></dt><dd><div class="para">
						Path to the S/MIME private key file.
					</div><div class="para">
						The file must contain a PEM-encoded private key matching the S/MIME certificate.
					</div></dd><dt><span class="term">$g_email_smime_key_password</span></dt><dd><div class="para">
						Password for the S/MIME private key.
					</div><div class="para">
						Leave blank if the private key is not protected by a passphrase.
					</div></dd><dt><span class="term">$g_email_smime_extracerts_file</span></dt><dd><div class="para">
						Optional path to S/MIME extra certificates.
					</div><div class="para">
						The file must contain one (or more) PEM-encoded certificates, which will be included in the signature to help the recipient verify the certificate specified in <span class="emphasis"><em>$g_email_smime_cert_file</em></span> ("CA Chain").
					</div></dd></dl></div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
				MantisBT expects the S/MIME certificates and the private key files to be in <a href="https://en.wikipedia.org/wiki/Privacy-Enhanced_Mail">PEM</a> format. If you have a <a href="https://en.wikipedia.org/wiki/PKCS_12">PKCS12</a> encrypted certificate (typically with a .pfx or .p12 extension), you may use the following <code class="literal">openssl</code> commands to extract and convert the individual elements:
			</div><div class="itemizedlist"><ul><li class="listitem"><div class="para">
						Certificate
					</div><pre class="programlisting">
openssl pkcs12 -in cert.pfx -clcerts -nokeys -out cert.crt
</pre></li><li class="listitem"><div class="para">
						Extra certificates ("CA chain")
					</div><pre class="programlisting">
openssl pkcs12 -in cert.pfx -cacerts -nokeys -out ca-chain.crt
</pre></li><li class="listitem"><div class="para">
						Private key (<code class="literal">-passout</code> specifies the private key's password)
					</div><pre class="programlisting">
openssl pkcs12 -in cert.pfx -nocerts -out cert.key -passout pass:
</pre></li></ul></div><div class="para">
				If the input file is protected, openssl will ask for the password; alternatively, you can specify it on the command-line with the <span class="emphasis"><em>-passin</em></span> option, e.g. <code class="literal">-passin pass:PASSWORD</code>
			</div></div></div></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.version">
      ⁠</a>5.9. Version</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_show_version</span></dt><dd><div class="para">
					Display MantisBT Version number to users in the page footer.
				</div><div class="para">
					This is more of a cosmetic setting and should NOT be considered as a security measure to avoid disclosure of version information to users. Default is OFF.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						When the REST API is enabled (see <a class="xref" href="#admin.config.api">Section 5.38, “API”</a>), accessing an endpoint will always return the version number in the <code class="literal">X-Mantis-Version</code> header, even if the request fails.
					</div></div></div></dd><dt><span class="term">$g_version_suffix</span></dt><dd><div class="para">
					String appended to the MantisBT version when displayed to the user. Default is ''.
				</div></dd><dt><span class="term">$g_copyright_statement</span></dt><dd><div class="para">
					Custom copyright and licensing statement shown at the footer of each page.
				</div><div class="para">
					Can contain HTML elements that are valid children of the <code class="literal">&lt;address&gt;</code> element. This string is treated as raw HTML and thus you must use <code class="literal">&amp;amp;</code> instead of <code class="literal">&amp;</code>. Default is ''.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.language">
      ⁠</a>5.10. Language</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_default_language</span></dt><dd><div class="para">
					This is the language used by default in MantisBT. This may be set to 'auto' where MantisBT will try to determine the language from the browser.
				</div></dd><dt><span class="term">$g_language_choices_arr</span></dt><dd><div class="para">
					This is to be set to an array of languages that are available for users to choose from. The default value includes all languages supported by MantisBT. The administrator can limit the languages available for users to choose from by overriding this value. For example, to support English, French and German include the following code: 
<pre class="programlisting">
$g_language_choices_arr = array( 'english', 'french', 'german' );
</pre>
					 Of course, administrators can also add their own languages by translating the strings and creating their own language files. You are encouraged to share any translation work that you do with the MantisBT team. This will ensure that the newly created language file is maintained with future MantisBT releases.All language files reside in the lang/ folder. They are all named according to the following pattern: strings_&lt;language&gt;.txt.
				</div></dd><dt><span class="term">$g_language_auto_map</span></dt><dd><div class="para">
					Browser language mapping for 'auto' language selection
				</div></dd><dt><span class="term">$g_fallback_language</span></dt><dd><div class="para">
					This is the language used if MantisBT cannot determine the language from the browser. It defaults to 'english'.As of 0.19.0, this may be set to 'auto' where MantisBT will try to determine the language from the browser.
				</div></dd></dl></div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
			If a string does not exist in the active language, the English string is used instead.
		</div></div></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.display">
      ⁠</a>5.11. Display</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_font_family</span></dt><dd><div class="para">
					Name of the google font family for the browser to use. For all available fonts, see: <a href="https://fonts.google.com/"> fonts.google.com </a>.
				</div></dd><dt><span class="term">$g_font_family_choices</span></dt><dd><div class="para">
					Google font family list offered to the user to chose from. Font files are fetched from google servers.
				</div></dd><dt><span class="term">$g_font_family_choices_local</span></dt><dd><div class="para">
					This is a small subset of <span class="emphasis"><em>$g_font_family_choices</em></span> in which font files are part of MantisBT installation.
				</div></dd><dt><span class="term">$g_window_title</span></dt><dd><div class="para">
					This is the browser window title (&lt;TITLE&gt; tag).
				</div></dd><dt><span class="term">$g_search_title</span></dt><dd><div class="para">
					This is used as prefix to describe Browser Search entries, and must be short enough so that when inserted into the 'opensearch_XXX_short' language string, the resulting text is 16 characters or less, to be compliant with the limit for the ShortName element as defined in the <a href="https://github.com/dewitt/opensearch/blob/master/opensearch-1-1-draft-6.md"> OpenSearch specification </a>.
				</div><div class="para">
					Defaults to the value of $g_window_title.
				</div></dd><dt><span class="term">$g_admin_checks</span></dt><dd><div class="para">
					Check for admin directory, database upgrades, etc. It defaults to ON.
				</div></dd><dt><span class="term">$g_favicon_image</span></dt><dd><div class="para">
					Path to the favorites icon relative to MantisBT root folder This icon should be of <span class="emphasis"><em>image/x-icon</em></span> MIME type, and its size 16x16 pixels. It is also used to decorate OpenSearch Browser search entries. (default 'images/favicon.ico').
				</div></dd><dt><span class="term">$g_logo_image</span></dt><dd><div class="para">
					Path to the logo image relative to MantisBT root folder (default 'images/mantis_logo.gif').
				</div></dd><dt><span class="term">$g_logo_url</span></dt><dd><div class="para">
					The default URL to be associated with the logo. By default this is set to $g_default_home_page (which defaults to My View page). Clicking on the logo from any page in the bug tracker will navigate to the URL specified in this configuration option.
				</div></dd><dt><span class="term">$g_show_project_menu_bar</span></dt><dd><div class="para">
					This option specifies whether to add menu at the top of the page which includes links to all the projects. The default value is OFF.
				</div></dd><dt><span class="term">$g_show_assigned_names</span></dt><dd><div class="para">
					When a bug is assigned then replace the word "assigned" with the name of the developer in parenthesis. Default is ON.
				</div></dd><dt><span class="term">$g_show_priority_text</span></dt><dd><div class="para">
					Specifies whether to show priority as text (ON) or icon (OFF) in the view all bugs page. Default is OFF (icon).
				</div></dd><dt><span class="term">$g_priority_significant_threshold</span></dt><dd><div class="para">
					Define the priority level at which a bug becomes significant. Significant bugs are displayed with emphasis. Set this value to -1 to disable the feature. The default value is HIGH.
				</div></dd><dt><span class="term">$g_severity_significant_threshold</span></dt><dd><div class="para">
					Define the severity level at which a bug becomes significant. Significant bugs are displayed with emphasis. Set this value to -1 to disable the feature. The default value is MAJOR.
				</div></dd><dt><span class="term">$g_view_issues_page_columns</span></dt><dd><div class="para">
					This configuration option is used to set the columns to be included in the <span class="emphasis"><em>View Issues page</em></span>, and the order in which they will be displayed.
				</div><div class="para">
					This can be overridden using <span class="emphasis"><em>Manage &gt; Manage Configuration &gt; Manage Columns</em></span>; users can also configure their own columns using <span class="emphasis"><em>My Account &gt; Manage Columns</em></span>.
				</div><div class="para">
					The list of all available columns (i.e. the names to choose from) can be retrieved from the above-mentioned pages. In addition to standard column names, that will also include:
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							Custom Fields: the column name will be the Custom Field's name prefixed with <code class="literal">custom_</code>, e.g. <span class="emphasis"><em>xyz</em></span> should be included as <code class="literal">custom_xyz</code>.
						</div></li><li class="listitem"><div class="para">
							Plugin-specific columns (prefixed with the Plugin's basename)
						</div></li></ul></div><div class="para">
					If one of the columns specified here is not accessible to the logged-in user or corresponds to a disabled feature, then it will automatically be removed from the list at runtime. The same configuration may therefore show a different set of columns depending on the logged in user, the currently selected project and enabled features.
				</div><div class="para">
					For example, the <code class="literal">eta</code> column will only be shown if usage of the ETA field is enabled (see $g_enable_eta in <a class="xref" href="#admin.config.fields">Section 5.35, “Field Visibility”</a>), and the <code class="literal">custom_xyz</code> column will be removed if the <span class="emphasis"><em>xyz</em></span> Custom Field is not available in the current Project.
				</div><div class="para">
					By default the following columns are selected: selection, edit, priority, id, bugnotes_count, attachment_count, category_id, severity, status, last_updated, summary.
				</div></dd><dt><span class="term">$g_print_issues_page_columns</span></dt><dd><div class="para">
					This configuration option is used to set the columns to be included in the <span class="emphasis"><em>Print Issues page</em></span>, and the order in which they will be displayed.
				</div><div class="para">
					See $g_view_issues_page_columns for details.
				</div><div class="para">
					By default the following columns are selected: selection, priority, id, bugnotes_count, attachment_count, category_id, severity, status, last_updated, summary.
				</div></dd><dt><span class="term">$g_csv_columns</span></dt><dd><div class="para">
					This configuration option is used to set the columns to be included in <span class="emphasis"><em>CSV exports</em></span>, and the order in which they will be displayed.
				</div><div class="para">
					See $g_view_issues_page_columns for details.
				</div><div class="para">
					By default the following columns are selected: id, project_id, reporter_id, handler_id, priority, severity, reproducibility, version, build, projection, category_id, date_submitted, eta, os, os_build, platform, view_state, last_updated, summary, status, resolution, fixed_in_version.
				</div></dd><dt><span class="term">$g_excel_columns</span></dt><dd><div class="para">
					This configuration option is used to set the columns to be included in <span class="emphasis"><em>Excel exports</em></span>, and the order in which they will be displayed.
				</div><div class="para">
					See $g_view_issues_page_columns for details.
				</div><div class="para">
					By default the following columns are selected: id, project_id, reporter_id, handler_id, priority, severity, reproducibility, version, build, projection, category_id, date_submitted, eta, os, os_build, platform, view_state, last_updated, summary, status, resolution, fixed_in_version.
				</div></dd><dt><span class="term">$g_show_bug_project_links</span></dt><dd><div class="para">
					Show project links when in All Projects mode. Default is ON.
				</div></dd><dt><span class="term">$g_filter_position</span></dt><dd><div class="para">
					Position of the filter box, can be: POSITION_* (POSITION_TOP, POSITION_BOTTOM, or POSITION_NONE for none). Default is FILTER_POSITION_TOP.
				</div></dd><dt><span class="term">$g_action_button_position</span></dt><dd><div class="para">
					Position of action buttons when viewing issues. Can be: POSITION_TOP, POSITION_BOTTOM, or POSITION_BOTH. Default is POSITION_BOTTOM.
				</div></dd><dt><span class="term">$g_show_product_version</span></dt><dd><div class="para">
					This controls display of the product version in the report, view, update and print issue pages. This flag also applies to other product version related fields like product build, fixed in version, and target version. Valid values are ON, OFF, and AUTO. ON for always displayed, AUTO for displayed when project has versions defined, and OFF for always OFF. The default value is AUTO.
				</div></dd><dt><span class="term">$g_show_version_dates_threshold</span></dt><dd><div class="para">
					The access level threshold at which users will see the date of release for product versions. Dates will be shown next to the product version, target version and fixed in version fields. Set this threshold to NOBODY to disable the feature. Default value is NOBODY.
				</div></dd><dt><span class="term">$g_show_realname</span></dt><dd><div class="para">
					This control will replace the user's userid with their realname. If it is set to ON, and the real name field has been populated, the replacement will occur. It defaults to OFF.
				</div></dd><dt><span class="term">$g_sort_by_last_name</span></dt><dd><div class="para">
					Sorting for names in dropdown lists. If turned on, "Jane Doe" will be sorted with the "D"s. It defaults to OFF.
				</div></dd><dt><span class="term">$g_show_avatar</span></dt><dd><div class="para">
					Show the users' avatar
				</div><div class="para">
					In addition to enabling this configuration option it is necessary to install an avatar plugin like the <a href="https://www.gravatar.com">Gravatar</a> plugin which is bundled out of the box.
				</div></dd><dt><span class="term">$g_show_avatar_threshold</span></dt><dd><div class="para">
					The threshold of users for which MantisBT should show the avatar (default DEVELOPER). Note that the threshold is related to the user for whom the avatar is being shown, rather than the user who is currently logged in.
				</div></dd><dt><span class="term">$g_show_changelog_dates</span></dt><dd><div class="para">
					Show release dates on changelog. It defaults to ON.
				</div></dd><dt><span class="term">$g_show_roadmap_dates</span></dt><dd><div class="para">
					Show release dates on roadmap. It defaults to ON.
				</div></dd><dt><span class="term">$g_status_colors</span></dt><dd><div class="para">
					Status color codes, using the Tango color palette.
				</div></dd><dt><span class="term">$g_display_bug_padding</span></dt><dd><div class="para">
					The padding level when displaying bug ids. The bug id will be padded with 0's up to the size given.
				</div></dd><dt><span class="term">$g_display_bugnote_padding</span></dt><dd><div class="para">
					The padding level when displaying bugnote ids. The bugnote id will be padded with 0's up to the size given.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.time">
      ⁠</a>5.12. Time</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_cookie_time_length</span></dt><dd><div class="para">
					Time for long lived cookie to live in seconds. It is also used as the default for permanent logins if $g_allow_permanent_cookie is enabled and selected. Default is 1 year.
				</div></dd><dt><span class="term">$g_allow_permanent_cookie</span></dt><dd><div class="para">
					Allow users to opt for a 'permanent' cookie when logging in. Controls the display of the 'Remember my login in this browser' checkbox on the login page. See $g_cookie_time_length.
				</div></dd><dt><span class="term">$g_wait_time</span></dt><dd><div class="para">
					Time to delay between page redirects (in seconds). Users can override this setting in their user preferences. Default is 2 seconds.
				</div></dd><dt><span class="term">$g_long_process_timeout</span></dt><dd><div class="para">
					This timeout is used by pages which does time consuming operations like upgrading the database. The default value of 0 disables timeout. Note that this timeout is specified in seconds.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.date">
      ⁠</a>5.13. Date</h2></div></div></div><div class="para">
		These variables control how the date is displayed. The default is <a href="https://en.wikipedia.org/wiki/ISO_8601">ISO 8601</a> formatting.
	</div><div class="para">
		Please refer to the <a href="https://www.php.net/manual/en/function.date.php#refsect1-function.date-parameters"> PHP manual </a> for details on available formatting options.
	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_short_date_format</span></dt><dd><div class="para">
					This format is used in the bug listing pages (eg: View Bugs). Default is <code class="literal">Y-m-d</code>.
				</div></dd><dt><span class="term">$g_normal_date_format</span></dt><dd><div class="para">
					This format is used in the view/update bug pages, bug notes, manage section, and news section. Default is <code class="literal">Y-m-d H:i</code>.
				</div></dd><dt><span class="term">$g_complete_date_format</span></dt><dd><div class="para">
					This format is used on the top of each page (current time) and the emails that are sent out. Default is <code class="literal">Y-m-d H:i T</code>.
				</div></dd><dt><span class="term">$g_datetime_picker_format</span></dt><dd><div class="para">
					This format is used with the datetime picker widget. Default is <code class="literal">Y-MM-DD HH:mm</code>.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						The formatting convention for the DateTime picker is different from the one used for the other date settings described above; see <a href="https://momentjs.com/docs/#/displaying/format/"> Moment.js documentation </a> for details.
					</div></div></div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						This format needs to match the one defined in <span class="emphasis"><em>$g_normal_date_format</em></span>. Inconsistencies between these two settings, e.g. using different date ordering (DMY, MDY or YMD) or displaying the month as a number vs a word or abbreviation, may result in unexpected behavior such as an invalid interpretation of the date by the DateTime picker widget, or errors trying to save a modified date.
					</div></div></div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.timezone">
      ⁠</a>5.14. Time Zone</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_default_timezone</span></dt><dd><div class="para">
					Default timezone to use in MantisBT. This configuration is normally initialized when installing Mantis. It should be set to one of the values specified in the <a href="https://www.php.net/timezones"> List of Supported Timezones</a>.
				</div><div class="para">
					If this config is left blank, the timezone will be initialized by calling function <a href="https://www.php.net/date-default-timezone-get"> date_default_timezone_get()</a>, which will fall back to <span class="emphasis"><em>UTC</em></span> if unable to determine the timezone.
				</div><div class="para">
					Correct configuration of this variable can be confirmed by running the administration checks. Users can override the default timezone under user their preferences.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.news">
      ⁠</a>5.15. News</h2></div></div></div><div class="para">
		These options are used to control the query that selects the news entries to be displayed.
	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_news_enabled</span></dt><dd><div class="para">
					Indicates whether the news feature should be enabled or disabled. The default is OFF. The news feature is deprecated in favor of being moved to a plugin.
				</div></dd><dt><span class="term">$g_news_limit_method</span></dt><dd><div class="para">
					Limit the news entry that are displayed by number of entries (BY_LIMIT) or by date (BY_DATE). The default is BY_LIMIT.
				</div></dd><dt><span class="term">$g_news_view_limit</span></dt><dd><div class="para">
					The limit for the number of news entries to be displayed. This option is only used if $g_news_limit_method is set to BY_LIMIT.
				</div></dd><dt><span class="term">$g_news_view_limit_days</span></dt><dd><div class="para">
					Specifies the number of dates after which the news are not displayed. This option is only used if $g_news_limit_method is set to BY_DATE.
				</div></dd><dt><span class="term">$g_private_news_threshold</span></dt><dd><div class="para">
					Specifies the access level required to view private news. The default is DEVELOPER.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.defaults">
      ⁠</a>5.16. Default Preferences</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_default_new_account_access_level</span></dt><dd><div class="para">
					This is the default access level users are given when their account is created by email. The default access level is REPORTER. Look in constant_inc.php for other values.
				</div></dd><dt><span class="term">$g_default_project_view_status</span></dt><dd><div class="para">
					The default viewing status for new projects (VS_PUBLIC or VS_PRIVATE). The default is VS_PUBLIC.
				</div></dd><dt><span class="term">$g_default_bug_description</span></dt><dd><div class="para">
					Default value for bug description field used on bug report page. Default is empty description.
				</div></dd><dt><span class="term">$g_default_bug_additional_info</span></dt><dd><div class="para">
					Default value for bug additional info field used on bug report page. Default is empty.
				</div></dd><dt><span class="term">$g_default_bug_steps_to_reproduce</span></dt><dd><div class="para">
					Default value for bug steps to reproduce field used on bug report page. Default is empty.
				</div></dd><dt><span class="term">$g_default_bug_view_status</span></dt><dd><div class="para">
					The default viewing status for the new bug (VS_PUBLIC or VS_PRIVATE). The default is VS_PUBLIC.
				</div></dd><dt><span class="term">$g_default_bugnote_view_status</span></dt><dd><div class="para">
					The default viewing status for the new bugnote (VS_PUBLIC or VS_PRIVATE). The default is VS_PUBLIC.
				</div></dd><dt><span class="term">$g_timeline_view_threshold</span></dt><dd><div class="para">
					Threshold for viewing timeline information. Use NOBODY to turn it off. If the timeline is turned off, the other widgets are displayed in a two column view. The default is VIEWER.
				</div></dd><dt><span class="term">$g_default_reminder_view_status</span></dt><dd><div class="para">
					The default viewing status for the new reminders (VS_PUBLIC or VS_PRIVATE). The default is VS_PUBLIC.
				</div></dd><dt><span class="term">$g_reminder_receive_threshold</span></dt><dd><div class="para">
					The minimum access level for a user to show up in the reminder user picker. Note that this is the access level for the project for which the issue belongs. The default is DEVELOPER.
				</div></dd><dt><span class="term">$g_default_bug_resolution</span></dt><dd><div class="para">
					The resolution for a newly created issue. The default is OPEN. Look in constant_inc.php for other values.
				</div></dd><dt><span class="term">$g_default_bug_severity</span></dt><dd><div class="para">
					The severity for a newly created issue. The default is MINOR. Look in constant_inc.php for other values.
				</div></dd><dt><span class="term">$g_default_bug_priority</span></dt><dd><div class="para">
					The priority for a newly created issue. The default is NORMAL. Look in constant_inc.php for other values.
				</div></dd><dt><span class="term">$g_default_bug_reproducibility</span></dt><dd><div class="para">
					The reproducibility for a newly created issue. The default is REPRODUCIBILITY_HAVENOTTRIED. Look in constant_inc.php for other values.
				</div></dd><dt><span class="term">$g_default_bug_projection</span></dt><dd><div class="para">
					The projection for a newly created issue. The default is PROJECTION_NONE. Look in constant_inc.php for other values.
				</div></dd><dt><span class="term">$g_default_bug_eta</span></dt><dd><div class="para">
					The ETA for a newly created issue. The default is ETA_NONE. Look in constant_inc.php for other values.
				</div></dd><dt><span class="term">$g_default_category_for_moves</span></dt><dd><div class="para">
					Default global category to be used when an issue is moved from a project to another that doesn't have a category with a matching name. The default is 1 which is the "General" category that is created in the default database.
				</div></dd><dt><span class="term">$g_default_limit_view</span></dt><dd><div class="para">
					Number of bugs to show in the View Bugs page. The default value is 50.
				</div></dd><dt><span class="term">$g_default_show_changed</span></dt><dd><div class="para">
					Highlight bugs that have changed during the last N hours. The default value is 6.
				</div></dd><dt><span class="term">$g_hide_status_default</span></dt><dd><div class="para">
					Controls which issues will be displayed in the View Issues page. Default value is CLOSED, implying that all issues at "closed" or higher state will not be shown.
				</div></dd><dt><span class="term">$g_min_refresh_delay</span></dt><dd><div class="para">
					This is the delay between automatic refreshes of the View Issues page in minutes. Make sure refresh delay in user preferences isn't too short. If a users set their preferences to be lower then it is bumped back up to this minimum value. The default value is 10 minutes.
				</div></dd></dl></div><div class="para">
		These settings are used as the default values for preferences for new users. Each user can override these settings through the user preferences form. Default language is set to default site language ($g_default_language).
	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_default_refresh_delay</span></dt><dd><div class="para">
					Default page refresh delay (in minutes). This is for the bug listing pages. Default value is 30 minutes.
				</div></dd><dt><span class="term">$g_default_redirect_delay</span></dt><dd><div class="para">
					Default delay before a user is redirected to a page after being prompted by a message (eg: operational successful). Default value is 2 seconds.
				</div></dd><dt><span class="term">$g_default_bugnote_order</span></dt><dd><div class="para">
					This controls the time order in which bug notes are displayed. It can be either ASC (oldest first, the default) or DESC (newest first).
				</div></dd><dt><span class="term">$g_default_email_on_new, $g_default_email_on_assigned, $g_default_email_on_feedback, $g_default_email_on_resolved, $g_default_email_on_closed</span></dt><dd><div class="para">
					Default user preferences to enable receiving emails when a bug is set to the corresponding status. This option only has an effect if users have the required access level to receive such emails. Default value is ON.
				</div></dd><dt><span class="term">$g_default_email_on_reopened</span></dt><dd><div class="para">
					Default user preferences to enable receiving emails when bugs are re-opened. Default value is ON.
				</div></dd><dt><span class="term">$g_default_email_on_bugnote</span></dt><dd><div class="para">
					Default user preferences to enable receiving emails when bugnotes are added to bugs. Default value is ON.
				</div></dd><dt><span class="term">$g_default_email_on_status</span></dt><dd><div class="para">
					Default user preferences to enable receiving emails when status is changed. Default is OFF.
				</div></dd><dt><span class="term">$g_default_email_on_priority</span></dt><dd><div class="para">
					Default user preferences to enable receiving emails when priority is changed. Default is OFF.
				</div></dd><dt><span class="term">$g_default_email_on_new_minimum_severity, $g_default_email_on_assigned_minimum_severity, $g_default_email_on_feedback_minimum_severity, $g_default_email_on_resolved_minimum_severity, $g_default_email_on_closed_minimum_severity, $g_default_email_on_reopened_minimum_severity, $g_default_email_on_bugnote_minimum_severity</span></dt><dd><div class="para">
					Default user preferences to enable filtering based on issue severity. These correspond to the email_on_&lt;status&gt; settings. Default is 'any'.
				</div></dd><dt><span class="term">$g_default_email_on_bugnote_minimum_severity</span></dt><dd><div class="para">
					Default user preference to enable filtering based on issue severity. These corresponds to the email_on_bugnote setting. Default is 'any'.
				</div></dd><dt><span class="term">$g_default_email_on_status_minimum_severity</span></dt><dd><div class="para">
					Default user preference to enable filtering based on issue severity. These corresponds to the email_on_status settings. Default is 'any'.
				</div></dd><dt><span class="term">$g_default_email_on_priority_minimum_severity</span></dt><dd><div class="para">
					Default user preferences to enable filtering based on issue severity. These corresponds to the email_on_priority settings. Default is 'any'.
				</div></dd><dt><span class="term">$g_default_bug_relationship_clone</span></dt><dd><div class="para">
					Default relationship between a new bug and its parent when cloning it
				</div></dd><dt><span class="term">$g_default_bug_relationship</span></dt><dd><div class="para">
					Default for new bug relationships
				</div></dd><dt><span class="term">$g_show_sticky_issues</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_default_email_on_new</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_default_email_on_assigned</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_default_email_on_feedback</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_default_email_on_resolved</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_default_email_on_closed</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_default_email_on_new_minimum_severity</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_default_email_on_assigned_minimum_severity</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_default_email_on_feedback_minimum_severity</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_default_email_on_resolved_minimum_severity</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_default_email_on_closed_minimum_severity</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_default_email_on_reopened_minimum_severity</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_default_email_bugnote_limit</span></dt><dd><div class="para">
					TODO
				</div></dd></dl></div><div class="para">
		See also: <a class="xref" href="#admin.customize.email">Section 7.4, “Email Notifications”</a>
	</div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.summary">
      ⁠</a>5.17. Summary</h2></div></div></div><div class="para">
		These are the settings that are used to configuration options related to the Summary page. This page contains statistics about the bugs in MantisBT.
	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_reporter_summary_limit</span></dt><dd><div class="para">
					Limit how many reporters to show in the summary page. This is useful when there are dozens or hundreds of reporters. The default value is 10.
				</div></dd><dt><span class="term">$g_date_partitions</span></dt><dd><div class="para">
					An array of date lengths to count bugs by (in days) for the summary by date. The default is to count for 1, 2, 3, 7, 30, 60, 90, 180, and 365.
				</div></dd><dt><span class="term">$g_summary_category_include_project</span></dt><dd><div class="para">
					Specifies whether category names should be preceded by project names (eg: [Project] Category) when the summary page is viewed for all projects. This is useful in the case where category names are common across projects. The default is OFF.
				</div></dd><dt><span class="term">$g_view_summary_threshold</span></dt><dd><div class="para">
					Specifies the access level required to view the summary page. Default is MANAGER.
				</div></dd><dt><span class="term">$g_severity_multipliers</span></dt><dd><div class="para">
					An array of multipliers which are used to determine the effectiveness of reporters based on the severity of bugs. Higher multipliers will result in an increase in reporter effectiveness. The default multipliers are: 
<pre class="programlisting">
$g_severity_multipliers = array ( FEATURE =&gt; 1,
                                  TRIVIAL =&gt; 2,
                                  TEXT =&gt; 3,
                                  TWEAK =&gt; 2,
                                  MINOR =&gt; 5,
                                  MAJOR =&gt; 8,
                                  CRASH =&gt; 8,
                                  BLOCK =&gt; 10 );
</pre>
					 The keys of the array are severity constants from constant_inc.php or from custom_constants_inc.php if you have custom severities defined. The values are integers, typically in the range of 0 to 10. If you would like for a severity to not count towards effectiveness, set the value to 0 for that severity.
				</div></dd><dt><span class="term">$g_resolution_multipliers</span></dt><dd><div class="para">
					An array of multipliers which are used to determine the effectiveness of reporters based on the resolution of bugs. Higher multipliers will result in a decrease in reporter effectiveness. The only resolutions that need to be defined here are those which match or exceed $g_bug_resolution_not_fixed_threshold. The default multipliers are: 
<pre class="programlisting">
$g_resolution_multipliers = array( UNABLE_TO_REPRODUCE =&gt; 2,
                                   NOT_FIXABLE =&gt; 1,
                                   DUPLICATE =&gt; 3,
                                   NOT_A_BUG =&gt; 5,
                                   SUSPENDED =&gt; 1,
                                   WONT_FIX =&gt; 1 );
</pre>
					 The keys of the array are resolution constants from constant_inc.php or from custom_constants_inc.php if you have custom resolutions defined. Resolutions not included here will be assumed to have a multiplier value of 0. The values are integers, typically in the range of 0 to 10. If you would like for a resolution to not count towards effectiveness, set the value to 0 for that resolution or remove it from the array completely. Note that these resolution multipliers are stacked on top of the severity multipliers. Therefore by default, a user reporting many duplicate bugs at severity level BLOCK will be far worse off than a user reporting many duplicate bugs at severity level FEATURE.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.bugnote">
      ⁠</a>5.18. Bugnote</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_bugnote_order</span></dt><dd><div class="para">
					Order to use for sorting bugnotes by submit date. Possible values include ASC for ascending and DESC for descending order. The default value is ASC.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.uploads">
      ⁠</a>5.19. File Upload</h2></div></div></div><div class="para">
		MantisBT allows users to upload file attachments and associate them with bugs as well as projects. Bug attachments / project documents can be uploaded to the webserver or database. When bugs are uploaded to the webserver they are uploaded to the path that is configured in the project properties. In case of problems getting the file upload feature to work, check the following resources: <a href="https://www.php.net/manual/en/features.file-upload.php">PHP Manual </a> .
	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_allow_file_upload</span></dt><dd><div class="para">
					Whether to allow/disallow uploading of attachments. Default value is ON.
				</div></dd><dt><span class="term">$g_file_upload_method</span></dt><dd><div class="para">
					Specify the location for uploading attachments. In case of DISK methods you need to provide the webserver with write access rights to the configured upload path (configured in the project) and temporary upload path (used by PHP).
				</div><div class="para">
					Values: DISK or DATABASE
				</div><div class="para">
					Default: DATABASE
				</div></dd><dt><span class="term">$g_dropzone_enabled</span></dt><dd><div class="para">
					Whether to enable/disable drag and drop zone for uploading of attachments. Default value is ON.
				</div></dd><dt><span class="term">$g_file_upload_max_num</span></dt><dd><div class="para">
					Maximum number of files that can be uploaded simultaneously. Default value is 10.
				</div></dd><dt><span class="term">$g_max_file_size</span></dt><dd><div class="para">
					Maximum file size that can be uploaded. Default value is about 5 MiB. The maximum size is also affected by the PHP options post_max_size (default 8 MiB), upload_max_filesize (default 2 MiB) and memory_limit (default 128 MiB) specified in php.ini.
				</div></dd><dt><span class="term">$g_allowed_files</span></dt><dd><div class="para">
					Authorized file types (whitelist).
				</div><div class="para">
					If $g_allowed_files is filled in, NO other file types will be allowed. If empty, any extensions not specifically excluded by <span class="emphasis"><em>$g_disallowed_files list</em></span> will be authorized ($g_disallowed_files takes precedence over $g_allowed_files). Separate items by commas, e.g. <code class="literal">'bmp,gif,jpg,png,txt,zip'</code>.
				</div></dd><dt><span class="term">$g_disallowed_files</span></dt><dd><div class="para">
					Forbidden file types (blacklist).
				</div><div class="para">
					All file extensions in this list will be unauthorized. Separate items by commas, e.g. <code class="literal">'php,html,java,exe,pl,svg'</code>.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						<a href="https://en.wikipedia.org/wiki/Scalable_Vector_Graphics">SVG files</a> are disabled by default, for security reasons. It is recommended to also disable all extensions that can be executed by your server.
					</div></div></div></dd><dt><span class="term">$g_preview_attachments_inline_max_size</span></dt><dd><div class="para">
					This limit applies to previewing of image / text attachments. If the attachment size is smaller than the specified value, the attachment is previewed with the issue details. The previewing can be disabled by setting this configuration to 0. The default value is 256 * 1024 (256KB).
				</div></dd><dt><span class="term">$g_preview_text_extensions</span></dt><dd><div class="para">
					An array of file extensions (not including dots) for text files that can be previewed inline.
				</div></dd><dt><span class="term">$g_preview_image_extensions</span></dt><dd><div class="para">
					An array of file extensions (not including dots) for image files that can be previewed inline.
				</div></dd><dt><span class="term">$g_fileinfo_magic_db_file</span></dt><dd><div class="para">
					Specify the filename of the magic database file. This is used by PHP to guess what the MIME type of a file is. Usually it is safe to leave this setting as the default (blank) as PHP is usually able to find this file by itself.
				</div></dd><dt><span class="term">$g_file_download_xsendfile_enabled</span></dt><dd><div class="para">
					Enable support for sending files to users via a more efficient X-Sendfile method. HTTP server software supporting this technique includes Lighttpd, Cherokee, Apache with mod_xsendfile and nginx. You may need to set the proceeding file_download_xsendfile_header_name option to suit the server you are using.
				</div></dd><dt><span class="term">$g_file_download_xsendfile_header_name</span></dt><dd><div class="para">
					The name of the X-Sendfile header to use. Each server tends to implement this functionality in a slightly different way and thus the naming conventions for the header differ between each server. Lighttpd from v1.5, Apache with mod_xsendfile and Cherokee web servers use X-Sendfile. nginx uses X-Accel-Redirect and Lighttpd v1.4 uses X-LIGHTTPD-send-file.
				</div></dd><dt><span class="term">$g_attachments_file_permissions</span></dt><dd><div class="para">
					When using DISK for storing uploaded files, this setting control the access permissions they will have on the web server: with the default value (0400) files will be read-only, and accessible only by the user running the apache process (probably "apache" in Linux and "Administrator" in Windows). For more details on unix style permissions: <a href="http://www.perlfect.com/articles/chmod.shtml">http://www.perlfect.com/articles/chmod.shtml</a>
				</div></dd><dt><span class="term">$g_absolute_path_default_upload_folder</span></dt><dd><div class="para">
					Absolute path to the default upload folder. Requires trailing / or \.
				</div></dd><dt><span class="term">$g_preview_max_width</span></dt><dd><div class="para">
					Specifies the maximum width for the auto-preview feature. If no maximum width should be imposed then it should be set to 0.
				</div></dd><dt><span class="term">$g_preview_max_height</span></dt><dd><div class="para">
					Specifies the maximum height for the auto-preview feature. If no maximum height should be imposed then it should be set to 0.
				</div></dd><dt><span class="term">$g_view_attachments_threshold</span></dt><dd><div class="para">
					Access level needed to view bugs attachments. View means to see the file names, sizes, and timestamps of the attachments.
				</div></dd><dt><span class="term">$g_download_attachments_threshold</span></dt><dd><div class="para">
					Access level needed to download bug attachments.
				</div></dd><dt><span class="term">$g_delete_attachments_threshold</span></dt><dd><div class="para">
					Access level needed to delete bug attachments.
				</div></dd><dt><span class="term">$g_allow_view_own_attachments</span></dt><dd><div class="para">
					Allow users to view attachments uploaded by themselves even if their access level is below view_attachments_threshold.
				</div></dd><dt><span class="term">$g_allow_download_own_attachments</span></dt><dd><div class="para">
					Allow users to download attachments uploaded by themselves even if their access level is below download_attachments_threshold.
				</div></dd><dt><span class="term">$g_allow_delete_own_attachments</span></dt><dd><div class="para">
					Allow users to delete attachments uploaded by themselves even if their access level is below delete_attachments_threshold.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.html">
      ⁠</a>5.20. HTML</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_html_make_links</span></dt><dd><div class="para">
					This flag controls whether URLs and email addresses are automatically converted to clickable links. Additionally, for URL links, it determines where they open when clicked (<span class="emphasis"><em>target</em></span> attribute) and their type.
				</div><div class="para">
					The options below can be combined using bitwise operators, though not all possible combinations make sense. The default is <span class="emphasis"><em>LINKS_SAME_WINDOW | LINKS_NOOPENER</em></span>.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							<span class="emphasis"><em>OFF</em></span> - do not convert URLs or emails
						</div></li><li class="listitem"><div class="para">
							<span class="emphasis"><em>LINKS_SAME_WINDOW</em></span> - convert to links that open in current tab/window. NOTE: for backwards-compatibility, this is equivalent to <span class="emphasis"><em>ON</em></span>.
						</div></li><li class="listitem"><div class="para">
							<span class="emphasis"><em>LINKS_NEW_WINDOW</em></span> - convert to links that open in a new tab/window. Overrides <span class="emphasis"><em>LINKS_SAME_WINDOW</em></span>.
						</div></li><li class="listitem"><div class="para">
							<span class="emphasis"><em>LINKS_NOOPENER</em></span> - Links have the <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types/noopener">noopener</a> type.
						</div></li><li class="listitem"><div class="para">
							<span class="emphasis"><em>LINKS_NOREFERRER</em></span> - Links have the <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types/noreferrer">noreferrer</a> type, i.e. they omit the <span class="emphasis"><em>Referer</em></span> header. Implies <span class="emphasis"><em>LINKS_NOOPENER</em></span>.
						</div></li></ul></div></dd><dt><span class="term">$g_html_valid_tags</span></dt><dd><div class="para">
					This is the list of HTML tags that are allowed.Do NOT include href or img tags here.Do NOT include tags that have parameters (eg. )The HTML code is allowed to enter the database as is. The $g_allow_href_tags does not have to be enabled to make URL links. The package will automatically hyperlink properly formatted URLs eg. https://blah.blah/ or mailto://<EMAIL>/
				</div></dd><dt><span class="term">$g_bottom_include_page</span></dt><dd><div class="para">
					Specifies a file to be included at the bottom of each page. It can be used e.g. for company branding, to include Google Analytics script, etc.
				</div></dd><dt><span class="term">$g_top_include_page</span></dt><dd><div class="para">
					Specifies a file to be included at the top of each page. It can be used e.g. for company branding.
				</div><div class="para">
					If a file is supplied, the logo specified by <code class="literal">$g_logo_image</code> (see <a class="xref" href="#admin.config.display">Section 5.11, “Display”</a>) will not be shown, and the include file will have to handle display of the logo. To do so you can use the <code class="literal">html_print_logo()</code> API function, which will display the logo with an URL link if one has been specified in <code class="literal">$g_logo_url</code>
				</div><div class="para">
					Example top include PHP file with logo and centered page title: 
<pre class="programlisting">
&lt;div id="banner" style="display: flex; align-items: center;"&gt;
	&lt;div style="width: 10%;"&gt;
		&lt;?php html_print_logo(); ?&gt;
	&lt;/div&gt;

	&lt;div class="center"&gt;
		&lt;span class="pagetitle"&gt;
			&lt;?php global $g_window_title; echo $g_window_title; ?&gt;
		&lt;/span&gt;
	&lt;/div&gt;

	&lt;div style="width: 10%;"&gt;
	&lt;/div&gt;
&lt;/div&gt;
</pre>

				</div></dd><dt><span class="term">$g_css_include_file</span></dt><dd><div class="para">
					Set this to point to the CSS file of your choice.
				</div></dd><dt><span class="term">$g_css_rtl_include_file</span></dt><dd><div class="para">
					Set this to point to the RTL CSS file of your choice.
				</div></dd><dt><span class="term">$g_cdn_enabled</span></dt><dd><div class="para">
					A flag that indicates whether to use CDN (content delivery networks) for loading javascript libraries and their associated CSS. This improves performance for loading MantisBT pages. This can be disabled if it is desired that MantisBT doesn't reach out outside corporate network. Default OFF.
				</div></dd><dt><span class="term">$g_main_menu_custom_options</span></dt><dd><div class="para">
					This option will add custom options to the main menu. It is an array of arrays listing the caption, access level required, and the link to be executed. For example: 
<pre class="programlisting">
$g_main_menu_custom_options = array(
    array( 
        'title'        =&gt; 'My Link',
        'access_level' =&gt; MANAGER,
        'url'          =&gt; 'my_link.php',
        'icon'         =&gt; 'fa-plug'
    ),
    array( 
        'title'        =&gt; 'My Link2',
        'access_level' =&gt; ADMINISTRATOR,
        'url'          =&gt; 'my_link2.php',
        'icon'         =&gt; 'fa-plug'
    )
);
</pre>
					 Note that if the caption is found in <code class="filename">custom_strings_inc.php</code> (see <a class="xref" href="#admin.customize.strings">Section 7.1, “Strings / Translations”</a>), it will be replaced by the corresponding translated string. Options will only be added to the menu if the current logged in user has the appropriate access level.
				</div><div class="para">
					Use icons from <a href="https://fontawesome.io/icons/">Font Awesome</a>. Add "fa-" prefix to icon name.
				</div><div class="para">
					Access level is an optional field, and no check will be done if it is not set. Icon is an optional field, and 'fa-plug' will be used if it is not set.
				</div></dd><dt><span class="term">$g_html_valid_tags_single_line</span></dt><dd><div class="para">
					These are the valid html tags for single line fields (e.g. issue summary). do NOT include a or img tags here. do NOT include tags that require attributes.
				</div></dd><dt><span class="term">$g_max_dropdown_length</span></dt><dd><div class="para">
					Maximum length of the description in a dropdown menu (for search) set to 0 to disable truncations
				</div></dd><dt><span class="term">$g_wrap_in_preformatted_text</span></dt><dd><div class="para">
					This flag controls whether pre-formatted text (delimited by HTML pre tags is wrapped to a maximum linelength (defaults to 100 chars in strings_api). If turned off, the display may be wide when viewing the text.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.auth">
      ⁠</a>5.21. Authentication</h2></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.auth.global">
      ⁠</a>5.21.1. Global authentication parameters</h3></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_login_method</span></dt><dd><div class="para">
						Specifies which method will be used to authenticate. It should be one of the following values (defaults to <span class="emphasis"><em>MD5</em></span>): 
						<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
									MD5 - user's password is stored as a hash in the database
								</div></li><li class="listitem"><div class="para">
									LDAP - authenticates against an LDAP (or Active Directory) server
								</div></li><li class="listitem"><div class="para">
									BASIC_AUTH
								</div></li><li class="listitem"><div class="para">
									HTTP_AUTH
								</div></li></ul></div>
						 In addition, the following deprecated values are supported for backwards-compatibility, and should no longer be used: 
						<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
									PLAIN - password is stored in plain, unencrypted text in the database
								</div></li><li class="listitem"><div class="para">
									CRYPT
								</div></li><li class="listitem"><div class="para">
									CRYPT_FULL_SALT
								</div></li></ul></div>
					</div><div class="para">
						Note: you may not be able to easily switch encryption methods, so this should be carefully chosen at install time. However, MantisBT will attempt to "fall back" to older methods if possible.
					</div></dd><dt><span class="term">$g_reauthentication</span></dt><dd><div class="para">
						Determines whether MantisBT will require the user to re-authenticate before granting access to the Admin areas after timeout expiration. Defaults to <span class="emphasis"><em>ON</em></span>
					</div></dd><dt><span class="term">$g_reauthentication_expiry</span></dt><dd><div class="para">
						Duration of the reauthentication timeout, in seconds. Defaults to 5 minutes.
					</div></dd></dl></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.auth.ldap">
      ⁠</a>5.21.2. LDAP authentication method parameters</h3></div></div></div><div class="para">
			The parameters below are only used if $g_login_method (see <a class="xref" href="#admin.config.auth.global">Section 5.21.1, “Global authentication parameters”</a> above) is set to <code class="literal">LDAP</code>.
		</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_ldap_server</span></dt><dd><div class="para">
						Specifies the LDAP or Active Directory server to connect to.
					</div><div class="para">
						This must be a full LDAP URI (<code class="literal">protocol://hostname:port</code>)
					</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								<span class="emphasis"><em>Protocol</em></span> must be either: 
								<div class="itemizedlist"><ul><li class="listitem"><div class="para">
											<code class="literal">ldap</code> - unencrypted or opportunistic TLS (<a href="https://en.wikipedia.org/wiki/StartTLS">STARTTLS</a>)
										</div></li><li class="listitem"><div class="para">
											<code class="literal">ldaps</code> - TLS encryption
										</div></li></ul></div>

							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>Port</em></span> number is optional, and defaults to <code class="literal">389</code>.
							</div><div class="para">
								If this doesn't work, try using one of the following standard port numbers: <code class="literal">636</code> (ldaps); for Active Directory Global Catalog forest-wide search, use <code class="literal">3268</code> (ldap) or <code class="literal">3269</code> (ldaps).
							</div></li></ul></div><div class="para">
						Examples of valid URI:
					</div><pre class="programlisting">
ldap://ldap.example.com/
ldaps://ldap.example.com:3269/
</pre><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
							Multiple servers can be specified as a space-separated list.
						</div></div></div></dd><dt><span class="term">$g_ldap_use_starttls</span></dt><dd><div class="para">
						Determines whether the connection will attempt an opportunistic upgrade to a TLS connection (STARTTLS).
					</div><div class="para">
						Defaults to <code class="literal">ON</code>.
					</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
							For security, a failure aborts the entire connection, so make sure your server supports StartTLS if this setting is ON, and use the <code class="literal">ldap://</code> scheme (not <code class="literal">ldaps://</code>).
						</div></div></div></dd><dt><span class="term">$g_ldap_tls_protocol_min</span></dt><dd><div class="para">
						An integer indicating the minimum version of the TLS protocol to allow. This maps to the <a href="https://www.php.net/manual/en/ldap.constants.php"> LDAP_OPT_X_TLS_PROTOCOL_MIN</a> LDAP library option.
					</div><div class="para">
						For example, <code class="literal">LDAP_OPT_X_TLS_PROTOCOL_TLS1_2</code>.
					</div><div class="para">
						Defaults to <code class="literal">OFF</code> (protocol version not set).
					</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
							Requires PHP 7.1 or later.
						</div></div></div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
							For security, a failure aborts the entire connection.
						</div></div></div></dd><dt><span class="term">$g_ldap_root_dn</span></dt><dd><div class="para">
						The root distinguished name for LDAP searches. For example, <code class="literal">dc=example, dc=com</code>.
					</div></dd><dt><span class="term">$g_ldap_organization</span></dt><dd><div class="para">
						LDAP search filter for the organization. For example, <code class="literal">(organizationname=*Traffic)</code>. Defaults to <code class="literal">''</code> (empty string).
					</div></dd><dt><span class="term">$g_ldap_protocol_version</span></dt><dd><div class="para">
						The LDAP Protocol Version to use (2, 3 or 0). This maps to the LDAP_OPT_PROTOCOL_VERSION ldap library option.
					</div><div class="para">
						Defaults to <code class="literal">3</code>.
					</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
							If <code class="literal">0</code>, then the protocol version is not set, and you get whatever default the underlying LDAP library uses.
						</div><div class="para">
							In almost all cases you should use <code class="literal">3</code>. LDAPv3 was introduced back in 1997, and LDAPv2 was deprecated in 2003 by RFC3494.
						</div></div></div></dd><dt><span class="term">$g_ldap_network_timeout</span></dt><dd><div class="para">
						Duration of the timeout for TCP connection to the LDAP server (in seconds). This maps to LDAP_OPT_NETWORK_TIMEOUT ldap library option. Defaults to <code class="literal">0</code> (infinite).
					</div><div class="para">
						Set this to a low value when the hostname defined in $g_ldap_server resolves to multiple IP addresses, allowing rapid failover to the next available LDAP server.
					</div></dd><dt><span class="term">$g_ldap_follow_referrals</span></dt><dd><div class="para">
						Determines whether the LDAP library automatically follows referrals returned by LDAP servers or not. This maps to LDAP_OPT_REFERRALS ldap library option. Defaults to <code class="literal">ON</code>.
					</div><div class="para">
						For Active Directory, this should be set to <code class="literal">OFF</code>. If you have only one LDAP server, setting to this to OFF is advisable to prevent any man-in-the-middle attacks.
					</div></dd><dt><span class="term">$g_ldap_bind_dn</span></dt><dd><div class="para">
						The distinguished name of the service account to use for binding to the LDAP server. For example, <code class="literal">cn=ldap,ou=Administrators,dc=example,dc=com</code>. Leave empty for anonymous binding.
					</div></dd><dt><span class="term">$g_ldap_bind_passwd</span></dt><dd><div class="para">
						The password for the service account used to establish the connection to the LDAP server. For anonymous binding, leave empty.
					</div></dd><dt><span class="term">$g_ldap_uid_field</span></dt><dd><div class="para">
						The LDAP field for username. Defaults to <code class="literal">uid</code>.
					</div><div class="para">
						For Active Directory, set to <code class="literal">sAMAccountName</code>.
					</div></dd><dt><span class="term">$g_ldap_email_field</span></dt><dd><div class="para">
						The LDAP field for e-mail address. Defaults to <code class="literal">mail</code>.
					</div></dd><dt><span class="term">$g_ldap_realname_field</span></dt><dd><div class="para">
						The LDAP field for the user's real name (i.e. common name). Defaults to <code class="literal">cn</code>.
					</div></dd><dt><span class="term">$g_use_ldap_realname</span></dt><dd><div class="para">
						Use the realname specified in LDAP (ON) rather than the one stored in the database (OFF). Defaults to <code class="literal">OFF</code>.
					</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
							MantisBT will update the database with the data retrieved from LDAP when ON.
						</div></div></div></dd><dt><span class="term">$g_use_ldap_email</span></dt><dd><div class="para">
						Use the email address specified in LDAP (ON) rather than the one stored in the database (OFF). Defaults to <code class="literal">OFF</code>.
					</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
							MantisBT will update the database with the data retrieved from LDAP when ON.
						</div></div></div></dd><dt><span class="term">$g_ldap_simulation_file_path</span></dt><dd><div class="para">
						This configuration option allows replacing the ldap server with a comma-delimited text file for development or testing purposes.
					</div><div class="para">
						The LDAP simulation file format is as follows:
					</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								No headers
							</div></li><li class="listitem"><div class="para">
								One line per user
							</div></li><li class="listitem"><div class="para">
								Each line has 4 comma-delimited fields
							</div><div class="itemizedlist"><ul><li class="listitem"><div class="para">
										username
									</div></li><li class="listitem"><div class="para">
										realname
									</div></li><li class="listitem"><div class="para">
										e-mail
									</div></li><li class="listitem"><div class="para">
										password
									</div></li></ul></div></li><li class="listitem"><div class="para">
								Any extra fields are ignored
							</div></li></ul></div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
							On production systems, this option should be set to <code class="literal">''</code> (This is the default).
						</div></div></div></dd></dl></div></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.status">
      ⁠</a>5.22. Status Settings</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_bug_submit_status</span></dt><dd><div class="para">
					Status to assign to the bug when submitted. Default value is NEW_.
				</div></dd><dt><span class="term">$g_bug_assigned_status</span></dt><dd><div class="para">
					Status to assign to the bug when assigned. Default value is ASSIGNED.
				</div></dd><dt><span class="term">$g_bug_reopen_status</span></dt><dd><div class="para">
					Status to assign to the bug when reopened. Default value is FEEDBACK.
				</div></dd><dt><span class="term">$g_bug_feedback_status</span></dt><dd><div class="para">
					Status to assign to the bug when feedback is required from the issue reporter. Once the reporter adds a note the status moves back from feedback to $g_bug_assigned_status or $g_bug_submit_status based on whether the bug assigned or not.
				</div></dd><dt><span class="term">$g_reassign_on_feedback</span></dt><dd><div class="para">
					When a note is added to a bug currently in $g_bug_feedback_status, and the note author is the bug's reporter, this option will automatically set the bug status to $g_bug_submit_status or $g_bug_assigned_status if the bug is assigned to a developer. Default value is ON.
				</div></dd><dt><span class="term">$g_bug_duplicate_resolution</span></dt><dd><div class="para">
					Default resolution to assign to a bug when it is resolved as being a duplicate of another issue. Default value is DUPLICATE.
				</div></dd><dt><span class="term">$g_bug_reopen_resolution</span></dt><dd><div class="para">
					Resolution to assign to the bug when reopened. Default value is REOPENED.
				</div></dd><dt><span class="term">$g_auto_set_status_to_assigned</span></dt><dd><div class="para">
					Automatically set status to $g_bug_assigned_status whenever a bug is assigned to a person. Installations where assigned status is to be used when the defect is in progress, rather than just put in a person's queue should set it to OFF. Default is ON. For the status change to be effective, these conditions must be met: 
					<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								Bug has no handler, and a new handler is selected
							</div></li><li class="listitem"><div class="para">
								The assignment is not part of a explicit status change
							</div></li><li class="listitem"><div class="para">
								Current bug status is lower than defined "assigned" status
							</div></li><li class="listitem"><div class="para">
								"Assigned" status is reachable by workflow configuration
							</div></li></ul></div>
					 If the conditions are not met, the assignment is still made, but status will not be modified.
				</div></dd><dt><span class="term">$g_bug_resolved_status_threshold</span></dt><dd><div class="para">
					Bug is resolved, ready to be closed or reopened. In some custom installations a bug maybe considered as resolved when it is moved to a custom (FIXED OR TESTED) status.
				</div></dd><dt><span class="term">$g_bug_resolution_fixed_threshold</span></dt><dd><div class="para">
					Threshold resolution which denotes that a bug has been resolved and successfully fixed by developers. Resolutions above and including this threshold and below $g_bug_resolution_not_fixed_threshold are considered to be resolved successfully. Default value is FIXED.
				</div></dd><dt><span class="term">$g_bug_resolution_not_fixed_threshold</span></dt><dd><div class="para">
					Threshold resolution which denotes that a bug has been resolved without being successfully fixed by developers. Resolutions above this threshold are considered to be resolved in an unsuccessful way. Default value is UNABLE_TO_REPRODUCE.
				</div></dd><dt><span class="term"> $g_bug_readonly_status_threshold $g_update_readonly_bug_threshold </span></dt><dd><div class="para">
					Bug becomes readonly if its status is &gt;= $g_bug_readonly_status_threshold. The bug becomes read/write again if re-opened and its status becomes less than this threshold. The default is RESOLVED. Once the bug becomes readonly, a user with an access level greater than or equal to $g_update_readonly_bug_threshold can still edit the bug.
				</div></dd><dt><span class="term">$g_status_enum_workflow</span></dt><dd><div class="para">
					'status_enum_workflow' defines the workflow, and reflects a simple 2-dimensional matrix. For each existing status, you define which statuses you can go to from that status, e.g. from NEW_ you might list statuses '10:new,20:feedback,30:acknowledged' but not higher ones.The default is no workflow, where all states are accessible from any others.
				</div></dd><dt><span class="term">$g_report_bug_threshold</span></dt><dd><div class="para">
					This is the access level required to open a bug. The default is REPORTER.
				</div></dd><dt><span class="term">$g_update_bug_threshold</span></dt><dd><div class="para">
					This is the access level generally required to update the content of a bug. The default is UPDATER.
				</div></dd><dt><span class="term">$g_handle_bug_threshold</span></dt><dd><div class="para">
					This is the access level generally required to be access level needed to be listed in the assign to field. The default is DEVELOPER. If a more restrictive setting can be determined from $g_set_status_threshold, it will be used.
				</div></dd><dt><span class="term">$g_update_bug_status_threshold $g_set_status_threshold </span></dt><dd><div class="para">
					These settings control the access level required to promote a bug to a new status once the bug is opened.$g_set_status_threshold is an array indexed by the status value that allows a distinct setting for each status. It defaults to blank.If the appropriate status is not defined above, $g_update_bug_status_threshold is used instead. The default is DEVELOPER.
				</div></dd><dt><span class="term">$g_bugnote_user_edit_threshold</span></dt><dd><div class="para">
					Threshold at which a user can edit his/her own bugnotes. The default value is equal to the configuration setting $g_update_bugnote_threshold.
				</div></dd><dt><span class="term">$g_bugnote_user_delete_threshold</span></dt><dd><div class="para">
					Threshold at which a user can delete his/her own bugnotes. The default value is equal to the configuration setting $g_delete_bugnote_threshold.
				</div></dd><dt><span class="term">$g_bugnote_user_change_view_state_threshold</span></dt><dd><div class="para">
					Threshold at which a user can change the view status of his/her own bugnotes. The default value is equal to the configuration setting $g_change_view_status_threshold.
				</div></dd><dt><span class="term">$g_allow_reporter_reopen</span></dt><dd><div class="para">
					If set, the bug reporter is allowed to reopen their own bugs once resolved, regardless of their access level. This allows the reporter to disagree with the resolution. The default is ON.
				</div></dd><dt><span class="term">$g_allow_parent_of_unresolved_to_close</span></dt><dd><div class="para">
					If set, no check is performed on the status of a bug's children, which allows the parent to be closed whether or not the children have been resolved. The default is OFF.
				</div></dd><dt><span class="term">$g_bug_readonly_status_threshold</span></dt><dd><div class="para">
					Bug becomes readonly if its status is &gt;= this status. The bug becomes read/write again if re-opened and its status becomes less than this threshold.
				</div></dd><dt><span class="term">$g_bug_closed_status_threshold</span></dt><dd><div class="para">
					Bug is closed. In some custom installations a bug may be considered as closed when it is moved to a custom (COMPLETED or IMPLEMENTED) status.
				</div></dd></dl></div><div class="para">
		See also: <a class="xref" href="#admin.customize.status">Section 7.5, “Customizing Status Values”</a>
	</div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.filters">
      ⁠</a>5.23. Filters</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_filter_by_custom_fields</span></dt><dd><div class="para">
					Show custom fields in the filter dialog and use these in filtering. Defaults to ON.
				</div></dd><dt><span class="term">$g_filter_custom_fields_per_row</span></dt><dd><div class="para">
					The number of filter fields to display per row. The default is 8.
				</div></dd><dt><span class="term">$g_view_filters = SIMPLE_DEFAULT;</span></dt><dd><div class="para">
					Controls the display of the filter pages. Possible values are: 
					<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								SIMPLE_ONLY - only allow use of simple view
							</div></li><li class="listitem"><div class="para">
								ADVANCED_ONLY - only allow use of advanced view (allows multiple value selections)
							</div></li><li class="listitem"><div class="para">
								SIMPLE_DEFAULT - defaults to simple view, but shows a link for advanced
							</div></li><li class="listitem"><div class="para">
								ADVANCED_DEFAULT - defaults to advanced view, but shows a link for simple
							</div></li></ul></div>

				</div></dd><dt><span class="term">$g_use_dynamic_filters = ON;</span></dt><dd><div class="para">
					This switch enables the use of AJAX to dynamically load and create filter form controls upon request. This method will reduce the amount of data that needs to be transferred upon each page load dealing with filters and thus will result in speed improvements and bandwidth reduction.
				</div></dd><dt><span class="term">$g_create_permalink_threshold</span></dt><dd><div class="para">
					The threshold required for users to be able to create permalinks (default DEVELOPER). To turn this feature off use NOBODY.
				</div></dd><dt><span class="term">$g_create_short_url</span></dt><dd><div class="para">
					The service to use to create a short URL. The %s will be replaced by the long URL. By default https://www.tinyurl service is used to shorten URLs.
				</div></dd><dt><span class="term">$g_view_filters</span></dt><dd><div class="para">
					Controls the display of the filter pages.
				</div></dd><dt><span class="term">$g_use_dynamic_filters</span></dt><dd><div class="para">
					This switch enables the use of AJAX to dynamically load and create filter form controls upon request. This method will reduce the amount of data that needs to be transferred upon each page load dealing with filters and thus will result in speed improvements and bandwidth reduction.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.misc">
      ⁠</a>5.24. Misc</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_user_login_valid_regex</span></dt><dd><div class="para">
					The regular expression to use when validating new user login names. The default regular expression allows a-z, A-Z, 0-9, +, -, dot, space and underscore. If you change this, you may want to update the ERROR_USER_NAME_INVALID string in the language files to explain the rules you are using on your site.
				</div><div class="para">
					See <a href="https://en.wikipedia.org/wiki/Regular_Expression">Wikipedia</a> for more details about regular expressions. For testing regular expressions, use <a href="https://rubular.com/">Rubular</a>.
				</div></dd><dt><span class="term">$g_monitor_bug_threshold</span></dt><dd><div class="para">
					Access level needed to monitor issues. The default value is REPORTER.
				</div></dd><dt><span class="term">$g_show_monitor_list_threshold</span></dt><dd><div class="para">
					Access level needed to show the list of users monitoring an issue. The default value is DEVELOPER.
				</div></dd><dt><span class="term">$g_monitor_add_others_bug_threshold</span></dt><dd><div class="para">
					Access level needed to add other users to the list of users monitoring an issue. The default value is DEVELOPER.
				</div><div class="para">
					This setting should not be lower than <span class="emphasis"><em>$g_show_monitor_list_threshold</em></span>.
				</div></dd><dt><span class="term">$g_monitor_delete_others_bug_threshold</span></dt><dd><div class="para">
					Access level needed to delete other users from the list of users monitoring an issue. The default value is DEVELOPER.
				</div><div class="para">
					This setting should not be lower than <span class="emphasis"><em>$g_show_monitor_list_threshold</em></span>.
				</div></dd><dt><span class="term">$g_print_reports_threshold</span></dt><dd><div class="para">
					Grants users access to the Print Reports functionality (Word/HTML) from the View Issues page. The default value is UPDATER.
				</div></dd><dt><span class="term">$g_export_issues_threshold</span></dt><dd><div class="para">
					Access level required to export issues to CSV and Excel formats from the View Issues page. The default value is VIEWER.
				</div></dd><dt><span class="term">$g_allow_reporter_close</span></dt><dd><div class="para">
					Allow reporters to close the bugs they reported.
				</div></dd><dt><span class="term">$g_delete_bug_threshold</span></dt><dd><div class="para">
					Allow the specified access level and above to delete bugs.
				</div></dd><dt><span class="term">$g_bug_move_access_level</span></dt><dd><div class="para">
					Allow the specified access level and above to move bugs between projects.
				</div></dd><dt><span class="term">$g_allow_account_delete</span></dt><dd><div class="para">
					Allow users to delete their own accounts.
				</div></dd><dt><span class="term">$g_allow_anonymous_login</span></dt><dd><div class="para">
					Enable anonymous access to Mantis. You must also specify $g_anonymous_account as the account which anonymous users will browse Mantis with. The default setting is OFF.
				</div></dd><dt><span class="term">$g_anonymous_account</span></dt><dd><div class="para">
					Define the account which anonymous users will assume when using Mantis. This account is considered by Mantis to be protected from modification. In other words, this account can only be modified by users with an access level equal to or higher than $g_manage_user_threshold. Anonymous users will not be able to adjust preferences or change account settings like normal users can.
				</div><div class="para">
					You will need to create a new account to use for this $g_anonymous_account setting. When creating the account you should specify a password, email address and so forth in the same way you'd create any other account. It is suggested that the access level for this account be set to VIEWER or some other read only level.
				</div><div class="para">
					The anonymous user account will not receive standard notifications and can not monitor issues.
				</div><div class="para">
					The default setting is blank/undefined. You only need to define this setting when $g_allow_anonymous_login is set to ON.
				</div></dd><dt><span class="term">$g_bug_link_tag</span></dt><dd><div class="para">
					If a number follows this tag it will create a link to a bug. Default is '#'. 
					<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								'#': a link would be #45
							</div></li><li class="listitem"><div class="para">
								'bug:' a link would be bug:98
							</div></li></ul></div>

				</div></dd><dt><span class="term">$g_bugnote_link_tag</span></dt><dd><div class="para">
					If a number follows this tag it will create a link to a bug note. Default is '~'. 
					<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								'~': a link would be ~45
							</div></li><li class="listitem"><div class="para">
								'bugnote:' a link would be bugnote:98
							</div></li></ul></div>

				</div></dd><dt><span class="term">$g_enable_project_documentation</span></dt><dd><div class="para">
					Specifies whether to enable support for project documents or not. Default is OFF. This feature is deprecated and is expected to be moved to a plugin in the future.
				</div></dd><dt><span class="term">$g_admin_site_threshold</span></dt><dd><div class="para">
					Threshold at which a user is considered to be a site administrator. These users have the highest level of access to your Mantis installation. This access level is required to change key Mantis settings (such as server paths) and perform other administrative duties. You may need to change this value from the default of ADMINISTRATOR if you have defined a new access level to replace the default ADMINISTRATOR level in constant_inc.php.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						This is a potentially dangerous configuration option. Users at or above this threshold value will have permission to all aspects of Mantis including the admin/ directory. With this access level, users can damage your installation of Mantis, destroy your database or have elevated access to your server.
					</div><div class="para">
						DO NOT CHANGE THIS VALUE UNLESS YOU ABSOLUTELY KNOW WHAT YOU'RE DOING. BE VERY CAREFUL WITH CHANGING THIS CONFIGURATION VALUE FROM THE DEFAULT SETTING.
					</div></div></div></dd><dt><span class="term">$g_manage_configuration_threshold</span></dt><dd><div class="para">
					The threshold required for users to be able to manage configuration of a project. This includes workflow, email notifications, columns to view, and others. Default is MANAGER.
				</div></dd><dt><span class="term">$g_view_configuration_threshold</span></dt><dd><div class="para">
					Threshold for users to view the raw system configurations as stored in the database. The default value is ADMINISTRATOR.
				</div></dd><dt><span class="term">$g_set_configuration_threshold</span></dt><dd><div class="para">
					Threshold for users to set the system configurations generically via MantisBT web interface. The default value is ADMINISTRATOR.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						Users who have access to set configuration via the interface MUST be trusted. This is due to the fact that these users can leverage the interface to <span class="emphasis"><em>inject PHP code</em></span> into the system, which is a potential security risk.
					</div></div></div></dd><dt><span class="term">$g_csv_separator</span></dt><dd><div class="para">
					The separator to use for CSV exports. The default value is the comma (<code class="literal">,</code>).
				</div></dd><dt><span class="term">$g_csv_injection_protection</span></dt><dd><div class="para">
					When this setting is <span class="emphasis"><em>ON</em></span> (default), any data that could be interpreted as a formula by a spreadsheet program such as Excel (i.e. starting with <code class="literal">=</code>, <code class="literal">@</code>, <code class="literal">-</code> or <code class="literal">+</code>), will be prefixed with a tab character (<code class="literal">\t</code>) in order to prevent CSV injection.
				</div><div class="para">
					Sometimes this may not be appropriate (e.g. if the CSV needs to be consumed programmatically). In that case, $g_csv_injection_protection can be set to <span class="emphasis"><em>OFF</em></span>, resulting in raw data to be exported.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						Setting this to <span class="emphasis"><em>OFF</em></span> is a security risk. An attacker could upload a crafted CSV file containing formulas that will be executed when opened with Excel, as described in <a href="http://georgemauer.net/2017/10/07/csv-injection.html">this article</a>.
					</div></div></div></dd><dt><span class="term">$g_view_bug_threshold</span></dt><dd><div class="para">
					Access level needed to view bugs.
				</div></dd><dt><span class="term">$g_update_bug_assign_threshold</span></dt><dd><div class="para">
					Access level needed to show the Assign To: button bug_view*_page or the Assigned list in bug_update*_page. This allows control over who can route bugs This defaults to $g_handle_bug_threshold.
				</div></dd><dt><span class="term">$g_private_bugnote_threshold</span></dt><dd><div class="para">
					Access level needed to view private bugnotes.
				</div></dd><dt><span class="term">$g_view_handler_threshold</span></dt><dd><div class="para">
					Access level needed to view handler.
				</div></dd><dt><span class="term">$g_view_history_threshold</span></dt><dd><div class="para">
					Access level needed to view history.
				</div></dd><dt><span class="term">$g_bug_reminder_threshold</span></dt><dd><div class="para">
					Access level needed to send a reminder from the bug view pages set to NOBODY to disable the feature.
				</div></dd><dt><span class="term">$g_upload_project_file_threshold</span></dt><dd><div class="para">
					Access level needed to upload files to the project documentation section You can set this to NOBODY to prevent uploads to projects.
				</div></dd><dt><span class="term">$g_upload_bug_file_threshold</span></dt><dd><div class="para">
					Access level needed to upload files to attach to a bug You can set this to NOBODY to prevent uploads to bugs but note that the reporter of the bug will still be able to upload unless you set $g_allow_reporter_upload or $g_allow_file_upload to OFF See also: $g_upload_project_file_threshold, $g_allow_file_upload, $g_allow_reporter_upload.
				</div></dd><dt><span class="term">$g_add_bugnote_threshold</span></dt><dd><div class="para">
					Add bugnote threshold.
				</div></dd><dt><span class="term">$g_update_bugnote_threshold</span></dt><dd><div class="para">
					Threshold at which a user can edit the bugnotes of other users.
				</div></dd><dt><span class="term">$g_view_proj_doc_threshold</span></dt><dd><div class="para">
					Threshold needed to view project documentation Note: setting this to ANYBODY will let any user download attachments from private projects, regardless of their being a member of it.
				</div></dd><dt><span class="term">$g_manage_site_threshold</span></dt><dd><div class="para">
					Site manager.
				</div></dd><dt><span class="term">$g_manage_project_threshold</span></dt><dd><div class="para">
					Threshold needed to manage a project: edit project details (not to add/delete projects) ...etc.
				</div></dd><dt><span class="term">$g_manage_news_threshold</span></dt><dd><div class="para">
					Threshold needed to add/delete/modify news.
				</div></dd><dt><span class="term">$g_delete_project_threshold</span></dt><dd><div class="para">
					Threshold required to delete a project.
				</div></dd><dt><span class="term">$g_create_project_threshold</span></dt><dd><div class="para">
					Threshold needed to create a new project.
				</div></dd><dt><span class="term">$g_private_project_threshold</span></dt><dd><div class="para">
					Threshold needed to be automatically included in private projects.
				</div></dd><dt><span class="term">$g_project_user_threshold</span></dt><dd><div class="para">
					Threshold needed to manage user access to a project.
				</div></dd><dt><span class="term">$g_delete_bugnote_threshold</span></dt><dd><div class="para">
					Threshold at which a user can delete the bugnotes of other users. The default value is equal to the configuration setting $g_delete_bug_threshold.
				</div></dd><dt><span class="term">$g_move_bug_threshold</span></dt><dd><div class="para">
					Move bug threshold.
				</div></dd><dt><span class="term">$g_stored_query_use_threshold</span></dt><dd><div class="para">
					Threshold needed to be able to use stored queries.
				</div></dd><dt><span class="term">$g_stored_query_create_threshold</span></dt><dd><div class="para">
					Threshold needed to be able to create stored queries.
				</div></dd><dt><span class="term">$g_stored_query_create_shared_threshold</span></dt><dd><div class="para">
					Threshold needed to be able to create shared stored queries.
				</div></dd><dt><span class="term">$g_update_readonly_bug_threshold</span></dt><dd><div class="para">
					Threshold needed to update readonly bugs. Readonly bugs are identified via $g_bug_readonly_status_threshold.
				</div></dd><dt><span class="term">$g_view_changelog_threshold</span></dt><dd><div class="para">
					Threshold for viewing changelog.
				</div></dd><dt><span class="term">$g_roadmap_view_threshold</span></dt><dd><div class="para">
					Threshold for viewing roadmap.
				</div></dd><dt><span class="term">$g_roadmap_update_threshold</span></dt><dd><div class="para">
					Threshold for updating roadmap, target_version, etc.
				</div></dd><dt><span class="term">$g_update_bug_status_threshold</span></dt><dd><div class="para">
					Status change thresholds.
				</div></dd><dt><span class="term">$g_reopen_bug_threshold</span></dt><dd><div class="para">
					Access level needed to re-open bugs.
				</div></dd><dt><span class="term">$g_report_issues_for_unreleased_versions_threshold</span></dt><dd><div class="para">
					Access level needed to assign bugs to unreleased product versions.
				</div></dd><dt><span class="term">$g_set_bug_sticky_threshold</span></dt><dd><div class="para">
					Access level needed to set a bug sticky.
				</div></dd><dt><span class="term">$g_set_status_threshold</span></dt><dd><div class="para">
					This array sets the access thresholds needed to enter each status listed. if a status is not listed, it falls back to $g_update_bug_status_threshold.
				</div></dd><dt><span class="term">$g_allow_no_category</span></dt><dd><div class="para">
					Allow a bug to have no category.
				</div></dd><dt><span class="term">$g_limit_view_unless_threshold</span></dt><dd><div class="para">
					Threshold at which a user can view all issues in the project (as allowed by other permissions). Not meeting this threshold means the user can only see the issues they reported, are handling or monitoring. A value of ANYBODY means that all users have full visibility (as default) This is a replacement for old option: $g_limit_reporters.
				</div></dd><dt><span class="term">$g_allow_reporter_upload</span></dt><dd><div class="para">
					Reporter can upload Allow reporters to upload attachments to bugs they reported.
				</div></dd><dt><span class="term">$g_bug_count_hyperlink_prefix</span></dt><dd><div class="para">
					Bug Count Linking This is the prefix to use when creating links to bug views from bug counts (eg. on the main page and the summary page). Default is a temporary filter.
				</div></dd><dt><span class="term">$g_default_manage_tag_prefix</span></dt><dd><div class="para">
					Default tag prefix used to filter the list of tags in manage_tags_page.php. Change this to 'A' (or any other letter) if you have a lot of tags in the system and loading the manage tags page takes a long time.
				</div></dd><dt><span class="term">$g_access_levels_enum_string</span></dt><dd><div class="para">
					Status from $g_status_index-1 to 79 are used for the onboard customization (if enabled) directly use MantisBT to edit them.
				</div></dd><dt><span class="term">$g_project_status_enum_string</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_project_view_state_enum_string</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_view_state_enum_string</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_priority_enum_string</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_severity_enum_string</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_reproducibility_enum_string</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_status_enum_string</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_resolution_enum_string</span></dt><dd><div class="para">
					The values in this list are also used to define variables in the language files (e.g., $s_new_bug_title referenced in bug_change_status_page.php ). Embedded spaces are converted to underscores (e.g., "working on" references $s_working_on_bug_title). They are also expected to be English names for the states
				</div></dd><dt><span class="term">$g_projection_enum_string</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_eta_enum_string</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_sponsorship_enum_string</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_custom_field_type_enum_string</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_file_type_icons</span></dt><dd><div class="para">
					Maps a file extension to a file type icon. These icons are printed next to project documents and bug attachments.
				</div></dd><dt><span class="term">$g_file_download_content_type_overrides</span></dt><dd><div class="para">
					Content types which will be overridden when downloading files.
				</div></dd><dt><span class="term">$g_status_icon_arr</span></dt><dd><div class="para">
					Icon associative arrays. Status to icon mapping.
				</div></dd><dt><span class="term">$g_sort_icon_arr</span></dt><dd><div class="para">
					Sort direction to icon mapping.
				</div></dd><dt><span class="term">$g_rss_enabled</span></dt><dd><div class="para">
					This flag enables or disables RSS syndication. In the case where RSS syndication is not used, it is recommended to set it to OFF.
				</div></dd><dt><span class="term">$g_recently_visited_count</span></dt><dd><div class="para">
					This controls whether to show the most recently visited issues by the current user or not. If set to 0, this feature is disabled. Otherwise it is the maximum number of issues to keep in the recently visited list.
				</div></dd><dt><span class="term">$g_tag_separator</span></dt><dd><div class="para">
					String that will separate tags as entered for input.
				</div></dd><dt><span class="term">$g_tag_view_threshold</span></dt><dd><div class="para">
					Access level required to view tags attached to a bug.
				</div></dd><dt><span class="term">$g_tag_attach_threshold</span></dt><dd><div class="para">
					Access level required to attach tags to a bug.
				</div></dd><dt><span class="term">$g_tag_detach_threshold</span></dt><dd><div class="para">
					Access level required to detach tags from a bug.
				</div></dd><dt><span class="term">$g_tag_detach_own_threshold</span></dt><dd><div class="para">
					Access level required to detach tags attached by the same user.
				</div></dd><dt><span class="term">$g_tag_create_threshold</span></dt><dd><div class="para">
					Access level required to create new tags.
				</div></dd><dt><span class="term">$g_tag_edit_threshold</span></dt><dd><div class="para">
					Access level required to edit tag names and descriptions.
				</div></dd><dt><span class="term">$g_tag_edit_own_threshold</span></dt><dd><div class="para">
					Access level required to edit descriptions by the creating user.
				</div></dd><dt><span class="term">$g_enable_profiles</span></dt><dd><div class="para">
					Enable Profiles.
				</div></dd><dt><span class="term">$g_add_profile_threshold</span></dt><dd><div class="para">
					Add profile threshold.
				</div></dd><dt><span class="term">$g_manage_global_profile_threshold</span></dt><dd><div class="para">
					Threshold needed to be able to create and modify global profiles.
				</div></dd><dt><span class="term">$g_allow_freetext_in_profile_fields</span></dt><dd><div class="para">
					Allows the users to enter free text when reporting/updating issues for the profile related fields (i.e. platform, os, os build).
				</div></dd><dt><span class="term">$g_plugins_enabled</span></dt><dd><div class="para">
					Enable/disable plugins.
				</div></dd><dt><span class="term">$g_plugin_path</span></dt><dd><div class="para">
					Absolute path to plugin files.
				</div></dd><dt><span class="term">$g_manage_plugin_threshold</span></dt><dd><div class="para">
					Threshold needed to manage plugins.
				</div></dd><dt><span class="term">$g_plugin_mime_types</span></dt><dd><div class="para">
					A mapping of file extensions to mime types, used when serving resources from plugins.
				</div></dd><dt><span class="term">$g_plugins_force_installed</span></dt><dd><div class="para">
					Force installation and protection of certain plugins. Note that this is not the preferred method of installing plugins, which should generally be done directly through the plugin management interface. However, this method will prevent users with admin access from uninstalling plugins through the plugin management interface.
				</div><div class="para">
					Entries in the array must be in the form of a key/value pair consisting of the plugin basename and priority.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.cookies">
      ⁠</a>5.25. Cookies</h2></div></div></div><div class="para">
	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_cookie_path</span></dt><dd><div class="para">
					Specifies the path under which a cookie is visible.
				</div><div class="para">
					All scripts in this directory and its sub-directories will be able to access MantisBT cookies.
				</div><div class="para">
					Default value is '/'. It is recommended to set this to the actual MantisBT path.
				</div></dd><dt><span class="term">$g_cookie_domain</span></dt><dd><div class="para">
					The domain that the MantisBT cookies are available to.
				</div></dd><dt><span class="term">$g_cookie_samesite</span></dt><dd><div class="para">
					Specifies the <a href="https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite">SameSite attribute</a> to use for the MantisBT cookies.
				</div><div class="para">
					Valid values are <code class="literal">Strict</code>, <code class="literal">Lax</code> (default) or <code class="literal">None</code>.
				</div><div class="para">
					If this setting is changed, users with a non-expired Session cookie (see <span class="emphasis"><em>$g_string_cookie</em></span> below) may need to log out and log back in, to switch the cookie's secure attribute to the new value.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						While <code class="literal">Strict</code> provides stronger protection against CSRF attacks, it actually prevents the user's session from being recognized when clicking a link from a notification e-mail, causing MantisBT to start an anonymous session even if the user is already logged in.
					</div></div></div></dd><dt><span class="term">$g_cookie_prefix</span></dt><dd><div class="para">
					Prefix for all MantisBT cookies
				</div><div class="para">
					This must be an identifier which does not include spaces or periods, and should be unique per MantisBT installation, especially if $g_cookie_path is not restricting the cookies' scope to the actual MantisBT directory.
				</div><div class="para">
					It applies to the cookies listed below. Their actual names are calculated by prepending the prefix, and it is not expected for the user to need to change these.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							$g_bug_list_cookie
						</div></li><li class="listitem"><div class="para">
							$g_collapse_settings_cookie
						</div><div class="para">
							Stores the open/closed state of the collapsible sections.
						</div></li><li class="listitem"><div class="para">
							$g_logout_cookie
						</div></li><li class="listitem"><div class="para">
							$g_manage_config_cookie
						</div><div class="para">
							Stores the filter criteria for the Manage Config Report page.
						</div></li><li class="listitem"><div class="para">
							$g_manage_users_cookie
						</div><div class="para">
							Stores the filter criteria for the Manage Users page.
						</div></li><li class="listitem"><div class="para">
							$g_project_cookie
						</div></li><li class="listitem"><div class="para">
							$g_string_cookie
						</div></li><li class="listitem"><div class="para">
							$g_view_all_cookie
						</div></li></ul></div></dd><dt><span class="term">$g_string_cookie</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_project_cookie</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_view_all_cookie</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_collapse_settings_cookie</span></dt><dd><div class="para">
					Collapse settings cookie. Stores the open/closed state of the collapsible sections.
				</div></dd><dt><span class="term">$g_manage_users_cookie</span></dt><dd><div class="para">
					Stores the filter criteria for the Manage User page
				</div></dd><dt><span class="term">$g_manage_config_cookie</span></dt><dd><div class="para">
					Stores the filter criteria for the Manage Config Report page
				</div></dd><dt><span class="term">$g_logout_cookie</span></dt><dd><div class="para">
					TODO
				</div></dd><dt><span class="term">$g_bug_list_cookie</span></dt><dd><div class="para">
					TODO
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.speed">
      ⁠</a>5.26. Speed Optimisation</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_compress_html</span></dt><dd><div class="para">
					This option is used to enable buffering/compression of HTML output if the user's browser supports it. Default value is ON. This option will be ignored in the following scenarios:
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							php.ini has <span class="emphasis"><em>zlib.output_compression</em></span> enabled.
						</div></li><li class="listitem"><div class="para">
							php.ini has <span class="emphasis"><em>output_handler</em></span> set to a handler.
						</div></li><li class="listitem"><div class="para">
							<a href="https://www.php.net/manual/en/book.zlib.php">zlib extension</a> is not enabled. The Windows version of PHP has built-in support for this extension.
						</div></li></ul></div></dd><dt><span class="term">$g_use_persistent_connections</span></dt><dd><div class="para">
					Use persistent database connections, setting this to ON will open the database once per connection, rather than once per page. There might be some scalability issues here and that is why it is defaulted to OFF.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.reminders">
      ⁠</a>5.27. Reminders</h2></div></div></div><div class="para">
		Sending reminders is a feature where a user can notify / remind other users about a bug. In the past, only selected users like the managers, or developers would get notified about bugs. However, these people can not invite other people (through MantisBT) to look at or monitor these bugs.
	</div><div class="para">
		This feature is useful if the Manager needs to get feedback from testers / requirements team about a certain bug. It avoid needing this person to do this manual outside MantisBT. It also records the history of such reminders.
	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_store_reminders</span></dt><dd><div class="para">
					Specifies if reminders should be stored as bugnotes. The bugnote will still reflect that it is a reminder and list the names of users that got it. Default is ON.
				</div></dd><dt><span class="term">$g_reminder_recipients_monitor_bug</span></dt><dd><div class="para">
					Specifies if users who receive reminders about a bug, should be automatically added to the monitor list of that bug. Default is ON.
				</div></dd><dt><span class="term">$g_mentions_enabled</span></dt><dd><div class="para">
					Enables or disables the @ mentions feature. Default is ON. When a user is @ mentioned in an issue or a note, they receive an email notification to get their attention. Users can be @ mentioned using their username and not realname.
				</div><div class="para">
					This feature works with fields like summary, description, additional info, steps to reproduce and notes.
				</div></dd><dt><span class="term">$g_mentions_tag</span></dt><dd><div class="para">
					The tag to use for prefixing mentions. Default is '@'.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.bughistory">
      ⁠</a>5.28. Bug History</h2></div></div></div><div class="para">
		Bug history is a feature where MantisBT tracks all modifications that are made to bugs. These include everything starting from its creation, till it is closed. For each change, the bug history will record the time stamp, user who made the change, field that changed, old value, and new value.
	</div><div class="para">
		Independent of the these settings, MantisBT will always track the changes to a bug and add them to its history.
	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_history_default_visible</span></dt><dd><div class="para">
					Make the bug history visible by default. If this option is not enabled, then the user will have to click on the Bug History link to see the bug history. Default is ON.
				</div></dd><dt><span class="term">$g_history_order</span></dt><dd><div class="para">
					Show bug history entries in ascending or descending order. Default value is 'ASC'.
				</div></dd></dl></div><div class="para">
		In this context, MantisBT records individual changes to text fields (<span class="emphasis"><em>Description</em></span>, <span class="emphasis"><em>Steps to Reproduce</em></span>, <span class="emphasis"><em>Additional Information</em></span> as well as <span class="emphasis"><em>Bug Notes</em></span>). These revisions are controlled by the following settings.
	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_bug_revision_view_threshold</span></dt><dd><div class="para">
					Access level required to view bug history revisions. Defaults to DEVELOPER.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						Users can always see revisions for the issues and bugnotes they reported.
					</div></div></div></dd><dt><span class="term">$g_bug_revision_drop_threshold</span></dt><dd><div class="para">
					Access level required to drop bug history revisions. Defaults to MANAGER.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.sponsorship">
      ⁠</a>5.29. Sponsorship</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_enable_sponsorship</span></dt><dd><div class="para">
					enable/disable the whole issue sponsorship feature. The default os OFF.
				</div></dd><dt><span class="term">$g_sponsorship_currency</span></dt><dd><div class="para">
					The currency string used for all sponsorships. The default is 'US$'.
				</div></dd><dt><span class="term">$g_minimum_sponsorship_amount</span></dt><dd><div class="para">
					The minimum sponsorship amount that can be entered. If the user enters a value less than this, an error will be flagged. The default is 5.
				</div></dd><dt><span class="term">$g_view_sponsorship_total_threshold</span></dt><dd><div class="para">
					The access level threshold needed to view the total sponsorship for an issue by all users. The default is VIEWER.
				</div></dd><dt><span class="term">$g_view_sponsorship_details_threshold</span></dt><dd><div class="para">
					The access level threshold needed to view the details of the sponsorship (i.e., who will donate what) for an issue by all users. The default is VIEWER.
				</div></dd><dt><span class="term">$g_sponsor_threshold</span></dt><dd><div class="para">
					The access level threshold needed to allow user to sponsor issues. The default is REPORTER. Note that sponsoring user must have their email set in their profile.
				</div></dd><dt><span class="term">$g_handle_sponsored_bugs_threshold</span></dt><dd><div class="para">
					The access level required to be able to handle sponsored issues. The default is DEVELOPER.
				</div></dd><dt><span class="term">$g_assign_sponsored_bugs_threshold</span></dt><dd><div class="para">
					The access level required to be able to assign a sponsored issue to a user with access level greater or equal to 'handle_sponsored_bugs_threshold'. The default is MANAGER.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.customfields">
      ⁠</a>5.30. Custom Fields</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_manage_custom_fields_threshold</span></dt><dd><div class="para">
					Access level needed to manage custom fields. The default is ADMINISTRATOR.
				</div></dd><dt><span class="term">$g_custom_field_link_threshold</span></dt><dd><div class="para">
					Access level needed to link a custom field to a project. The default is MANAGER.
				</div></dd><dt><span class="term">$g_custom_field_edit_after_create</span></dt><dd><div class="para">
					This flag determines whether to start editing a custom field immediately after creating it, or return to the definition list. The default is ON (edit the custom field after creating).
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.myview">
      ⁠</a>5.31. My View Settings</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_my_view_boxes</span></dt><dd><div class="para">
					This is an array of values defining the order that the boxes to be shown. A box that is not to be shown can have its value set to 0. The default is: 
<pre class="programlisting">
$g_my_view_boxes = array(
	'assigned'      =&gt; '1',
	'unassigned'    =&gt; '2',
	'reported'      =&gt; '3',
	'resolved'      =&gt; '4',
	'recent_mod'    =&gt; '5',
	'monitored'     =&gt; '6',
	'feedback'      =&gt; '0',
	'verify'        =&gt; '0',
	'my_comments'   =&gt; '0'
);
</pre>
					 If you want to change the definition, copy the default value and apply the changes.
				</div></dd><dt><span class="term">$g_my_view_bug_count</span></dt><dd><div class="para">
					Number of bugs shown in each box. The default is 10.
				</div></dd><dt><span class="term">$g_default_home_page</span></dt><dd><div class="para">
					Default page to transfer to after Login or Set Project. The default is 'my_view_page.php'. An alternative would be 'view_all_bugs_page.php' or 'main_page.php'.
				</div></dd><dt><span class="term">$g_logout_redirect_page</span></dt><dd><div class="para">
					Specify where the user should be sent after logging out.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.relationship">
      ⁠</a>5.32. Relationship Graphs</h2></div></div></div><div class="para">
		MantisBT can display a graphical representation of the relationships between issues. Two types of interactive visualizations are available, <span class="emphasis"><em>dependencies</em></span> and a full <span class="emphasis"><em>relationships</em></span> graph.
	</div><div xmlns:d="http://docbook.org/ns/docbook" class="important"><div class="admonition_header"><p><strong>Important</strong></p></div><div class="admonition"><div class="para">
			This feature relies on the external <span class="emphasis"><em>dot</em></span> and <span class="emphasis"><em>neato</em></span> tools from the <a href="https://www.graphviz.org/">GraphViz</a> library, which must be installed separately.
		</div><div class="para">
			Most Linux distributions have a GraphViz package available for easy download and install.
		</div><div class="para">
			Under Windows, the software needs to be installed manually. The following post-installation steps <a href="https://mantisbt.org/bugs/view.php?id=27584#c64693">may be required</a> for proper operations: 
			<div class="itemizedlist"><ul><li class="listitem"><div class="para">
						Update the system PATH to point to GraphViz's <code class="literal">bin</code> directory
					</div></li><li class="listitem"><div class="para">
						Initialize the graph engine by running <code class="literal">dot -c</code> from an <span class="emphasis"><em>Administrator</em></span> command prompt.
					</div></li></ul></div>

		</div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_relationship_graph_enable</span></dt><dd><div class="para">
					This enables the relationship graphs feature where issues are represented by nodes and relationships as links between such nodes. Possible values are ON or OFF. Default is OFF.
				</div></dd><dt><span class="term">$g_relationship_graph_fontname</span></dt><dd><div class="para">
					Font name and size, as required by Graphviz. If Graphviz fails to run for you, you are probably using a font name that gd PHP extension can't find. On Linux, try the name of the font file without the extension. The default value is 'Arial'.
				</div></dd><dt><span class="term">$g_relationship_graph_fontsize</span></dt><dd><div class="para">
					Font size, default is 8.
				</div></dd><dt><span class="term">$g_relationship_graph_orientation</span></dt><dd><div class="para">
					Default dependency orientation. If you have issues with lots of children or parents, leave as 'horizontal', otherwise, if you have lots of "chained" issue dependencies, change to 'vertical'. Default is 'horizontal'.
				</div></dd><dt><span class="term">$g_relationship_graph_max_depth</span></dt><dd><div class="para">
					Max depth for relation graphs. This only affects relationship graphs, dependency graphs are drawn to the full depth. The default value is 2.
				</div></dd><dt><span class="term">$g_relationship_graph_view_on_click</span></dt><dd><div class="para">
					If set to ON, clicking on an issue on the relationship graph will open the bug view page for that issue, otherwise, will navigate to the relationship graph for that issue.
				</div></dd><dt><span class="term">$g_dot_tool</span></dt><dd><div class="para">
					The full path for the dot tool. The webserver must have execute permission to this program in order to generate relationship graphs. This configuration option is not relevant for Windows. The default value is '/usr/bin/dot'.
				</div></dd><dt><span class="term">$g_neato_tool</span></dt><dd><div class="para">
					The full path for the neato tool. The webserver must have execute permission to this program in order to generate relationship graphs. This configuration option is not relevant for Windows. The default value is '/usr/bin/neato'.
				</div></dd><dt><span class="term">$g_backward_year_count</span></dt><dd><div class="para">
					Number of years in the past that custom date fields will display in drop down boxes.
				</div></dd><dt><span class="term">$g_forward_year_count</span></dt><dd><div class="para">
					Number of years in the future that custom date fields will display in drop down boxes.
				</div></dd><dt><span class="term">$g_custom_group_actions</span></dt><dd><div class="para">
					This extensibility model allows developing new group custom actions. This can be implemented with a totally custom form and action pages or with a pre-implemented form and action page and call-outs to some functions. These functions are to be implemented in a predefined file whose name is based on the action name. For example, for an action to add a note, the action would be EXT_ADD_NOTE and the file implementing it would be bug_actiongroup_add_note_inc.php. See implementation of this file for details.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.wiki">
      ⁠</a>5.33. Wiki Integration</h2></div></div></div><div class="para">

	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_wiki_enable</span></dt><dd><div class="para">
					Set to ON to enable Wiki integration. Defaults to OFF.
				</div></dd><dt><span class="term">$g_wiki_engine</span></dt><dd><div class="para">
					The following Wiki Engine values are supported: 
					<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								<span class="emphasis"><em>dokuwiki</em></span>: <a href="https://www.dokuwiki.org/">DokuWiki</a>
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>mediawiki</em></span>: <a href="https://www.mediawiki.org/">MediaWiki</a>
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>twiki</em></span>: <a href="http://twiki.org/">TWiki</a>
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>wackowiki</em></span>: <a href="https://wackowiki.org/">WackoWiki</a>
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>wikka</em></span>: <a href="http://wikkawiki.org/">WikkaWiki</a>
							</div></li><li class="listitem"><div class="para">
								<span class="emphasis"><em>xwiki</em></span>: <a href="http://www.xwiki.org/">XWiki</a>
							</div></li></ul></div>

				</div></dd><dt><span class="term">$g_wiki_root_namespace</span></dt><dd><div class="para">
					Wiki namespace to be used as root for all pages relating to this MantisBT installation.
				</div></dd><dt><span class="term">$g_wiki_engine_url</span></dt><dd><div class="para">
					URL under which the wiki engine is hosted.
				</div><div class="para">
					Must be on the same server as MantisBT, requires a trailing '/'.
				</div><div class="para">
					If left empty (default), the URL is derived from the global MantisBT path ($g_path, see <a class="xref" href="#admin.config.path">Section 5.3, “Path”</a>), replacing the URL's path component by the wiki engine string (i.e. if $g_path = 'http://example.com/mantis/' and $g_wiki_engine = 'dokuwiki', the wiki URL will be 'http://example.com/dokuwiki/').
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.subprojects">
      ⁠</a>5.34. Sub-Projects</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_subprojects_enabled</span></dt><dd><div class="para">
					Whether sub-projects feature should be enabled. Before turning this flag OFF, make sure all sub-projects are moved to top level projects, otherwise they won't be accessible. The default value is ON.
				</div></dd><dt><span class="term">$g_subprojects_inherit_versions</span></dt><dd><div class="para">
					Whether sub-projects should inherit versions from parent projects. For project X which is a sub-project of A and B, it will have versions from X, A and B. The default value is ON.
				</div></dd><dt><span class="term">$g_subprojects_inherit_categories</span></dt><dd><div class="para">
					Whether sub-projects should inherit categories from parent projects. For project X which is a sub-project of A and B, it will have categories from X, A and B. The default value is ON.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.fields">
      ⁠</a>5.35. Field Visibility</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_enable_eta</span></dt><dd><div class="para">
					Enable or disable usage of 'ETA' field. Default value is OFF.
				</div></dd><dt><span class="term">$g_enable_projection</span></dt><dd><div class="para">
					Enable or disable usage of 'Projection' field. Default value is OFF.
				</div></dd><dt><span class="term">$g_enable_product_build</span></dt><dd><div class="para">
					Enable or disable usage of 'Product Build' field. Default is OFF.
				</div></dd><dt><span class="term">$g_bug_report_page_fields</span></dt><dd><div class="para">
					An array of optional fields to show on the bug report page.
				</div><div class="para">
					The following optional fields are allowed: additional_info, attachments, category_id, due_date, eta, handler, monitors, os, os_build, platform, priority, product_build, product_version, reproducibility, resolution, severity, status, steps_to_reproduce, tags, target_version, view_state.
				</div><div class="para">
					The summary and description fields are always shown and do not need to be listed in this option. Fields not listed above cannot be shown on the bug report page. Visibility of custom fields is handled via the Manage =&gt; Custom Fields administrator page.
				</div><div class="para">
					Note that <span class="emphasis"><em>monitors</em></span> is not an actual field; adding it to the list will let authorized reporters (see <span class="emphasis"><em>monitor_add_others_bug_threshold</em></span> in <a class="xref" href="#admin.config.misc">Section 5.24, “Misc”</a>) select users to add to the issue's monitoring list. Monitors will only be notified of the submission if both their e-mail preferencess and the <span class="emphasis"><em>notify_flags</em></span> configuration (see <a class="xref" href="#admin.config.email">Section 5.8, “Email”</a>) allows it, i.e. 
<pre class="programlisting">$g_notify_flags['new']['monitor'] = ON;</pre>

				</div><div class="para">
					This setting can be set on a per-project basis by using the Manage =&gt; Configuration administrator page.
				</div></dd><dt><span class="term">$g_bug_view_page_fields</span></dt><dd><div class="para">
					An array of optional fields to show on the issue view page and other pages that include issue details.
				</div><div class="para">
					The following optional fields are allowed: additional_info, attachments, category_id, date_submitted, description, due_date, eta, fixed_in_version, handler, id, last_updated, os, os_build, platform, priority, product_build, product_version, project, projection, reporter, reproducibility, resolution, severity, status, steps_to_reproduce, summary, tags, target_version, view_state.
				</div><div class="para">
					Fields not listed above cannot be shown on the bug view page. Visibility of custom fields is handled via the Manage =&gt; Custom Fields administrator page.
				</div><div class="para">
					This setting can be set on a per-project basis by using the Manage =&gt; Configuration administrator page.
				</div></dd><dt><span class="term">$g_bug_update_page_fields</span></dt><dd><div class="para">
					An array of optional fields to show on the bug update page.
				</div><div class="para">
					The following optional fields are allowed: additional_info, category_id, date_submitted, description, due_date, eta, fixed_in_version, handler, id, last_updated, os, os_build, platform, priority, product_build, product_version, project, projection, reporter, reproducibility, resolution, severity, status, steps_to_reproduce, summary, target_version, view_state.
				</div><div class="para">
					Fields not listed above cannot be shown on the bug update page. Visibility of custom fields is handled via the Manage =&gt; Custom Fields administrator page.
				</div><div class="para">
					This setting can be set on a per-project basis by using the Manage =&gt; Configuration administrator page.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.logging">
      ⁠</a>5.36. System Logging and Debugging</h2></div></div></div><div class="para">
		This section describes settings which can be used to troubleshoot MantisBT operations as well as assist during development.
	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_show_timer</span></dt><dd><div class="para">
					Time page loads. The page execution timer shows at the bottom of each page.
				</div><div class="para">
					Default is OFF.
				</div></dd><dt><span class="term">$g_show_memory_usage</span></dt><dd><div class="para">
					Show memory usage for each page load in the footer.
				</div><div class="para">
					Default is OFF.
				</div></dd><dt><span class="term">$g_debug_email</span></dt><dd><div class="para">
					Used for debugging e-mail notifications. When it is '', the emails are sent normally. If set to an e-mail address, all messages are sent to it, with the original recipients (To, Cc, Bcc) included in the message body.
				</div><div class="para">
					Default is ''.
				</div></dd><dt><span class="term">$g_show_queries_count</span></dt><dd><div class="para">
					Shows the total number/unique number of queries executed to serve the page.
				</div><div class="para">
					Default is OFF.
				</div></dd><dt><span class="term">$g_display_errors</span></dt><dd><div class="para">
					Errors Display Method. Defines what <a href="https://www.php.net/errorfunc.constants"> errors</a> are displayed and how. Available options are:
				</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">DISPLAY_ERROR_HALT</span></dt><dd><div class="para">
								Stop and display the error message (including variables and backtrace if <span class="emphasis"><em>$g_show_detailed_errors</em></span> is ON).
							</div></dd><dt><span class="term">DISPLAY_ERROR_INLINE</span></dt><dd><div class="para">
								Display a one line error and continue execution.
							</div></dd><dt><span class="term">DISPLAY_ERROR_NONE</span></dt><dd><div class="para">
								Suppress the error (no display). This is the default behavior for unspecified <a href="https://www.php.net/errorfunc.constants"> errors constants</a>.
							</div></dd></dl></div><div class="para">
					The default settings are recommended for use in production, and will only display MantisBT fatal errors, suppressing output of all other error types.
				</div><div class="para">
					Recommended <code class="filename">config_inc.php</code> settings for developers: 
<pre class="programlisting">
$g_display_errors = array(
	E_WARNING           =&gt; DISPLAY_ERROR_HALT,
	E_ALL               =&gt; DISPLAY_ERROR_INLINE,
);
</pre>

				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						The system automatically sets <span class="emphasis"><em>$g_display_errors</em></span> to the above recommended development values when the server's name is <span class="emphasis"><em>localhost</em></span>.
					</div></div></div><div class="para">
					Less intrusive settings, recommended for testing purposes: 
<pre class="programlisting">
$g_display_errors = array(
	E_USER_WARNING =&gt; DISPLAY_ERROR_INLINE,
	E_WARNING      =&gt; DISPLAY_ERROR_INLINE,
);
</pre>

				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						<code class="literal">E_USER_ERROR</code>, <code class="literal">E_RECOVERABLE_ERROR</code> and <code class="literal">E_ERROR</code> will always be set to <span class="emphasis"><em>DISPLAY_ERROR_HALT</em></span> internally, regardless of the actual configured value. This ensures that program execution stops, to prevent potential integrity issues and/or MantisBT from functioning incorrectly.
					</div></div></div></dd><dt><span class="term">$g_show_detailed_errors</span></dt><dd><div class="para">
					Shows a list of variables and their values whenever an error is triggered. Only applies to error types configured to <code class="literal">DISPLAY_ERROR_HALT</code> in <span class="emphasis"><em>$g_display_errors</em></span>.
				</div><div class="para">
					Default is OFF.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
						Setting this to ON is a potential security hazard, as it can expose sensitive information. Only enable this setting for debugging purposes when you really need it.
					</div></div></div></dd><dt><span class="term">$g_stop_on_errors</span></dt><dd><div class="para">
					Debug messages. If this option is turned OFF, page redirects will function if a non-fatal error occurs. For debugging purposes, you can set this to ON so that any non-fatal error will prevent page redirection, allowing you to see the errors.
				</div><div class="para">
					Default is OFF.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						This should only be turned on when debugging.
					</div></div></div></dd><dt><span class="term">$g_log_level</span></dt><dd><div class="para">
					The system logging interface is used to extract detailed debugging information for the MantisBT system. It can also serve as an audit trail for users' actions.
				</div><div class="para">
					This controls the type of logging information recorded. Refer to <span class="emphasis"><em>$g_log_destination</em></span> for details on where to save the logs.
				</div><div class="para">
					The available log channels are:
				</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">LOG_NONE</span></dt><dd><div class="para">
								Disable logging
							</div></dd><dt><span class="term">LOG_AJAX</span></dt><dd><div class="para">
								logs AJAX events
							</div></dd><dt><span class="term">LOG_DATABASE</span></dt><dd><div class="para">
								logs database events and executed SQL queries
							</div></dd><dt><span class="term">LOG_EMAIL</span></dt><dd><div class="para">
								logs issue id, message type and recipients for all emails sent
							</div></dd><dt><span class="term">LOG_EMAIL_VERBOSE</span></dt><dd><div class="para">
								Enables extra logging for troubleshooting internals of email queuing and sending.
							</div></dd><dt><span class="term">LOG_EMAIL_RECIPIENT</span></dt><dd><div class="para">
								logs the details of email recipient determination. Each user id is listed as well as why they are added, or deleted from the recipient list
							</div></dd><dt><span class="term">LOG_FILTERING</span></dt><dd><div class="para">
								logs filter operations
							</div></dd><dt><span class="term">LOG_LDAP</span></dt><dd><div class="para">
								logs the details of LDAP operations
							</div></dd><dt><span class="term">LOG_WEBSERVICE</span></dt><dd><div class="para">
								logs the details of Web Services operations (e.g. SOAP API)
							</div></dd><dt><span class="term">LOG_PLUGIN</span></dt><dd><div class="para">
								Enables logging from plugins.
							</div></dd><dt><span class="term">LOG_ALL</span></dt><dd><div class="para">
								combines all of the above log levels
							</div></dd></dl></div><div class="para">
					Default is LOG_NONE.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						Multiple log channels can be combined using <a href="https://www.php.net/language.operators.bitwise"> PHP bitwise operators </a>, e.g. 
<pre class="programlisting">$g_log_level = LOG_EMAIL | LOG_EMAIL_RECIPIENT;</pre>
						 or 
<pre class="programlisting">$g_log_level = LOG_ALL &amp; ~LOG_DATABASE;</pre>

					</div></div></div></dd><dt><span class="term">$g_log_destination</span></dt><dd><div class="para">
					Specifies where the log data goes. The following five options are available:
				</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">''</span></dt><dd><div class="para">
								The empty string means <a href="https://www.php.net/error_log"> default PHP error log settings </a>
							</div></dd><dt><span class="term">'none'</span></dt><dd><div class="para">
								Don't output the logs, but would still trigger EVENT_LOG plugin event.
							</div></dd><dt><span class="term">'file'</span></dt><dd><div class="para">
								Log to a specific file, specified as an absolute path, e.g. <code class="literal">'file:/var/log/mantis.log'</code> (Unix) or <code class="literal">'file:c:/temp/mantisbt.log'</code> (Windows)
							</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
									This file must be writable by the web server running MantisBT.
								</div></div></div></dd><dt><span class="term">'page'</span></dt><dd><div class="para">
								Display log output at bottom of the page. See also <span class="emphasis"><em>$g_show_log_threshold</em></span> to restrict who can see log data.
							</div></dd></dl></div><div class="para">
					Default is '' (empty string).
				</div></dd><dt><span class="term">$g_show_log_threshold</span></dt><dd><div class="para">
					Indicates the access level required for a user to see the log output (if <span class="emphasis"><em>$g_log_destination</em></span> is 'page').
				</div><div class="para">
					Default is ADMINISTRATOR.
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						This threshold is compared against the user's <span class="emphasis"><em>global access level</em></span> rather than the one from the currently active project.
					</div></div></div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.timetracking">
      ⁠</a>5.37. Time Tracking</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_time_tracking_enabled</span></dt><dd><div class="para">
					Turns Time Tracking features ON or OFF - Default is OFF
				</div></dd><dt><span class="term">$g_time_tracking_without_note</span></dt><dd><div class="para">
					Allow time tracking to be recorded without writing some text in the associated bugnote - Default is ON
				</div></dd><dt><span class="term">$g_time_tracking_with_billing</span></dt><dd><div class="para">
					Adds calculation links to workout how much time has been spent between a particular time frame. Currently it will allow you to enter a cost/hour and will work out some billing information. This will become more extensive in the future. Currently it is more of a proof of concept.
				</div></dd><dt><span class="term">$g_time_tracking_billing_rate</span></dt><dd><div class="para">
					Default billing rate per hour - Default is 0
				</div></dd><dt><span class="term">$g_time_tracking_stopwatch</span></dt><dd><div class="para">
					Instead of a text field turning this option on places a stopwatch on the page with <span class="guibutton"><strong>Start/Stop</strong></span> and <span class="guibutton"><strong>Reset</strong></span> buttons next to it. A bit gimmicky, but who cares.
				</div></dd><dt><span class="term">$g_time_tracking_view_threshold</span></dt><dd><div class="para">
					Access level required to view time tracking information - Default DEVELOPER.
				</div></dd><dt><span class="term">$g_time_tracking_edit_threshold</span></dt><dd><div class="para">
					Access level required to add/edit time tracking information (If you give a user $g_time_tracking_edit_threshold you must give them $g_time_tracking_view_threshold as well) - Default DEVELOPER.
				</div></dd><dt><span class="term">$g_time_tracking_reporting_threshold</span></dt><dd><div class="para">
					Access level required to run reports (not completed yet) - Default MANAGER.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.api">
      ⁠</a>5.38. API</h2></div></div></div><div class="para">
		MantisBT exposes a webservice API which allows remote clients to interact with MantisBT and perform many of the usual tasks, such as reporting issues, running filtered searches and retrieving attachments.
	</div><div class="para">
		The SOAP API is enabled by default and available at <code class="literal">/api/soap/mantisconnect.php</code> below the MantisBT root. A WSDL file which describes the web service is available at <code class="literal">/api/soap/mantisconnect.php?wsdl</code> below the MantisBT root.
	</div><div class="para">
		The REST API is enabled by default. A Swagger sandbox and documentation for REST API is available at <code class="literal">/api/rest/swagger/</code> below the MantisBT root.
	</div><div class="para">
		The following options are used to control the behaviour of the MantisBT SOAP API:
	</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_webservice_rest_enabled</span></dt><dd><div class="para">
					Whether the REST API is enabled or not. Note that this flag only impacts API Token based auth. Hence, even if the API is disabled, it can still be used from the Web UI using cookie based authentication. Default ON.
				</div></dd><dt><span class="term">$g_webservice_readonly_access_level_threshold</span></dt><dd><div class="para">
					Minimum global access level required to access webservice for readonly operations.
				</div></dd><dt><span class="term">$g_webservice_readwrite_access_level_threshold</span></dt><dd><div class="para">
					Minimum global access level required to access webservice for read/write operations.
				</div></dd><dt><span class="term">$g_webservice_admin_access_level_threshold</span></dt><dd><div class="para">
					Minimum global access level required to access the administrator webservices.
				</div></dd><dt><span class="term">$g_webservice_specify_reporter_on_add_access_level_threshold</span></dt><dd><div class="para">
					Minimum project access level required for caller to be able to specify reporter when adding issues or issue notes. Defaults to DEVELOPER.
				</div></dd><dt><span class="term">$g_webservice_priority_enum_default_when_not_found</span></dt><dd><div class="para">
					The following enum id is used when the webservices get enum labels that are not defined in the associated MantisBT installation. In this case, the enum id is set to the value specified by the corresponding configuration option.
				</div></dd><dt><span class="term">$g_webservice_severity_enum_default_when_not_found</span></dt><dd><div class="para">
					The following enum id is used when the webservices get enum labels that are not defined in the associated MantisBT installation. In this case, the enum id is set to the value specified by the corresponding configuration option.
				</div></dd><dt><span class="term">$g_webservice_status_enum_default_when_not_found</span></dt><dd><div class="para">
					The following enum id is used when the webservices get enum labels that are not defined in the associated MantisBT installation. In this case, the enum id is set to the value specified by the corresponding configuration option.
				</div></dd><dt><span class="term">$g_webservice_resolution_enum_default_when_not_found</span></dt><dd><div class="para">
					The following enum id is used when the webservices get enum labels that are not defined in the associated MantisBT installation. In this case, the enum id is set to the value specified by the corresponding configuration option.
				</div></dd><dt><span class="term">$g_webservice_projection_enum_default_when_not_found</span></dt><dd><div class="para">
					The following enum id is used when the webservices get enum labels that are not defined in the associated MantisBT installation. In this case, the enum id is set to the value specified by the corresponding configuration option.
				</div></dd><dt><span class="term">$g_webservice_eta_enum_default_when_not_found</span></dt><dd><div class="para">
					The following enum id is used when the webservices get enum labels that are not defined in the associated MantisBT installation. In this case, the enum id is set to the value specified by the corresponding configuration option.
				</div></dd><dt><span class="term">$g_webservice_error_when_version_not_found</span></dt><dd><div class="para">
					If ON and the supplied version is not found, then a SoapException will be raised.
				</div></dd><dt><span class="term">$g_webservice_version_when_not_found</span></dt><dd><div class="para">
					Default version to be used if the specified version is not found and $g_webservice_error_when_version_not_found == OFF. (at the moment this value does not depend on the project).
				</div></dd></dl></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.api.disable">
      ⁠</a>5.38.1. Disabling the webservice API</h3></div></div></div><div class="para">
			If you wish to temporarily disable the webservice API it is sufficient to set the specific access thresholds to NOBODY:
		</div><div class="para">
			<code class="literal">$g_webservice_readonly_access_level_threshold = $g_webservice_readwrite_access_level_threshold = $g_webservice_admin_access_level_threshold = NOBODY;</code>
		</div><div class="para">
			While the SOAP API will still be accessible, it will not allow users to retrieve or modify data.
		</div></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.antispam">
      ⁠</a>5.39. Anti-Spam Configuration</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_antispam_max_event_count</span></dt><dd><div class="para">
					Max number of events to allow for users with default access level (see $g_default_new_account_access_level) when signup is enabled. Use 0 for no limit. Default is 10.
				</div></dd><dt><span class="term">$g_antispam_time_window_in_seconds</span></dt><dd><div class="para">
					Time window to enforce max events within. Default is 3600 seconds (1 hour).
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.duedate">
      ⁠</a>5.40. Due Date</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_due_date_update_threshold</span></dt><dd><div class="para">
					Threshold to update due date submitted. Default is NOBODY.
				</div></dd><dt><span class="term">$g_due_date_view_threshold</span></dt><dd><div class="para">
					Threshold to see due date. Default is NOBODY.
				</div></dd><dt><span class="term">$g_due_date_default</span></dt><dd><div class="para">
					Default due date value for newly submitted issues. A valid <a href="https://php.net/manual/en/datetime.formats.relative.php">relative date format</a> e.g. <code class="literal">today</code> or <code class="literal">+2 days</code>, or empty string for no due date set (default).
				</div></dd><dt><span class="term">$g_due_date_warning_levels</span></dt><dd><div class="para">
					Due date warning levels. A variable number of Levels (defined as a number of seconds going backwards from the current timestamp, compared to an issue's due date) can be defined. Levels must be defined in ascending order. 
					<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								The first entry (array key 0) defines <span class="emphasis"><em>Overdue</em></span>. Normally and by default, its value is <code class="literal">0</code>, meaning that issues will be marked overdue as soon as their due date has passed. However, it is also possible to set it to a higher value to flag overdue issues earlier, or even use a negative value to allow a "grace period" after due date.
							</div></li><li class="listitem"><div class="para">
								Array keys 1 and 2 offer two levels of <span class="emphasis"><em>Due soon</em></span>: orange and green. By default, only the first one is set, to 7 days.
							</div></li></ul></div>
					 Out of the box, MantisBT allows for 3 warning levels. Additional ones may be defined, but in that case new <code class="literal">due-N</code> CSS rules (where N is the array's index) must be created otherwise the extra levels will not be highlighted in the UI.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.users">
      ⁠</a>5.41. User Management</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_impersonate_user_threshold</span></dt><dd><div class="para">
					The threshold for a user to be able to impersonate another user, or NOBODY to disable impersonation. Default ADMINISTRATOR.
				</div></dd><dt><span class="term">$g_manage_user_threshold</span></dt><dd><div class="para">
					The threshold for a user to manage user accounts. Default ADMINISTRATOR.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.view">
      ⁠</a>5.42. View Page Settings</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_issue_activity_note_attachments_seconds_threshold</span></dt><dd><div class="para">
					If a user submits a note with an attachments (with the specified # of seconds) the attachment is linked to the note. Or 0 for disabling this feature.
				</div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.issues">
      ⁠</a>5.43. Issues visibility</h2></div></div></div><div class="para">
		By default, all issues are visible to any user within a project. To limit the visibility of issues there are several mechanisms.
	</div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.issues.private">
      ⁠</a>5.43.1. Public/Private view status</h3></div></div></div><div class="para">
			A view status flag can be set, for an issue, to be either public or private. Private issues are accesible by the user who created it, and by those users that meet a threshold defined in <code class="literal">$g_private_bug_threshold</code>.
		</div><div class="para">
			Refer to the following configuration options related to issue view status configurations:
		</div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_private_bug_threshold</span></dt><dd><div class="para">
						The threshold for a user to be able to view any private issue within a project.
					</div></dd><dt><span class="term">$g_set_view_status_threshold</span></dt><dd><div class="para">
						The threshold for a user to be able to set an issue to Private/Public.
					</div></dd><dt><span class="term">$g_change_view_status_threshold</span></dt><dd><div class="para">
						The threshold for a user to be able to update the view status while updating an issue.
					</div></dd></dl></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.issues.limitedview">
      ⁠</a>5.43.2. Limited view configuration</h3></div></div></div><div class="para">
			The <code class="literal">$g_limit_view_unless_threshold</code> option allows the administrator to configure access limitations for users, letting them view only those issues that they are involved with, i.e. if: 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						They reported the issue,
					</div></li><li class="listitem"><div class="para">
						It is assigned to them,
					</div></li><li class="listitem"><div class="para">
						Or they are monitoring the issue.
					</div></li></ul></div>

		</div><div class="para">
			This configuration option can be set individually for each project. It defaults to ANYBODY, effectively disabling the limitation (i.e. users can see all issues).
		</div><div class="para">
			The value for this option is an access level threshold, so that those users that meet that threshold have an unrestricted view of any issue in the project. A user that doesn't meet this threshold, will have a restricted view of only those issues in the conditions previously described.
		</div><div class="para">
			Note that this visibility does not override other restrictions as <span class="emphasis"><em>private issues</em></span> or <span class="emphasis"><em>pivate projects</em></span> user assignments.
		</div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.issues.limitreporters">
      ⁠</a>5.43.3. "Limit reporters" configuration (deprecated)</h3></div></div></div><div class="para">
			When the option <code class="literal">$g_limit_reporters</code> is enabled, users that are reporters in a project, or lower access level, are only allowed to see the issues they reported. Issues reported by other users are not accessible by them.
		</div><div class="para">
			This option is only supported for ALL_PROJECTS, this means that it's a global setting that affects all projects
		</div><div class="para">
			Note that the definition of <span class="emphasis"><em>reporter</em></span> in this context is the actual access level for which a user is able to report issues, and is determined by <code class="literal">$g_report_bug_threshold</code>. Additionally, that threshold can have different values in each project. Being dependant on that threshold, the behaviour of this option is not well defined when the reporting threshold is configured as discrete values with gaps, instead of a simple threshold. In that scenario, the visibilty is determined by the minimum access level contained in the <code class="literal">$g_report_bug_threshold</code> access levels array.
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
				This option option is deprecated in favour of <code class="literal">$g_limit_view_unless_threshold</code>. The new option will be available by default on new installations, or after disabling <code class="literal">$g_limit_reporters</code> if enabled in an existing instance.
			</div></div></div></div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.pages">
      ⁠</a>Chapter 6. Page descriptions</h1></div></div></div><div class="toc"><dl class="toc"><dt><span class="section"><a href="#admin.pages.login">6.1. Login page</a></span></dt><dt><span class="section"><a href="#admin.pages.main">6.2. Main page</a></span></dt><dt><span class="section"><a href="#admin.pages.filter">6.3. View Issues page</a></span></dt><dt><span class="section"><a href="#admin.pages.issueview">6.4. Issue View page</a></span></dt><dt><span class="section"><a href="#admin.pages.issuestatus">6.5. Issue Change Status page</a></span></dt><dt><span class="section"><a href="#admin.pages.issueedit">6.6. Issue Edit page</a></span></dt><dt><span class="section"><a href="#admin.pages.account">6.7. My Account Page</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.pages.account.prefs">6.7.1. Preferences</a></span></dt><dt><span class="section"><a href="#admin.pages.account.profiles">6.7.2. Profiles</a></span></dt><dt><span class="section"><a href="#admin.pages.account.managecolumns">6.7.3. Manage Columns</a></span></dt><dt><span class="section"><a href="#admin.pages.account.apitokens">6.7.4. API Tokens</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.pages.manage">6.8. System Management Pages</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.pages.manage.users">6.8.1. Users</a></span></dt><dt><span class="section"><a href="#admin.pages.manage.projects">6.8.2. Manage Projects Page</a></span></dt><dt><span class="section"><a href="#admin.pages.manage.customfields">6.8.3. Manage Custom Fields</a></span></dt><dt><span class="section"><a href="#admin.pages.manage.profiles">6.8.4. Global Profiles</a></span></dt><dt><span class="section"><a href="#admin.pages.manage.config">6.8.5. Configuration</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.pages.monitor">6.9. Monitor Issue</a></span></dt><dt><span class="section"><a href="#admin.pages.reopen">6.10. Reopen Issue</a></span></dt><dt><span class="section"><a href="#admin.pages.delete">6.11. Delete Issue</a></span></dt><dt><span class="section"><a href="#admin.pages.close">6.12. Close Issue</a></span></dt><dt><span class="section"><a href="#admin.pages.assigntome">6.13. Assign to Me</a></span></dt><dt><span class="section"><a href="#admin.pages.resolve">6.14. Resolve Issue</a></span></dt><dt><span class="section"><a href="#admin.pages.news">6.15. News Syndication</a></span></dt></dl></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.login">
      ⁠</a>6.1. Login page</h2></div></div></div><div class="para">
			Just enter your username and password and hit the login button. There is also a Save Login checkbox to have the package remember that you are logged in between browser sessions. You will have to have cookies enabled to login.If the account doesn't exist, the account is disabled, or the password is incorrect then you will remain at the login page. An error message will be displayed.The administrator may allow users to sign up for their own accounts. If so, a link to Signup for your own account will be available.The administrator may also have anonymous login allowed. Anonymous users will be logged in under a common account.You will be allowed to select a project to work in after logging in. You can make a project your default selection from the Select Project screen or from your Account Options.SignupHere you can signup for a new account. You must supply a valid email address and select a unique username. Your randomly generated password will be emailed to your email account. If MantisBT is setup so that the email password is not to be emailed, newly generated accounts will have an empty password.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.main">
      ⁠</a>6.2. Main page</h2></div></div></div><div class="para">
			This is the first page you see upon logging in. It shows you the latest news updates for the bugtracker. This is a simple news module (based off of work by Scott Roberts) and is to keep users abreast of changes in the bugtracker or project. Some news postings are specific to projects and others are global across the entire bugtracker. This is set at the time of posting in the Edit News section.The number of news posts is controlled by a global variable. When the number of posts is more than the limit, a link to show "older news" is displayed at the bottom. Similarly a "newer news" is displayed when you have clicked on "older news".There is an Archives option at the bottom of the page to view all listings.ArchivesA title/date/poster listing of ALL past news articles will be listed here. Clicking on the link will bring up the specified article. This listing will also only display items that are either global or specific to the selected project.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.filter">
      ⁠</a>6.3. View Issues page</h2></div></div></div><div class="para">
			Here we can view the issue listings. The page has a set of viewing filters at the top and the issues are listed below.FiltersThe filters control the behavior of the issues list. The filters are saved between browsing sessions but do not currently save sort order or direction.If the number of issues exceeds the "Show" count in the filter a set of navigation to go to "First", "Last", "Previous", "Next" and specific page numbers are added.The Search field will look for simple keyword matches in the summary, description, steps to reproduce, additional information, issue id, or issue text id fields. It does not search through issue notes. Issue List - The issues are listed in a table and the attributes are listed in the following order: priority, id, number of issue notes, category, severity, status, last updated, and summary. Each (except for number of issue notes) can be clicked on to sort by that column. Clicking again will reverse the direction of the sort. The default is to sort by last modification time, where the last modified issue appears at the top. The issue id is a link that leads to a more detailed report about the issue. You can also add issue notes here. The number in the issue note count column will be bold if an issue note has been added in the specified time frame. The addition of an issue note will make the issue note link of the issue appear in the unvisited state. The text in the "Severity" column will be bold if the severity is major, crash, or block and the issue not resolved. The text in the "Updated" column will be bold if the issue has changed in the last "Changed(hrs)" field which is specified in the viewing filters. Each table row is color coded according to the issue status. The colors can be customised through MantisBT configuration pages (see <a class="xref" href="#admin.config">Chapter 5, <em>Configuration</em></a> for details). Severities block - prevents further work/progress from being made crash - crashes the application or blocking, major - major issue, minor - minor issue, tweak - needs tweaking, text - error in the text, trivial - being nit picky, feature - requesting new feature - Status new - new issue, feedback - issue requires more information from reporter, acknowledged - issue has been looked at but not confirmed or assigned, confirmed - confirmed and reproducible (typically set by an Updater or other Developer), assigned - assigned to a Developer, resolved - issue should be fixed, waiting on confirmation of fix, closed - issue is closed, Moving the mouse over the status text will show the resolution as a title. This is rendered by some browsers as a bubble and in others as a status line text.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.issueview">
      ⁠</a>6.4. Issue View page</h2></div></div></div><div class="para">
			Here is the simple listing of the issue report. Most of the fields are self-explanatory. "Assigned To" will contain the developer assigned to handle the issue. Priority is fully functional but currently does nothing of importance. Duplicate ID is used when an issue is a duplicate of another. It links to the duplicate issue which allows users to read up on the original issue report. Below the issue report is a set of buttons that a user can select to work on the issue.
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
					Update Issue - brings up a page to edit all aspects of the issue
				</div></li><li class="listitem"><div class="para">
					Assign to - in conjunction with the dropdown list next top the button, this is a shortcut to change the assignment of an issue
				</div></li><li class="listitem"><div class="para">
					Change Status to - in conjunction with the dropdown list next top the button, this is a shortcut to change the status of an issue. Another page (Change Status) will be presented to allow the user to add notes or change relevant information
				</div></li><li class="listitem"><div class="para">
					Monitor / Unmonitor Issue - allows the user to monitor any additions to the issue by email
				</div></li><li class="listitem"><div class="para">
					Create Clone - create a copy of the current issue. This presents the user with a new issue reporting form with all of the information in the current issue filled in. Upon submission, a new issue, related to the current issue, will be created.
				</div></li><li class="listitem"><div class="para">
					Reopen Issue - Allows the user to re-open a resolved issue
				</div></li><li class="listitem"><div class="para">
					Move Issue - allows the user to move the issue to another project
				</div></li><li class="listitem"><div class="para">
					Delete Issue - Allows the user to delete the issue permanently. It is recommended against deleting issues unless the entry is frivolous. Instead issues should be set to resolved and an appropriate resolution category chosen.
				</div></li></ul></div><div class="para">
			A panel is provided to view and update the sponsorship of an issue.Another panel is provided to view, delete and add relationships for an issue. Issues can have a parent/child relationship, where the user is warned about resolving a parent issue before all of the children are resolved. A peer relationship is also possible.Below this, there may be a form for uploading file attachments. The Administrator needs to configure the bugtracker to handle file uploads. If uploading to disk is selected, each project needs to set its own upload path. Issue notes are shown at the bottom of the issue report. A panel to add issue notes is also shown.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.issuestatus">
      ⁠</a>6.5. Issue Change Status page</h2></div></div></div><div class="para">
			This page is used to change the status of an issue. A user can add an issue note to describe the reason for change.In addition, the following fields may be displayed for update: 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						Resolution and Duplicate ID - for issues being resolved or closed
					</div></li><li class="listitem"><div class="para">
						Issue Handler (Assigned to)
					</div></li><li class="listitem"><div class="para">
						any Custom Fields that are to be visible on update or resolution
					</div></li><li class="listitem"><div class="para">
						Fixed in Version - for issues being resolved
					</div></li><li class="listitem"><div class="para">
						Close Immediately - to immediately close a resolved issue
					</div></li></ul></div>

		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.issueedit">
      ⁠</a>6.6. Issue Edit page</h2></div></div></div><div class="para">
			The layout of this page resembles the Simple Issue View page, but here you can update various issue fields. The Reporter, Category, Severity, and Reproducibility fields are editable but shouldn't be unless there is a gross mis-categorization.Also modifiable are the Assigned To, Priority, Projection, ETA, Resolution, and Duplicate ID fields.As per version 0.18.0, the user can also add an issue note as part of an issue update.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.account">
      ⁠</a>6.7. My Account Page</h2></div></div></div><div class="para">
			This page changes user alterable parameters for the system. These selections are user specific. This allows the user to change their password, username, real name and email address. It also reports the user's access levels on the current project and default access level used for public projects.
		</div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.account.prefs">
      ⁠</a>6.7.1. Preferences</h3></div></div></div><div class="para">
				This sets the following information: 
				<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							Default project
						</div></li><li class="listitem"><div class="para">
							whether the pages used for reporting, viewing, and updating are the simple or advanced views
						</div></li><li class="listitem"><div class="para">
							the delay in minutes between refreshes of the view all issues page
						</div></li><li class="listitem"><div class="para">
							the delay in seconds when redirecting from a confirmation page to the display page
						</div></li><li class="listitem"><div class="para">
							the time order in which notes will be sorted
						</div></li><li class="listitem"><div class="para">
							whether to filter email messages based on type of message and severity
						</div></li><li class="listitem"><div class="para">
							the number of notes to append to notification emails
						</div></li><li class="listitem"><div class="para">
							the default language for the system. The additional setting of "auto" will use the browser's default language for the system.
						</div></li></ul></div>

			</div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.account.profiles">
      ⁠</a>6.7.2. Profiles</h3></div></div></div><div class="para">
				Profiles are shortcuts to define the values for Platform, OS, and version. This page allows you to define and edit personal shortcuts.
			</div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.account.managecolumns">
      ⁠</a>6.7.3. Manage Columns</h3></div></div></div><div class="para">
				Provides the ability to select the fields to be displayed in View Issues, Print Issues, CSV and Excel exports. The changes apply to the currently selected projects or All Projects for setting the defaults. It is also possible to copy such settings from/to other projects.
			</div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.account.apitokens">
      ⁠</a>6.7.4. API Tokens</h3></div></div></div><div class="para">
				Provides the ability to generate and revoke tokens that can be used by applications and services to access MantisBT via its APIs. This page also provides information about the creation and last used timestamps for such tokens.
			</div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.manage">
      ⁠</a>6.8. System Management Pages</h2></div></div></div><div class="para">
			A number of pages exist under the "Manage" link. These will only be visible to those who have an appropriate access level.
		</div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.manage.users">
      ⁠</a>6.8.1. Users</h3></div></div></div><div class="para">
				This page allow an administrator to manage the users in the system.It essentially supplies a list of users defined in the system. The user names are linked to a page where you can change the user's name, access level, and projects to which they are assigned. You can also reset their passwords through this page.At the top, there is also a list of new users (who have created an account in the last week), and accounts where the user has yet to log in.New users are created using the "Create User" link above the list of existing users. Note that the username must be unique in the system. Further, note that the user's real name (as displayed on the screen) cannot match another user's user name.
			</div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.manage.projects">
      ⁠</a>6.8.2. Manage Projects Page</h3></div></div></div><div class="para">
				This page allows the user to manage the projects listed in the system.Each project is listed along with a link to manage that specific project. The specific project pages allow the user to change: 
				<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							the project name
						</div></li><li class="listitem"><div class="para">
							the project description
						</div></li><li class="listitem"><div class="para">
							its status
						</div></li><li class="listitem"><div class="para">
							whether the project is public or private. Private projects are only visible to users who are assigned to it or users who have the access level to automatically have access to private projects (eg: administrators).
						</div></li><li class="listitem"><div class="para">
							file directory used to store attachments for issues and documents associated with the project. This folder is located on the webserver, it can be absolute path or path relative to the main MantisBT folder. Note that this is only used if the files are stored on disk.
						</div></li><li class="listitem"><div class="para">
							common subprojects. These are other projects who can be considered a sub-project of this one. They can be shared amongst multiple projects. For example, a "documentation" project may be shared amongst several development projects.
						</div></li><li class="listitem"><div class="para">
							project categories. These are used to sub-divide the issues stored in the system.
						</div></li><li class="listitem"><div class="para">
							project versions. These are used to create ChangeLog reports and can be used to filter issues. They are used for both the Found In and Fixed In versions.
						</div></li><li class="listitem"><div class="para">
							Custom Fields linked to this project
						</div></li><li class="listitem"><div class="para">
							Users linked to this project. Here is the place where a user's access level may be upgraded or downgraded depending on their particular role in the project.
						</div></li></ul></div>

			</div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.manage.customfields">
      ⁠</a>6.8.3. Manage Custom Fields</h3></div></div></div><div class="para">
				This page is the base point for managing custom fields. It lists the custom fields defined in the system. There is also a place to enter a new field name to create a new field.
			</div><div class="para">
				The "Edit" links take you to a page where you can define the details of a custom field. These include it's name, type, value, and display information. On the edit page, the following information is defined to control the custom field:
			</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						name
					</div></li><li class="listitem"><div class="para">
						type
					</div></li><li class="listitem"><div class="para">
						Value constraints (Possible values, default value, regular expression, minimum length, maximum length).
					</div></li><li class="listitem"><div class="para">
						Access (who can read and write the field based on their access level).
					</div></li><li class="listitem"><div class="para">
						Display control (where the field will show up and must be filled in
					</div></li></ul></div><div class="para">
				All fields are compared in length to be greater than or equal to the minimum length, and less than or equal to the minimum length, unless these values are 0 in which case the check is skipped. All fields are also compared against the regular expression; if the value matches, then it is valid. For example, the expression <code class="literal">^-?([0-9])*$</code> can be used to constrain an integer.
			</div><div class="para">
				Please refer to <a class="xref" href="#admin.customize.customfields">Section 7.2, “Custom Fields”</a> for further details about Custom Fields and all the above-mentioned properties.
			</div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.manage.profiles">
      ⁠</a>6.8.4. Global Profiles</h3></div></div></div><div class="para">
				This page allows the definition of global profiles accessible to all users of the system. It is similar to the user definition of a profile consisting of Platform, OS and Version.
			</div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.manage.config">
      ⁠</a>6.8.5. Configuration</h3></div></div></div><div class="para">
				This set of pages control the configuration of the MantisBT system. Note that the configuration items displayed may be on a project by project basis.These pages serve two purposes. First, they will display the settings for the particular aspects of the system. If authorized, they will allow a user to change the parameters. They also have settings for what access level is required to change these settings ON A PROJECT basis. In general, this should be left alone, but administrators may want to delegate some of these settings to managers.
			</div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.pages.manage.config.thresholds">
      ⁠</a>6.8.5.1. Workflow Thresholds</h4></div></div></div><div class="para">
					This page covers the adjustment of the settings for many of the workflow related parameters. For most of these, the fields are self explanatory and relate to a similarly named setting in the configuration file. At the right of each row is a selector that allows the administrator to lower the access level required to change the particular parameter.The values changeable on this page are:
				</div><div class="para"><div xmlns:d="http://docbook.org/ns/docbook" class="title">Issues</div>
						<div class="informaltable"><table xmlns:d="http://docbook.org/ns/docbook" class="lt-4-cols gt-14-rows"><colgroup><col /><col /><col /></colgroup><thead><tr><th>Title</th><th>Variable</th><th>Description</th></tr></thead><tbody><tr><td>Report an Issue</td><td>$g_report_bug_threshold</td><td>threshold to report an issue</td></tr><tr><td>Status to which a new issue is set</td><td>$g_bug_submit_status</td><td>status issue is set to when submitted</td></tr><tr><td>Update an Issue</td><td>$g_update_bug_threshold</td><td>threshold to update an issue</td></tr><tr><td>Allow Reporter to close an issue</td><td>$g_allow_reporter_close</td><td>allow reporter to close issues they reported</td></tr><tr><td>Monitor an issue</td><td>$g_monitor_bug_threshold</td><td>threshold to monitor an issue</td></tr><tr><td>Handle Issue</td><td>$g_handle_bug_threshold</td><td>threshold to handle (be assigned) an issue</td></tr><tr><td>Assign Issue</td><td>$g_update_bug_assign_threshold</td><td>threshold to be in the assign to list</td></tr><tr><td>Move Issue</td><td>$g_move_bug_threshold</td><td>threshold to move an issue to another project. This setting is for all projects. </td></tr><tr><td>Delete Issue</td><td>$g_delete_bug_threshold</td><td>threshold to delete an issue</td></tr><tr><td>Reopen Issue</td><td>$g_reopen_bug_threshold</td><td>threshold to reopen an issue</td></tr><tr><td>Allow reporter to reopen Issue</td><td>$g_allow_reporter_reopen</td><td>allow reporter to reopen issues they reported</td></tr><tr><td>Status to which a reopened Issue is set</td><td>$g_bug_reopen_status</td><td>status issue is set to when reopened</td></tr><tr><td>Resolution to which a reopened Issue is set</td><td>$g_bug_reopen_resolution</td><td>resolution issue is set to when reopened</td></tr><tr><td>Status where an issue is considered resolved</td><td>$g_bug_resolved_status_threshold</td><td>status where bug is resolved</td></tr><tr><td>Status where an issue becomes read-only</td><td>$g_bug_readonly_status_threshold</td><td>status where bug is read-only (see update_readonly_bug_threshold) </td></tr><tr><td>Update readonly issue</td><td>$g_update_readonly_bug_threshold</td><td>threshold to update an issue marked as read-only</td></tr><tr><td>Update Issue Status</td><td>$g_update_bug_status_threshold</td><td>threshold to update an issue's status</td></tr><tr><td>View Private Issues</td><td>$g_private_bug_threshold</td><td>threshold to view a private issue</td></tr><tr><td>Set View Status</td><td>$g_set_view_status_threshold</td><td>threshold to set an issue to Private/Public</td></tr><tr><td>Update View Status</td><td>$g_change_view_status_threshold</td><td>threshold needed to update the view status while updating an issue or an issue note </td></tr><tr><td>Show list of users monitoring issue</td><td>$g_show_monitor_list_threshold</td><td>threshold to see who is monitoring an issue</td></tr><tr><td>Add monitors to an issue</td><td>$g_monitor_add_others_bug_threshold</td><td>threshold to add users to the list of users monitoring an issue</td></tr><tr><td>Remove monitors from an issue</td><td>$g_monitor_delete_others_bug_threshold</td><td>threshold to remove users from the list of users monitoring an issue</td></tr><tr><td>Set status on assignment of handler</td><td>$g_auto_set_status_to_assigned</td><td>change status when an issue is assigned</td></tr><tr><td>Status to set auto-assigned issues to</td><td>$g_bug_assigned_status</td><td>status to use when an issue is auto-assigned</td></tr><tr><td>Limit reporter's access to their own issues (deprecated option)</td><td>$g_limit_reporters</td><td>reporters can see only issues they reported. This setting is for all projects. </td></tr><tr><td>Limit access only to those issues reported, handled, or monitored by the user</td><td>$g_limit_view_unless_threshold</td><td>threshold that, if not met, hides other users' issues. </td></tr></tbody></table></div>
					</div><div class="para"><div xmlns:d="http://docbook.org/ns/docbook" class="title">Notes</div>
						<div class="informaltable"><table xmlns:d="http://docbook.org/ns/docbook" class="lt-4-cols gt-7-rows"><colgroup><col /><col /><col /></colgroup><thead><tr><th>Title</th><th>Variable</th><th>Description</th></tr></thead><tbody><tr><td>Add Notes</td><td>$g_add_bugnote_threshold</td><td>threshold to add an issue note</td></tr><tr><td>Update Others' Notes</td><td>$g_update_bugnote_threshold</td><td>threshold at which a user can edit issue notes created by other users</td></tr><tr><td>Update Own Notes</td><td>$g_bugnote_user_edit_threshold</td><td>threshold at which a user can edit issue notes created by themselves</td></tr><tr><td>Delete Others' Notes</td><td>$g_delete_bugnote_threshold</td><td>threshold at which a user can delete issue notes created by other users</td></tr><tr><td>Delete Own Notes</td><td>$g_bugnote_user_delete_threshold</td><td>threshold at which a user can delete issue notes created by themselves</td></tr><tr><td>View private notes</td><td>$g_private_bugnote_threshold</td><td>threshold to view a private issue note</td></tr><tr><td>Change view state of own notes</td><td>$g_bugnote_user_change_view_state_threshold</td><td>threshold at which a user can change the view state of issue notes created by themselves</td></tr></tbody></table></div>
					</div><div class="para"><div xmlns:d="http://docbook.org/ns/docbook" class="title">Others</div>
						<div class="informaltable"><table xmlns:d="http://docbook.org/ns/docbook" class="lt-4-cols lt-7-rows"><colgroup><col /><col /><col /></colgroup><thead><tr><th>Title</th><th>Variable</th><th>Description</th></tr></thead><tbody><tr><td>View Change Log</td><td>$g_view_changelog_threshold</td><td>threshold to view the changelog</td></tr><tr><td>View Roadmap</td><td>$g_roadmap_view_threshold</td><td>threshold to view the roadmap</td></tr><tr><td>View Summary</td><td>$g_view_summary_threshold</td><td>threshold to view the summary</td></tr><tr><td>View Assigned To</td><td>$g_view_handler_threshold</td><td>threshold to see who is handling an issue</td></tr><tr><td>View Issue History</td><td>$g_view_history_threshold</td><td>threshold to view the issue history</td></tr><tr><td>Send Reminders</td><td>$g_bug_reminder_threshold</td><td>threshold to send a reminder</td></tr></tbody></table></div>
					</div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.pages.manage.config.transitions">
      ⁠</a>*******. Workflow Transitions</h4></div></div></div><div class="para">
					This page covers the status workflow. For most of these, the fields are self explanatory and relate to a similarly named setting in the configuration file. At the right of each row is a selector that allows the administrator to lower the access level required to change the particular parameter.The values changeable on this page are:
				</div><div class="table"><a id="idm4163">
      ⁠</a><p class="title"><strong>Table 6.1. Issues</strong></p><div class="table-contents"><table xmlns:d="http://docbook.org/ns/docbook" class="lt-4-cols lt-7-rows" summary="Issues"><colgroup><col /><col /><col /></colgroup><thead><tr><th>Title</th><th>Variable</th><th>Description</th></tr></thead><tbody><tr><td>Status to which a new issue is set</td><td>$g_bug_submit_status</td><td>status issue is set to when submitted</td></tr><tr><td>Status where an issue is considered resolved</td><td>$g_bug_resolved_status_threshold</td><td>status where issue is resolved</td></tr><tr><td>Status to which a reopened Issue is set</td><td>$g_bug_reopen_status</td><td>status issue is set to when reopened</td></tr></tbody></table></div></div><div class="para">
					The matrix that follows has checkmarks where the transitions are allowed from the status on the left edge to the status listed across the top. This corresponds to the $g_enum_workflow array.At the bottom, there is a list of access levels that are required to change the status to the value listed across the top. This can be used, for instance, to restrict those who can close an issue to a specific level, say a manager. This corresponds to the $g_set_status_threshold array and the $g_report_bug_threshold setting.
				</div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.pages.manage.config.email">
      ⁠</a>6.8.5.3. Email Notifications</h4></div></div></div><div class="para">
					This page sets the system defaults for sending emails on issue related events. MantisBT uses flags and a threshold system to generate emails on events. For each new event, email is sent to: 
					<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								the reporter
							</div></li><li class="listitem"><div class="para">
								the handler (or Assigned to)
							</div></li><li class="listitem"><div class="para">
								anyone monitoring the issue
							</div></li><li class="listitem"><div class="para">
								anyone who has ever added a issue note the issue
							</div></li><li class="listitem"><div class="para">
								anyone assigned to the project whose access level matches a range
							</div></li></ul></div>
					 From this list, those recipients who meet the following criteria are eliminated: 
					<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
								the originator of the change, if $g_email_receive_own is OFF
							</div></li><li class="listitem"><div class="para">
								the recipient either no longer exists, or is disabled
							</div></li><li class="listitem"><div class="para">
								the recipient has turned their email_on_&lt;new status&gt; preference OFF
							</div></li><li class="listitem"><div class="para">
								the recipient has no email address entered
							</div></li></ul></div>
					 The matrix on this page selects who will receive messages for each of the events listed down the left hand side. The first four columns correspond to the first four points listed above. The next columns correspond to the access levels defined. Note that because a minimum and maximum threshold are used, a discontinuous selection is not allowed.
				</div></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.monitor">
      ⁠</a>6.9. Monitor Issue</h2></div></div></div><div class="para">
			The monitor issues feature allows users to subscribe to certain issues and hence get copied on all notification emails that are sent for these issues.Depending on the configuration, sending a reminder to a user about an issue can add this issue to the user's list of monitored issues. Users who reported the issue or are assigned the issue typically don't need to monitor the issue to get the notifications. This is because by default they get notified on changes related to the issue anyway. However, administrators can change the configuration to disable notifications to reporters or handlers in specific scenarios.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.reopen">
      ⁠</a>6.10. Reopen Issue</h2></div></div></div><div class="para">
			Re-open issue button is visible in the issue view pages if the user has the appropriate access level and the issue is resolved/closed. Re-opening a issue will allow users to enter issue notes for the re-opening reason. The issue will automatically be put into the Feedback status.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.delete">
      ⁠</a>6.11. Delete Issue</h2></div></div></div><div class="para">
			The delete issues button appears on the issue view pages for the users who have the appropriate access level. This allows you to delete an existing issue. This should only be used on frivolous or test issues. A confirmation screen will prompt you if you really want to delete the issue. Updaters, Developers, Managers, and Administrators can remove issues (you can also configure this).
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.close">
      ⁠</a>6.12. Close Issue</h2></div></div></div><div class="para">
			This is a button that appears on the issue view pages for users that are authorized to close issues. Depending on the configuration, users may be able to close issues without having to resolve them first, or may be able to only close resolved issues. After the button is clicked, the user is redirected to a page where an issue note maybe added.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.assigntome">
      ⁠</a>6.13. Assign to Me</h2></div></div></div><div class="para">
			This button appears in the issue view pages in case of users with access level that is equal to handle_bug_threshold or higher. When this button is clicked the issue is assigned to the user.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.resolve">
      ⁠</a>6.14. Resolve Issue</h2></div></div></div><div class="para">
			This option on the View Issues page allows you to resolve the issue. It will lead you to a page where you can set the resolution state and a duplicate id (if applicable). After choosing that the user can choose to enter an issue note detailing the reason for the closure. The issue is then set to the Resolved state. The reporter should check off on the issue by using the Close Issue button.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.news">
      ⁠</a>6.15. News Syndication</h2></div></div></div><div class="para">
			MantisBT supports news syndication using RSS v2.0 protocol. MantisBT also supports authenticated news feeds for private projects or installations where anonymous access is not enabled. Authenticated feeds takes a user name and a key token that are used to authenticate the user and generate the feed results in the context of the user's access rights (i.e. the same as what the user would see if they were to logged into MantisBT).To get access to the News RSS as anonymous user, visit the following page: http://www.example.com/mantisbt/news_rss.php While a user is logged in, the RSS links provided in the UI will always provide links to the authenticated feeds, if no user is logged in (i.e. anonymous), then anonymous links will be provided.
		</div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.customize">
      ⁠</a>Chapter 7. Customizing MantisBT</h1></div></div></div><div class="toc"><dl class="toc"><dt><span class="section"><a href="#admin.customize.strings">7.1. Strings / Translations</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.customize.strings.format">7.1.1. Custom Strings File Format</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.customize.customfields">7.2. Custom Fields</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.customize.customfields.overview">7.2.1. Overview</a></span></dt><dt><span class="section"><a href="#admin.customize.customfields.definitions">7.2.2. Custom Field Definition</a></span></dt><dt><span class="section"><a href="#admin.customize.customfields.editing">7.2.3. Adding/Editing Custom Fields</a></span></dt><dt><span class="section"><a href="#admin.customize.customfields.linking">7.2.4. Linking/Unlinking/Ordering Existing Custom Fields in Projects</a></span></dt><dt><span class="section"><a href="#admin.customize.customfields.localize">7.2.5. Localizing Custom Field Names</a></span></dt><dt><span class="section"><a href="#admin.customize.customfields.defaults">7.2.6. Dynamic default values</a></span></dt><dt><span class="section"><a href="#admin.customize.customfields.dynamic">7.2.7. Dynamic values for Enumeration Custom Fields</a></span></dt></dl></dd><dt><span class="section"><a href="#admin.customize.enums">7.3. Enumerations</a></span></dt><dt><span class="section"><a href="#admin.customize.email">7.4. Email Notifications</a></span></dt><dt><span class="section"><a href="#admin.customize.status">7.5. Customizing Status Values</a></span></dt><dt><span class="section"><a href="#admin.customize.customfuncs">7.6. Custom Functions</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.customize.customfuncs.defined">7.6.1. Default Custom Functions</a></span></dt><dt><span class="section"><a href="#admin.customize.customfuncs.example">7.6.2. Example Custom Function Override</a></span></dt></dl></dd></dl></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.customize.strings">
      ⁠</a>7.1. Strings / Translations</h2></div></div></div><div class="para">
			All the strings used in MantisBT including error messages, as well as those defined in plugins, can be customized or translated differently. This is achieved by overriding them in the <span class="emphasis"><em>Custom Strings File</em></span> (<code class="filename">config/custom_strings_inc.php</code>), which is automatically detected and included by MantisBT code.
		</div><div class="para">
			Defining custom strings in this file provides a simple upgrade path, and avoids having to re-apply changes to modified core language files when upgrading MantisBT to the next release.
		</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
				The standard MantisBT language strings are sometimes reused in different contexts. If you are planning to override some strings to meet your specific requirements, make sure to analyze where and how they are used to avoid unexpected issues.
			</div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.strings.format">
      ⁠</a>7.1.1. Custom Strings File Format</h3></div></div></div><div class="para">
				This is a regular PHP script, containing variable assignments and optionally some control structures to conditionally define strings based on specific criteria (see <a class="xref" href="#admin.customize.customfields.localize">Section 7.2.5, “Localizing Custom Field Names”</a> for an example).
			</div><pre class="programlisting">
&lt;?php
$s_CODE = STRING;
$MANTIS_ERROR[ERROR_NUMBER] = STRING;
</pre><div class="para">
				Where 
				<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							<span class="emphasis"><em>CODE</em></span> = language string code, as called by <code class="literal">lang_get()</code> function. Search in <code class="filename">lang/strings_english.txt</code> for existing codes.
						</div></li><li class="listitem"><div class="para">
							<span class="emphasis"><em>ERROR_NUMBER</em></span> = error number or constant, see <code class="filename">constant_inc.php</code>.
						</div></li><li class="listitem"><div class="para">
							<span class="emphasis"><em>STRING</em></span> = string value / translation.
						</div></li></ul></div>

			</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
					The <code class="filename">custom_strings_inc.php</code> file should only contain variable assignments and basic PHP control structures. In particular, <span class="emphasis"><em>calling MantisBT core functions in it is not recommended</em></span>, as it could lead to unexpected behavior and even errors depending on context.
				</div><div class="para">
					If you <span class="emphasis"><em>must</em></span> use API calls, then anything that expects an active database connection needs to be protected, e.g.
				</div><pre class="programlisting">
&lt;?php
if( db_is_connected() ) {
	if( helper_get_current_project() == 1 ) {
		$s_summary = 'Title';
	}
}
</pre></div></div><div xmlns:d="http://docbook.org/ns/docbook" class="warning"><div class="admonition_header"><p><strong>Warning</strong></p></div><div class="admonition"><div class="para">
					NEVER call <code class="literal">lang_get_current()</code> from the <code class="filename">custom_strings_inc.php</code>. Doing so will reset the active_language, causing the code to return incorrect translations if the default language is different from English. Always use the <code class="literal">$g_active_language</code> global variable instead.
				</div></div></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.customize.customfields">
      ⁠</a>7.2. Custom Fields</h2></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.overview">
      ⁠</a>7.2.1. Overview</h3></div></div></div><div class="para">
				Different teams typically like to capture different information as users report issues, in some cases, the data required is even different from one project to another. Hence, MantisBT provides the ability for managers and administrators to define custom fields as way to extend MantisBT to deal with information that is specific to their teams or their projects. The aim is for this to keep MantisBT native fields to a minimum. Following are some facts about the implementation of custom fields in MantisBT:
			</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						Custom fields are defined system wide.
					</div></li><li class="listitem"><div class="para">
						Custom fields can be linked to multiple projects.
					</div></li><li class="listitem"><div class="para">
						The sequence of displaying custom fields can be different per project.
					</div></li><li class="listitem"><div class="para">
						Custom fields must be defined by users with access level ADMINISTRATOR.
					</div></li><li class="listitem"><div class="para">
						Custom fields can be linked to projects by users with access level MANAGER or above (by default, this can be configurable).
					</div></li><li class="listitem"><div class="para">
						Number of custom fields is not restricted.
					</div></li><li class="listitem"><div class="para">
						Users can define filters that include custom fields.
					</div></li><li class="listitem"><div class="para">
						Custom fields can be included in View Issues, Print Issues, and CSV exports.
					</div></li><li class="listitem"><div class="para">
						Enumeration custom fields can have a set of static values or values that are calculated dynamically based on a custom function.
					</div></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.definitions">
      ⁠</a>7.2.2. Custom Field Definition</h3></div></div></div><div class="para">
				The definition of a custom field includes the following logical attributes: 
				<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							Caption variable name. This value is supplied to the lang_get() API; it is therefore mandatory to set this to a <a href="https://www.php.net/manual/en/language.variables.basics.php">valid PHP identifier</a> (i.e. only letters, numbers and underscores; no spaces) if you intend to translate the field label (see <a class="xref" href="#admin.customize.customfields.localize">Section 7.2.5, “Localizing Custom Field Names”</a>).
						</div><div class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
								If the specified variable is not found in the language files or in <code class="filename">custom_strings_inc.php</code>, then it will be displayed as-is.
							</div></div></div></li><li class="listitem"><div class="para">
							Custom field type, can be one of:
						</div><div class="itemizedlist"><ul><li class="listitem"><div class="para">
									<code class="literal">string</code>, for strings of up to 255 characters.
								</div></li><li class="listitem"><div class="para">
									<code class="literal">numeric</code>, for numerical integer values.
								</div></li><li class="listitem"><div class="para">
									<code class="literal">float</code>, for real (float / double) numbers.
								</div></li><li class="listitem"><div class="para">
									<code class="literal">email</code>, for storing email addresses.
								</div></li><li class="listitem"><div class="para">
									<code class="literal">enumeration</code> is used when a user selects one entry from a list. The user interface for this type is a combo-box.
								</div></li><li class="listitem"><div class="para">
									<code class="literal">checkbox</code> is like enumeration, but the options are shown as checkboxes and the user is allowed to tick more than one item.
								</div><div class="para">
									The default value and the possible value can contain multiple values like <strong class="userinput"><code>RED|YELLOW|BLUE</code></strong>.
								</div></li><li class="listitem"><div class="para">
									<code class="literal">radio</code> is like enumeration, but the list is shown as radio buttons and the user is only allowed to tick a single option.
								</div><div class="para">
									The possible values can be <strong class="userinput"><code>RED|YELLOW|BLUE</code></strong>, and default <strong class="userinput"><code>YELLOW</code></strong>.
								</div><div class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
										The default value can't contain multiple values.
									</div></div></div></li><li class="listitem"><div class="para">
									<code class="literal">list</code> is like enumeration but the list is shown as a list box where the user is only allowed to select one option.
								</div><div class="para">
									The possible values can be <strong class="userinput"><code>RED|YELLOW|BLUE</code></strong>, and default <strong class="userinput"><code>YELLOW</code></strong>.
								</div><div class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
										The default value can't contain multiple values.
									</div></div></div></li><li class="listitem"><div class="para">
									<code class="literal">multi-selection list</code> is like enumeration, but the list is shown as a list box where the user is allowed to select multiple options.
								</div><div class="para">
									The possible values can be <strong class="userinput"><code>RED|YELLOW|BLUE</code></strong>, and default <strong class="userinput"><code>RED|BLUE</code></strong>.
								</div><div class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
										Multiple values are allowed as default.
									</div></div></div></li><li class="listitem"><div class="para">
									<code class="literal">date</code>, for date values.
								</div><div class="para">
									The default value can be <span class="emphasis"><em>empty</em></span>, a numeric <span class="emphasis"><em>UNIX timestamp</em></span>, or a date in a <a href="https://www.php.net/manual/en/datetime.formats.php">valid format</a>, including relative indications such as <strong class="userinput"><code>tomorrow</code></strong>, <strong class="userinput"><code>next week</code></strong>, <strong class="userinput"><code>last month</code></strong>, <strong class="userinput"><code>+3 days</code></strong>, <strong class="userinput"><code>last day of this month</code></strong>, etc.
								</div><div class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
										The legacy format where the dynamic date had to be wrapped in curly brackets (e.g. <code class="literal">{tomorrow}</code>) is still supported for backwards-compatibility, but no longer necessary. This is considered a deprecated feature, that will be removed in a future released of MantisBT.
									</div></div></div></li></ul></div></li><li class="listitem"><div class="para">
							Possible values for the Custom Field (e.g. <strong class="userinput"><code>RED|YELLOW|BLUE</code></strong>). Use the pipe (<code class="literal">|</code>) character to separate the enumeration's values. It is possible for one of the values to be empty (e.g. <strong class="userinput"><code>|RED|YELLOW|BLUE</code></strong>, note the leading <code class="literal">|</code>).
						</div><div class="para">
							The set of values can also be calculated at runtime. For example, <code class="literal">=versions</code> would automatically resolve into all the versions defined for the current project. See <a class="xref" href="#admin.customize.customfields.dynamic">Section 7.2.7, “Dynamic values for Enumeration Custom Fields”</a> for more information.
						</div></li><li class="listitem"><div class="para">
							Default value - see details above for a sample default value for each type.
						</div></li><li class="listitem"><div class="para">
							Minimum/maximum length for the custom field value (use 0 to disable). Note that these metrics are not really relevant to custom fields that are based on an enumeration of possible values.
						</div></li><li class="listitem"><div class="para">
							Regular expression to use for validating user input (use <a href="https://www.php.net/manual/en/reference.pcre.pattern.syntax.php">PCRE syntax</a>).
						</div></li><li class="listitem"><div class="para">
							Read Access level: Minimum access level for users to be able to <span class="emphasis"><em>see</em></span> the value of the custom field.
						</div></li><li class="listitem"><div class="para">
							Write Access level: Minimum access level for users to be able to <span class="emphasis"><em>edit</em></span> the value of the custom field.
						</div></li><li class="listitem"><div class="para">
							Display when reporting issues? - If this custom field should be shown on the Report Issue page.
						</div></li><li class="listitem"><div class="para">
							Display when updating issues? - If this custom field should be shown on the Update Issue page.
						</div></li><li class="listitem"><div class="para">
							Display when resolving issues? - If this custom field should be shown when resolving an issue. For example, a "root cause" custom field would make sense to set when resolving the issue.
						</div></li><li class="listitem"><div class="para">
							Display when closing issues? - If this custom field should be shown when closing an issue.
						</div></li><li class="listitem"><div class="para">
							Required on Report - If this custom field is a mandatory field on the Report Issue page.
						</div></li><li class="listitem"><div class="para">
							Required on Update - If this custom field is a mandatory field on the Update Issue page.
						</div></li><li class="listitem"><div class="para">
							Required on Resolve - If this custom field is a mandatory field when resolving an issue.
						</div></li><li class="listitem"><div class="para">
							Required on Close - If this custom field is a mandatory field when closing an issue.
						</div></li></ul></div>

			</div><div class="para">
				If the value of a custom field for a certain defect is not found, the default value is assumed.
			</div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.editing">
      ⁠</a>7.2.3. Adding/Editing Custom Fields</h3></div></div></div><div class="para">
				<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							The logged in user needs $g_manage_custom_fields_threshold access level.
						</div></li><li class="listitem"><div class="para">
							Select "Manage" from the main menu.
						</div></li><li class="listitem"><div class="para">
							Select "Custom Fields" from the management menu.
						</div></li><li class="listitem"><div class="para">
							In case of edit, click on the name of an existing custom field to edit its information.
						</div></li><li class="listitem"><div class="para">
							In case of adding a new one, enter the name of the new custom field then click "New Custom Field".
						</div></li></ul></div>

			</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
					Added custom fields will not show up in any of the issues until the added custom field is linked to the appropriate projects.
				</div></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.linking">
      ⁠</a>7.2.4. Linking/Unlinking/Ordering Existing Custom Fields in Projects</h3></div></div></div><div class="para">
				<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							The logged in user needs to have access level that is greater than or equal to $g_custom_field_link_threshold and $g_manage_project_threshold.
						</div></li><li class="listitem"><div class="para">
							Select "Manage" from the main menu.
						</div></li><li class="listitem"><div class="para">
							Select "Projects".
						</div></li><li class="listitem"><div class="para">
							Select the name of the project to manage.
						</div></li><li class="listitem"><div class="para">
							Scroll down to the "Custom Fields" box.
						</div></li><li class="listitem"><div class="para">
							Select the field to add from the list, then click "Add This Existing Custom Field".
						</div></li><li class="listitem"><div class="para">
							To change the order of the custom fields, edit the "Sequence" value and click update. Custom fields with smaller values are displayed first.
						</div></li><li class="listitem"><div class="para">
							To unlink a custom field, click on "Remove" link next to the field. Unlinking a custom field will not delete the values that are associated with the issues for this field. These values are only deleted if the custom field definition is removed (not unlinked!) from the database. This is useful if you decide to re-link the custom field. These values may also re-appear if issues are moved to another project which has this field linked.
						</div></li></ul></div>

			</div><div class="para"><div xmlns:d="http://docbook.org/ns/docbook" class="title">Moving Issues</div>
					When an issue is moved from one project to another, custom fields that are not defined for the new project are not deleted. These fields will re-appear with their correct values if the issue is moved back to the original project, or if these custom fields are linked to the new project.
				</div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.localize">
      ⁠</a>7.2.5. Localizing Custom Field Names</h3></div></div></div><div class="para">
				It is possible to localize the custom fields' labels. This can be done as follows: 
				<div xmlns:d="http://docbook.org/ns/docbook" class="orderedlist"><ol><li class="listitem"><div class="para">
							Define the custom field (see <a class="xref" href="#admin.customize.customfields.definitions">Section 7.2.2, “Custom Field Definition”</a>), keeping in mind that its name must be a <a href="https://www.php.net/manual/en/language.variables.basics.php">valid PHP identifier</a>.
						</div><div class="para">
							As an example, we will use <span class="emphasis"><em>my_start_date</em></span> for a custom field of type "Date", storing the date when work on an issue was initiated.
						</div></li><li class="listitem"><div class="para">
							Set the localization strings 
							<div class="itemizedlist"><ul><li class="listitem"><div class="para">
										In the MantisBT <code class="filename">config</code> directory, locate and edit <code class="filename">custom_strings_inc.php</code> (see <a class="xref" href="#admin.customize.strings">Section 7.1, “Strings / Translations”</a>), create it if it does not exist.
									</div></li><li class="listitem"><div class="para">
										Localize the custom field's label <span class="emphasis"><em>my_start_date</em></span> by adding the following code 
<pre class="programlisting">
&lt;?php
switch( $g_active_language ) {
	case 'french':
		$s_my_start_date = 'Date de début';
		break;

	default:
		# Default language, as defined in config/config_inc.php
		# ($g_default_language, English in this case)
		$s_my_start_date = 'Start Date';
		break;
}
</pre>

									</div></li></ul></div>

						</div></li></ol></div>

			</div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
					Had we decided to use <span class="emphasis"><em>start_date</em></span> as the custom field's name, then it would not have been necessary to modify <code class="filename">custom_strings_inc.php</code> (see <a class="xref" href="#admin.customize.strings">Section 7.1, “Strings / Translations”</a>), since MantisBT would have used the existing, already localized string from the standard language files. To check for standard strings, inspect <code class="filename">lang/strings_english.txt</code>.
				</div></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.defaults">
      ⁠</a>7.2.6. Dynamic default values</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.customize.customfields.defaults.date">
      ⁠</a>*******. Dynamic defaults for Date fields</h4></div></div></div><div class="para">
					Custom fields of type date can be defaulted to either specific or relative dates. Typically, relative dates is the scenario that makes sense in most of the cases.
				</div><div class="para">
					The format for specific dates is an integer which indicates the number of seconds since the <a href="https://en.wikipedia.org/wiki/Unix_time"> Unix Epoch</a> (January 1 1970 00:00:00 UTC), which is the format consumed by the PHP <a href="https://www.php.net/manual/en/function.date.php">date()</a> method.
				</div><div class="para">
					The relative scenario expects default values like {tomorrow}, {yesterday}, {+2 days}, {-3 days}, {next week}, etc. The curly brackets indicate that this is a logical value which is then evaluated using the PHP <a href="https://www.php.net/manual/en/function.strtotime.php">strtotime()</a> function.
				</div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.dynamic">
      ⁠</a>7.2.7. Dynamic values for Enumeration Custom Fields</h3></div></div></div><div class="para">
				As discussed earlier, one of the possible types of a custom field is "enumeration". This type of custom field allows the user to select one value from a provided list of possible values. The standard way of defining such custom fields is to provide a '|' separated list of possible values. However, this approach has two limitations: the list is static, and the maximum length of the list must be no longer than 255 characters. Hence, the need for the ability to construct the list of possible values dynamically.
			</div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.customize.customfields.dynamic.default">
      ⁠</a>*******. Dynamic possible values included by default</h4></div></div></div><div class="para">
					MantisBT ships with some dynamic possible values, these include the following:
				</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							<code class="literal">=categories</code> a list of categories defined in the current project (or the project to which the issue belongs).
						</div></li><li class="listitem"><div class="para">
							<code class="literal">=versions</code> a list of all versions defined in the current project (or the project to which the issue belongs).
						</div></li><li class="listitem"><div class="para">
							<code class="literal">=future_versions</code> a list of all versions that belong to the current project with <span class="emphasis"><em>released</em></span> flag set to false.
						</div></li><li class="listitem"><div class="para">
							<code class="literal">=released_versions</code> a list of all versions that belong to the current project with <span class="emphasis"><em>released</em></span> flag set to true.
						</div></li></ul></div><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						The <code class="literal">=</code> before the list of options tells MantisBT that this is a dynamic list, rather than a static one with a single option.
					</div></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.customize.customfields.dynamic.custom">
      ⁠</a>7.2.7.2. Defining Custom Dynamic Possible Values</h4></div></div></div><div class="para">
					If the user selects <code class="literal">=versions</code>, the actual custom function that is executed is <span class="emphasis"><em>custom_function_*_enum_versions()</em></span>. The reason why the "enum_" is not included is to have a fixed prefix for all custom functions used for this purpose and protect against users using custom functions that were not intended for this purpose.
				</div><div class="para">
					For example, you would not want the user to use <span class="emphasis"><em>custom_function_*_issue_delete_notify()</em></span> which may be overridden by the web master to delete associated data in other databases.
				</div><div class="para">
					Following is a sample custom function that is used to populate a field with the categories belonging to the currently selected project:
				</div><pre class="programlisting">
/**
 * Construct an enumeration for all categories for the current project.
 *
 * The enumeration will be empty if current project is ALL PROJECTS.
 * Enumerations format is: "abc|lmn|xyz"
 * To use this in a custom field type "=categories" in the possible values field.
 */
function custom_function_override_enum_categories() {
	$t_categories = category_get_all_rows( helper_get_current_project() );

	$t_enum = array();
	foreach( $t_categories as $t_category ) {
		$t_enum[] = $t_category['category'];
	}

	$t_possible_values = implode( '|', $t_enum );

	return $t_possible_values;
}
</pre><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="itemizedlist"><ul><li class="listitem"><div class="para">
								The custom function doesn't take any parameters.
							</div></li><li class="listitem"><div class="para">
								The custom function returns the possible values in the format (A|B|C).
							</div></li><li class="listitem"><div class="para">
								The custom function uses the current project.
							</div></li><li class="listitem"><div class="para">
								The custom function builds on top of the already existing APIs.
							</div></li></ul></div></div></div><div class="para">
					To define your own function <code class="literal">mine</code>, you will have to define it with the following signature:
				</div><pre class="programlisting">
/**
 * Use this in a custom field type "=mine" in the possible values field.
 */
function custom_function_override_enum_mine() {
	# Populate $t_enum values as appropriate here
	$t_enum = array();

	$t_possible_values = implode( '|', $t_enum );

	return $t_possible_values;
}
</pre><div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
						Notice the <span class="emphasis"><em>override</em></span> in the function name. This is because this method is defined by the MantisBT administrator and not part of the MantisBT source. It is OK to override a method that doesn't exist.
					</div></div></div><div class="para">
					As usual, when MantisBT is upgraded to future releases, the custom functions will not be overwritten. The difference between the "default" implementation and the "override" implementation is explained in more details in <a class="xref" href="#admin.customize.customfuncs">Section 7.6, “Custom Functions”</a>.
				</div></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.customize.enums">
      ⁠</a>7.3. Enumerations</h2></div></div></div><div class="para">
			Enumerations are used in MantisBT to represent a set of possible values for an attribute. Enumerations are used for access levels, severities, priorities, project statuses, project view state, reproducibility, resolution, ETA, and projection. MantisBT provides the administrator with the flexibility of altering the values in these enumerations. The rest of this topic explains how enumerations work, and then how they can be customised.
		</div><div class="para"><div xmlns:d="http://docbook.org/ns/docbook" class="title">How do enumerations work?</div>
				<code class="filename">core/constant_inc.php</code> defines the constants that correspond to those in the enumeration. These are useful to refer to these enumerations in the configs and the code. 
<pre class="programlisting">
define( 'VIEWER', 10 );
define( 'REPORTER', 25 );
define( 'UPDATER',  40 );
define( 'DEVELOPER', 55 );
define( 'MANAGER', 70 );
define( 'ADMINISTRATOR', 90 );
</pre>

			</div><div class="para">
			<code class="filename">config_defaults_inc.php</code> includes the defaults for the enumerations. The configuration options that are defaulted here are used in specifying which enumerations are active and should be used in MantisBT. 
<pre class="programlisting">
$g_access_levels_enum_string =
	'10:viewer,25:reporter,40:updater,55:developer,70:manager,90:administrator';
</pre>
			 <div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
					The strings included in the enumerations here are just for documentation purposes, they are not actually shown to the user (due to the need for localisation). Hence, if an entry in this enumeration is not found in the corresponding localised string (i.e. 70:manager), then it will be printed to the user as @70@.
				</div></div></div>

		</div><div class="para">
			The Language Files (e.g. <code class="filename">lang/strings_german.txt</code>) provide the localised strings (German in this case) for enumerations. But again, the <span class="emphasis"><em>master list</em></span> is the enumeration in the configs themselves, the ones in the language files are just used for finding the localised equivalent for an entry. Hence, if a user changes the config to have only two types of users developers and administrators, then only those will be prompted to the users even if the enumerations in the language files still includes the full list. 
<pre class="programlisting">
$s_access_levels_enum_string =
	'10:Betrachter,25:Reporter,40:Updater,55:Entwickler,70:Manager,90:Administrator';
</pre>

		</div><div class="para"><div xmlns:d="http://docbook.org/ns/docbook" class="title">How can they be customised?</div>
				Let say we want to remove access level "Updater" and add access level "Senior Developer".
			</div><div class="para">
			The file <code class="filename">config/custom_constants_inc.php</code> is supported for the exclusive purpose of allowing administrators to define their own constants while maintaining a simple upgrade path for future releases of MantisBT. Note that this file is not distributed with MantisBT and you will need to create it if you need such customisation. In our example, we need to define a constant for the new access level. 
<pre class="programlisting">
define( 'SENIOR_DEVELOPER', 60 );
</pre>

		</div><div class="para">
			In <code class="filename">config/config_inc.php</code>
<pre class="programlisting">
// Remove Updater and add Senior Developer
$g_access_levels_enum_string =
	'10:viewer,25:reporter,55:developer,60:senior_developer,70:manager,90:administrator';

// Give access to Senior developers to create/delete custom field.
$g_manage_custom_fields_threshold = SENIOR_DEVELOPER;
</pre>

		</div><div class="para">
			Update <code class="filename">custom_strings_inc.php</code> (see <a class="xref" href="#admin.customize.strings">Section 7.1, “Strings / Translations”</a>) 
<pre class="programlisting">
$s_access_levels_enum_string =
	'10:Betrachter,25:Reporter,40:Updater,55:Entwickler,60:Senior Developer,70:Manager,90:Administrator';
</pre>
			 <div xmlns:d="http://docbook.org/ns/docbook" class="note"><div class="admonition_header"><p><strong>Note</strong></p></div><div class="admonition"><div class="para">
					We don't need to remove the <span class="emphasis"><em>Updater</em></span> entry from the localisation file if the current language is 'English'.
				</div></div></div>

		</div><div class="para"><div xmlns:d="http://docbook.org/ns/docbook" class="title">Conclusion</div>
				We have covered how enumerations work in general, and how to customise one of them. If you are interested in customising other enumerations, a good starting point would be to go to <span class="emphasis"><em>MantisBT Enum Strings</em></span> section in <code class="filename">config_defaults_inc.php</code>. This section defines all enumerations that are used by MantisBT.
			</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.customize.email">
      ⁠</a>7.4. Email Notifications</h2></div></div></div><div class="para">
			See <a class="xref" href="#admin.config.email">Section 5.8, “Email”</a> in the Configuration section.
		</div><div class="para">
			Examples: 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						Notify only managers of new issues. 
<pre class="programlisting">
$g_notify_flags['new'] = array(
	'threshold_min' =&gt; MANAGER,
	'threshold_max' =&gt; MANAGER,
);
</pre>

					</div></li><li class="listitem"><div class="para">
						Notify Developers and managers of all project events, except, exclude developers from the 'closed' events. 
<pre class="programlisting">
$g_default_notify_flags = array(
	'threshold_min' =&gt; DEVELOPER,
	'threshold_max' =&gt; MANAGER,
);
$g_notify_flags['closed'] = array(
	'threshold_min' =&gt; MANAGER,
	'threshold_max' =&gt; MANAGER,
);
</pre>

					</div></li><li class="listitem"><div class="para">
						Exclude those who contributed issue notes from getting messages about other changes in the issue. 
<pre class="programlisting">
$g_default_notify_flags['bugnotes'] = OFF;
</pre>

					</div></li><li class="listitem"><div class="para">
						Exclude those monitoring issues from seeing the 'closed' message 
<pre class="programlisting">
$g_notify_flags['closed']['monitor'] = OFF;
</pre>

					</div></li><li class="listitem"><div class="para">
						Only notify developers when issue notes are added. 
<pre class="programlisting">
$g_notify_flags['bugnote'] = array(
	'threshold_min' =&gt; DEVELOPER,
	'threshold_max' =&gt; DEVELOPER,
);
</pre>

					</div></li><li class="listitem"><div class="para">
						Notify managers of changes in sponsorship. 
<pre class="programlisting">
$g_notify_flags['sponsor'] = array(
	'threshold_min' =&gt; MANAGER,
	'threshold_max' =&gt; MANAGER,
);
</pre>

					</div></li><li class="listitem"><div class="para">
						Notify originator and managers of changes in ownership ("Assigned To:"). 
<pre class="programlisting">
$g_notify_flags['owner'] = array(
	'threshold_min' =&gt; MANAGER,
	'threshold_max' =&gt; MANAGER,
	'reporter'      =&gt; ON,
);
</pre>

					</div></li><li class="listitem"><div class="para">
						I'm paranoid about mail. Only send information on issues to those involved in them. Don't send mail people already know about. Also send new issue notifications to managers so they can screen them. 
<pre class="programlisting">
$g_email_receive_own = OFF;
$g_default_notify_flags = array(
	'reporter'      =&gt; ON,
	'handler'       =&gt; ON,
	'monitor'       =&gt; ON,
	'bugnotes'      =&gt; ON,
	'category'      =&gt; ON,
	'threshold_min' =&gt; NOBODY,
	'threshold_max' =&gt; NOBODY
);
$g_notify_flags['new'] = array(
	'threshold_min' =&gt; MANAGER,
	'threshold_max' =&gt; MANAGER,
);
</pre>

					</div></li><li class="listitem"><div class="para">
						How do I send all messages to an email logger.
					</div><div class="para">
						You will need to create a dummy user with the appropriate access level for the notices you want to log. Once this user is added to projects, they will receive mail using the appropriate rules.
					</div></li></ul></div>

		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.customize.status">
      ⁠</a>7.5. Customizing Status Values</h2></div></div></div><div class="para">
			This section describes how to add a custom status. 
			<div xmlns:d="http://docbook.org/ns/docbook" class="orderedlist"><ol><li class="listitem"><div class="para">
						Define a constant to map the new status to.
					</div><div class="para">
						In subfolder config, locate and edit file <span class="emphasis"><em>custom_constants_inc.php</em></span>; (create it if it does not exist) 
<pre class="programlisting">
&lt;?php
	# Custom status code
	define( 'TESTING', 60 );
</pre>

					</div></li><li class="listitem"><div class="para">
						Define the new status in the enumeration, as well as the corresponding color code.
					</div><div class="para">
						In subfolder config, edit your <span class="emphasis"><em>config_inc.php</em></span> 
<pre class="programlisting">
# Revised enum string with new 'testing' status
$g_status_enum_string = '10:new,20:feedback,30:acknowledged,40:confirmed,50:assigned,<span class="emphasis"><em>60:testing,</em></span>80:resolved,90:closed';

# Status color additions
$g_status_colors['<span class="emphasis"><em>testing</em></span>'] = '#ACE7AE';
</pre>
						 Note that the key in the $g_status_colors array must be equal to the value defined for the new status code in $g_status_enum_string.
					</div></li><li class="listitem"><div class="para">
						Define the required translation strings for the new status, for each language used in the installation. 
						<div class="itemizedlist"><ul><li class="listitem"><div class="para">
									<span class="emphasis"><em>s_status_enum_string</em></span>: status codes translation (refer to the original language strings for standard values)
								</div></li><li class="listitem"><div class="para">
									<span class="emphasis"><em>s_XXXX_bug_title</em></span>: title displayed in the change status page
								</div></li><li class="listitem"><div class="para">
									<span class="emphasis"><em>s_XXXX_bug_button</em></span>: label for the submit button in the change status page
								</div></li><li class="listitem"><div class="para">
									<span class="emphasis"><em>s_email_notification_title_for_status_bug_XXXX</em></span>: title for notification e-mails
								</div></li></ul></div>
						 where XXXX is the name of the new status as it was defined in <span class="emphasis"><em>g_status_enum_string</em></span> above. If XXXX contains spaces, they should be replaced by underscores in the language strings names (e.g. for '35:pending user', use '$s_pending_user_bug_button')
					</div><div class="para">
						In the <code class="filename">config</code> subfolder, locate and edit <code class="filename">custom_strings_inc.php</code> (see <a class="xref" href="#admin.customize.strings">Section 7.1, “Strings / Translations”</a>), create it if it does not exist 
<pre class="programlisting">
&lt;?php
# Translation for Custom Status Code: <span class="emphasis"><em>testing</em></span>
switch( $g_active_language ) {

	case 'french':
		$s_status_enum_string = '10:nouveau,20:commentaire,30:accepté,40:confirmé,50:affecté,60:à tester,80:résolu,90:fermé';

		$s_testing_bug_title = 'Mettre le bogue en test';
		$s_testing_bug_button = 'A tester';

		$s_email_notification_title_for_status_bug_testing = 'Le bogue suivant est prêt à être TESTE.';
		break;

	default: # english
		$s_status_enum_string = '10:new,20:feedback,30:acknowledged,40:confirmed,50:assigned,60:testing,80:resolved,90:closed';

		$s_testing_bug_title = 'Mark issue Ready for Testing';
		$s_testing_bug_button = 'Ready for Testing';

		$s_email_notification_title_for_status_bug_testing = 'The following issue is ready for TESTING.';
		break;
}
</pre>

					</div></li><li class="listitem"><div class="para">
						Add the new status to the workflow as required.
					</div><div class="para">
						This can either be done from the Manage Workflow Transitions page (see <a class="xref" href="#admin.lifecycle.workflow.transitions">Section 4.3.1, “Workflow Transitions”</a>) or by manually editing <span class="emphasis"><em>config_inc.php</em></span> as per the example below: 
<pre class="programlisting">
$g_status_enum_workflow[NEW_]         ='30:acknowledged,20:feedback,40:confirmed,50:assigned,80:resolved';
$g_status_enum_workflow[FEEDBACK]     ='30:acknowledged,40:confirmed,50:assigned,80:resolved';
$g_status_enum_workflow[ACKNOWLEDGED] ='40:confirmed,20:feedback,50:assigned,80:resolved';
$g_status_enum_workflow[CONFIRMED]    ='50:assigned,20:feedback,30:acknowledged,80:resolved';
$g_status_enum_workflow[ASSIGNED]     ='60:testing,20:feedback,30:acknowledged,40:confirmed,80:resolved';
$g_status_enum_workflow[TESTING]      ='80:resolved,20:feedback,50:assigned';
$g_status_enum_workflow[RESOLVED]     ='90:closed,20:feedback,50:assigned';
$g_status_enum_workflow[CLOSED]       ='20:feedback,50:assigned';
</pre>

					</div></li><li class="listitem"><div class="para">
						Check and update existing workflow configurations
					</div><div class="para">
						If you do not perform this step and have existing workflow definitions, it will not be possible to transition to and from your new status.
					</div><div class="para">
						Go to the Workflow Transitions page (manage_config_workflow_page.php), and update the workflow as appropriate. Make sure that you have picked the correct Project in the selection list).
					</div><div class="para">
						Hint: to identify whether you have any workflows that should be updated, open the Manage Configuration Report page (adm_config_report.php) and filter on 'All Users', [any] project and config option = 'status_enum_workflow'. All of the listed projects should be reviewed to eventually include transitions to and from the newly added states.
					</div></li></ol></div>

		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.customize.customfuncs">
      ⁠</a>7.6. Custom Functions</h2></div></div></div><div class="para">
			Custom functions are used to extend the functionality of MantisBT by integrating user-written functions into the issue processing at strategic places. This allows the system administrator to change the functionality without touching MantisBT's core.
		</div><div class="para">
			Default Custom Functions are defined in the API file <code class="filename">core/custom_function_api.php</code> , and are named <span class="emphasis"><em>custom_function_default_descriptive_name</em></span>, where <span class="emphasis"><em>descriptive_name</em></span> describes the particular function. See <a class="xref" href="#admin.customize.customfuncs.defined">Section 7.6.1, “Default Custom Functions”</a> for a description of the specific functions.
		</div><div class="para">
			User versions of these functions (overrides) are named like <span class="emphasis"><em>custom_function_override_descriptive_name</em></span>, and placed in a file called <code class="filename">custom_functions_inc.php</code> that must be saved in MantisBT's config directory. In normal processing, the system will look for override functions and execute them instead of the provided default functions.
		</div><div class="para">
			The simplest way to create a custom function is to copy the default one from the api to your override file (<code class="filename">custom_functions_inc.php</code>), and rename it (i.e. replacing 'default' by 'override'). The specific functionality you need can then be coded into the override function.
		</div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfuncs.defined">
      ⁠</a>7.6.1. Default Custom Functions</h3></div></div></div><div class="para">
				Refer to <code class="filename">core/custom_functions_api.php</code> for further details.
			</div><div class="informaltable"><table xmlns:d="http://docbook.org/ns/docbook" class="lt-4-cols gt-14-rows"><colgroup><col /><col /><col /></colgroup><thead><tr><th>Custom Function Name</th><th>Description</th><th>Return value</th></tr></thead><tbody><tr><td>custom_function_default_auth_can_change_password()</td><td>Determines whether MantisBT can update the password</td><td>True if yes, False if not</td></tr><tr><td>custom_function_default_changelog_include_issue( $p_issue_id )</td><td>Determines whether the specified issue should be included in the Changelog or not.</td><td>True to include, False to exclude</td></tr><tr><td>custom_function_default_changelog_print_issue( $p_issue_id, $p_issue_level = 0 )</td><td>Prints one entry in the Changelog</td><td>None</td></tr><tr><td>custom_function_default_enum_categories()</td><td>Build a list of all categories for the current project</td><td>Enumeration, delimited by "|"</td></tr><tr><td>custom_function_default_enum_future_versions()</td><td>Build a list of all future versions for the current project</td><td>Enumeration, delimited by "|"</td></tr><tr><td>custom_function_default_enum_released_versions()</td><td>Build a list of all released versions for the current project</td><td>Enumeration, delimited by "|"</td></tr><tr><td>custom_function_default_enum_versions()</td><td>Build a list of all versions for the current project</td><td>Enumeration, delimited by "|"</td></tr><tr><td>custom_function_default_format_issue_summary( $p_issue_id, $p_context = 0 )</td><td>Format the bug summary</td><td>Formatted string</td></tr><tr><td>custom_function_default_get_columns_to_view( $p_columns_target = COLUMNS_TARGET_VIEW_PAGE, $p_user_id = null )</td><td>Defines which columns should be displayed</td><td>Array of the column names</td></tr><tr><td>custom_function_default_issue_create_notify( $p_issue_id )</td><td>Notify after an issue has been created</td><td>In case of invalid data, this function should call trigger_error()</td></tr><tr><td>custom_function_default_issue_create_validate( $p_new_issue_data )</td><td>Validate field settings before creating an issue</td><td>In case of invalid data, this function should call trigger_error()</td></tr><tr><td>custom_function_default_issue_delete_notify( $p_issue_data )</td><td>Notify after an issue has been deleted</td><td>In case of invalid data, this function should call trigger_error()</td></tr><tr><td>custom_function_default_issue_delete_validate( $p_issue_id )</td><td>Validate field settings before deleting an issue</td><td>In case of invalid data, this function should call trigger_error()</td></tr><tr><td>custom_function_default_issue_update_notify( $p_issue_id )</td><td>Notify after an issue has been updated</td><td>In case of invalid data, this function should call trigger_error()</td></tr><tr><td>custom_function_default_issue_update_validate( $p_issue_id, $p_new_issue_data, $p_bugnote_text )</td><td>Validate field issue data before updating</td><td>In case of invalid data, this function should call trigger_error()</td></tr><tr><td>custom_function_default_print_bug_view_page_custom_buttons( $p_bug_id )</td><td>Prints the custom buttons on the current view page</td><td>None</td></tr><tr><td>custom_function_default_print_column_title( $p_column, $p_columns_target = COLUMNS_TARGET_VIEW_PAGE, array $p_sort_properties = null )</td><td>Print a column's title based on its name</td><td>None</td></tr><tr><td>custom_function_default_print_column_value( $p_column, $p_bug, $p_columns_target = COLUMNS_TARGET_VIEW_PAGE )</td><td>Print a column's value based on its name</td><td>None</td></tr><tr><td>custom_function_default_roadmap_include_issue( $p_issue_id )</td><td>Determines whether the specified issue should be included in the Roadmap or not.</td><td>True to include, False to exclude</td></tr><tr><td>custom_function_default_roadmap_print_issue( $p_issue_id, $p_issue_level = 0 )</td><td>Prints one entry in the Roadmap</td><td>None</td></tr></tbody></table></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfuncs.example">
      ⁠</a>7.6.2. Example Custom Function Override</h3></div></div></div><div class="para">
				The following function is used to validate an issue before it is resolved.
			</div><pre class="programlisting" width="102">&lt;?php

/**
 * Hook to validate Validate field settings before resolving
 * verify that the resolution is not set to OPEN
 * verify that the fixed in version is set (if versions of the product exist)
 */
function custom_function_override_issue_update_validate( $p_issue_id, $p_bug_data, $p_bugnote_text ) {
	if( $p_bug_data-&gt;status == RESOLVED ) {
		if( $p_bug_data-&gt;resolution == OPEN ) {
			error_parameters( 'the resolution cannot be open to resolve the issue' );
			trigger_error( ERROR_VALIDATE_FAILURE, ERROR );
		}
		$t_version_count = count( version_get_all_rows( $p_bug_data-&gt;project_id ) );
		if( ( $t_version_count &gt; 0 ) &amp;&amp; ( $p_bug_data-&gt;fixed_in_version == '' ) ) {
			error_parameters( 'fixed in version must be set to resolve the issue' );
			trigger_error( ERROR_VALIDATE_FAILURE, ERROR );
		}
	}
}

?&gt;</pre><div class="para">
				The errors will also need to be defined, by modifying the following files
			</div><div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						<code class="filename">custom_constants_inc.php</code>
					</div><pre class="programlisting">
define( 'ERROR_VALIDATE_FAILURE', 2000 );
</pre></li><li class="listitem"><div class="para">
						<code class="filename">custom_strings_inc.php</code> (see <a class="xref" href="#admin.customize.strings">Section 7.1, “Strings / Translations”</a>)
					</div><pre class="programlisting">
$MANTIS_ERROR['ERROR_VALIDATE_FAILURE'] = 'This change cannot be made because %s';
</pre></li></ul></div></div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.auth">
      ⁠</a>Chapter 8. Authentication</h1></div></div></div><div class="toc"><dl class="toc"><dt><span class="section"><a href="#admin.auth.standard">8.1. Standard Authentication</a></span></dt><dt><span class="section"><a href="#admin.auth.ldap">8.2. LDAP and Microsoft Active Directory</a></span></dt><dt><span class="section"><a href="#admin.auth.basic">8.3. Basic Authentication</a></span></dt><dt><span class="section"><a href="#admin.auth.http">8.4. HTTP Authentication</a></span></dt><dt><span class="section"><a href="#admin.auth.deprecated">8.5. Deprecated authentication methods</a></span></dt></dl></div><div class="para">
		MantisBT supports several authentication methods out of the box. In addition, there is work in progress relating to supporting authentication plug-ins. Once these are implemented, authentication against any protocol or repository of user names and passwords will be possible without having to touch MantisBT core code.
	</div><div class="para">
		It is important to note that MantisBT does not yet support hybrid authentication scenarios. For example, internal staff authenticating against LDAP while customers authenticate against the MantisBT database with MD5 hash.
	</div><div class="para">
		See $g_login_method in <a class="xref" href="#admin.config.auth.global">Section 5.21.1, “Global authentication parameters”</a> for more details about how to configure MantisBT to use one of these authentication techniques.
	</div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.auth.standard">
      ⁠</a>8.1. Standard Authentication</h2></div></div></div><div class="para">
			With Standard login method, MantisBT users are authenticated against records in the MantisBT database, where the passwords are stored as a hash.
		</div><div class="para">
			Note: while technically unlimited, the password's length is arbitrarily restricted to 1024 characters (PASSWORD_MAX_SIZE_BEFORE_HASH constant).
		</div><div class="para">
			Values for $g_login_method: 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						<span class="emphasis"><em><a href="https://en.wikipedia.org/wiki/MD5">MD5</a></em></span> is the default method
					</div></li><li class="listitem"><div class="para">
						Support for additional methods could be added in the future
					</div></li></ul></div>

		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.auth.ldap">
      ⁠</a>8.2. LDAP and Microsoft Active Directory</h2></div></div></div><div class="para">
			Value for $g_login_method: <span class="emphasis"><em>LDAP</em></span>
		</div><div class="para">
			Authentication is made against an <a href="https://en.wikipedia.org/wiki/LDAP">LDAP</a> or <a href="https://en.wikipedia.org/wiki/Active_Directory">Active Directory</a> server.
		</div><div class="para">
			The LDAP parameters should be setup as explained in <a class="xref" href="#admin.config.auth.ldap">Section 5.21.2, “LDAP authentication method parameters”</a>.
		</div><div class="para">
			An MD5 hash of the user's password will be stored in the database upon successful login, allowing fall-back to Standard Authentication when the LDAP server is not available.
		</div><div class="para">
			The user's ID and password is checked against the Directory; if the credentials are valid, then the user is allowed to login and their user account in MantisBT is created automatically.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.auth.basic">
      ⁠</a>8.3. Basic Authentication</h2></div></div></div><div class="para">
			Value for $g_login_method: <span class="emphasis"><em>BASIC_AUTH</em></span>
		</div><div class="para">
			When MantisBT is configured to use basic auth, it automatically detects the logged in user and checks if they are already registered in MantisBT, if not, then a new account is automatically created for the username.
		</div><div class="para">
			The password length is limited to the size of the underlying database field (DB_FIELD_SIZE_PASSWORD constant), currently 32 characters.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.auth.http">
      ⁠</a>8.4. HTTP Authentication</h2></div></div></div><div class="para">
			Value for $g_login_method: <span class="emphasis"><em>HTTP_AUTH</em></span>
		</div><div class="para">
			TODO
		</div><div class="para">
			The password length is limited to the size of the underlying database field (DB_FIELD_SIZE_PASSWORD constant), currently 32 characters.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.auth.deprecated">
      ⁠</a>8.5. Deprecated authentication methods</h2></div></div></div><div class="para">
			The following methods of authentication are deprecated, and supported for backwards-compatibility reasons only. It is strongly recommended to update MantisBT installations relying on these to use <a class="xref" href="#admin.auth.standard">Section 8.1, “Standard Authentication”</a> instead.
		</div><div class="para">
			Deprecated values for $g_login_method: 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						CRYPT
					</div></li><li class="listitem"><div class="para">
						CRYPT_FULL_SALT
					</div></li><li class="listitem"><div class="para">
						PLAIN
					</div></li></ul></div>
			 With CRYPT-based methods, the password's length is limited as per Standard Authentication. With PLAIN, its size is restricted as for Basic Authentication.
		</div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.troubleshooting">
      ⁠</a>Chapter 9. Troubleshooting</h1></div></div></div><div class="toc"><dl class="toc"><dt><span class="section"><a href="#admin.troubleshooting.errors">9.1. Application Errors</a></span></dt><dd><dl><dt><span class="section"><a href="#admin.troubleshooting.errors.2800">9.1.1. Error 2800 - Invalid form security token</a></span></dt></dl></dd></dl></div><div class="para">
		This chapter provides the Administrator with additional information related to Application Errors and common problems in MantisBT.
	</div><div class="para">
		Useful additional reference information and support may also be found on the <a href="https://mantisbt.org/">MantisBT website</a>, more specifically the <a href="https://mantisbt.org/forums/">Forums</a> and the <a href="https://mantisbt.org/bugs/">Bugtracker</a>.
	</div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.troubleshooting.errors">
      ⁠</a>9.1. Application Errors</h2></div></div></div><div class="para">
			Additional information about common MantisBT errors.
		</div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.troubleshooting.errors.2800">
      ⁠</a>9.1.1. Error 2800 - Invalid form security token</h3></div></div></div><div class="para">
				This error may only occur when Form Validation is enabled with $g_form_security_validation = ON (see <a class="xref" href="#admin.config.webserver">Section 5.4, “Webserver”</a>). There are several known cases that could trigger it: 
				<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							Multiple submissions of a form by clicking on the submit button several times (user error)
						</div></li><li class="listitem"><div class="para">
							Invalid or unauthorized submission of a form, e.g. by hand-crafting the URL (CSRF attack)
						</div></li><li class="listitem"><div class="para">
							Expired PHP session
						</div></li></ul></div>
				 In the first two instances, MantisBT's behavior is by design, and the response as expected. For expired sessions however, the user is impacted by system behavior, which could not only cause confusion, but also potential loss of submitted form data. What happens is driven by several php.ini configuration settings: 
				<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							The ratio <a href="https://www.php.net/session.gc-probability"> session.gc_probability</a> divided by <a href="https://www.php.net/session.gc-divisor"> session.gc_divisor</a>, which determines the probability that the garbage collection process will start when a session is initialized.
						</div></li><li class="listitem"><div class="para">
							<a href="https://www.php.net/session.gc-maxlifetime"> session.gc_maxlifetime</a> which specifies (as the name does not indicate) the <span class="emphasis"><em>minimum</em></span> validity of session data.
						</div></li></ul></div>
				 With PHP default values, sessions created more than 1440 seconds (24 minutes) ago have a 1% chance to be invalidated each time a new session is initialized. This explains the seemingly random occurrence of this error.
			</div><div class="para">
				Unfortunately, this problem cannot be fixed without a major rework of the way sessions and form security are handled in MantisBT.
			</div><div class="para">
				As a workaround, the Administrator can 
				<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							Increase the value of <a href="https://www.php.net/session.gc-maxlifetime">session.gc_maxlifetime</a>
						</div></li><li class="listitem"><div class="para">
							Set $g_form_security_validation = OFF. <span class="emphasis"><em>Note that for security reasons, it is strongly recommended not to do this.</em></span>
						</div></li></ul></div>
				 Users may also install local tools to avoid loss of form data, such as <a href="https://chrome.google.com/webstore/detail/typio-form-recovery/djkbihbnjhkjahbhjaadbepppbpoedaa"> Typio Form Recovery </a> Chrome extension, or <a href="https://stephanmahieu.github.io/fhc-home/"> Form History Control </a> add-on for Firefox and Chrome.
			</div><div class="para">
				Further references and reading: 
				<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
							MantisBT issues <a href="https://mantisbt.org/bugs/view.php?id=12381">12381</a>, <a href="https://mantisbt.org/bugs/view.php?id=12492">12492</a>, <a href="https://mantisbt.org/bugs/view.php?id=13106">13106</a>, <a href="https://mantisbt.org/bugs/view.php?id=13246">13246</a>
						</div></li><li class="listitem"><div class="para">
							<a href="https://mantisbt.org/forums/search.php?keywords=2800">MantisBT forums</a>
						</div></li></ul></div>

			</div></div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.project">
      ⁠</a>Chapter 10. Project Management</h1></div></div></div><div class="toc"><dl class="toc"><dt><span class="section"><a href="#admin.project.changelog">10.1. Change Log</a></span></dt><dt><span class="section"><a href="#admin.project.roadmap">10.2. Roadmap</a></span></dt><dt><span class="section"><a href="#admin.project.timetracking">10.3. Time Tracking</a></span></dt><dt><span class="section"><a href="#admin.project.graphs">10.4. Graphs</a></span></dt><dt><span class="section"><a href="#admin.project.summary">10.5. Summary Page</a></span></dt></dl></div><div class="para">
		This section covers the project management features of MantisBT. This includes features like change log, roadmap, time tracking, reporting and others.
	</div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.project.changelog">
      ⁠</a>10.1. Change Log</h2></div></div></div><div class="para">
			MantisBT doesn't just track the status of issues, it also relates issues to versions. Each project can have several versions, which are marked with attributes like released and obsolete. Users typically report issues against released issues and developers typically fix issues in not released versions. With every new release comes question like: what's new? what has been fixed? Customers wonder if the new release is of interest to them and whether they should take an upgrade. Well, the change log is specifically tailored to answer these kind of questions.
		</div><div class="para">
			In order for an issue to show up in the change log, it has to satisfy certain criteria. The criteria is that the issue has to be resolved with a 'fixed' resolution and has to have the 'fixed_in_version' field set. Users sometimes wonder why resolved or closed issues don't show up in the change log, and the answer is that the 'fixed_in_version' field is not set. Without the 'fixed_in_version', it is not possible for MantisBT to include the issues in the appropriate section of the changelog. Note that it is possible to set the 'fixed_in_version' for multiple issues using the 'Update Fixed in Version' group action on the View Issues page (just below the issues list). This option is only available when the selected project is not 'All Projects'. Once a version is marked as obsolete, it is now longer included in the change log.
		</div><div class="para">
			MantisBT also provides the ability to customize the criteria used for an issue to be included in the change log. For example, for installations that use a custom set of resolutions, it is possible to select multiple resolutions as valid candidates for the change log. This can be done using custom functions (see custom functions documentation for more details). The custom function below overrides the MantisBT default behavior to include issues with both FIXED and IMPLEMENTED (a custom resolution) resolutions in the change log. 
<pre class="programlisting"><span xmlns="" class="line">​</span><span xmlns="" class="perl_Keyword">&lt;?php</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Comment"># --------------------</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Comment"># Checks the provided bug and determines whether it should be included in the changelog</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Comment"># or not.</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Comment"># returns true: to include, false: to exclude.</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Keyword">function</span> custom_function_override_changelog_include_issue<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$p_issue_id</span> <span xmlns="" class="perl_Others">)</span> {
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Variable">$t_issue</span> = bug_get<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$p_issue_id</span> <span xmlns="" class="perl_Others">);</span>
<span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Operator">return</span> <span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_issue</span>-&gt;resolution == FIXED || <span xmlns="" class="perl_Variable">$t_issue</span>-&gt;resolution == IMPLEMENTED <span xmlns="" class="perl_Others">)</span> &amp;&amp;
<span xmlns="" class="line">​</span>        <span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_issue</span>-&gt;status &gt;= config_get<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_String">'bug_resolved_status_threshold'</span> <span xmlns="" class="perl_Others">)</span> <span xmlns="" class="perl_Others">)</span> <span xmlns="" class="perl_Others">);</span>
<span xmlns="" class="line">​</span>}
</pre>

		</div><div class="para">
			MantisBT also provides the ability to customize the details to include from the issue and in what format. This can be done using the following custom function. 
<pre class="programlisting"><span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Keyword">&lt;?php</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Comment"># --------------------</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Comment"># Prints one entry in the changelog.</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Keyword">function</span> custom_function_override_changelog_print_issue<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$p_issue_id</span><span xmlns="" class="perl_Others">,</span> <span xmlns="" class="perl_Variable">$p_issue_level</span> = 0 <span xmlns="" class="perl_Others">)</span> {
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Variable">$t_bug</span> = bug_get<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$p_issue_id</span> <span xmlns="" class="perl_Others">);</span>
<span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Operator">if</span><span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_bug</span>-&gt;category_id <span xmlns="" class="perl_Others">)</span> {
<span xmlns="" class="line">​</span>        <span xmlns="" class="perl_Variable">$t_category_name</span> = category_get_name<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_bug</span>-&gt;category_id <span xmlns="" class="perl_Others">);</span>
<span xmlns="" class="line">​</span>    } <span xmlns="" class="perl_Operator">else</span> {
<span xmlns="" class="line">​</span>        <span xmlns="" class="perl_Variable">$t_category_name</span> = <span xmlns="" class="perl_String">''</span><span xmlns="" class="perl_Others">;</span>
<span xmlns="" class="line">​</span>    }
<span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Variable">$t_category</span> = is_blank<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_category_name</span> <span xmlns="" class="perl_Others">)</span> ? <span xmlns="" class="perl_String">''</span> <span xmlns="" class="perl_Others">:</span> <span xmlns="" class="perl_String">'&amp;lt;b&amp;gt;['</span> . <span xmlns="" class="perl_Variable">$t_category_name</span> . <span xmlns="" class="perl_String">']&amp;lt;/b&amp;gt; '</span><span xmlns="" class="perl_Others">;</span>
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Function">echo</span> <span xmlns="" class="perl_Function">str_pad</span><span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_String">''</span><span xmlns="" class="perl_Others">,</span> <span xmlns="" class="perl_Variable">$p_issue_level</span> * <span xmlns="" class="perl_Float">6</span><span xmlns="" class="perl_Others">,</span> <span xmlns="" class="perl_String">'&amp;#160;'</span> <span xmlns="" class="perl_Others">),</span> <span xmlns="" class="perl_String">'- '</span><span xmlns="" class="perl_Others">,</span> string_get_bug_view_link<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$p_issue_id</span> <span xmlns="" class="perl_Others">),</span> <span xmlns="" class="perl_String">': '</span><span xmlns="" class="perl_Others">,</span> <span xmlns="" class="perl_Variable">$t_category</span><span xmlns="" class="perl_Others">,</span> string_display_line_links<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_bug</span>-&gt;summary <span xmlns="" class="perl_Others">);</span>
<span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Operator">if</span><span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_bug</span>-&gt;handler_id != 0 <span xmlns="" class="perl_Others">)</span> {
<span xmlns="" class="line">​</span>        <span xmlns="" class="perl_Function">echo</span> <span xmlns="" class="perl_String">' ('</span><span xmlns="" class="perl_Others">,</span> prepare_user_name<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_bug</span>-&gt;handler_id <span xmlns="" class="perl_Others">),</span> <span xmlns="" class="perl_String">')'</span><span xmlns="" class="perl_Others">;</span>
<span xmlns="" class="line">​</span>    }
<span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Function">echo</span> <span xmlns="" class="perl_String">' - '</span><span xmlns="" class="perl_Others">,</span> get_enum_element<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_String">'status'</span><span xmlns="" class="perl_Others">,</span> <span xmlns="" class="perl_Variable">$t_bug</span>-&gt;status <span xmlns="" class="perl_Others">),</span> <span xmlns="" class="perl_String">'.&amp;lt;br /&amp;gt;'</span><span xmlns="" class="perl_Others">;</span>
<span xmlns="" class="line">​</span>}
</pre>

		</div><div class="para">
			By combining both customization features, it is also possible to do more advanced customization scenarios. For example, users can add a 'ChangelogSummary' custom field and include all issues that have such field in the change log. Through customizing what information being included for a qualifying issue, users can also include the 'ChangelogSummary' text rather than the native summary field.
		</div><div class="para">
			In some cases, users know that they fixed an issue and that the fix will be included in the next release, however, they don't know yet the name of the release. In such case, the recommended approach is to always have a version defined that corresponds to the next release, which is typically called 'Next Release'. Once the release is cut and has a concrete name, then 'Next Release' can be renamed to the appropriate name and a new 'Next Release' can then be created. For teams that manage releases from multiple branches for the same project, then more than one next release can be possible. For example, 'Next Dev Release' and 'Next Stable Release'.
		</div><div class="para">
			Another common requirement is to be able to link to the change log of a specific project from the project's main website. There is a variety of ways to do that: 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						To link to the changelog of version "ver1" of project "myproject": 
<pre class="programlisting">
http://www.example.com/mantisbt/changelog_page.php?project=myproject&amp;version=ver1
</pre>

					</div></li><li class="listitem"><div class="para">
						To link to the changelog of all non-obsolete versions of project 'myproject': 
<pre class="programlisting">
http://www.example.com/mantisbt/changelog_page.php?project=myproject
</pre>

					</div></li><li class="listitem"><div class="para">
						To link to the changelog of project with id 1. The project id can be figured out by going to the management page for the project and getting the value of project_id field form the URL. 
<pre class="programlisting">
http://www.example.com/mantisbt/changelog_page.php?project_id=1
</pre>

					</div></li><li class="listitem"><div class="para">
						To link to the changelog of version with id 1. The version id is unique across all projects and hence in this case it is not necessary to include the project id/name. The version id can be figured out by going to the manage project page and editing the required version. The version_id will be included in the URL. 
<pre class="programlisting">
http://www.example.com/mantisbt/changelog_page.php?version_id=1
</pre>

					</div></li></ul></div>

		</div><div class="para">
			Another approach is to go to the project page and from there users can get to multiple other locations relating to the project include the change log. This can be done by a URL like the following: 
<pre class="programlisting">
http://www.example.com/mantisbt/project_page.php?project_id=1
</pre>

		</div><div class="para">
			It is possible to customize the access level required for viewing the change log page. This can be done using the $g_view_changelog_threshold configuration option.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.project.roadmap">
      ⁠</a>10.2. Roadmap</h2></div></div></div><div class="para">
			One of the very important scenarios in project management is where the project managers (or team leads) triage the issues to set their priorities, target version, and possibly assign the issues to specific developers or take other actions on the issue. By setting the target version of an issue to a version that is not yet released, the issue shows up on the project roadmap, providing user with information about when to expect the issues to be resolved. The roadmap page has a section for each release showing information like planned issues, issues done and percentage of issues completed. Issues that are fixed in a specific version, but didn't have the target_version field set, will not show up in the roadmap. This allows the ability to control the issues that are significant enough to show in the roadmap, while all resolved fields can be found in the change log. Note that it is possible to set the 'target_version' for multiple issues using the 'Update Target Version' group action that is available through the View Issues page (below the issues list). This option is only available when the current project is not 'All Projects'. Although it is not a typical scenario, it is worth mentioning that once a version is marked as obsolete, it is not included in the roadmap.
		</div><div class="para">
			Note that the roadmap only includes future versions, once a version is marked as released, it no longer is included in the roadmap. For information about such releases, the change log feature should be used. For an issue to be shown on the roadmap, it has to have the target version set. It does not matter whether the feature is resolved or not. Resolved features will be decorated with a strikethrough and will be counted as done.
		</div><div class="para">
			MantisBT provides the ability to customize the criteria for issues to show up on the roadmap. The default criteria is that the issue has to belong to a version that is not yet released and that the issues is not a duplicate. However, such criteria can be customized by using custom functions as below. 
<pre class="programlisting"><span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Keyword">&lt;?php</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Comment"># --------------------</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Comment"># Checks the provided bug and determines whether it should be included in the roadmap or not.</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Comment"># returns true: to include, false: to exclude.</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Keyword">function</span> custom_function_override_roadmap_include_issue<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$p_issue_id</span> <span xmlns="" class="perl_Others">)</span> {
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Operator">return</span> <span xmlns="" class="perl_Others">(</span> true <span xmlns="" class="perl_Others">);</span>
<span xmlns="" class="line">​</span>}
</pre>

		</div><div class="para">
			It is also possible to customize the details included about an issues and the presentation of such details. This can be done through the following custom function: 
<pre class="programlisting"><span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Keyword">&lt;?php</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Comment"># --------------------</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Comment"># Prints one entry in the roadmap.</span>
<span xmlns="" class="line">​</span><span xmlns="" class="perl_Keyword">function</span> custom_function_override_roadmap_print_issue<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$p_issue_id</span><span xmlns="" class="perl_Others">,</span> <span xmlns="" class="perl_Variable">$p_issue_level</span> = 0 <span xmlns="" class="perl_Others">)</span> {
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Variable">$t_bug</span> = bug_get<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$p_issue_id</span> <span xmlns="" class="perl_Others">);</span>
<span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Operator">if</span><span xmlns="" class="perl_Others">(</span> bug_is_resolved<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$p_issue_id</span> <span xmlns="" class="perl_Others">)</span> <span xmlns="" class="perl_Others">)</span> {
<span xmlns="" class="line">​</span>        <span xmlns="" class="perl_Variable">$t_strike_start</span> = <span xmlns="" class="perl_String">'&amp;lt;strike&amp;gt;'</span><span xmlns="" class="perl_Others">;</span>
<span xmlns="" class="line">​</span>        <span xmlns="" class="perl_Variable">$t_strike_end</span> = <span xmlns="" class="perl_String">'&amp;lt;/strike&amp;gt;'</span><span xmlns="" class="perl_Others">;</span>
<span xmlns="" class="line">​</span>    } <span xmlns="" class="perl_Operator">else</span> {
<span xmlns="" class="line">​</span>        <span xmlns="" class="perl_Variable">$t_strike_start</span> = <span xmlns="" class="perl_Variable">$t_strike_end</span> = <span xmlns="" class="perl_String">''</span><span xmlns="" class="perl_Others">;</span>
<span xmlns="" class="line">​</span>    }
<span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Operator">if</span><span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_bug</span>-&gt;category_id <span xmlns="" class="perl_Others">)</span> {
<span xmlns="" class="line">​</span>        <span xmlns="" class="perl_Variable">$t_category_name</span> = category_get_name<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_bug</span>-&gt;category_id <span xmlns="" class="perl_Others">);</span>
<span xmlns="" class="line">​</span>    } <span xmlns="" class="perl_Operator">else</span> {
<span xmlns="" class="line">​</span>        <span xmlns="" class="perl_Variable">$t_category_name</span> = <span xmlns="" class="perl_String">''</span><span xmlns="" class="perl_Others">;</span>
<span xmlns="" class="line">​</span>    }
<span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Variable">$t_category</span> = is_blank<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_category_name</span> <span xmlns="" class="perl_Others">)</span> ? <span xmlns="" class="perl_String">''</span> <span xmlns="" class="perl_Others">:</span> <span xmlns="" class="perl_String">'&amp;lt;b&amp;gt;['</span> . <span xmlns="" class="perl_Variable">$t_category_name</span> . <span xmlns="" class="perl_String">']&amp;lt;/b&amp;gt; '</span><span xmlns="" class="perl_Others">;</span>
<span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Function">echo</span> <span xmlns="" class="perl_Function">str_pad</span><span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_String">''</span><span xmlns="" class="perl_Others">,</span> <span xmlns="" class="perl_Variable">$p_issue_level</span> * <span xmlns="" class="perl_Float">6</span><span xmlns="" class="perl_Others">,</span> <span xmlns="" class="perl_String">'&amp;#160;'</span> <span xmlns="" class="perl_Others">),</span> <span xmlns="" class="perl_String">'- '</span><span xmlns="" class="perl_Others">,</span> <span xmlns="" class="perl_Variable">$t_strike_start</span><span xmlns="" class="perl_Others">,</span> string_get_bug_view_link<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$p_issue_id</span> <span xmlns="" class="perl_Others">),</span> <span xmlns="" class="perl_String">': '</span><span xmlns="" class="perl_Others">,</span> <span xmlns="" class="perl_Variable">$t_category</span><span xmlns="" class="perl_Others">,</span> string_display_line_links<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_bug</span>-&gt;summary <span xmlns="" class="perl_Others">);</span>
<span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Operator">if</span><span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_bug</span>-&gt;handler_id != 0 <span xmlns="" class="perl_Others">)</span> {
<span xmlns="" class="line">​</span>        <span xmlns="" class="perl_Function">echo</span> <span xmlns="" class="perl_String">' ('</span><span xmlns="" class="perl_Others">,</span> prepare_user_name<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_Variable">$t_bug</span>-&gt;handler_id <span xmlns="" class="perl_Others">),</span> <span xmlns="" class="perl_String">')'</span><span xmlns="" class="perl_Others">;</span>
<span xmlns="" class="line">​</span>    }
<span xmlns="" class="line">​</span>
<span xmlns="" class="line">​</span>    <span xmlns="" class="perl_Function">echo</span> <span xmlns="" class="perl_String">' - '</span><span xmlns="" class="perl_Others">,</span> get_enum_element<span xmlns="" class="perl_Others">(</span> <span xmlns="" class="perl_String">'status'</span><span xmlns="" class="perl_Others">,</span> <span xmlns="" class="perl_Variable">$t_bug</span>-&gt;status <span xmlns="" class="perl_Others">),</span> <span xmlns="" class="perl_Variable">$t_strike_end</span><span xmlns="" class="perl_Others">,</span> <span xmlns="" class="perl_String">'.&amp;lt;br /&amp;gt;'</span><span xmlns="" class="perl_Others">;</span>
<span xmlns="" class="line">​</span>}
</pre>

		</div><div class="para">
			Some teams manage different branches for each of their projects (e.g. development and maintenance branches). As part of triaging the issue, they may decide that an issue should be targeted to multiple branches. Hence, frequently the request comes up to be able to target a single issue to multiple releases. The current MantisBT approach is that an issues represents an implementation or a fix for an issue on a specific branch. Since sometimes applying and verifying a fix to the two branches does not happen at the same time and in some cases the approach for fixing an issue is different based on the branch. Hence, the way to manage such scenario is to have the main issue for the initial fix and have related issues which capture the work relating to applying the fix to other branches. The issues for porting the fix can contain any discussions relating to progress, reflect the appropriate status and can go through the standard workflow process independent of the original issues.
		</div><div class="para">
			Another common requirement is to be able to link to the roadmap of a specific project from the project's main website. There is a variety of ways to do that: 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						To link to the roadmap of version "ver1" of project "myproject": 
<pre class="programlisting">
http://www.example.com/mantisbt/roadmap_page.php?project=myproject&amp;version=ver1
</pre>

					</div></li><li class="listitem"><div class="para">
						To link to the roadmap of all non-obsolete versions of project 'myproject': 
<pre class="programlisting">
http://www.example.com/mantisbt/roadmap_page.php?project=myproject
</pre>

					</div></li><li class="listitem"><div class="para">
						To link to the roadmap of project with id 1. The project id can be figured out by going to the management page for the project and getting the value of project_id field form the URL. 
<pre class="programlisting">
http://www.example.com/mantisbt/roadmap_page.php?project_id=1
</pre>

					</div></li><li class="listitem"><div class="para">
						To link to the roadmap of version with id 1. The version id is unique across all projects and hence in this case it is not necessary to include the project id/name. The version id can be figured out by going to the manage project page and editing the required version. The version_id will be included in the URL. 
<pre class="programlisting">
http://www.example.com/mantisbt/roadmap_page.php?version_id=1
</pre>

					</div></li></ul></div>

		</div><div class="para">
			Another approach is to go to the project page and from there users can get to multiple other locations relating to the project include the roadmap. This can be done by a URL like the following: 
<pre class="programlisting">
http://www.example.com/mantisbt/project_page.php?project_id=1
</pre>

		</div><div class="para">
			The access level required to view and modify the roadmap can be configured through $g_roadmap_view_threshold and $g_roadmap_update_threshold respectively. Modifying the roadmap is the ability to set the target versions for issues. Users who have such access can set the target versions while reporting new issues or by updating existing issues.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.project.timetracking">
      ⁠</a>10.3. Time Tracking</h2></div></div></div><div class="para">
			To activate the Time Tracking feature you have to set the configuration option "time_tracking_enabled" to ON. To activating the Time Tracking you can : 
			<div xmlns:d="http://docbook.org/ns/docbook" class="itemizedlist"><ul><li class="listitem"><div class="para">
						Static solution : change the variable '$g_time_tracking_enabled' in the configuration file 'config_defaults_inc.php', this will change the configuration for all the MantisBT instance ;
					</div></li><li class="listitem"><div class="para">
						Dynamic and "project by project" solution : Use the administration page "Manage Configuration" and set the variable 'time_tracking_enabled' to '1' for which user and which project of you choice.
					</div></li></ul></div>

		</div><div class="para">
			All Time Tracking configuration options are described in the configuration section off this guide.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.project.graphs">
      ⁠</a>10.4. Graphs</h2></div></div></div><div class="para">
			Assigned to me: TODO
		</div><div class="para">
			Release Delta: TODO
		</div><div class="para">
			Category: TODO
		</div><div class="para">
			Severity: TODO
		</div><div class="para">
			Severity / Status: TODO
		</div><div class="para">
			Daily Delta: TODO
		</div><div class="para">
			Reported by Me: TODO
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.project.summary">
      ⁠</a>10.5. Summary Page</h2></div></div></div><div class="para">
			By Status: TODO
		</div><div class="para">
			By Severity: TODO
		</div><div class="para">
			By Category: TODO
		</div><div class="para">
			Time Stats for Resolved Issues (days): TODO
		</div><div class="para">
			Developer Status: TODO
		</div><div class="para">
			Reporter by Resolution: TODO
		</div><div class="para">
			Developer by Resolution: TODO
		</div><div class="para">
			By Date: TODO
		</div><div class="para">
			Most Active: TODO
		</div><div class="para">
			Longest Open: TODO
		</div><div class="para">
			By Resolution: TODO
		</div><div class="para">
			By Priority: TODO
		</div><div class="para">
			Reporter Status: TODO
		</div><div class="para">
			Reporter Effectiveness: TODO
		</div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.contributing">
      ⁠</a>Chapter 11. Contributing to MantisBT</h1></div></div></div><div class="toc"><dl class="toc"><dt><span class="section"><a href="#admin.contributing.develop">11.1. Talent and Time</a></span></dt><dt><span class="section"><a href="#admin.contributing.share">11.2. Recommend MantisBT to Others</a></span></dt><dt><span class="section"><a href="#admin.contributing.blog">11.3. Blog about MantisBT</a></span></dt><dt><span class="section"><a href="#admin.contributing.integrate">11.4. Integrate with MantisBT</a></span></dt></dl></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.contributing.develop">
      ⁠</a>11.1. Talent and Time</h2></div></div></div><div class="para">
			One of the greatest ways to contribute to MantisBT is to contribute your talent and time. For MantisBT to keep growing we need such support in all areas related to the software development cycle. This includes: business analysts, developers, web designers, graphics designers, technical writers, globalization developers, translators, testers, super users, packagers and active users. If you would like to contribute in any of these capacities please contact us through the "Contact Us" page.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.contributing.share">
      ⁠</a>11.2. Recommend MantisBT to Others</h2></div></div></div><div class="para">
			It feels great when we get feedback from the user community about how MantisBT boosted their productivity, and benefited their organization. A lot of the feedback I get is via email, some on mailing lists, and some on forums. I would encourage such users to blog about it, tell their friends about MantisBT, and recommend MantisBT to other organizations. MantisBT is driven by it's community, the greater the community, the greater the ideas, the greater of a product it becomes.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.contributing.blog">
      ⁠</a>11.3. Blog about MantisBT</h2></div></div></div><div class="para">
			If you have a blog, then talk about MantisBT, review it's features and help us spread the word. A lot of users also like to blog about how they customized MantisBT to fit their needs or to integrate with other tools that they use in their work environment.
		</div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.contributing.integrate">
      ⁠</a>11.4. Integrate with MantisBT</h2></div></div></div><div class="para">
			If you have a product that can be integrated with MantisBT to provide value for MantisBT users, that would be a great place to contribute and benefit both your project's and the MantisBT community.
		</div><div class="para">
			A great example in this area are integrations with content management systems (e.g. *Nuke, Xoops), project management (PHPProjekt), and TestLink for Test Management. MantisBT can easily be integrated with projects in any programming language whether it is hosted on the same webserver or anywhere else in the world. This can be achieved through its SOAP API and MantisConnect client libraries. MantisConnect comes with client libraries and samples in languages like PHP, .NET, Java and Cocoa.
		</div></div></div><div xml:lang="en-US" class="appendix" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="appe-Admin_Guide-Revision_History">
      ⁠</a>Appendix A. Revision History</h1></div></div></div><div xmlns:d="http://docbook.org/ns/docbook" class="para"><p></p>
		<div class="revhistory"><table summary="Revision History"><tr><th align="left" valign="top" colspan="3"><strong>Revision History</strong></th></tr><tr><td align="left">Revision 2.26-0</td><td align="left">Sun Oct 15 2023</td><td align="left"><span class="author"><span class="firstname">Damien</span> <span class="surname">Regad</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.26.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.25-0</td><td align="left">Mon Mar 8 2021</td><td align="left"><span class="author"><span class="firstname">Damien</span> <span class="surname">Regad</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.25.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.24-1</td><td align="left">Sun May 3 2020</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.24.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.24-0</td><td align="left">Sun Mar 15 2020</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.24.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.23-0</td><td align="left">Sun Dec 9 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.23.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.22-1</td><td align="left">Thu Sep 26 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.22.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.22-0</td><td align="left">Sun Aug 25 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.22.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.21-2</td><td align="left">Mon Aug 19 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.21.2</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.21-1</td><td align="left">Thu Jun 13 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.21.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.21-0</td><td align="left">Sat Apr 20 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.21.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.20-0</td><td align="left">Sat Mar 16 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.20.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.19-0</td><td align="left">Wed Jan 2 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.19.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.18-0</td><td align="left">Tue Oct 16 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.18.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.17-1</td><td align="left">Mon Sep 24 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.17.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.17-0</td><td align="left">Mon Sep 3 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.17.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.16-0</td><td align="left">Sun Jul 29 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.16.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.15-0</td><td align="left">Tue Jun 5 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.15.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.14-0</td><td align="left">Sun Apr 29 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.14.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.13-1</td><td align="left">Wed Apr 4 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.13.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.13-0</td><td align="left">Sun Apr 1 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.13.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.12-0</td><td align="left">Sat Mar 3 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.12.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.11-0</td><td align="left">Tue Feb 6 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.11.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.10-0</td><td align="left">Sat Dec 30 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.10.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.9-0</td><td align="left">Sun Dec 3 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.9.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.8-0</td><td align="left">Sat Oct 28 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.8.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.7-0</td><td align="left">Sun Oct 8 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.7.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.6-0</td><td align="left">Sun Sep 3 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.6.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.5-1</td><td align="left">Sat Jun 17 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.5.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.5-0</td><td align="left">Sun Jun 4 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.5.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.4-1</td><td align="left">Sat May 20 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.4.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.4-0</td><td align="left">Sun Apr 30 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.4.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.3-3</td><td align="left">Sun Apr 30 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.3.2</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.3-2</td><td align="left">Sun Apr 17 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.3.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.3-1</td><td align="left">Fri Mar 31 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.3.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.2-3</td><td align="left">Wed Mar 22 2017</td><td align="left"><span class="author"><span class="firstname">Damien</span> <span class="surname">Regad</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.2.2</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.2-2</td><td align="left">Sun Mar 12 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.2.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.2-1</td><td align="left">Sun Feb 26 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.2.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.1-2</td><td align="left">Sun Feb 26 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.1.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.1-1</td><td align="left">Tue Jan 31 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.1.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.0-2</td><td align="left">Fri Dec 30 2016</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.0.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.0-1</td><td align="left">Sat Nov 26 2016</td><td align="left"><span class="author"><span class="firstname">Damien</span> <span class="surname">Regad</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.0.0-rc.2</td></tr></table>

				</td></tr></table></div>

	</div></div></div></body></html>