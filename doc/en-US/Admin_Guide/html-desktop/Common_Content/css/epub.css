/*headings*/
h1, h2, h3, h4, h5, h6,
div.producttitle,
div.subtitle,
div.author div.author,
div.translator div.translator,
div.othercredit div.othercredit,
div.editor div.editor,
div.contrib div.contrib,
.title,
.titlepage .edition {
}

div.para {
	margin-top: 1em;
}
/* inline syntax highlighting */
.perl_Alert {
	color: #0000ff;
}

.perl_BaseN {
	color: #007f00;
}

.perl_BString {
	color: #5C3566;
}

.perl_Char {
	color: #ff00ff;
}

.perl_Comment {
	color: #888888;
}


.perl_DataType {
	color: #0000ff;
}


.perl_DecVal {
	color: #00007f;
}


.perl_Error {
	color: #ff0000;
}


.perl_Float {
	color: #00007f;
}


.perl_Function {
	color: #007f00;
}


.perl_IString {
	color: #5C3566;
}


.perl_Keyword {
	color: #002F5D;
}


.perl_Operator {
	color: #ffa500;
}


.perl_Others {
	color: #b03060;
}


.perl_RegionMarker {
	color: #96b9ff;
}


.perl_Reserved {
	color: #9b30ff;
}


.perl_String {
	color: #5C3566;
}


.perl_Variable {
	color: #0000ff;
}


.perl_Warning {
	color: #0000ff;
}

b, strong {
	font-weight: bolder;
}

code.command {
	font-family: monospace;
	font-weight: bolder;
}
