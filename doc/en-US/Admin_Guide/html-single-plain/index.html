<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml"><head><title>Admin Guide</title><meta name="generator" content="DocBook XSL Stylesheets Vsnapshot" /><meta name="description" content="This book is targeted at MantisBT administrators, and documents the installation, upgrade, configuration, customization and administration tasks required to operate the software." /><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /></head><body><div xml:lang="en-US" class="book" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="idm1"></a>Admin Guide</h1></div><div><h2 class="subtitle">Reference for Administrators</h2></div><div><h3 class="corpauthor">
		<span class="inlinemediaobject"><img src="./images/mantis_logo.png" /></span>

	</h3></div><div><div xml:lang="en-US" class="authorgroup" lang="en-US"><div class="author"><h3 class="author"><span class="surname">MantisBT Development Team</span></h3><code class="email">&lt;<a class="email" href="mailto:<EMAIL>"><EMAIL></a>&gt;</code></div></div></div><div><div xml:lang="en-US" class="legalnotice" lang="en-US"><a id="idm13"></a><p>
		Copyright <span class="trademark"></span>© 2016 MantisBT team.  This material may only be distributed subject to the terms and conditions set forth in the GNU Free Documentation License (GFDL), V1.2 or later (the latest version is presently available at <a class="ulink" href="http://www.gnu.org/licenses/fdl.txt" target="_top">http://www.gnu.org/licenses/fdl.txt</a>).
	</p></div></div><div><div class="abstract"><div class="abstract-title">Abstract</div><p>
			This book is targeted at MantisBT administrators, and documents the installation, upgrade, configuration, customization and administration tasks required to operate the software.
		</p></div></div></div><hr /></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.about"></a>Chapter 1. About MantisBT</h1></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.what"></a>1.1. What is MantisBT?</h2></div></div></div><p>
			MantisBT is a web based bug tracking system that was first made available to the public in November 2000. Over time it has matured and gained a lot of popularity, and now it has become one of the most popular open source bug/issue tracking systems. MantisBT is developed in PHP, with support to multiple database backends including MySQL, MS SQL and PostgreSQL.
		</p><p>
			MantisBT, as a PHP script, can run on any operating system that is supported by PHP and has support for one of the DBMSes that are supported. MantisBT is known to run fine on Windows, Linux, macOS and a variety of Unix operating systems.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.who"></a>1.2. Who should read this manual?</h2></div></div></div><p>
			This manual is targeted for the person responsible for evaluating, installing and maintaining MantisBT in a company. Typically we refer to this person as the MantisBT administrator.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.license"></a>1.3. License</h2></div></div></div><p>
			MantisBT is released under the terms of <a class="ulink" href="https://www.gnu.org/copyleft/gpl.html" target="_top">GNU General Public License (GPL)</a>. MantisBT is free to use and modify. It is free to redistribute as long as you abide by the distribution terms of the <a class="ulink" href="https://www.gnu.org/copyleft/gpl.html" target="_top">GPL</a>.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.download"></a>1.4. How to get it?</h2></div></div></div><p>
			MantisBT is available in several Linux distributions including: Debian, Ubuntu, Fedora, Gentoo, Frugalware and others. Hence, if you are running Linux, start by checking if your distribution has a package for MantisBT. If not, or if the package is not up-to-date with the latest MantisBT version, then you may want to download it directly from <a class="ulink" href="https://mantisbt.org/download.php" target="_top">here</a>.
		</p><p>
			For Windows, macOS and other operating systems, use the link provided above to download MantisBT. The download is compressed in tar.gz or zip format. Both formats can be unpacked using tools like <a class="ulink" href="https://www.7-zip.org/" target="_top">7-Zip</a> (in case of Windows).
		</p><p>
			Note that at any point in time there are typically two "latest" MantisBT releases that are available for download. The latest production release (stable), and the latest development release which can be an alpha or a release candidate. It is not recommended to use development releases in production specially if it is still in the alpha stage unless the administrator is familiar with PHP and is able to troubleshoot and fix any issues that may arise.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.name"></a>1.5. About the Name</h2></div></div></div><p>
			When initially seeking to name this project Ken ran into a problem every programmer encounters. What is a good name? It has to be descriptive, unique, and not too verbose. Additionally having multiple meanings would be a nice touch. Quickly ruled out were php*Something* names which, incidentally, although popular, do not seem to be condoned by the PHP Group developers. Drawing inspiration from Open Source projects like Apache, Mozilla, Gnome, and so forth resulted in two eventual choices: Dragonfly and Mantis. Dragonfly was already the name of a webmail package. So the name became Mantis.
		</p><p>
			Praying Mantis are insects that feed primarily on other insects and bugs. They are extremely desirable in agriculture as they devour insects that feed on crops. They are also extremely elegant looking creatures. So, we have a name that is fairly distinctive and descriptive in multiple ways. The BT suffix stands for "Bug Tracker" and distinguishes this project from general usage of the word Mantis. However, over time the project was typically referred to as Mantis.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.history"></a>1.6. History</h2></div></div></div><p>
			Kenzaburo Ito and a friend originally created a bug tracker as an internal tool for their pet project. A search for good, free packages came up with nothing suitable so they wrote their own. After a rewrite and cleanup it was made available to the public via the GNU General Public License (GPL). The GPL was chosen partly because of his belief that development tools should be cheap or free. In 2002, Ken was joined by Jeroen Latour, Victor Boctor and Julian Fitzell to be the administrators and the core development team of MantisBT. This marks a new era in MantisBT lifetime where it is now a team project.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.support"></a>1.7. Support</h2></div></div></div><p>
			There are plenty of resources to help answer support queries. Following are the main ones:
		</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
					<a class="ulink" href="https://mantisbt.org/forums/" target="_top">Forums</a> - The forums are one of the most popular destinations for getting MantisBT support. Start off by searching the forums for your questions, if not found, then go ahead and submit a question.
				</p></li><li class="listitem"><p>
					<a class="ulink" href="http://www.mantisbt.org/mailinglists.php" target="_top">Mailing lists</a> - Several lists are available, each of them with its own, specific purpose. Note that posting messages is restricted to subscribers so you will have to register before you can send messages; however, there are public archives available if you're only interested in reading.
				</p></li><li class="listitem"><p>
					<a class="ulink" href="https://gitter.im/mantisbt/mantisbt" target="_top">Gitter</a> is a browser-based, on-line chat that has mainly replaced the team's use of IRC. In the main chat room, you can have a live discussion with the developers and other MantisBT users. Gitter supports all modern browsers and also offers Android and iOS-based clients, as well as an <a class="ulink" href="https://irc.gitter.im/" target="_top">IRC bridge</a>.
				</p></li><li class="listitem"><p>
					<a class="ulink" href="http://www.mantisbt.org/irc.php" target="_top">IRC</a> - The IRC channel not very active anymore, as the developers have moved on to using Gitter for live discussions; nevertheless, the channel is still open. There are many free IRC clients: XChat (for Linux), <a class="ulink" href="http://hexchat.github.io/" target="_top">HexChat</a>, <a class="ulink" href="http://www.icechat.net/" target="_top">IceChat</a> amongst others. You can also use <a class="ulink" href="http://webchat.freenode.net/" target="_top">Web Chat</a> to connect to IRC via your web browser, which may also be useful when you're behind a firewall that blocks the IRC port. The IRC channel logs are archived and made <a class="ulink" href="http://www.mantisbt.org/irclogs.php" target="_top"> available on the MantisBT web site</a>.
				</p></li><li class="listitem"><p>
					<a class="ulink" href="https://mantisbt.org/wiki/doku.php/mantisbt:start" target="_top">Wiki</a> - The MantisBT Wiki has information related to "How To (recipes)", FAQ, feature requirements, plugins etc.
				</p></li><li class="listitem"><p>
					Search - A good way for locating an answer to your question or finding more information about a topic is to search across all MantisBT website and the Internet via your favorite search engine, e.g. <a class="ulink" href="https://www.google.com" target="_top">Google</a> or <a class="ulink" href="https://www.bing.com" target="_top">Bing</a>.
				</p></li></ul></div><div class="note"><h3 class="title">Note</h3><p>
				Support questions should not be sent directly to MantisBT developers or through the MantisBT website's contact pages.
			</p><p>
				Also, our <a class="ulink" href="https://mantisbt.org/bugs/" target="_top">bug tracker</a> is reserved for reporting issues with the software, and <span class="emphasis"><em>must not be used for support requests</em></span>.
			</p></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.news"></a>1.8. MantisBT News</h2></div></div></div><p>
			There are several ways to keep up to date with MantisBT news. These include:
		</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
					We send release announcements and important updates to users registered on our <a class="ulink" href="https://mantisbt.org/bugs" target="_top">official bugtracker</a>. To get onto our mailing list, users will have to signup there and verify their email address. This same account can also be used to report, monitor, and comment on issues relating to MantisBT.
				</p></li><li class="listitem"><p>
					<a class="ulink" href="https://mantisbt.org/blog/" target="_top">MantisBT Blog</a> is used to communicate announcements about new releases, topics relating to MantisBT, etc. Users are encouraged to subscribe to the RSS feed to know when new posts are posted there.
				</p></li><li class="listitem"><p>
					<a class="ulink" href="https://twitter.com/mantisbt" target="_top">Twitter</a> is used to notify users about up-to-date details about what is happening with MantisBT development. Twitter users are encouraged to follow "@mantisbt".
				</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.about.versioning"></a>1.9. Versioning</h2></div></div></div><p>
			Our release numbering convention follows the guidelines of <a class="ulink" href="https://semver.org/" target="_top">Semantic Versioning</a>. Given a version number <span class="emphasis"><em>Major.Minor.Patch</em></span> and an optional <span class="emphasis"><em>Suffix</em></span> (eg. 1.3.0-rc.1):
		</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
					Major - Indicates a very large change in the core package. Rewrites or major milestones. API changes which are not backwards-compatible.
				</p></li><li class="listitem"><p>
					Minor - Introduction of new features or significant changes in functionality, in a backwards-compatible manner.
				</p></li><li class="listitem"><p>
					Patch - Bug fixes, maintenance and security releases.
				</p></li><li class="listitem"><p>
					Suffix - Optional, indicates a development release. 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								a<span class="emphasis"><em>N</em></span> or alpha.<span class="emphasis"><em>N</em></span> for alpha releases,
							</p></li><li class="listitem"><p>
								b<span class="emphasis"><em>N</em></span> or beta.<span class="emphasis"><em>N</em></span> for beta releases, or
							</p></li><li class="listitem"><p>
								rc<span class="emphasis"><em>N</em></span> or rc.<span class="emphasis"><em>N</em></span> for release candidates.
							</p></li></ul></div><p>
					 Absence of suffix indicates a stable release.
				</p></li></ul></div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.install"></a>Chapter 2. Installation</h1></div></div></div><p>
		This chapter explains how to install or upgrade MantisBT.
	</p><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.overview"></a>2.1. Overview</h2></div></div></div><p>
			The table below contains a high-level overview of the processes. Refer to the corresponding sections for details.
		</p><div class="informaltable"><table class="informaltable" border="1"><colgroup><col /><col /></colgroup><thead><tr><th>New Installation</th><th>Upgrade</th></tr></thead><tbody><tr><td> <div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>
									<a class="xref" href="#admin.install.requirements" title="2.2. System Requirements">Section 2.2, “System Requirements”</a>
								</p></li><li class="listitem"><p>
									<a class="xref" href="#admin.install.preinstall" title="2.3. Pre-installation / upgrade tasks">Section 2.3, “Pre-installation / upgrade tasks”</a>
								</p></li><li class="listitem"><p>
									<a class="xref" href="#admin.install.new" title="2.4. New Installation">Section 2.4, “New Installation”</a>
								</p></li><li class="listitem"><p>
									<a class="xref" href="#admin.install.config" title="2.6. Configure your installation">Section 2.6, “Configure your installation”</a>
								</p></li><li class="listitem"><p>
									<a class="xref" href="#admin.install.postcommon" title="2.7. Post-installation and upgrade tasks">Section 2.7, “Post-installation and upgrade tasks”</a>
								</p></li><li class="listitem"><p>
									<a class="xref" href="#admin.install.postinstall" title="2.8. Post-installation tasks">Section 2.8, “Post-installation tasks”</a>
								</p></li></ol></div>
						 </td><td> <div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>
									<a class="xref" href="#admin.install.preinstall" title="2.3. Pre-installation / upgrade tasks">Section 2.3, “Pre-installation / upgrade tasks”</a>
								</p></li><li class="listitem"><p>
									<a class="xref" href="#admin.install.backups" title="2.10. Backups">Section 2.10, “Backups”</a>
								</p></li><li class="listitem"><p>
									Put the site down for maintenance
								</p></li><li class="listitem"><p>
									<a class="xref" href="#admin.install.upgrade" title="2.5. Upgrading">Section 2.5, “Upgrading”</a>
								</p></li><li class="listitem"><p>
									<a class="xref" href="#admin.install.postcommon" title="2.7. Post-installation and upgrade tasks">Section 2.7, “Post-installation and upgrade tasks”</a>
								</p></li><li class="listitem"><p>
									<a class="xref" href="#admin.install.postupgrade" title="2.9. Post-upgrade tasks">Section 2.9, “Post-upgrade tasks”</a>
								</p></li></ol></div>
						 </td></tr></tbody></table></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.requirements"></a>2.2. System Requirements</h2></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.install.requirements.hardware"></a>2.2.1. Server Hardware Requirements</h3></div></div></div><p>
				MantisBT has modest hardware requirements. It requires a computer that is able to run the server software (see <a class="xref" href="#admin.install.requirements.software" title="2.2.2. Server Software Requirements">Section 2.2.2, “Server Software Requirements”</a>).
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Server type
					</p><p>
						The server can be a shared public web server or a dedicated co-located box.
					</p></li><li class="listitem"><p>
						CPU and Memory
					</p><p>
						As for any web application, you should size your server based on the traffic on the site.
					</p></li><li class="listitem"><p>
						Disk
					</p><p>
						The application code is less than 50 MiB.
					</p><p>
						The amount of disk space required for the database will vary depending on the RDBMS and the volume of data, the main driving factor being the expected number and size of attachments.
					</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.install.requirements.software"></a>2.2.2. Server Software Requirements</h3></div></div></div><p>
				All of the required software is free for commercial and non-commercial use (open source). Please refer to the table in <a class="xref" href="#admin.install.requirements.software.versions" title="*******. Versions compatibility table">Section *******, “Versions compatibility table”</a> for minimum and recommended versions.
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Operating System
					</p><p>
						MantisBT runs on Windows, macOS, Linux, Solaris, the BSDs, and just about anything that supports the required server software.
					</p></li><li class="listitem"><p>
						Web Server
					</p><p>
						MantisBT is mainly tested with <a class="ulink" href="https://docs.microsoft.com/en-us/iis" target="_top">Microsoft IIS</a> and <a class="ulink" href="https://www.apache.org/" target="_top">Apache</a>. However, it is expected to work with any recent web server software.
					</p><p>
						File Extensions: MantisBT uses only <span class="emphasis"><em>.php</em></span> files. If your webserver is configured for other extensions (e.g. .PHP3, .PHTML) then you will have to request the administrator to add support for .PHP files. This should be a trivial modification. Further details can be found in the <a class="ulink" href="https://www.php.net/manual/en/install.php" target="_top">PHP documentation</a>
					</p></li><li class="listitem"><p>
						<a class="ulink" href="https://www.php.net/" target="_top">PHP</a>
					</p><p>
						The web server must support PHP. It can be installed as CGI or any other integration technology.
					</p></li><li class="listitem"><p>
						PHP extensions
					</p><p>
						MantisBT is designed to work in as many environments as possible. Hence the required extensions are minimal and many of them are optional affecting only one feature.
					</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">Mandatory extensions</span></dt><dd><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
											The extension for the RDBMS being used ( mysqli with mysqlnd, pgsql, oci8, sqlsrv )
										</p></li><li class="listitem"><p>
											<span class="emphasis"><em>mbstring</em></span> - Required for Unicode (UTF-8) support.
										</p></li><li class="listitem"><p>
											<span class="emphasis"><em> ctype, filter, hash, json, session </em></span> - Required to run MantisBT in general. These are bundled with PHP, and enabled by default. Note that <span class="emphasis"><em>hash</em></span> is a core extension since PHP 7.4.0, and <span class="emphasis"><em>json</em></span> is a core extension since PHP 8.0.0.
										</p></li></ul></div></dd><dt><span class="term">Optional extensions</span></dt><dd><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
											<span class="emphasis"><em>Curl</em></span> - required for the Twitter integration feature
										</p></li><li class="listitem"><p>
											<span class="emphasis"><em>GD</em></span> - required for the captcha feature
										</p></li><li class="listitem"><p>
											<span class="emphasis"><em>Fileinfo</em></span> - required for file attachments and most of the plugins
										</p><p>
											Without this extension, file attachment previews and downloads do not work as MantisBT won't be able to send the Content-Type header to a browser requesting an attachment.
										</p></li><li class="listitem"><p>
											<span class="emphasis"><em>LDAP</em></span> - required for LDAP or Active Directory authentication (see <a class="xref" href="#admin.auth.ldap" title="8.2. LDAP and Microsoft Active Directory">Section 8.2, “LDAP and Microsoft Active Directory”</a>).
										</p></li><li class="listitem"><p>
											<span class="emphasis"><em>SOAP</em></span> - required to use the SOAP API (see <a class="xref" href="#admin.config.api" title="5.38. API">Section 5.38, “API”</a>).
										</p></li><li class="listitem"><p>
											<span class="emphasis"><em>zlib</em></span> - required to enable output compression (see <a class="xref" href="#admin.config.speed" title="5.26. Speed Optimisation">Section 5.26, “Speed Optimisation”</a>).
										</p></li></ul></div></dd></dl></div><div class="note"><h3 class="title">Note</h3><p>
							You can check which PHP modules are installed by running <code class="literal">php -m</code> on the command line, or by using the <code class="literal">php_info()</code> function in a PHP script.
						</p></div></li><li class="listitem"><p>
						Database
					</p><p>
						MantisBT requires a database to store its data. The supported RDBMS are:
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								MySQL (or one of its forks, e.g. MariaDB)
							</p></li><li class="listitem"><p>
								PostgreSQL
							</p></li></ul></div><p>
						Experimental support is also available for
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								Microsoft SQL Server
							</p></li><li class="listitem"><p>
								Oracle
							</p></li></ul></div><p>
						Experimental support means that manual intervention by a skilled Database Administrator may be required to complete the installation, and/or that there may be known issues or limitations when using the software. Please refer to our <a class="ulink" href="https://mantisbt.org/bugs/" target="_top">Issue tracker</a>, filtering on categories <span class="emphasis"><em>db mssql</em></span> and <span class="emphasis"><em>db oracle</em></span> to find out more about those.
					</p><div class="note"><h3 class="title">Note</h3><p>
							Please note that the MantisBT development team mainly works with MySQL, so testing for other drivers is not as extensive as we mainly rely on community contributions to improve support and fix issues with other RDBMS.
						</p><p>
							We therefore recommend MySQL to store your database.
						</p></div></li></ul></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.install.requirements.software.versions"></a>*******. Versions compatibility table</h4></div></div></div><div class="informaltable"><table class="informaltable" border="1"><colgroup><col /><col /><col /><col /><col /></colgroup><thead><tr><th>Category</th><th>Package</th><th>Minimum Version</th><th>Recommended</th><th>Comments</th></tr></thead><tbody><tr><td rowspan="5" valign="middle">RDBMS</td><td>MySQL</td><td>5.5.35</td><td>5.6 or later</td><td>PHP extension: mysqli with MySQL Native driver (mysqlnd) </td></tr><tr><td>MariaDB</td><td>5.5.35</td><td>10.4 or later</td><td>PHP extension: mysqli</td></tr><tr><td>PostgreSQL</td><td>9.2</td><td>11.20 or later</td><td>PHP extension: pgsql</td></tr><tr><td>MS SQL Server</td><td>2012</td><td>2019 or later</td><td>PHP extension: sqlsrv</td></tr><tr><td>Oracle</td><td>11gR2</td><td>19c or later</td><td>PHP extension: oci8</td></tr><tr><td>PHP</td><td>PHP</td><td>7.2.5</td><td>8.0 or later</td><td>See above for PHP extensions</td></tr><tr><td rowspan="4" valign="middle">Web Server</td><td>Apache</td><td>2.2.x</td><td>2.4.x</td><td> </td></tr><tr><td>lighttpd</td><td>1.4.x</td><td>1.4.x</td><td> </td></tr><tr><td>nginx</td><td>1.10.x</td><td>1.16.x or later</td><td> </td></tr><tr><td>IIS</td><td>7.5</td><td>10</td><td>Windows Server 2016 or later</td></tr></tbody></table></div><p>
					Our minimum requirements are generally based on availability of support for the underlying software by their respective vendors. In some cases, we do require a specific version because we rely on a feature that is not available in older releases.
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						Running MantisBT with versions of the software components lower than the minimum requirements listed above is not supported.
					</p></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.install.requirements.client"></a>2.2.3. Client Requirements</h3></div></div></div><p>
				MantisBT should run on all recent browsers in the market, including but not limited to:
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Firefox
					</p></li><li class="listitem"><p>
						Edge
					</p></li><li class="listitem"><p>
						Chrome
					</p></li><li class="listitem"><p>
						Safari
					</p></li><li class="listitem"><p>
						Opera
					</p></li></ul></div><div class="note"><h3 class="title">Note</h3><p>
					Support for <span class="emphasis"><em>Internet Explorer 11</em></span> ended with release 2.22.0.
				</p></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.preinstall"></a>2.3. Pre-installation / upgrade tasks</h2></div></div></div><p>
			These tasks cover the download and deployment of MantisBT, and should be performed prior to any new installation or upgrade.
		</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>
					Download MantisBT (see <a class="xref" href="#admin.about.download" title="1.4. How to get it?">Section 1.4, “How to get it?”</a>)
				</p></li><li class="listitem"><p>
					Transfer the downloaded file to your webserver
				</p><p>
					This can be done using whatever method you like best (ftp, scp, etc). You will need to telnet/ssh into the server machine for the next steps.
				</p></li><li class="listitem"><p>
					Extract the release
				</p><p>
					It is highly recommended to maintain a separate directory for each release. This not only avoids mismatch between versions, (files may have been added or removed) but also provides an easy path to downgrade your installation, should you need to.
				</p><p>
					The usual command is (1 step): 
</p><pre class="programlisting">tar -xzf <span class="emphasis"><em>filename.tar.gz</em></span></pre><p>
					 OR (2 steps): 
</p><pre class="programlisting">
gunzip <span class="emphasis"><em>filename.tar.gz</em></span>
tar -xf <span class="emphasis"><em>filename.tar</em></span></pre><p>
					 Other file archiving tools such as <a class="ulink" href="https://www.7-zip.org/" target="_top">7-Zip</a> should also be able to handle decompression of the archive.
				</p><p>
					The extraction process should create a new directory like <span class="emphasis"><em>mantisbt-1.3.x</em></span>
				</p></li><li class="listitem"><p>
					Rename the directory
				</p><p>
					For new installations, you may want to rename the directory just created to something simpler, e.g. <span class="emphasis"><em>mantisbt</em></span> 
</p><pre class="programlisting">mv mantisbt-1.3.x mantisbt</pre><p>

				</p></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.new"></a>2.4. New Installation</h2></div></div></div><p>
			This chapter explains how to perform a new installation of MantisBT.
		</p><p>
			Start by checking <a class="xref" href="#admin.install.requirements" title="2.2. System Requirements">Section 2.2, “System Requirements”</a> and installing the appropriate version of required software.
		</p><p>
			Once that is done, execute the installation script. From your web browser, access 
</p><pre class="programlisting">https://yoursite/mantisbt/admin/install.php</pre><p>
			 The installation procedure will go through the following steps:
		</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>
					The script checks basic parameters for the web server
				</p></li><li class="listitem"><p>
					Provide required information for the installation
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							database type
						</p></li><li class="listitem"><p>
							database server hostname
						</p></li><li class="listitem"><p>
							user and password
						</p><p>
							Required privileges: SELECT, INSERT, UPDATE, and DELETE
						</p></li><li class="listitem"><p>
							high-privileged database account
						</p><p>
							Additional privileges required: INDEX, CREATE, ALTER, and DROP
						</p><p>
							If this account is not specified, the database user will be used.
						</p></li></ul></div></li><li class="listitem"><p>
					Click the <span class="emphasis"><em>Install/Upgrade Database</em></span> button
				</p></li><li class="listitem"><p>
					The script creates the database and tables.
				</p><p>
					The default Administrator user account is created at this stage, to allow the initial login and setup of MantisBT.
				</p></li><li class="listitem"><p>
					The script attempts to write a basic <code class="filename">config_inc.php</code> file to define the database connection parameters.
				</p><p>
					This operation may fail if the web server's user account does not have write permissions to the directory (which is recommended for obvious security reasons). In this case, you will have to manually create the file and copy/paste the contents from the page.
				</p></li><li class="listitem"><p>
					The script perform post installation checks on the system.
				</p><p>
					Review and correct any errors.
				</p></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.upgrade"></a>2.5. Upgrading</h2></div></div></div><p>
			This chapter explains how to upgrade an existing MantisBT installation.
		</p><p>
			Start by Performing the steps described in <a class="xref" href="#admin.install.preinstall" title="2.3. Pre-installation / upgrade tasks">Section 2.3, “Pre-installation / upgrade tasks”</a> above.
		</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>
					Put the site down for maintenance 
</p><pre class="programlisting">cp mantis_offline.php.sample mantis_offline.php
</pre><p>
					 This will prevent users from using the system while the upgrade is in progress.
				</p></li><li class="listitem"><p>
					Always <span class="emphasis"><em>Backup your code, data and config files</em></span> before upgrading !
				</p><p>
					This includes your Mantis directory, your attachments, and your database. Refer to <a class="xref" href="#admin.install.backups" title="2.10. Backups">Section 2.10, “Backups”</a> for details.
				</p></li><li class="listitem"><p>
					Copy the configuration files
				</p><p>
					To preserve your system settings, you should copy the files listed below to subdirectory <code class="filename">config</code> of the new installation.
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							<code class="filename">config_inc.php</code>,
						</p></li><li class="listitem"><p>
							<code class="filename">custom_strings_inc.php</code>,
						</p></li><li class="listitem"><p>
							<code class="filename">custom_constants_inc.php</code> and
						</p></li><li class="listitem"><p>
							<code class="filename">custom_functions_inc.php</code>.
						</p></li></ul></div><div class="note"><h3 class="title">Note</h3><p>
						The above list is not exhaustive. You might also have to copy other custom files specific to your installation such as logo, favicon, css, etc.
					</p></div></li><li class="listitem"><p>
					Copy third party plugins
				</p><p>
					To maintain system functionality, you should copy any additional plugins in the <code class="filename">plugins</code> subdirectory.
				</p><p>
					For example on Unix, you could use the following command; it will copy all installed plugins (in local subdirectories or symlinked), excluding bundled ones.
				</p><pre class="programlisting">
cd /path/to/mantisbt-OLD/plugins
find -maxdepth 1 ! -path . -type d -o -type l |
    grep -Pv "(Gravatar|MantisCoreFormatting|MantisGraph|XmlImportExport)" |
    xargs -Idirs cp -r dirs /path/to/mantisbt-NEW/plugins
</pre><div class="warning"><h3 class="title">Warning</h3><p>
						Make sure that you <span class="emphasis"><em>do not overwrite any of the bundled plugins</em></span> as per the list below, with an older version.
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								Avatars via Gravatar (<code class="filename">Gravatar</code>)
							</p></li><li class="listitem"><p>
								MantisBT Formatting (<code class="filename">MantisCoreFormatting</code>)
							</p></li><li class="listitem"><p>
								Mantis Graphs (<code class="filename">MantisGraph</code>)
							</p></li><li class="listitem"><p>
								Import/Export issues (<code class="filename">XmlImportExport</code>)
							</p></li></ul></div></div></li><li class="listitem"><p>
					Execute the upgrade script. From your web browser, access 
</p><pre class="programlisting">https://yoursite/mantisbt-NEW/admin/install.php</pre><p>
					 where <span class="emphasis"><em>mantisbt-NEW</em></span> is the name of the directory where the new release was extracted
				</p></li><li class="listitem"><p>
					Provide required information for the upgrade
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							high-privileged database account
						</p><p>
							Additional privileges required: INDEX, CREATE, ALTER, and DROP
						</p><p>
							If this account is not specified, the database user will be used.
						</p></li></ul></div></li><li class="listitem"><p>
					Click the <span class="emphasis"><em>Install/Upgrade Database</em></span> button
				</p></li><li class="listitem"><p>
					At the end of the upgrade, review and correct any warnings or errors.
				</p></li></ol></div><div class="note"><h3 class="title">Upgrading large databases</h3><p>
				When processing large databases from versions older than 1.2, the upgrade script may fail during the conversion of date fields, leaving the system in an inconsistent (i.e. partially updated) state.
			</p><p>
				In this case, you should simply restart the upgrade process, which will resume where it left off. Note that you may have to repeat this several times, until normal completion.
			</p><p>
				Reference: MantisBT issue <a class="ulink" href="https://mantisbt.org/bugs/view.php?id=12735" target="_top">12735</a>.
			</p></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.config"></a>2.6. Configure your installation</h2></div></div></div><p>
			There are many settings that you can adjust to configure and customize MantisBT. Refer to <a class="xref" href="#admin.config" title="Chapter 5. Configuration">Chapter 5, <em>Configuration</em></a>, as well as the <code class="filename">config_defaults_inc.php</code> file for in depth explanations of the available options. Check out also <a class="xref" href="#admin.customize" title="Chapter 7. Customizing MantisBT">Chapter 7, <em>Customizing MantisBT</em></a> for further options to personalize your installation.
		</p><p>
			This step is normally only required for new installations, but when upgrading you may want to review and possibly customize any new configuration options.
		</p><p>
			Open or create the file <code class="filename">config_inc.php</code> in subfolder config in an editor and add or modify any values as required. These will override the default values.
		</p><p>
			You may want to use the provided <code class="filename">config_inc.php.sample</code> file as a starting point.
		</p><div class="warning"><h3 class="title">Warning</h3><p>
				you should never edit the <code class="filename">config_defaults_inc.php</code> file directly, as it could cause issues with future upgrades. Always store your custom configuration in your own <code class="filename">config_inc.php</code> file.
			</p></div><div class="warning"><h3 class="title">Warning</h3><p>
				The MantisBT configuration files (<code class="filename">config_inc.php</code> as well as <code class="filename">custom_strings_inc.php</code>, <code class="filename">custom_constants_inc.php</code>, <code class="filename">custom_functions_inc.php</code>, etc.) should always be saved as <span class="emphasis"><em>UTF-8 without BOM</em></span>. Failure to do so may lead to unexpected display issues.
			</p></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.postcommon"></a>2.7. Post-installation and upgrade tasks</h2></div></div></div><p>
			Instructions in this section are common to both new installations and upgrades, and should be applied after completing either process.
		</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>
					Test your configuration
				</p><p>
					Load up <span class="emphasis"><em>admin/check/index.php</em></span> to validate whether everything is setup correctly, and take corrective action as needed.
				</p></li><li class="listitem"><p>
					Delete the <span class="emphasis"><em>admin</em></span> folder
				</p><p>
					Once you have confirmed that the install or upgrade process was successful, you should delete this directory 
</p><pre class="programlisting">rm -r admin</pre><p>

				</p><p>
					For security reasons, the scripts within this directory should not be freely accessible on a live MantisBT site, particularly one which is accessible via the Internet, as they can allow unauthorized people (e.g. hackers) to gain technical knowledge about the system, as well as perform administrative tasks.
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						Omitting this important step will leave your MantisBT instance exposed to several potentially severe attacks, e.g. <a class="ulink" href="https://mantisbt.org/bugs/view.php?id=23173" target="_top"> issue #23173</a> (if <a class="ulink" href="https://www.php.net/manual/en/mysqli.configuration.php#ini.mysqli.allow-local-infile" target="_top"> mysqli.allow_local_infile</a> is enabled in php.ini).
					</p></div></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.postinstall"></a>2.8. Post-installation tasks</h2></div></div></div><p>
			Instructions in this section should only be applied after a new installation
		</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>
					Login to your bugtracker
				</p><p>
					Use the default Administrator account. The id and password are <span class="emphasis"><em>administrator / root</em></span>.
				</p></li><li class="listitem"><p>
					Create a new Administrator account
				</p><p>
					Go to <span class="emphasis"><em>Manage &gt; Users</em></span> and create a new account with 'administrator' access level.
				</p></li><li class="listitem"><p>
					Disable or delete the default Administrator account
				</p></li><li class="listitem"><p>
					Create a new Project
				</p><p>
					Go to <span class="emphasis"><em>Manage &gt; Projects</em></span> and create a new project
				</p></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.postupgrade"></a>2.9. Post-upgrade tasks</h2></div></div></div><p>
			Instructions in this section should only be applied after upgrading an existing installation.
		</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>
					Test the new release
				</p><p>
					Perform any additional testing as appropriate to ensure the new version does not introduce any regressions.
				</p></li><li class="listitem"><p>
					Switch the site to the new version
				</p><p>
					The commands below should be executed from the web root (or wherever the mantisbt scripts are installed) and assume that the "live" directory (old version) is named <span class="emphasis"><em>mantisbt</em></span> and the new release directory is <span class="emphasis"><em>mantisbt-1.3.x</em></span>. 
</p><pre class="programlisting">
mv mantisbt mantisbt-old
mv mantisbt-1.3.x mantisbt
</pre><p>

				</p></li><li class="listitem"><p>
					Put the site back on line 
</p><pre class="programlisting">rm mantis_offline.php</pre><p>
					 This should be the final step in the upgrade process, as it will let users login again.
				</p></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.backups"></a>2.10. Backups</h2></div></div></div><p>
			It is strongly recommended to backup your MantisBT database on a regular basis. The method to perform this operation depends on which RDBMS you use.
		</p><p>
			Backups are a complex subject, and the specificities of implementing and handling them for each RDBMS are beyond the scope of this document. For your convenience, the section below provides a simple method to backup MySQL databases.
		</p><p>
			You should also consider implementing backups of your MantisBT code (which includes your configs and possibly customization), as well as issue attachments (if stored on disk) and project documents.
		</p><div class="warning"><h3 class="title">Warning</h3><p>
				You should always backup your system (code and database) before upgrading !
			</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.install.backups.mysql"></a>2.10.1. MySQL Backups</h3></div></div></div><p>
				MySQL databases are easy to backup using the <span class="emphasis"><em>mysqldump</em></span> command: 
</p><pre class="programlisting">
mysqldump -u&lt;username&gt; -p&lt;password&gt; &lt;database name&gt; &gt; &lt;output file&gt;
</pre><p>

			</p><p>
				To restore a backup you will need to have a clean database. Then run: 
</p><pre class="programlisting">
mysql -u&lt;username&gt; -p&lt;password&gt; &lt;database name&gt; &lt; &lt;input file&gt;
</pre><p>

			</p><p>
				You can also perform both of these tasks using <a class="ulink" href="https://www.phpmyadmin.net/" target="_top">phpMyAdmin</a>
			</p><p>
				A good idea is to make a backup script and run it regularly through cron or a task scheduler. Using the current date in the filename can prevent overwriting and make cataloguing easier.
			</p><p>
				References and useful links: 
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							<a class="ulink" href="https://dev.mysql.com/doc/refman/8.0/en/mysqldump.html" target="_top"> mysqldump documentation </a>
						</p></li><li class="listitem"><p>
							<a class="ulink" href="https://www.percona.com/software/mysql-database/percona-xtrabackup" target="_top"> Percona XtraBackup </a>
						</p></li><li class="listitem"><p>
							<a class="ulink" href="https://sourceforge.net/projects/automysqlbackup/" target="_top"> AutoMySQLBackup script </a>
						</p></li></ul></div><p>

			</p></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.install.uninstall"></a>2.11. Uninstall</h2></div></div></div><p>
			It is recommended that you make a backup in case you wish to use your data in the future. See <a class="xref" href="#admin.install.backups" title="2.10. Backups">Section 2.10, “Backups”</a> for details.
		</p><p>
			To uninstall MantisBT: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Delete the MantisBT directory and all files and subdirectories.
					</p></li><li class="listitem"><p>
						Drop all MantisBT tables from the database, these can be identified by the configured prefix for the installation. The default prefix is 'mantis'.
					</p></li><li class="listitem"><p>
						Remove any customizations or additions that you may have made.
					</p></li></ul></div><p>
			 If you have the permissions to create/drop databases and you have a specific database for MantisBT that does not contain any other data, you can drop the whole database.
		</p></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.user"></a>Chapter 3. User Management</h1></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.create"></a>3.1. Creating User Accounts</h2></div></div></div><p>
			In MantisBT, there is no limit on the number of user accounts that can be created. Typically, installations with thousands of users tend to have a limited number of users that have access level above REPORTER.
		</p><p>
			By default users with ADMINISTRATOR access level have access to create new user accounts. The steps to do that are: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Click "Manage" on Main Menu.
					</p></li><li class="listitem"><p>
						Click "Users" (if not selected by default).
					</p></li><li class="listitem"><p>
						Click "Create New Account" button just below the alphabet key.
					</p></li><li class="listitem"><p>
						Enter user name, email address, global access level (more details about access levels later). Other fields are optional.
					</p></li><li class="listitem"><p>
						Click "Create Users".
					</p></li></ul></div><p>

		</p><p>
			Creating a user triggers the following actions: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Creating a user in the database.
					</p></li><li class="listitem"><p>
						If email notifications ($g_enable_email_notification) is set to ON, then the user will receive an email allowing them to activate their account and set their password. Otherwise, the account will be created with a blank password.
					</p></li><li class="listitem"><p>
						If email notifications ($g_enable_email_notification) is set to ON, users with access level of $g_notify_new_user_created_threshold_min and above will get a notification that a user account has been created. Information about the user like user name, email address, IP address are included in the email notification.
					</p></li></ul></div><p>
		</p><p>
			When the 'Protected' flag is set on a user account, it indicates that the account is a shared account (e.g. demo account) and hence users logged using such account will not be allowed to change account preferences and profile information.
		</p><p>
			The anonymous user account specified with the $g_anonymous_account option will always be treated as a protected user account. When you are creating the anonymous user account, the 'Protected' flag is essentially ignored because the anonymous user is always treated as a protected user.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.enable"></a>3.2. Enabling/Disabling User Accounts</h2></div></div></div><p>
			The recommended way of retiring user accounts is to disable them. Scenarios where this is useful is when a person leaves the team and it is necessary to retire their account.
		</p><p>
			Once an account is disabled the following will be enforced: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						All currently active sessions for the account will be invalidated (i.e. automatically logged out).
					</p></li><li class="listitem"><p>
						It will no longer be possible login using this account.
					</p></li><li class="listitem"><p>
						No further email notifications will be sent to the account once it is disabled.
					</p></li><li class="listitem"><p>
						The user account will not show anymore in lists like "assign to", "send reminder to", etc.
					</p></li></ul></div><p>
		</p><p>
			The disabling process is totally reversible. Hence, the account can be re-enabled and all the account history will remain intact. For example, the user will still have issues reported by them, assigned to them, monitored by them, etc.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.delete"></a>3.3. Deleting User Accounts</h2></div></div></div><p>
			Another way to retire user accounts is by deleting them. This approach is only recommended for accounts that have not been active (i.e. haven't reported issues). Once the account is deleted, any issues or actions associated with such account, will be associated with user123 (where 123 is the code of the account that was deleted). Note that associated issues or actions are not deleted.
		</p><p>
			As far as the underlying database, after the deletion of a user, records with the user id as a foreign key will have a value that no longer exists in the users table. Hence, any tools that operate directly on the database must take this into consideration.
		</p><p>
			By default administrators are the only users who can delete user accounts. They can delete accounts by clicking Manage, Users, locating the user to be deleted and opening it details page, then clicking on the "Delete User" button which deletes the user.
		</p><p>
			Note that "Deleting Users" is not a reversible process. Hence, if it is required to re-add the user account, it is not possible to recreate the user account so that it gets the same ID and hence retains its history. However, manually creating a record in the users table with the same id, can possibly do that. However, this approach is not recommended or supported.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.signup"></a>3.4. User Signup</h2></div></div></div><p>
			For open source and freeware projects, it is very common to setup MantisBT so that users can signup for an account and get a REPORTER access by default (configurable by the $g_default_new_account_access_level configuration option). The signup process can be enabled / disabled using the $g_allow_signup configuration option, which is enabled by default.
		</p><p>
			If user signup is enabled, then it is required that $g_send_reset_password is ON as well, and the e-mail settings properly configured (see <a class="xref" href="#admin.config.email" title="5.8. Email">Section 5.8, “Email”</a>).
		</p><p>
			If email notifications ($g_enable_email_notification) is set to ON, users with access level of $g_notify_new_user_created_threshold_min and above will get a notification that a user account has been created. Information about the user like user name, email address, IP address are included in the email notification.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.passwordreset"></a>3.5. Forgot Password and Reset Password</h2></div></div></div><p>
			It is pretty common for users to forget their password. MantisBT provides two ways to handle such scenario: "Forgot Password" and "Reset Password".
		</p><p>
			"Forgot Password" is a self service scenario where users go to the login page, figure out they don't remember their password, and then click the "Lost your password?" link. Users are then asked for their user name and email address. If correct, then they are sent an email with a link which allows them to login to MantisBT and change their password.
		</p><p>
			"Reset Password" scenario is where a user reports to the administrator that they are not able to login into MantisBT anymore. This can be due to forgetting their password and possibly user name or email address that they used when signing up. The administrator then goes to Manage, Users, locates the user account and opens its details. Under the user account details, there is a "Reset Password" button which the administrator can click to reset the password and trigger an email to the user to allow them to get into MantisBT and set their password. In the case where email notifications are disabled, resetting password will set the password to an empty string.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.impersonation"></a>3.6. Impersonating a user</h2></div></div></div><p>
			Administrators are able to impersonate users in order to reproduce an issue reported by a user, test their access making sure they can access the expected projects/issues/fields, or to create API tokens for service accounts that are used to grant other systems limited access to MantisBT.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.passwordchange"></a>3.7. Changing Password</h2></div></div></div><p>
			Users are able to change their own passwords (unless their account is "protected"). This can be done by clicking on "My Account", and then typing the new password in the "Password" and "Confirm Password" fields, then clicking "Update User". Changing the password automatically invalidates all logged in sessions and hence the user will be required to re-login. Invalidating existing sessions is very useful in the case where a user going onto a computer, logs into MantisBT and leaves the computer without logging out. By changing the password from another computer, the session on the original computer automatically becomes invalidated.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.pruning"></a>3.8. Pruning User Accounts</h2></div></div></div><p>
			The pruning function allows deleting of user accounts for accounts that have been created more than a week ago, and they never logged in. This is particularly useful for users who signed up with an invalid email or with a typo in their email address address.
		</p><p>
			The account pruning can be done by administrators by going to "Manage", "Users", and clicking the "Prune Accounts" button inside the "Never Logged In" box.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.access"></a>3.9. Authorization and Access Levels</h2></div></div></div><p>
			MantisBT uses access levels to define what a user can do. Each user account has a global or default access level that is associated with it. This access level is used as the access level for such users for all actions associated with public projects as well as actions that are not related to a specific project. Users with global access level less than $g_private_project_threshold will not have access to private projects by default.
		</p><p>
			The default access levels shipped with MantisBT out of the box are VIEWER, REPORTER, UPDATER, DEVELOPER, MANAGER and ADMINISTRATOR. Each features has several configuration options associated with it and identifies the required access level to do certain actions. For example, viewing an issue, reporting an issue, updating an issue, adding a note, etc.
		</p><p>
			For example, in the case of reporting issues, the required access level is configurable using the $g_report_bug_threshold configuration option (which is defaulted to REPORTER). So for a user to be able to report an issue against a public project, the user must have a project-specific or a global access level that is greater than or equal to REPORTER. However, in the case of reporting an issue against a private project, the user must have project specific access level (that is explicitly granted against the project) that is higher than REPORTER or have a global access level that is higher than both $g_private_project_threshold and $g_report_bug_threshold.
		</p><p>
			Note that project specific access levels override the global access levels. For example, a user may have REPORTER as the global access level, but have a MANAGER access level to a specific project. Or a user may have MANAGER as the global access level by VIEWER access to a specific project. Access levels can be overridden for both public and private projects. However, overriding access level is not allowed for users with global access ADMINISTRATOR.
		</p><p>
			Each feature typically has multiple access control configuration options to define what access level can perform the operation. For example, adding a note may require REPORTER access level, updating it note may require DEVELOPER access level, unless the note was added by the same user.
		</p><p>
			Such threshold configuration options can be set to a single access level, which means users with such threshold and above are authorized to perform the action. The other option is to specify an array of access levels which indicates that users with the explicitly specific thresholds are allowed to execute the actions.
		</p><p>
			It is also worth mentioning that the access levels are defined by the $g_access_levels_enum_string configuration option, and it is possible to customize such list. The default value for the available access levels is '10:viewer, 25:reporter, 40:updater, 55:developer, 70:manager, 90:administrator'. The instructions about how to customize the list of access levels will be covered in the customization section.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.autocreate"></a>3.10. Auto Creation of Accounts on Login</h2></div></div></div><p>
			If you are using a global user directory (LDAP, Active Directory), you may want to configure MantisBT so users who already exists in the directory will be automatically authenticated and added to MantisBT.
		</p><p>
			For example, a company may setup their MantisBT installation in a way, where its staff members that are already registered in their LDAP directory, should be allowed to login into MantisBT with the same user name and password. Another option could be if MantisBT is integrated into some content management system, where it is desired to have a single registration and single sign-on experience.
		</p><p>
			In such scenarios, once a user logs in for the first time, a user account is automatically created for them, although the password verification is still done against LDAP or the main users repository.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.prefs"></a>3.11. User Preferences</h2></div></div></div><p>
			Users can fine tune the way MantisBT interacts with them by modifying their user preferences to override the defaults set by the administrator; If the administrator changes a default setting, it will not automatically cascade in the users' preferences once they have been set, so it is the users' responsibility to manage their own preferences.
		</p><p>
			The user preferences include the following:
		</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
					<span class="emphasis"><em>Default Project</em></span>: A user can choose the default project that is selected when the user first logs in. This can be a specific project or "All Projects". For users that only work on one project, it would make sense to set such project as the default project (rather than "All Projects"). The active project is part of the filter applied on the issues listed in the "View Issues" page. Also any newly reported issues will be associated with the active project.
				</p></li><li class="listitem"><p>
					<span class="emphasis"><em>Refresh Delay</em></span>: The refresh delay is used to specify the number of seconds between auto-refreshes of the View Issues page.
				</p></li><li class="listitem"><p>
					<span class="emphasis"><em>Redirect Delay</em></span>: The redirect delay is the number of seconds to wait after displaying flash messages like "Issue created successfully", and before the user gets redirected to the next page.
				</p></li><li class="listitem"><p>
					<span class="emphasis"><em>Notes Sort Order</em></span>: The preference relating to how notes should be ordered when issue is viewed or in email notifications. Ascending order means that older notes are displayed first
				</p></li><li class="listitem"><p>
					<span class="emphasis"><em>Email on XXX</em></span>: If unticked, then the notifications related to the corresponding event would be disabled. User can also specify the minimum issue severity of for the email to be sent.
				</p><p>
					Note that the preference is only used to disable notifications that as per the administrator's configuration, this user would have qualified to receive.
				</p></li><li class="listitem"><p>
					<span class="emphasis"><em>Email Notes Limit</em></span>: This preference can be used to limit the number of issue notes to be included in a email notifications. Specifying N here will cause only the latest N to be included. The value 0 means that all notes will be included.
				</p></li><li class="listitem"><p>
					<span class="emphasis"><em>Language</em></span>: The preferred language of the user. This language is used by the GUI and in email notifications. Note that MantisBT uses UTF-8 for encoding the data, hence the user could for example use MantisBT with a Chinese interface, while logging issue data in German.
				</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.user.profiles"></a>3.12. User Profiles</h2></div></div></div><p>
			A user profile describes an environment that used to run the software for which issues are being tracked.
		</p><p>
			When reporting issues, users can elect to enter information like platform, operating system and version manually, or they can choose from a list of available profiles.
		</p><p>
			Each user has access to all the personal profiles they create, in addition to global ones; Profile data includes "Platform", "Operating System", "OS Version", and "Additional Description".
		</p><p>
			Global profiles are typically used by the administrator to define a set of standard system settings used in their environment, which saves users the trouble of having to define them individually. The access level required to manage global profiles is configured by the $g_manage_global_profile_threshold configuration option and defaults to MANAGER.
		</p></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.lifecycle"></a>Chapter 4. Issue Lifecycle and Workflow</h1></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.lifecycle.create"></a>4.1. Issue Creation</h2></div></div></div><p>
			The life cycle of an issue starts with its creation. An issue can be created via one of the following channels: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						MantisBT Web Interface - This is where a user logs into MantisBT and reports a new issue.
					</p></li><li class="listitem"><p>
						SOAP API - Where an application automatically reports an issue into MantisBT using the SOAP API web services interfaces. For example, the nightly build script can automatically report an issue if the build fails.
					</p></li><li class="listitem"><p>
						Email - This is not supported out of the box, but there are existing MantisBT patches that would listen to emails on pre-configured email addresses and adds them to the MantisBT database.
					</p></li><li class="listitem"><p>
						Others - There can be several other ways to report issues. For example, applications / scripts that directly injects issues into MantisBT database (not recommended, except for one-off migration scripts), or PHP scripts that use the core MantisBT API to create new issues.
					</p></li></ul></div><p>

		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.lifecycle.status"></a>4.2. Issue Statuses</h2></div></div></div><p>
			An important part of issue tracking is to classify issues as per their status. Each team may decide to have a different set of categorization for the status of the issues, and hence, MantisBT provides the ability to customize the list of statuses. MantisBT assumes that an issue can be in one of three stages: opened, resolved and closed. Hence, the customized statuses list will be mapped to these three stages. For example, MantisBT comes out of the box with the following statuses: new, feedback, acknowledged, confirmed, assigned, resolved and closed. In this case "new" -&gt; "assigned" map to opened, "resolved" means resolved and "closed" means closed.
		</p><p>
			Following is the explanation of what the standard statuses that are shipped with MantisBT means. 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						New - This is the landing status for new issues. Issues stay in this status until they are assigned, acknowledged, confirmed or resolved. The next status can be "acknowledged", "confirmed", "assigned" or "resolved".
					</p></li><li class="listitem"><p>
						Acknowledged - This status is used by the development team to reflect their agreement to the suggested feature request. Or to agree with what the reporter is suggesting in an issue report, although they didn't yet attempt to reproduce what the reporter is referring to. The next status is typically "assigned" or "confirmed".
					</p></li><li class="listitem"><p>
						Confirmed - This status is typically used by the development team to mention that they agree with what the reporter is suggesting in the issue and that they have confirmed and reproduced the issue. The next status is typically "assigned".
					</p></li><li class="listitem"><p>
						Assigned - This status is used to reflect that the issue has been assigned to one of the team members and that such team member is actively working on the issue. The next status is typically "resolved".
					</p></li><li class="listitem"><p>
						Resolved - This status is used to reflect that the issue has been resolved. An issue can be resolved with one of many resolutions (customizable). For example, an issue can be resolved as "fixed", "duplicate", "won't fix", "no change required", etc. The next statuses are typically "closed" or in case of the issue being re-opened, then it would be "feedback".
					</p></li><li class="listitem"><p>
						Closed - This status reflects that the issue is completely closed and no further actions are required on it. It also typically hides the issue from the View Issues page. Some teams use "closed" to reflect sign-off by the reporter and others use it to reflect the fact that the fix has been released to customers.
					</p></li></ul></div><p>

		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.lifecycle.workflow"></a>4.3. Workflow</h2></div></div></div><p>
			Now that we have covered how an issue gets created, and what are the different statuses during the life cycle of such issues, the next step is to define the workflow. The workflow dictates the valid transitions between statuses and the user access level required of the user who triggers such transitions; in other words, how issues move from one status to another and who is authorized to trigger such transitions.
		</p><p>
			MantisBT provides the ability for teams to define their own custom workflow which works on top of their custom status (see <a class="xref" href="#admin.customize.status" title="7.5. Customizing Status Values">Section 7.5, “Customizing Status Values”</a>).
		</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.lifecycle.workflow.transitions"></a>4.3.1. Workflow Transitions</h3></div></div></div><p>
				By default, there is no workflow defined, which means that all states are accessible from any other, by anyone.
			</p><p>
				The "Manage &gt; Configuration &gt; Workflow Transitions" page allows users with ADMINISTRATOR access level to do the following tasks: 
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							Define the valid next statuses for each status.
						</p></li><li class="listitem"><p>
							Define the default next status for each status.
						</p></li><li class="listitem"><p>
							Define the minimum access level required for a user to transition to each status.
						</p></li><li class="listitem"><p>
							Define the default status for newly created issues.
						</p></li><li class="listitem"><p>
							Define the status at which the issue is considered resolved. Any issues a status code greater than or equal to the specified status will be considered resolved.
						</p></li><li class="listitem"><p>
							Define the status which is assigned to issues that are re-opened.
						</p></li><li class="listitem"><p>
							Define the required access level to change the workflow.
						</p></li></ul></div><p>

			</p><p>
				Note that the scope of the applied change is dependent on the selected project. If "All Projects" is selected, then the configuration is to be used as the default for all projects, unless overridden by a specific project. To configure for a specific project, switch to it via the combobox at the top right corner of the screen.
			</p><p>
				The Global ("All Projects") workflow can also be defined in the <span class="emphasis"><em>config_inc.php</em></span> file, as per the following example. 
</p><pre class="programlisting">
$g_status_enum_workflow[NEW_]           ='30:acknowledged,20:feedback,40:confirmed,50:assigned,80:resolved';
$g_status_enum_workflow[FEEDBACK]       ='30:acknowledged,40:confirmed,50:assigned,80:resolved';
$g_status_enum_workflow[ACKNOWLEDGED]   ='40:confirmed,20:feedback,50:assigned,80:resolved';
$g_status_enum_workflow[CONFIRMED]      ='50:assigned,20:feedback,30:acknowledged,80:resolved';
$g_status_enum_workflow[ASSIGNED]       ='80:resolved,20:feedback,30:acknowledged,40:confirmed';
$g_status_enum_workflow[RESOLVED]       ='90:closed,20:feedback,50:assigned';
$g_status_enum_workflow[CLOSED]         ='20:feedback,50:assigned';
</pre><p>

			</p><div class="note"><h3 class="title">Note</h3><p>
					The workflow needs to have a path from the statuses greater than or equal to the 'resolved' state back to the 'feedback' state (see <span class="emphasis"><em>$g_bug_resolved_status_threshold</em></span> and <span class="emphasis"><em>$g_bug_feedback_status</em></span> under <a class="xref" href="#admin.config.status" title="5.22. Status Settings">Section 5.22, “Status Settings”</a>), otherwise, the re-open operation won't work.
				</p></div><div class="note"><h3 class="title">Note</h3><p>
					The first item in each list denotes the default value for this status, which will be pre-selected in the Change Status combobox in the View Issues page.
				</p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.lifecycle.workflow.thresholds"></a>4.3.2. Workflow Thresholds</h3></div></div></div><p>
				The "Manage &gt; Configuration &gt; Workflow Thresholds" page allows users with ADMINISTRATOR access level to define the thresholds required to do certain actions. Following is a list of such actions and what they mean:
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Report an issue - The access levels that are allowed to report an issue.
					</p></li><li class="listitem"><p>
						Update an issue - The access levels that are allowed to update the header information of an issue.
					</p></li><li class="listitem"><p>
						Allow issue to be closed on resolved - The access levels that are allow to resolve and close an issue in one step.
					</p></li><li class="listitem"><p>
						Allow reporter to close issue - Indicates if reporters should be allowed to close issues reported by them.
					</p></li><li class="listitem"><p>
						Monitor an issue - The access levels required for a user to be able to monitor an issue. Once a user monitors an issue, the user will be included in all future email notifications relating to changes in the issue.
					</p></li><li class="listitem"><p>
						Handle an issue - The access levels required for a user to be shown in the list of users that can handle an issue.
					</p></li><li class="listitem"><p>
						Assign an issue - The access levels required for a user to be able to change the handler (i.e. assign / unassign) an issue.
					</p></li><li class="listitem"><p>
						Move an issue - The access levels required for a user to be able to move an issue from one project to another. (TODO: are these access levels evaluated against source or destination project?).
					</p></li><li class="listitem"><p>
						Delete an issue - The access levels required for a user to be able to delete an issue.
					</p></li><li class="listitem"><p>
						Reopen an issue - The access levels required for a user to be able to re-open a resolved or closed issue.
					</p></li><li class="listitem"><p>
						Allow Reporter to re-open Issue - Whether the reporter of an issue can re-open a resolved or closed issue, independent of their access level.
					</p></li><li class="listitem"><p>
						Status to which a reopened issue is set - This is the status to which an issue is set after it is re-opened.
					</p></li><li class="listitem"><p>
						Resolution to which a reopen issue is set - The resolution to set on issues that are reopened.
					</p></li><li class="listitem"><p>
						Status where an issue is considered resolved - The status at which an issue is considered resolved.
					</p></li><li class="listitem"><p>
						Status where an issue becomes readonly - Issues with such status and above are considered read-only. Read-only issues can only be modified by users with a configured access level. Read-only applies to the issue header information as well as other issue related information like relationships, attachments, notes, etc.
					</p></li><li class="listitem"><p>
						Update readonly issues - The access levels required for a user to be able to modify a readonly issue.
					</p></li><li class="listitem"><p>
						Update issue status - The access levels required for a user to be able to modify the status of an issue.
					</p></li><li class="listitem"><p>
						View private issues - The access levels for a user to be able to view a private issue.
					</p></li><li class="listitem"><p>
						Set view status (public vs. private) - The access level for a user to be able to set whether an issue is private or public, when reporting the issue. If the user reporting the issues doesn't have the required access, then the issue will be created with the default view state.
					</p></li><li class="listitem"><p>
						Update view status (public vs private) - The access level required for a user to be able to update the view status (i.e. public vs. private).
					</p></li><li class="listitem"><p>
						Show list of users monitoring issue - The access level required for a user to be able to view the list of users monitoring an issue.
					</p></li><li class="listitem"><p>
						Set status on assignment of handler - The access levels required for a user to be able to re-assign an issue when changing its status.
					</p></li><li class="listitem"><p>
						Status to set auto-assigned issues to - The status - This is the status that is set on issues that are auto assigned to users that are associated with the category that the issuer is reported under.
					</p></li><li class="listitem"><p>
						Limit reporter's access to their own issues - When set, reporters are only allow to view issues that they have reported.
					</p></li><li class="listitem"><p>
						Add notes - The access levels required for users to be able to add notes.
					</p></li><li class="listitem"><p>
						Update notes - The access levels required for users to be able to update issue notes.
					</p></li><li class="listitem"><p>
						Allow user to edit their own issue notes - A flag that indicates the ability for users to edit issue notes report by them.
					</p></li><li class="listitem"><p>
						Delete note - The access levels required for a user to delete a note that they may or may not have reported themselves.
					</p></li><li class="listitem"><p>
						View private notes - The access levels required for a user to be able to view private notes associated with an issue that they have access to view.
					</p></li><li class="listitem"><p>
						View Change Log - The access levels required for a user to be able to view the change log.
					</p></li><li class="listitem"><p>
						View Assigned To - The access levels required for a user to be able to know the handler of an issue that they have access to.
					</p></li><li class="listitem"><p>
						View Issue History - The access levels required for a user to be able to view the history of changes of an issue.
					</p></li><li class="listitem"><p>
						Send reminders - The access levels required for a user to be able to send reminders to other users relating to an issue that they have access to.
					</p></li></ul></div></div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.config"></a>Chapter 5. Configuration</h1></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.intro"></a>5.1. Introduction</h2></div></div></div><p>
			MantisBT is highly customizable through the web interface and configuration files. Configuration options can be set globally as well as customized for a specific project or user (except for options listed in <span class="emphasis"><em>$g_global_settings</em></span>, see <a class="xref" href="#admin.config.settings" title="5.5. Configuration Settings">Section 5.5, “Configuration Settings”</a>).
		</p><p>
			Configuration options can be set in <span class="emphasis"><em>config_inc.php</em></span> and in the <span class="emphasis"><em>database</em></span> (using the various manage pages). Values stored in the database take precedence over values defined in <span class="emphasis"><em>config_inc.php</em></span>. The former can also be viewed and updated on the <span class="emphasis"><em>Configuration Report</em></span> page (Manage &gt; Configuration &gt; Configuration Report).
		</p><p>
			To determine which value to use, MantisBT follows the list below, sequentially searching for the specified configuration option until a match is found.
		</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>
					<span class="emphasis"><em>database</em></span>: current user, current project
				</p></li><li class="listitem"><p>
					<span class="emphasis"><em>database</em></span>: current user, all projects
				</p></li><li class="listitem"><p>
					<span class="emphasis"><em>database</em></span>: all users, current project
				</p></li><li class="listitem"><p>
					<span class="emphasis"><em>database</em></span>: all users, all projects
				</p></li><li class="listitem"><p>
					<span class="emphasis"><em>config_inc.php</em></span>
				</p></li><li class="listitem"><p>
					<span class="emphasis"><em>config_defaults_inc.php</em></span>
				</p></li></ol></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.database"></a>5.2. Database</h2></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.database.mandatory"></a>5.2.1. Base Database settings</h3></div></div></div><p>
			These settings are required for the system to work, and are typically set when installing MantisBT. They should be provided to you by your system administrator or your hosting company.
		</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_hostname</span></dt><dd><p>
						Host name or connection string for Database server. The default value is localhost. For MySql, this should be hostname or hostname:port (e.g. localhost:3306).
					</p></dd><dt><span class="term">$g_db_username</span></dt><dd><p>
						User name to use for connecting to the database. The user needs to have read/write access to the MantisBT database. The default user name is "root".
					</p></dd><dt><span class="term">$g_db_password</span></dt><dd><p>
						Password for the specified user name. The default password is empty.
					</p></dd><dt><span class="term">$g_database_name</span></dt><dd><p>
						Name of database that contains MantisBT tables. The default name is 'bugtracker'.
					</p></dd><dt><span class="term">$g_db_type</span></dt><dd><p>
						The supported database types are listed in the table below.
					</p><p>
						The PHP extension corresponding to the selected type must be enabled (see also <a class="xref" href="#admin.install.requirements.software.versions" title="*******. Versions compatibility table">Section *******, “Versions compatibility table”</a>).
					</p><div class="informaltable"><table class="informaltable" border="1"><colgroup><col /><col /><col /><col /></colgroup><thead><tr><th>RDBMS</th><th>db_type (ADOdb)</th><th>PHP extension</th><th>Comments</th></tr></thead><tbody><tr><td>MySQL</td><td>mysqli</td><td>mysqli</td><td>default</td></tr><tr><td>PostgreSQL</td><td>pgsql</td><td>pgsql</td><td> </td></tr><tr><td>MS SQL Server</td><td>mssqlnative</td><td>sqlsrv</td><td> </td></tr><tr><td>Oracle</td><td>oci8</td><td>oci8</td><td> </td></tr></tbody></table></div></dd></dl></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.database.tablenaming"></a>5.2.2. Database table naming settings</h3></div></div></div><p>
			MantisBT allows administrators to configure a prefix and a suffix for its tables. This enables multiple MantisBT installation in the same database or schema.
		</p><div class="warning"><h3 class="title">Warning</h3><p>
				Use of long strings for these configuration options may cause issues on RDBMS restricting the size of its identifiers, such as Oracle (which imposed a maximum size of 30 characters until version 12.1; starting with 12cR2 this <a class="ulink" href="https://docs.oracle.com/en/database/oracle/oracle-database/12.2/sqlrf/Database-Object-Names-and-Qualifiers.html" target="_top"> limit has been increased to 128</a>).
			</p><p>
				To avoid this limitation, it is recommended that
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						the <span class="emphasis"><em>prefix</em></span> is set to blank or kept as short as possible (e.g. <code class="literal">m</code>).
					</p></li><li class="listitem"><p>
						the <span class="emphasis"><em>suffix</em></span> is set to blank.
					</p></li><li class="listitem"><p>
						the <span class="emphasis"><em>plugin prefix</em></span> is kept as short as possible (e.g. <code class="literal">plg</code>).
					</p></li></ul></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_db_table_prefix</span></dt><dd><p>
						Specifies the prefix to be used for all table names. The default value is <code class="literal">mantis</code>.
					</p><p>
						The given string is added with an underscore before the base table name, e.g. for the <code class="literal">bug</code> table, the actual table name with the default prefix would be <code class="literal">mantis_bug</code>.
					</p></dd><dt><span class="term">$g_db_table_suffix</span></dt><dd><p>
						Specifies the suffix to be appended to all table names. The default value is <code class="literal">table</code>.
					</p><p>
						The given string is added with an underscore after the base table name, e.g. for the <code class="literal">bug</code> table, the actual table name with the default suffix would be <code class="literal">bug_table</code>.
					</p></dd><dt><span class="term">$g_db_table_plugin_prefix</span></dt><dd><p>
						Specifies the prefix to be used to differentiate tables belonging to a plugin's schema from MantisBT's own base tables. The default value is <code class="literal">plugin</code>.
					</p><p>
						The given string is inserted with an underscore between the table prefix and the base table name, and the plugin <span class="emphasis"><em>basename</em></span> is added after that, e.g. for a table named <code class="literal">foo</code> in the <code class="literal">Example</code> plugin, with default values for prefixes and suffix the physical table name would be <code class="literal">mantis_plugin_Example_foo_table</code>.
					</p><div class="warning"><h3 class="title">Warning</h3><p>
							It is strongly recommended <span class="emphasis"><em>not to use an empty string</em></span> here, as this could lead to problems, e.g. conflicts if a plugin's basename happens to match one of MantisBT's base tables.
						</p></div></dd><dt><span class="term">$g_dsn</span></dt><dd><p>
						Adodb Data Source Name This is an EXPERIMENTAL field. If the above database settings, do not provide enough flexibility, it is possible to specify a dsn for the database connection. NOTE: the installer does not yet fully support the use of dsn's
					</p></dd></dl></div></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.path"></a>5.3. Path</h2></div></div></div><p>
		These path settings are important for proper linking within MantisBT. In most scenarios the default values should work fine, and you should not need to override them.
	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_path</span></dt><dd><p>
					Full URL to your installation as seen from the web browser.
				</p><p>
					This is what users type into the URL field, e.g. <code class="literal">https://www.example.com/mantisbt/</code>. Requires trailing `/`.
				</p><p>
					If not set, MantisBT will default this to a working URL valid for most installations. However, in some cases (typically when an installation can be accessed by multiple URLs, e.g. internal vs external), it might be necessary to override the default.
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						The default is built based on headers from the HTTP request. This is a potential security risk, as the system will be exposed to <a class="ulink" href="https://owasp.org/www-project-web-security-testing-guide/stable/4-Web_Application_Security_Testing/07-Input_Validation_Testing/17-Testing_for_Host_Header_Injection" target="_top">Host Header injection</a> attacks, so it is strongly recommended to initialize this in config_inc.php.
					</p></div></dd><dt><span class="term">$g_short_path</span></dt><dd><p>
					Short web path without the domain name. This requires the trailing '/'.
				</p></dd><dt><span class="term">$g_absolute_path</span></dt><dd><p>
					This is the absolute file system path to the MantisBT installation, it is defaulted to the directory where config_defaults_inc.php resides. Requires trailing '/' character (eg. '/usr/apache/htdocs/mantisbt/').
				</p></dd><dt><span class="term">$g_core_path</span></dt><dd><p>
					This is the path to the core directory of your installation. The default value is usually OK but it is recommended that you move the 'core' directory out of your webroot. Requires trailing DIRECTORY_SEPARATOR character.
				</p></dd><dt><span class="term">$g_class_path</span></dt><dd><p>
					This is the path to the classes directory which is a sub-directory of core by default. The default value is typically OK. Requires trailing DIRECTORY_SEPARATOR. character.
				</p></dd><dt><span class="term">$g_library_path</span></dt><dd><p>
					This is the path to the library directory of your installation. The default value is usually OK but it is recommended that you move the 'library' directory out of your webroot. Requires trailing DIRECTORY_SEPARATOR character.
				</p></dd><dt><span class="term">$g_vendor_path</span></dt><dd><p>
					Path to vendor folder for 3rd party libraries. Requires trailing DIRECTORY_SEPARATOR character.
				</p></dd><dt><span class="term">$g_language_path</span></dt><dd><p>
					This is the path to the language directory of your installation. The default value is usually OK but it is recommended that you move the 'language' directory out of your webroot. Requires trailing DIRECTORY_SEPARATOR character.
				</p></dd><dt><span class="term">$g_manual_url</span></dt><dd><p>
					This is the url to the MantisBT online manual. Requires trailing '/' character.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.webserver"></a>5.4. Webserver</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_session_save_path</span></dt><dd><p>
					Location where session files are stored. The default is <span class="emphasis"><em>false</em></span>, meaning the session handler's default location will be used.
				</p></dd><dt><span class="term">$g_session_validation</span></dt><dd><p>
					Use Session validation (defaults to <span class="emphasis"><em>ON</em></span>)
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						Disabling this could be a potential security risk !
					</p></div></dd><dt><span class="term">$g_form_security_validation</span></dt><dd><p>
					Form security validation, defaults to <span class="emphasis"><em>ON</em></span>. This protects against <a class="ulink" href="https://en.wikipedia.org/wiki/Cross-site_request_forgery" target="_top"> Cross-Site Request Forgery</a>. Some proxy servers may not correctly work with this option enabled because they cache pages incorrectly.
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						Disabling this option is a security risk, it is strongly recommended to leave it ON
					</p></div></dd><dt><span class="term">$g_custom_headers</span></dt><dd><p>
					An array of custom headers to be sent with each page.
				</p><p>
					For example, to allow your MantisBT installation to be viewed in a frame in IE6 when the frameset is not at the same hostname as the MantisBT install, you need to add a P3P header. You could try something like 
</p><pre class="programlisting">
$g_custom_headers = array( 'P3P: CP="CUR ADM"' );
</pre><p>
					 in your config file, but make sure to check that your policy actually matches with what you are promising. See <a class="ulink" href="http://msdn.microsoft.com/en-us/library/ms537343.aspx" target="_top"> MSDN</a> for more information.
				</p><p>
					Even though it is not recommended, you could also use this setting to disable previously sent headers. For example, assuming you didn't want to benefit from Content Security Policy (CSP), you could set: 
</p><pre class="programlisting">
$g_custom_headers = array( 'Content-Security-Policy:' );
</pre><p>

				</p><div class="warning"><h3 class="title">Warning</h3><p>
						Disabling CSP is a security risk, it is strongly recommended that you leave it as Mantis defines it.
					</p></div></dd><dt><span class="term">$g_logout_redirect_page</span></dt><dd><p>
					Specify where the user should be sent after logging out.
				</p></dd><dt><span class="term">$g_allow_browser_cache</span></dt><dd><p>
					This will allow the browser to cache all pages. The upside will be better performance, but there may be cases where obsolete information is displayed. Note that this will be bypassed (and caching is allowed) for the bug report pages.
				</p></dd><dt><span class="term">$g_allow_file_cache</span></dt><dd><p>
					This will allow the browser to cache downloaded files. Without this set, there may be issues with IE receiving files, and launching support programs.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.settings"></a>5.5. Configuration Settings</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_global_settings</span></dt><dd><p>
					This option contains the list of configuration options that are used to determine if it is allowed for a specific configuration option to be saved to or loaded from the database. Configuration options that are in the list are considered global only and hence are only configurable via the config_inc.php file and defaulted by config_defaults_inc.php file.
				</p></dd><dt><span class="term">$g_public_config_names</span></dt><dd><p>
					This option contains a list of configuration options that can be queried via SOAP API.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.security"></a>5.6. Security and Cryptography</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">Content Security Policy</span></dt><dd><p>
					Amongst other things, MantisBT relies on <a class="ulink" href="https://en.wikipedia.org/wiki/Content_Security_Policy" target="_top"> Content Security Policy</a> (CSP), which is a <a class="ulink" href="https://www.w3.org/TR/CSP/" target="_top"> W3C candidate recommendation</a> improving the system's security against <a class="ulink" href="https://en.wikipedia.org/wiki/Cross-site_scripting" target="_top"> cross-site scripting (XSS)</a> and other, similar types of attacks. It is currently supported in <a class="ulink" href="https://caniuse.com/#feat=contentsecuritypolicy" target="_top"> recent versions of many browsers</a>.
				</p><div class="note"><h3 class="title">Note</h3><p>
						CSP may cause issues in certain situations (e.g. during development), or when using plugins relying on externally hosted resources such as images or scripts.
					</p></div><p>
					MantisBT currently does not provide any mechanism for plugins to notify the Core of 'safe' external domains. Because of that, even though it is not recommended for obvious security reasons, you may wish to disable CSP. You can do so by specifying a <span class="emphasis"><em>Custom Header</em></span> in your <code class="literal">config_inc.php</code> file (see <a class="xref" href="#admin.config.webserver" title="5.4. Webserver">Section 5.4, “Webserver”</a>).
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						Disabling Content Security Policy is a security risk !
					</p></div></dd><dt><span class="term">$g_crypto_master_salt</span></dt><dd><p>
					Master salt value used for cryptographic hashing throughout MantisBT. This value must be kept secret at all costs. You must generate a unique and random salt value for each installation of MantisBT you control. The minimum length of this string must be at least 16 characters.
				</p><p>
					The value you select for this salt should be a long string generated using a secure random number generator. An example for Linux systems is:
				</p><pre class="programlisting">
cat /dev/urandom | head -c 64 | base64
</pre><p>
					Note that the number of bits of entropy per byte of output from /dev/urandom is not 8. If you're particularly paranoid and don't mind waiting a long time, you could use /dev/random to get much closer to 8 bits of entropy per byte. Moving the mouse (if possible) while generating entropy via /dev/random will greatly improve the speed at which /dev/random produces entropy.
				</p><p>
					This setting is blank by default. MantisBT will not operate in this state. Hence you are forced to change the value of this configuration option.
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						This configuration option has a profound impact on the security of your MantisBT installation. Failure to set this configuration option correctly could lead to your MantisBT installation being compromised. Ensure that this value remains secret. Treat it with the same security that you'd treat the password to your MantisDB database.
					</p></div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.signup"></a>5.7. Signup and Lost Password</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_allow_signup</span></dt><dd><p>
					Allow users to signup for their own accounts.
				</p><p>
					If ON (default), then $g_send_reset_password must be ON as well, and mail settings must be correctly configured (see <a class="xref" href="#admin.config.email" title="5.8. Email">Section 5.8, “Email”</a>).
				</p></dd><dt><span class="term">$g_max_failed_login_count</span></dt><dd><p>
					Maximum number of failed login attempts before the user's account is locked. Once locked, it is required to reset the password (lost password). The counter is reset to zero after each successful login.
				</p><p>
					Default is set to 5, in order to prevent brute force attacks attempting to gain access to end users accounts. Set to OFF to disable this feature and allow unlimited failed login attempts.
				</p></dd><dt><span class="term">$g_notify_new_user_created_threshold_min</span></dt><dd><p>
					The minimum global access level required to be notified when a new user registers via the "signup form". To pick specific access levels that are not necessarily at the higher end of access levels, use an array of access levels. Default is ADMINISTRATOR.
				</p></dd><dt><span class="term">$g_send_reset_password</span></dt><dd><p>
					If ON (default), users will be sent their password when their account is created or password reset (this requires mail settings to be correctly configured).
				</p><p>
					If OFF, then the Administrator will have to provide a password when creating new accounts, and the password will be set to blank when reset.
				</p></dd><dt><span class="term">$g_signup_use_captcha</span></dt><dd><p>
					Use captcha image to validate subscription it requires GD library installed.
				</p></dd><dt><span class="term">$g_system_font_folder</span></dt><dd><p>
					Absolute path (with trailing slash!) to folder which contains your TrueType-Font files used for the Relationship Graphs, and the Workflow Graphs.
				</p></dd><dt><span class="term">$g_lost_password_feature</span></dt><dd><p>
					Setting to disable the 'lost your password' feature.
				</p></dd><dt><span class="term">$g_max_lost_password_in_progress_count</span></dt><dd><p>
					Max. simultaneous requests of 'lost password'. When this value is reached, it's no longer possible to request new password reset. Value resets to zero at each successfully login.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.email"></a>5.8. Email</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_webmaster_email</span></dt><dd><p>
					The webmaster's e-mail address. This address is displayed in the bottom of all MantisBT pages. <EMAIL>
				</p></dd><dt><span class="term">$g_from_email</span></dt><dd><p>
					The email address to be used as the source of all emails sent by MantisBT. <EMAIL>
				</p></dd><dt><span class="term">$g_from_name</span></dt><dd><p>
					The sender name of all emails sent by MantisBT. Mantis Bug Tracker
				</p></dd><dt><span class="term">$g_return_path_email</span></dt><dd><p>
					Email address to receive bounced emails.
				</p></dd><dt><span class="term">$g_enable_email_notification</span></dt><dd><p>
					Set to ON to enable email notifications, OFF to disable them. Default is ON. Note that disabling email notifications has no effect on emails generated as part of the user signup process. When set to OFF, the password reset feature is disabled. Additionally, notifications of administrators updating accounts are not sent to users.
				</p></dd><dt><span class="term">$g_email_notifications_verbose</span></dt><dd><p>
					When enabled, the email notifications will include the full issue with a hint about the change type at the top, rather than using dedicated notifications that are focused on what changed. This change can be overridden in the database per user. Default is OFF.
				</p></dd><dt><span class="term">$g_default_notify_flags</span></dt><dd><p>
					Associates a default notification flag with each action, to control who should be notified. The default will be used if the action is not defined in <span class="emphasis"><em>$g_notify_flags</em></span> or if the flag is not included in the specific action definition.
				</p><p>
					The list of actions include: <span class="emphasis"><em>new</em></span>, <span class="emphasis"><em>assigned</em></span>, <span class="emphasis"><em>resolved</em></span>, <span class="emphasis"><em>bugnote</em></span>, <span class="emphasis"><em>reopened</em></span>, <span class="emphasis"><em>closed</em></span>, <span class="emphasis"><em>deleted</em></span>, <span class="emphasis"><em>feedback</em></span>.
				</p><p>
					The default is: 
</p><pre class="programlisting">
$g_default_notify_flags = array(
	'reporter'      =&gt; ON,
	'handler'       =&gt; ON,
	'monitor'       =&gt; ON,
	'bugnotes'      =&gt; ON,
	'category'      =&gt; ON,
	'explicit'      =&gt; ON,
	'threshold_min' =&gt; NOBODY,
	'threshold_max' =&gt; NOBODY
);
</pre><p>
					 <span class="emphasis"><em>threshold_min</em></span> and <span class="emphasis"><em>threshold_max</em></span> are used to send messages to all members of the project whose status is 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								greater than or equal to <span class="emphasis"><em>threshold_min</em></span>, and
							</p></li><li class="listitem"><p>
								less than or equal to <span class="emphasis"><em>threshold_max</em></span>.
							</p></li></ul></div><p>

				</p><p>
					Sending messages to everyone would set <span class="emphasis"><em>threshold_min</em></span> to ANYBODY and <span class="emphasis"><em>threshold_max</em></span> to NOBODY. To send to all DEVELOPERS and above, use DEVELOPER and NOBODY respectively.
				</p></dd><dt><span class="term">$g_notify_flags</span></dt><dd><p>
					Defines the specific notification flags when they are different from the defaults defined in <span class="emphasis"><em>$g_default_notify_flags</em></span>.
				</p><p>
					For example, the following code overrides the default by disabling notifications to bugnote authors and users monitoring the bug when submitting a new bug: 
</p><pre class="programlisting">
$g_notify_flags['new'] = array(
	'bugnotes' =&gt; OFF,
	'monitor' =&gt; OFF,
);
</pre><p>
					 See <a class="xref" href="#admin.customize.email" title="7.4. Email Notifications">Section 7.4, “Email Notifications”</a> for further examples of customizing the notification flags.
				</p><p>
					Available actions include: 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								<span class="emphasis"><em>new</em></span>: a new bug has been added
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>reopened</em></span>: the bug has been reopened
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>deleted</em></span>: a bug has been deleted
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>owner</em></span>: the bug has been assigned a new owner
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>bugnote</em></span>: a bugnote has been added to a bug
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>sponsor</em></span>: the sponsorship for the bug has changed (added, deleted or updated)
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>relation</em></span>: a relationship for the bug has changed (added, deleted or updated)
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>monitor</em></span>: a user is added to the monitor list.
							</p></li></ul></div><p>
					 In addition, an action can match the bug status in <span class="emphasis"><em>$g_status_enum_string</em></span>. Note that spaces in the string are replaced with underscores ('_') when creating the action. Thus, using the defaults, 'feedback' would be a valid action.
				</p></dd><dt><span class="term">$g_email_receive_own</span></dt><dd><p>
					This defines whether users should receive emails for their own actions. This option is defaulted to OFF, hence, users do not receive email notification for their own actions. This can be a source for confusions for users upgrading from MantisBT 0.17.x versions, since in these versions users used to get notified of their own actions.
				</p></dd><dt><span class="term">$g_validate_email</span></dt><dd><p>
					Determines whether email addresses are validated.
				</p><p>
					When ON (default), validation is performed using the pattern given by the <a class="ulink" href="https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address" target="_top"> HTML5 specification for <span class="emphasis"><em>email</em></span> type form input elements</a>. When OFF, validation is disabled.
				</p><div class="note"><h3 class="title">Note</h3><p>
						Regardless of how this option is set, validation is never performed when using LDAP email (i.e. when $g_use_ldap_email = ON, see <a class="xref" href="#admin.config.auth.ldap" title="5.21.2. LDAP authentication method parameters">Section 5.21.2, “LDAP authentication method parameters”</a>), as we assume that it is handled by the directory.
					</p></div></dd><dt><span class="term">$g_check_mx_record</span></dt><dd><p>
					Set to OFF to disable email checking. Default is OFF.
				</p></dd><dt><span class="term">$g_allow_blank_email</span></dt><dd><p>
					If ON, allows the user to omit an email address field. If you allow users to create their own accounts, they must specify an email at that point, no matter what the value of this option is. Otherwise they wouldn't get their passwords.
				</p><p>
					Administrators are able to bypass this check to enable them to create special accounts like anonymous access and other service accounts that don't need notifications.
				</p></dd><dt><span class="term">$g_email_login_enabled</span></dt><dd><p>
					Enable support for logging in by email and password, in addition to username and password. This will only work as long as there is a single user with the specified email address and the email address is not blank. The default value is OFF.
				</p></dd><dt><span class="term">$g_email_ensure_unique</span></dt><dd><p>
					When enabled, the uniqueness of email addresses will be enforced for new users as well as updates to existing ones. Default is ON.
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						When this setting changes from OFF to ON (which will de facto occur when upgrading to MantisBT 1.3.0 or later from an older version), there could be existing user accounts sharing the same email address.
					</p><p>
						It important that such duplicates are identified and fixed, to avoid unexpected and unpredictable behavior when looking up users with their email address, as the system expects them to be unique.
					</p><p>
						To facilitate this task, the <span class="emphasis"><em>Administration Checks</em></span> will detect duplicate email addresses and identify the related user accounts. A warning will also be displayed in the Manage Users page (see <a class="xref" href="#admin.pages.manage.users" title="6.8.1. Users">Section 6.8.1, “Users”</a>) and when editing a user account whose email address is associated with one or more other accounts.
					</p></div></dd><dt><span class="term">$g_limit_email_domains</span></dt><dd><p>
					Only allow and send email to addresses in the given domain(s). This is useful as a security feature and it is also useful in cases like Sourceforge where its servers are limited to only sending emails to SourceForge email addresses in order to avoid spam. $g_limit_email_domains = array( 'users.sourceforge.net', 'sourceforge.net' );
				</p></dd><dt><span class="term">$g_show_user_email_threshold</span></dt><dd><p>
					This specifies the access level that is needed to have user names hyperlinked with mailto: links. The default value is NOBODY, hence, even administrators won't have this feature enabled.
				</p></dd><dt><span class="term">$g_show_user_realname_threshold</span></dt><dd><p>
					This specifies the access level that is needed to see realnames on user view page. The default value is NOBODY, hence, even administrators won't have this feature enabled.
				</p></dd><dt><span class="term">$g_phpMailer_method</span></dt><dd><p>
					Select the method to send mail: 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								<span class="emphasis"><em>PHPMAILER_METHOD_MAIL</em></span> for use of mail() function,
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>PHPMAILER_METHOD_SENDMAIL</em></span> for sendmail (or postfix),
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>PHPMAILER_METHOD_SMTP</em></span> for SMTP,
							</p></li></ul></div><p>
					 Default is PHPMAILER_METHOD_MAIL.
				</p></dd><dt><span class="term">$g_smtp_host</span></dt><dd><p>
					This option specifies the SMTP server to submit messages to. The SMTP server (MTA) then takes on the responsibility of delivering messages to their final destinations.
				</p><p>
					To use the local SMTP (if available) set this to 'localhost', otherwise use the fully qualified domain name of the remote SMTP server.
				</p><p>
					It can be either a single hostname, or multiple semicolon-delimited hostnames. You can specify for each host a port other than the default, using format: <span class="emphasis"><em>hostname:port</em></span> (e.g. "smtp1.example.com:25;smtp2.example.com").
				</p><p>
					Hosts will be tried in the given order.
				</p><div class="note"><h3 class="title">Note</h3><p>
						This is only used with <span class="emphasis"><em>PHPMAILER_METHOD_SMTP</em></span> (see $g_phpmailer_method).
					</p></div><p>
					The default is 'localhost'.
				</p></dd><dt><span class="term">$g_smtp_port</span></dt><dd><p>
					The default SMTP port to use. This can be overridden individually for specific hosts. (see $g_smtp_host).
				</p><p>
					Typical SMTP ports are 25 and 587.
				</p><p>
					The default is 25.
				</p></dd><dt><span class="term">$g_smtp_connection_mode</span></dt><dd><p>
					Allow secure connection to the SMTP server. Valid values are: 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								<span class="emphasis"><em>'' (empty string)</em></span>: No encryption. This is the default.
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>ssl</em></span>
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>tls</em></span>
							</p></li></ul></div><p>

				</p></dd><dt><span class="term">$g_smtp_username</span></dt><dd><p>
					SMTP Server Authentication user
				</p><p>
					Allows the use of SMTP Authentication when using a remote SMTP host.
				</p><div class="note"><h3 class="title">Note</h3><p>
						must be set to '' (empty string) if the SMTP host does not require authentication.
					</p></div><p>
					Default is ''.
				</p></dd><dt><span class="term">$g_smtp_password</span></dt><dd><p>
					This is the password that is used in SMTP Authentication. Not used when $g_smtp_username = ''
				</p><p>
					Default is ''.
				</p></dd><dt><span class="term">$g_email_retry_in_days</span></dt><dd><p>
					Duration (in days) to retry failed emails before deleting them from queue. Default 7 days.
				</p></dd><dt><span class="term">$g_email_send_using_cronjob</span></dt><dd><p>
					Disables sending of emails as soon as an action is performed. Emails are instead queued and must be sent by running scripts/send_emails.php periodically. This script can only be executed from the CLI, not from the web interface, for security reasons.
				</p><p>
					Enabling this option can help with performance problems if large numbers of emails are generated or mail delivery is slow by not delaying page execution when sending emails.
				</p></dd><dt><span class="term">$g_email_separator1</span></dt><dd><p>
					Default is str_pad('', 70, '='); This means 70 equal signs.
				</p></dd><dt><span class="term">$g_email_separator2</span></dt><dd><p>
					Default is str_pad('', 70, '-'); This means 70 minus signs.
				</p></dd><dt><span class="term">$g_email_padding_length</span></dt><dd><p>
					Default is 28.
				</p></dd></dl></div><p>
		MantisBT uses flags and a threshold system to generate emails on events. For each new event, email is sent to: 
		</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
					the reporter, qualified by the notify flag 'reporter' below
				</p></li><li class="listitem"><p>
					the handler (or Assigned to), qualified by the notify flag 'handler' below
				</p></li><li class="listitem"><p>
					anyone monitoring the bug, qualified by the notify flag 'monitor' below
				</p></li><li class="listitem"><p>
					anyone who has ever added a bugnote the bug, qualified by the notify flag 'bugnotes' below
				</p></li><li class="listitem"><p>
					anyone assigned to the project whose access level is greater than or equal to the notify flag 'threshold_min' and less than or equal to the notify flag 'threshold_max' below
				</p></li></ul></div><p>

	</p><p>
		From this list, those recipients who meet the following criteria are eliminated: 
		</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
					the originator of the change, if $g_email_receive_own is OFF
				</p></li><li class="listitem"><p>
					the recipient either no longer exists, or is disabled
				</p></li><li class="listitem"><p>
					the recipient has turned their email_on_&lt;new status&gt; preference OFF
				</p></li><li class="listitem"><p>
					the recipient has no email address entered
				</p></li></ul></div><p>

	</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.email.dkim"></a>5.8.1. DKIM signature</h3></div></div></div><p>
			In order to setup <a class="ulink" href="https://en.wikipedia.org/wiki/DomainKeys_Identified_Mail" target="_top">DomainKeys Identified Mail (DKIM) Signatures</a> (as defined in <a class="ulink" href="https://tools.ietf.org/html/rfc6376" target="_top">RFC 6376</a>), you need to enable the feature (see <span class="emphasis"><em>$g_email_dkim_enable</em></span>), and provide at least:
		</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
					Domain (see <span class="emphasis"><em>$g_email_dkim_domain</em></span>),
				</p></li><li class="listitem"><p>
					Private key or key file path (see <span class="emphasis"><em>$g_email_dkim_private_key_file_path</em></span> and <span class="emphasis"><em>$g_email_dkim_private_key_string</em></span>),
				</p></li><li class="listitem"><p>
					Selector (see <span class="emphasis"><em>$g_email_dkim_selector</em></span>),
				</p></li><li class="listitem"><p>
					Identity (see <span class="emphasis"><em>$g_email_dkim_identity</em></span>).
				</p></li></ul></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_email_dkim_enable</span></dt><dd><p>
						Enables DomainKeys Identified Mail (DKIM).
					</p><p>
						The default is OFF.
					</p></dd><dt><span class="term">$g_email_dkim_domain</span></dt><dd><p>
						Defines the domain for DKIM Signatures.
					</p><p>
						This is typically same as the host part of the $g_from_email. For example <span class="emphasis"><em>example.com</em></span>.
					</p></dd><dt><span class="term">$g_email_dkim_private_key_file_path</span></dt><dd><p>
						Path to the private domain key to be used for DKIM Signatures.
					</p><p>
						If the key is specified in $g_email_dkim_private_key_string this setting will not be used.
					</p></dd><dt><span class="term">$g_email_dkim_private_key_string</span></dt><dd><p>
						Private domain key to be used for DKIM Signatures.
					</p><p>
						This string should contain private key for signing. Leave empty string if you wish to load the key from the file defined with $g_email_dkim_private_key_file_path.
					</p></dd><dt><span class="term">$g_email_dkim_selector</span></dt><dd><p>
						Selector to be used for DKIM Signatures.
					</p><p>
						If your domain is example.com, typically DNS TXT field should have: <span class="emphasis"><em>host: mail.example._domainkey</em></span>, <span class="emphasis"><em>value: v=DKIM1; t=s; n=core; k=rsa; p=[public key]</em></span>. In this case selector should be mail.example
					</p></dd><dt><span class="term">$g_email_dkim_passphrase</span></dt><dd><p>
						Private DKIM domain key password.
					</p><p>
						Leave empty string if your private key does not have password
					</p></dd><dt><span class="term">$g_email_dkim_identity</span></dt><dd><p>
						Identity to be used for DomainKeys Identified Mail (DKIM) Signatures.
					</p><p>
						This is usually the same as <span class="emphasis"><em>$g_from_email</em></span>. For example, <EMAIL>
					</p></dd></dl></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.email.smime"></a>5.8.2. S/MIME signature</h3></div></div></div><p>
			This sections describes the necessary settings to enable <a class="ulink" href="https://en.wikipedia.org/wiki/S/MIME" target="_top">S/MIME</a> signature for outgoing MantisBT e-mails.
		</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_email_smime_enable</span></dt><dd><p>
						Enables S/MIME signature.
					</p><p>
						Defaults to OFF.
					</p></dd><dt><span class="term">$g_email_smime_cert_file</span></dt><dd><p>
						Path to the S/MIME certificate.
					</p><p>
						The file must contain a <a class="ulink" href="https://en.wikipedia.org/wiki/Privacy-Enhanced_Mail" target="_top">PEM-encoded</a> certificate.
					</p></dd><dt><span class="term">$g_email_smime_key_file</span></dt><dd><p>
						Path to the S/MIME private key file.
					</p><p>
						The file must contain a PEM-encoded private key matching the S/MIME certificate.
					</p></dd><dt><span class="term">$g_email_smime_key_password</span></dt><dd><p>
						Password for the S/MIME private key.
					</p><p>
						Leave blank if the private key is not protected by a passphrase.
					</p></dd><dt><span class="term">$g_email_smime_extracerts_file</span></dt><dd><p>
						Optional path to S/MIME extra certificates.
					</p><p>
						The file must contain one (or more) PEM-encoded certificates, which will be included in the signature to help the recipient verify the certificate specified in <span class="emphasis"><em>$g_email_smime_cert_file</em></span> ("CA Chain").
					</p></dd></dl></div><div class="note"><h3 class="title">Note</h3><p>
				MantisBT expects the S/MIME certificates and the private key files to be in <a class="ulink" href="https://en.wikipedia.org/wiki/Privacy-Enhanced_Mail" target="_top">PEM</a> format. If you have a <a class="ulink" href="https://en.wikipedia.org/wiki/PKCS_12" target="_top">PKCS12</a> encrypted certificate (typically with a .pfx or .p12 extension), you may use the following <code class="literal">openssl</code> commands to extract and convert the individual elements:
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Certificate
					</p><pre class="programlisting">
openssl pkcs12 -in cert.pfx -clcerts -nokeys -out cert.crt
</pre></li><li class="listitem"><p>
						Extra certificates ("CA chain")
					</p><pre class="programlisting">
openssl pkcs12 -in cert.pfx -cacerts -nokeys -out ca-chain.crt
</pre></li><li class="listitem"><p>
						Private key (<code class="literal">-passout</code> specifies the private key's password)
					</p><pre class="programlisting">
openssl pkcs12 -in cert.pfx -nocerts -out cert.key -passout pass:
</pre></li></ul></div><p>
				If the input file is protected, openssl will ask for the password; alternatively, you can specify it on the command-line with the <span class="emphasis"><em>-passin</em></span> option, e.g. <code class="literal">-passin pass:PASSWORD</code>
			</p></div></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.version"></a>5.9. Version</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_show_version</span></dt><dd><p>
					Display MantisBT Version number to users in the page footer.
				</p><p>
					This is more of a cosmetic setting and should NOT be considered as a security measure to avoid disclosure of version information to users. Default is OFF.
				</p><div class="note"><h3 class="title">Note</h3><p>
						When the REST API is enabled (see <a class="xref" href="#admin.config.api" title="5.38. API">Section 5.38, “API”</a>), accessing an endpoint will always return the version number in the <code class="literal">X-Mantis-Version</code> header, even if the request fails.
					</p></div></dd><dt><span class="term">$g_version_suffix</span></dt><dd><p>
					String appended to the MantisBT version when displayed to the user. Default is ''.
				</p></dd><dt><span class="term">$g_copyright_statement</span></dt><dd><p>
					Custom copyright and licensing statement shown at the footer of each page.
				</p><p>
					Can contain HTML elements that are valid children of the <code class="literal">&lt;address&gt;</code> element. This string is treated as raw HTML and thus you must use <code class="literal">&amp;amp;</code> instead of <code class="literal">&amp;</code>. Default is ''.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.language"></a>5.10. Language</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_default_language</span></dt><dd><p>
					This is the language used by default in MantisBT. This may be set to 'auto' where MantisBT will try to determine the language from the browser.
				</p></dd><dt><span class="term">$g_language_choices_arr</span></dt><dd><p>
					This is to be set to an array of languages that are available for users to choose from. The default value includes all languages supported by MantisBT. The administrator can limit the languages available for users to choose from by overriding this value. For example, to support English, French and German include the following code: 
</p><pre class="programlisting">
$g_language_choices_arr = array( 'english', 'french', 'german' );
</pre><p>
					 Of course, administrators can also add their own languages by translating the strings and creating their own language files. You are encouraged to share any translation work that you do with the MantisBT team. This will ensure that the newly created language file is maintained with future MantisBT releases.All language files reside in the lang/ folder. They are all named according to the following pattern: strings_&lt;language&gt;.txt.
				</p></dd><dt><span class="term">$g_language_auto_map</span></dt><dd><p>
					Browser language mapping for 'auto' language selection
				</p></dd><dt><span class="term">$g_fallback_language</span></dt><dd><p>
					This is the language used if MantisBT cannot determine the language from the browser. It defaults to 'english'.As of 0.19.0, this may be set to 'auto' where MantisBT will try to determine the language from the browser.
				</p></dd></dl></div><div class="note"><h3 class="title">Note</h3><p>
			If a string does not exist in the active language, the English string is used instead.
		</p></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.display"></a>5.11. Display</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_font_family</span></dt><dd><p>
					Name of the google font family for the browser to use. For all available fonts, see: <a class="ulink" href="https://fonts.google.com/" target="_top"> fonts.google.com </a>.
				</p></dd><dt><span class="term">$g_font_family_choices</span></dt><dd><p>
					Google font family list offered to the user to chose from. Font files are fetched from google servers.
				</p></dd><dt><span class="term">$g_font_family_choices_local</span></dt><dd><p>
					This is a small subset of <span class="emphasis"><em>$g_font_family_choices</em></span> in which font files are part of MantisBT installation.
				</p></dd><dt><span class="term">$g_window_title</span></dt><dd><p>
					This is the browser window title (&lt;TITLE&gt; tag).
				</p></dd><dt><span class="term">$g_search_title</span></dt><dd><p>
					This is used as prefix to describe Browser Search entries, and must be short enough so that when inserted into the 'opensearch_XXX_short' language string, the resulting text is 16 characters or less, to be compliant with the limit for the ShortName element as defined in the <a class="ulink" href="https://github.com/dewitt/opensearch/blob/master/opensearch-1-1-draft-6.md" target="_top"> OpenSearch specification </a>.
				</p><p>
					Defaults to the value of $g_window_title.
				</p></dd><dt><span class="term">$g_admin_checks</span></dt><dd><p>
					Check for admin directory, database upgrades, etc. It defaults to ON.
				</p></dd><dt><span class="term">$g_favicon_image</span></dt><dd><p>
					Path to the favorites icon relative to MantisBT root folder This icon should be of <span class="emphasis"><em>image/x-icon</em></span> MIME type, and its size 16x16 pixels. It is also used to decorate OpenSearch Browser search entries. (default 'images/favicon.ico').
				</p></dd><dt><span class="term">$g_logo_image</span></dt><dd><p>
					Path to the logo image relative to MantisBT root folder (default 'images/mantis_logo.gif').
				</p></dd><dt><span class="term">$g_logo_url</span></dt><dd><p>
					The default URL to be associated with the logo. By default this is set to $g_default_home_page (which defaults to My View page). Clicking on the logo from any page in the bug tracker will navigate to the URL specified in this configuration option.
				</p></dd><dt><span class="term">$g_show_project_menu_bar</span></dt><dd><p>
					This option specifies whether to add menu at the top of the page which includes links to all the projects. The default value is OFF.
				</p></dd><dt><span class="term">$g_show_assigned_names</span></dt><dd><p>
					When a bug is assigned then replace the word "assigned" with the name of the developer in parenthesis. Default is ON.
				</p></dd><dt><span class="term">$g_show_priority_text</span></dt><dd><p>
					Specifies whether to show priority as text (ON) or icon (OFF) in the view all bugs page. Default is OFF (icon).
				</p></dd><dt><span class="term">$g_priority_significant_threshold</span></dt><dd><p>
					Define the priority level at which a bug becomes significant. Significant bugs are displayed with emphasis. Set this value to -1 to disable the feature. The default value is HIGH.
				</p></dd><dt><span class="term">$g_severity_significant_threshold</span></dt><dd><p>
					Define the severity level at which a bug becomes significant. Significant bugs are displayed with emphasis. Set this value to -1 to disable the feature. The default value is MAJOR.
				</p></dd><dt><span class="term">$g_view_issues_page_columns</span></dt><dd><p>
					This configuration option is used to set the columns to be included in the <span class="emphasis"><em>View Issues page</em></span>, and the order in which they will be displayed.
				</p><p>
					This can be overridden using <span class="emphasis"><em>Manage &gt; Manage Configuration &gt; Manage Columns</em></span>; users can also configure their own columns using <span class="emphasis"><em>My Account &gt; Manage Columns</em></span>.
				</p><p>
					The list of all available columns (i.e. the names to choose from) can be retrieved from the above-mentioned pages. In addition to standard column names, that will also include:
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							Custom Fields: the column name will be the Custom Field's name prefixed with <code class="literal">custom_</code>, e.g. <span class="emphasis"><em>xyz</em></span> should be included as <code class="literal">custom_xyz</code>.
						</p></li><li class="listitem"><p>
							Plugin-specific columns (prefixed with the Plugin's basename)
						</p></li></ul></div><p>
					If one of the columns specified here is not accessible to the logged-in user or corresponds to a disabled feature, then it will automatically be removed from the list at runtime. The same configuration may therefore show a different set of columns depending on the logged in user, the currently selected project and enabled features.
				</p><p>
					For example, the <code class="literal">eta</code> column will only be shown if usage of the ETA field is enabled (see $g_enable_eta in <a class="xref" href="#admin.config.fields" title="5.35. Field Visibility">Section 5.35, “Field Visibility”</a>), and the <code class="literal">custom_xyz</code> column will be removed if the <span class="emphasis"><em>xyz</em></span> Custom Field is not available in the current Project.
				</p><p>
					By default the following columns are selected: selection, edit, priority, id, bugnotes_count, attachment_count, category_id, severity, status, last_updated, summary.
				</p></dd><dt><span class="term">$g_print_issues_page_columns</span></dt><dd><p>
					This configuration option is used to set the columns to be included in the <span class="emphasis"><em>Print Issues page</em></span>, and the order in which they will be displayed.
				</p><p>
					See $g_view_issues_page_columns for details.
				</p><p>
					By default the following columns are selected: selection, priority, id, bugnotes_count, attachment_count, category_id, severity, status, last_updated, summary.
				</p></dd><dt><span class="term">$g_csv_columns</span></dt><dd><p>
					This configuration option is used to set the columns to be included in <span class="emphasis"><em>CSV exports</em></span>, and the order in which they will be displayed.
				</p><p>
					See $g_view_issues_page_columns for details.
				</p><p>
					By default the following columns are selected: id, project_id, reporter_id, handler_id, priority, severity, reproducibility, version, build, projection, category_id, date_submitted, eta, os, os_build, platform, view_state, last_updated, summary, status, resolution, fixed_in_version.
				</p></dd><dt><span class="term">$g_excel_columns</span></dt><dd><p>
					This configuration option is used to set the columns to be included in <span class="emphasis"><em>Excel exports</em></span>, and the order in which they will be displayed.
				</p><p>
					See $g_view_issues_page_columns for details.
				</p><p>
					By default the following columns are selected: id, project_id, reporter_id, handler_id, priority, severity, reproducibility, version, build, projection, category_id, date_submitted, eta, os, os_build, platform, view_state, last_updated, summary, status, resolution, fixed_in_version.
				</p></dd><dt><span class="term">$g_show_bug_project_links</span></dt><dd><p>
					Show project links when in All Projects mode. Default is ON.
				</p></dd><dt><span class="term">$g_filter_position</span></dt><dd><p>
					Position of the filter box, can be: POSITION_* (POSITION_TOP, POSITION_BOTTOM, or POSITION_NONE for none). Default is FILTER_POSITION_TOP.
				</p></dd><dt><span class="term">$g_action_button_position</span></dt><dd><p>
					Position of action buttons when viewing issues. Can be: POSITION_TOP, POSITION_BOTTOM, or POSITION_BOTH. Default is POSITION_BOTTOM.
				</p></dd><dt><span class="term">$g_show_product_version</span></dt><dd><p>
					This controls display of the product version in the report, view, update and print issue pages. This flag also applies to other product version related fields like product build, fixed in version, and target version. Valid values are ON, OFF, and AUTO. ON for always displayed, AUTO for displayed when project has versions defined, and OFF for always OFF. The default value is AUTO.
				</p></dd><dt><span class="term">$g_show_version_dates_threshold</span></dt><dd><p>
					The access level threshold at which users will see the date of release for product versions. Dates will be shown next to the product version, target version and fixed in version fields. Set this threshold to NOBODY to disable the feature. Default value is NOBODY.
				</p></dd><dt><span class="term">$g_show_realname</span></dt><dd><p>
					This control will replace the user's userid with their realname. If it is set to ON, and the real name field has been populated, the replacement will occur. It defaults to OFF.
				</p></dd><dt><span class="term">$g_sort_by_last_name</span></dt><dd><p>
					Sorting for names in dropdown lists. If turned on, "Jane Doe" will be sorted with the "D"s. It defaults to OFF.
				</p></dd><dt><span class="term">$g_show_avatar</span></dt><dd><p>
					Show the users' avatar
				</p><p>
					In addition to enabling this configuration option it is necessary to install an avatar plugin like the <a class="ulink" href="https://www.gravatar.com" target="_top">Gravatar</a> plugin which is bundled out of the box.
				</p></dd><dt><span class="term">$g_show_avatar_threshold</span></dt><dd><p>
					The threshold of users for which MantisBT should show the avatar (default DEVELOPER). Note that the threshold is related to the user for whom the avatar is being shown, rather than the user who is currently logged in.
				</p></dd><dt><span class="term">$g_show_changelog_dates</span></dt><dd><p>
					Show release dates on changelog. It defaults to ON.
				</p></dd><dt><span class="term">$g_show_roadmap_dates</span></dt><dd><p>
					Show release dates on roadmap. It defaults to ON.
				</p></dd><dt><span class="term">$g_status_colors</span></dt><dd><p>
					Status color codes, using the Tango color palette.
				</p></dd><dt><span class="term">$g_display_bug_padding</span></dt><dd><p>
					The padding level when displaying bug ids. The bug id will be padded with 0's up to the size given.
				</p></dd><dt><span class="term">$g_display_bugnote_padding</span></dt><dd><p>
					The padding level when displaying bugnote ids. The bugnote id will be padded with 0's up to the size given.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.time"></a>5.12. Time</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_cookie_time_length</span></dt><dd><p>
					Time for long lived cookie to live in seconds. It is also used as the default for permanent logins if $g_allow_permanent_cookie is enabled and selected. Default is 1 year.
				</p></dd><dt><span class="term">$g_allow_permanent_cookie</span></dt><dd><p>
					Allow users to opt for a 'permanent' cookie when logging in. Controls the display of the 'Remember my login in this browser' checkbox on the login page. See $g_cookie_time_length.
				</p></dd><dt><span class="term">$g_wait_time</span></dt><dd><p>
					Time to delay between page redirects (in seconds). Users can override this setting in their user preferences. Default is 2 seconds.
				</p></dd><dt><span class="term">$g_long_process_timeout</span></dt><dd><p>
					This timeout is used by pages which does time consuming operations like upgrading the database. The default value of 0 disables timeout. Note that this timeout is specified in seconds.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.date"></a>5.13. Date</h2></div></div></div><p>
		These variables control how the date is displayed. The default is <a class="ulink" href="https://en.wikipedia.org/wiki/ISO_8601" target="_top">ISO 8601</a> formatting.
	</p><p>
		Please refer to the <a class="ulink" href="https://www.php.net/manual/en/function.date.php#refsect1-function.date-parameters" target="_top"> PHP manual </a> for details on available formatting options.
	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_short_date_format</span></dt><dd><p>
					This format is used in the bug listing pages (eg: View Bugs). Default is <code class="literal">Y-m-d</code>.
				</p></dd><dt><span class="term">$g_normal_date_format</span></dt><dd><p>
					This format is used in the view/update bug pages, bug notes, manage section, and news section. Default is <code class="literal">Y-m-d H:i</code>.
				</p></dd><dt><span class="term">$g_complete_date_format</span></dt><dd><p>
					This format is used on the top of each page (current time) and the emails that are sent out. Default is <code class="literal">Y-m-d H:i T</code>.
				</p></dd><dt><span class="term">$g_datetime_picker_format</span></dt><dd><p>
					This format is used with the datetime picker widget. Default is <code class="literal">Y-MM-DD HH:mm</code>.
				</p><div class="note"><h3 class="title">Note</h3><p>
						The formatting convention for the DateTime picker is different from the one used for the other date settings described above; see <a class="ulink" href="https://momentjs.com/docs/#/displaying/format/" target="_top"> Moment.js documentation </a> for details.
					</p></div><div class="warning"><h3 class="title">Warning</h3><p>
						This format needs to match the one defined in <span class="emphasis"><em>$g_normal_date_format</em></span>. Inconsistencies between these two settings, e.g. using different date ordering (DMY, MDY or YMD) or displaying the month as a number vs a word or abbreviation, may result in unexpected behavior such as an invalid interpretation of the date by the DateTime picker widget, or errors trying to save a modified date.
					</p></div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.timezone"></a>5.14. Time Zone</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_default_timezone</span></dt><dd><p>
					Default timezone to use in MantisBT. This configuration is normally initialized when installing Mantis. It should be set to one of the values specified in the <a class="ulink" href="https://www.php.net/timezones" target="_top"> List of Supported Timezones</a>.
				</p><p>
					If this config is left blank, the timezone will be initialized by calling function <a class="ulink" href="https://www.php.net/date-default-timezone-get" target="_top"> date_default_timezone_get()</a>, which will fall back to <span class="emphasis"><em>UTC</em></span> if unable to determine the timezone.
				</p><p>
					Correct configuration of this variable can be confirmed by running the administration checks. Users can override the default timezone under user their preferences.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.news"></a>5.15. News</h2></div></div></div><p>
		These options are used to control the query that selects the news entries to be displayed.
	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_news_enabled</span></dt><dd><p>
					Indicates whether the news feature should be enabled or disabled. The default is OFF. The news feature is deprecated in favor of being moved to a plugin.
				</p></dd><dt><span class="term">$g_news_limit_method</span></dt><dd><p>
					Limit the news entry that are displayed by number of entries (BY_LIMIT) or by date (BY_DATE). The default is BY_LIMIT.
				</p></dd><dt><span class="term">$g_news_view_limit</span></dt><dd><p>
					The limit for the number of news entries to be displayed. This option is only used if $g_news_limit_method is set to BY_LIMIT.
				</p></dd><dt><span class="term">$g_news_view_limit_days</span></dt><dd><p>
					Specifies the number of dates after which the news are not displayed. This option is only used if $g_news_limit_method is set to BY_DATE.
				</p></dd><dt><span class="term">$g_private_news_threshold</span></dt><dd><p>
					Specifies the access level required to view private news. The default is DEVELOPER.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.defaults"></a>5.16. Default Preferences</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_default_new_account_access_level</span></dt><dd><p>
					This is the default access level users are given when their account is created by email. The default access level is REPORTER. Look in constant_inc.php for other values.
				</p></dd><dt><span class="term">$g_default_project_view_status</span></dt><dd><p>
					The default viewing status for new projects (VS_PUBLIC or VS_PRIVATE). The default is VS_PUBLIC.
				</p></dd><dt><span class="term">$g_default_bug_description</span></dt><dd><p>
					Default value for bug description field used on bug report page. Default is empty description.
				</p></dd><dt><span class="term">$g_default_bug_additional_info</span></dt><dd><p>
					Default value for bug additional info field used on bug report page. Default is empty.
				</p></dd><dt><span class="term">$g_default_bug_steps_to_reproduce</span></dt><dd><p>
					Default value for bug steps to reproduce field used on bug report page. Default is empty.
				</p></dd><dt><span class="term">$g_default_bug_view_status</span></dt><dd><p>
					The default viewing status for the new bug (VS_PUBLIC or VS_PRIVATE). The default is VS_PUBLIC.
				</p></dd><dt><span class="term">$g_default_bugnote_view_status</span></dt><dd><p>
					The default viewing status for the new bugnote (VS_PUBLIC or VS_PRIVATE). The default is VS_PUBLIC.
				</p></dd><dt><span class="term">$g_timeline_view_threshold</span></dt><dd><p>
					Threshold for viewing timeline information. Use NOBODY to turn it off. If the timeline is turned off, the other widgets are displayed in a two column view. The default is VIEWER.
				</p></dd><dt><span class="term">$g_default_reminder_view_status</span></dt><dd><p>
					The default viewing status for the new reminders (VS_PUBLIC or VS_PRIVATE). The default is VS_PUBLIC.
				</p></dd><dt><span class="term">$g_reminder_receive_threshold</span></dt><dd><p>
					The minimum access level for a user to show up in the reminder user picker. Note that this is the access level for the project for which the issue belongs. The default is DEVELOPER.
				</p></dd><dt><span class="term">$g_default_bug_resolution</span></dt><dd><p>
					The resolution for a newly created issue. The default is OPEN. Look in constant_inc.php for other values.
				</p></dd><dt><span class="term">$g_default_bug_severity</span></dt><dd><p>
					The severity for a newly created issue. The default is MINOR. Look in constant_inc.php for other values.
				</p></dd><dt><span class="term">$g_default_bug_priority</span></dt><dd><p>
					The priority for a newly created issue. The default is NORMAL. Look in constant_inc.php for other values.
				</p></dd><dt><span class="term">$g_default_bug_reproducibility</span></dt><dd><p>
					The reproducibility for a newly created issue. The default is REPRODUCIBILITY_HAVENOTTRIED. Look in constant_inc.php for other values.
				</p></dd><dt><span class="term">$g_default_bug_projection</span></dt><dd><p>
					The projection for a newly created issue. The default is PROJECTION_NONE. Look in constant_inc.php for other values.
				</p></dd><dt><span class="term">$g_default_bug_eta</span></dt><dd><p>
					The ETA for a newly created issue. The default is ETA_NONE. Look in constant_inc.php for other values.
				</p></dd><dt><span class="term">$g_default_category_for_moves</span></dt><dd><p>
					Default global category to be used when an issue is moved from a project to another that doesn't have a category with a matching name. The default is 1 which is the "General" category that is created in the default database.
				</p></dd><dt><span class="term">$g_default_limit_view</span></dt><dd><p>
					Number of bugs to show in the View Bugs page. The default value is 50.
				</p></dd><dt><span class="term">$g_default_show_changed</span></dt><dd><p>
					Highlight bugs that have changed during the last N hours. The default value is 6.
				</p></dd><dt><span class="term">$g_hide_status_default</span></dt><dd><p>
					Controls which issues will be displayed in the View Issues page. Default value is CLOSED, implying that all issues at "closed" or higher state will not be shown.
				</p></dd><dt><span class="term">$g_min_refresh_delay</span></dt><dd><p>
					This is the delay between automatic refreshes of the View Issues page in minutes. Make sure refresh delay in user preferences isn't too short. If a users set their preferences to be lower then it is bumped back up to this minimum value. The default value is 10 minutes.
				</p></dd></dl></div><p>
		These settings are used as the default values for preferences for new users. Each user can override these settings through the user preferences form. Default language is set to default site language ($g_default_language).
	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_default_refresh_delay</span></dt><dd><p>
					Default page refresh delay (in minutes). This is for the bug listing pages. Default value is 30 minutes.
				</p></dd><dt><span class="term">$g_default_redirect_delay</span></dt><dd><p>
					Default delay before a user is redirected to a page after being prompted by a message (eg: operational successful). Default value is 2 seconds.
				</p></dd><dt><span class="term">$g_default_bugnote_order</span></dt><dd><p>
					This controls the time order in which bug notes are displayed. It can be either ASC (oldest first, the default) or DESC (newest first).
				</p></dd><dt><span class="term">$g_default_email_on_new, $g_default_email_on_assigned, $g_default_email_on_feedback, $g_default_email_on_resolved, $g_default_email_on_closed</span></dt><dd><p>
					Default user preferences to enable receiving emails when a bug is set to the corresponding status. This option only has an effect if users have the required access level to receive such emails. Default value is ON.
				</p></dd><dt><span class="term">$g_default_email_on_reopened</span></dt><dd><p>
					Default user preferences to enable receiving emails when bugs are re-opened. Default value is ON.
				</p></dd><dt><span class="term">$g_default_email_on_bugnote</span></dt><dd><p>
					Default user preferences to enable receiving emails when bugnotes are added to bugs. Default value is ON.
				</p></dd><dt><span class="term">$g_default_email_on_status</span></dt><dd><p>
					Default user preferences to enable receiving emails when status is changed. Default is OFF.
				</p></dd><dt><span class="term">$g_default_email_on_priority</span></dt><dd><p>
					Default user preferences to enable receiving emails when priority is changed. Default is OFF.
				</p></dd><dt><span class="term">$g_default_email_on_new_minimum_severity, $g_default_email_on_assigned_minimum_severity, $g_default_email_on_feedback_minimum_severity, $g_default_email_on_resolved_minimum_severity, $g_default_email_on_closed_minimum_severity, $g_default_email_on_reopened_minimum_severity, $g_default_email_on_bugnote_minimum_severity</span></dt><dd><p>
					Default user preferences to enable filtering based on issue severity. These correspond to the email_on_&lt;status&gt; settings. Default is 'any'.
				</p></dd><dt><span class="term">$g_default_email_on_bugnote_minimum_severity</span></dt><dd><p>
					Default user preference to enable filtering based on issue severity. These corresponds to the email_on_bugnote setting. Default is 'any'.
				</p></dd><dt><span class="term">$g_default_email_on_status_minimum_severity</span></dt><dd><p>
					Default user preference to enable filtering based on issue severity. These corresponds to the email_on_status settings. Default is 'any'.
				</p></dd><dt><span class="term">$g_default_email_on_priority_minimum_severity</span></dt><dd><p>
					Default user preferences to enable filtering based on issue severity. These corresponds to the email_on_priority settings. Default is 'any'.
				</p></dd><dt><span class="term">$g_default_bug_relationship_clone</span></dt><dd><p>
					Default relationship between a new bug and its parent when cloning it
				</p></dd><dt><span class="term">$g_default_bug_relationship</span></dt><dd><p>
					Default for new bug relationships
				</p></dd><dt><span class="term">$g_show_sticky_issues</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_default_email_on_new</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_default_email_on_assigned</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_default_email_on_feedback</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_default_email_on_resolved</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_default_email_on_closed</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_default_email_on_new_minimum_severity</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_default_email_on_assigned_minimum_severity</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_default_email_on_feedback_minimum_severity</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_default_email_on_resolved_minimum_severity</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_default_email_on_closed_minimum_severity</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_default_email_on_reopened_minimum_severity</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_default_email_bugnote_limit</span></dt><dd><p>
					TODO
				</p></dd></dl></div><p>
		See also: <a class="xref" href="#admin.customize.email" title="7.4. Email Notifications">Section 7.4, “Email Notifications”</a>
	</p></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.summary"></a>5.17. Summary</h2></div></div></div><p>
		These are the settings that are used to configuration options related to the Summary page. This page contains statistics about the bugs in MantisBT.
	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_reporter_summary_limit</span></dt><dd><p>
					Limit how many reporters to show in the summary page. This is useful when there are dozens or hundreds of reporters. The default value is 10.
				</p></dd><dt><span class="term">$g_date_partitions</span></dt><dd><p>
					An array of date lengths to count bugs by (in days) for the summary by date. The default is to count for 1, 2, 3, 7, 30, 60, 90, 180, and 365.
				</p></dd><dt><span class="term">$g_summary_category_include_project</span></dt><dd><p>
					Specifies whether category names should be preceded by project names (eg: [Project] Category) when the summary page is viewed for all projects. This is useful in the case where category names are common across projects. The default is OFF.
				</p></dd><dt><span class="term">$g_view_summary_threshold</span></dt><dd><p>
					Specifies the access level required to view the summary page. Default is MANAGER.
				</p></dd><dt><span class="term">$g_severity_multipliers</span></dt><dd><p>
					An array of multipliers which are used to determine the effectiveness of reporters based on the severity of bugs. Higher multipliers will result in an increase in reporter effectiveness. The default multipliers are: 
</p><pre class="programlisting">
$g_severity_multipliers = array ( FEATURE =&gt; 1,
                                  TRIVIAL =&gt; 2,
                                  TEXT =&gt; 3,
                                  TWEAK =&gt; 2,
                                  MINOR =&gt; 5,
                                  MAJOR =&gt; 8,
                                  CRASH =&gt; 8,
                                  BLOCK =&gt; 10 );
</pre><p>
					 The keys of the array are severity constants from constant_inc.php or from custom_constants_inc.php if you have custom severities defined. The values are integers, typically in the range of 0 to 10. If you would like for a severity to not count towards effectiveness, set the value to 0 for that severity.
				</p></dd><dt><span class="term">$g_resolution_multipliers</span></dt><dd><p>
					An array of multipliers which are used to determine the effectiveness of reporters based on the resolution of bugs. Higher multipliers will result in a decrease in reporter effectiveness. The only resolutions that need to be defined here are those which match or exceed $g_bug_resolution_not_fixed_threshold. The default multipliers are: 
</p><pre class="programlisting">
$g_resolution_multipliers = array( UNABLE_TO_REPRODUCE =&gt; 2,
                                   NOT_FIXABLE =&gt; 1,
                                   DUPLICATE =&gt; 3,
                                   NOT_A_BUG =&gt; 5,
                                   SUSPENDED =&gt; 1,
                                   WONT_FIX =&gt; 1 );
</pre><p>
					 The keys of the array are resolution constants from constant_inc.php or from custom_constants_inc.php if you have custom resolutions defined. Resolutions not included here will be assumed to have a multiplier value of 0. The values are integers, typically in the range of 0 to 10. If you would like for a resolution to not count towards effectiveness, set the value to 0 for that resolution or remove it from the array completely. Note that these resolution multipliers are stacked on top of the severity multipliers. Therefore by default, a user reporting many duplicate bugs at severity level BLOCK will be far worse off than a user reporting many duplicate bugs at severity level FEATURE.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.bugnote"></a>5.18. Bugnote</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_bugnote_order</span></dt><dd><p>
					Order to use for sorting bugnotes by submit date. Possible values include ASC for ascending and DESC for descending order. The default value is ASC.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.uploads"></a>5.19. File Upload</h2></div></div></div><p>
		MantisBT allows users to upload file attachments and associate them with bugs as well as projects. Bug attachments / project documents can be uploaded to the webserver or database. When bugs are uploaded to the webserver they are uploaded to the path that is configured in the project properties. In case of problems getting the file upload feature to work, check the following resources: <a class="ulink" href="https://www.php.net/manual/en/features.file-upload.php" target="_top">PHP Manual </a> .
	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_allow_file_upload</span></dt><dd><p>
					Whether to allow/disallow uploading of attachments. Default value is ON.
				</p></dd><dt><span class="term">$g_file_upload_method</span></dt><dd><p>
					Specify the location for uploading attachments. In case of DISK methods you need to provide the webserver with write access rights to the configured upload path (configured in the project) and temporary upload path (used by PHP).
				</p><p>
					Values: DISK or DATABASE
				</p><p>
					Default: DATABASE
				</p></dd><dt><span class="term">$g_dropzone_enabled</span></dt><dd><p>
					Whether to enable/disable drag and drop zone for uploading of attachments. Default value is ON.
				</p></dd><dt><span class="term">$g_file_upload_max_num</span></dt><dd><p>
					Maximum number of files that can be uploaded simultaneously. Default value is 10.
				</p></dd><dt><span class="term">$g_max_file_size</span></dt><dd><p>
					Maximum file size that can be uploaded. Default value is about 5 MiB. The maximum size is also affected by the PHP options post_max_size (default 8 MiB), upload_max_filesize (default 2 MiB) and memory_limit (default 128 MiB) specified in php.ini.
				</p></dd><dt><span class="term">$g_allowed_files</span></dt><dd><p>
					Authorized file types (whitelist).
				</p><p>
					If $g_allowed_files is filled in, NO other file types will be allowed. If empty, any extensions not specifically excluded by <span class="emphasis"><em>$g_disallowed_files list</em></span> will be authorized ($g_disallowed_files takes precedence over $g_allowed_files). Separate items by commas, e.g. <code class="literal">'bmp,gif,jpg,png,txt,zip'</code>.
				</p></dd><dt><span class="term">$g_disallowed_files</span></dt><dd><p>
					Forbidden file types (blacklist).
				</p><p>
					All file extensions in this list will be unauthorized. Separate items by commas, e.g. <code class="literal">'php,html,java,exe,pl,svg'</code>.
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						<a class="ulink" href="https://en.wikipedia.org/wiki/Scalable_Vector_Graphics" target="_top">SVG files</a> are disabled by default, for security reasons. It is recommended to also disable all extensions that can be executed by your server.
					</p></div></dd><dt><span class="term">$g_preview_attachments_inline_max_size</span></dt><dd><p>
					This limit applies to previewing of image / text attachments. If the attachment size is smaller than the specified value, the attachment is previewed with the issue details. The previewing can be disabled by setting this configuration to 0. The default value is 256 * 1024 (256KB).
				</p></dd><dt><span class="term">$g_preview_text_extensions</span></dt><dd><p>
					An array of file extensions (not including dots) for text files that can be previewed inline.
				</p></dd><dt><span class="term">$g_preview_image_extensions</span></dt><dd><p>
					An array of file extensions (not including dots) for image files that can be previewed inline.
				</p></dd><dt><span class="term">$g_fileinfo_magic_db_file</span></dt><dd><p>
					Specify the filename of the magic database file. This is used by PHP to guess what the MIME type of a file is. Usually it is safe to leave this setting as the default (blank) as PHP is usually able to find this file by itself.
				</p></dd><dt><span class="term">$g_file_download_xsendfile_enabled</span></dt><dd><p>
					Enable support for sending files to users via a more efficient X-Sendfile method. HTTP server software supporting this technique includes Lighttpd, Cherokee, Apache with mod_xsendfile and nginx. You may need to set the proceeding file_download_xsendfile_header_name option to suit the server you are using.
				</p></dd><dt><span class="term">$g_file_download_xsendfile_header_name</span></dt><dd><p>
					The name of the X-Sendfile header to use. Each server tends to implement this functionality in a slightly different way and thus the naming conventions for the header differ between each server. Lighttpd from v1.5, Apache with mod_xsendfile and Cherokee web servers use X-Sendfile. nginx uses X-Accel-Redirect and Lighttpd v1.4 uses X-LIGHTTPD-send-file.
				</p></dd><dt><span class="term">$g_attachments_file_permissions</span></dt><dd><p>
					When using DISK for storing uploaded files, this setting control the access permissions they will have on the web server: with the default value (0400) files will be read-only, and accessible only by the user running the apache process (probably "apache" in Linux and "Administrator" in Windows). For more details on unix style permissions: <a class="ulink" href="http://www.perlfect.com/articles/chmod.shtml" target="_top">http://www.perlfect.com/articles/chmod.shtml</a>
				</p></dd><dt><span class="term">$g_absolute_path_default_upload_folder</span></dt><dd><p>
					Absolute path to the default upload folder. Requires trailing / or \.
				</p></dd><dt><span class="term">$g_preview_max_width</span></dt><dd><p>
					Specifies the maximum width for the auto-preview feature. If no maximum width should be imposed then it should be set to 0.
				</p></dd><dt><span class="term">$g_preview_max_height</span></dt><dd><p>
					Specifies the maximum height for the auto-preview feature. If no maximum height should be imposed then it should be set to 0.
				</p></dd><dt><span class="term">$g_view_attachments_threshold</span></dt><dd><p>
					Access level needed to view bugs attachments. View means to see the file names, sizes, and timestamps of the attachments.
				</p></dd><dt><span class="term">$g_download_attachments_threshold</span></dt><dd><p>
					Access level needed to download bug attachments.
				</p></dd><dt><span class="term">$g_delete_attachments_threshold</span></dt><dd><p>
					Access level needed to delete bug attachments.
				</p></dd><dt><span class="term">$g_allow_view_own_attachments</span></dt><dd><p>
					Allow users to view attachments uploaded by themselves even if their access level is below view_attachments_threshold.
				</p></dd><dt><span class="term">$g_allow_download_own_attachments</span></dt><dd><p>
					Allow users to download attachments uploaded by themselves even if their access level is below download_attachments_threshold.
				</p></dd><dt><span class="term">$g_allow_delete_own_attachments</span></dt><dd><p>
					Allow users to delete attachments uploaded by themselves even if their access level is below delete_attachments_threshold.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.html"></a>5.20. HTML</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_html_make_links</span></dt><dd><p>
					This flag controls whether URLs and email addresses are automatically converted to clickable links. Additionally, for URL links, it determines where they open when clicked (<span class="emphasis"><em>target</em></span> attribute) and their type.
				</p><p>
					The options below can be combined using bitwise operators, though not all possible combinations make sense. The default is <span class="emphasis"><em>LINKS_SAME_WINDOW | LINKS_NOOPENER</em></span>.
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							<span class="emphasis"><em>OFF</em></span> - do not convert URLs or emails
						</p></li><li class="listitem"><p>
							<span class="emphasis"><em>LINKS_SAME_WINDOW</em></span> - convert to links that open in current tab/window. NOTE: for backwards-compatibility, this is equivalent to <span class="emphasis"><em>ON</em></span>.
						</p></li><li class="listitem"><p>
							<span class="emphasis"><em>LINKS_NEW_WINDOW</em></span> - convert to links that open in a new tab/window. Overrides <span class="emphasis"><em>LINKS_SAME_WINDOW</em></span>.
						</p></li><li class="listitem"><p>
							<span class="emphasis"><em>LINKS_NOOPENER</em></span> - Links have the <a class="ulink" href="https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types/noopener" target="_top">noopener</a> type.
						</p></li><li class="listitem"><p>
							<span class="emphasis"><em>LINKS_NOREFERRER</em></span> - Links have the <a class="ulink" href="https://developer.mozilla.org/en-US/docs/Web/HTML/Link_types/noreferrer" target="_top">noreferrer</a> type, i.e. they omit the <span class="emphasis"><em>Referer</em></span> header. Implies <span class="emphasis"><em>LINKS_NOOPENER</em></span>.
						</p></li></ul></div></dd><dt><span class="term">$g_html_valid_tags</span></dt><dd><p>
					This is the list of HTML tags that are allowed.Do NOT include href or img tags here.Do NOT include tags that have parameters (eg. )The HTML code is allowed to enter the database as is. The $g_allow_href_tags does not have to be enabled to make URL links. The package will automatically hyperlink properly formatted URLs eg. https://blah.blah/ or mailto://<EMAIL>/
				</p></dd><dt><span class="term">$g_bottom_include_page</span></dt><dd><p>
					Specifies a file to be included at the bottom of each page. It can be used e.g. for company branding, to include Google Analytics script, etc.
				</p></dd><dt><span class="term">$g_top_include_page</span></dt><dd><p>
					Specifies a file to be included at the top of each page. It can be used e.g. for company branding.
				</p><p>
					If a file is supplied, the logo specified by <code class="literal">$g_logo_image</code> (see <a class="xref" href="#admin.config.display" title="5.11. Display">Section 5.11, “Display”</a>) will not be shown, and the include file will have to handle display of the logo. To do so you can use the <code class="literal">html_print_logo()</code> API function, which will display the logo with an URL link if one has been specified in <code class="literal">$g_logo_url</code>
				</p><p>
					Example top include PHP file with logo and centered page title: 
</p><pre class="programlisting">
&lt;div id="banner" style="display: flex; align-items: center;"&gt;
	&lt;div style="width: 10%;"&gt;
		&lt;?php html_print_logo(); ?&gt;
	&lt;/div&gt;

	&lt;div class="center"&gt;
		&lt;span class="pagetitle"&gt;
			&lt;?php global $g_window_title; echo $g_window_title; ?&gt;
		&lt;/span&gt;
	&lt;/div&gt;

	&lt;div style="width: 10%;"&gt;
	&lt;/div&gt;
&lt;/div&gt;
</pre><p>

				</p></dd><dt><span class="term">$g_css_include_file</span></dt><dd><p>
					Set this to point to the CSS file of your choice.
				</p></dd><dt><span class="term">$g_css_rtl_include_file</span></dt><dd><p>
					Set this to point to the RTL CSS file of your choice.
				</p></dd><dt><span class="term">$g_cdn_enabled</span></dt><dd><p>
					A flag that indicates whether to use CDN (content delivery networks) for loading javascript libraries and their associated CSS. This improves performance for loading MantisBT pages. This can be disabled if it is desired that MantisBT doesn't reach out outside corporate network. Default OFF.
				</p></dd><dt><span class="term">$g_main_menu_custom_options</span></dt><dd><p>
					This option will add custom options to the main menu. It is an array of arrays listing the caption, access level required, and the link to be executed. For example: 
</p><pre class="programlisting">
$g_main_menu_custom_options = array(
    array( 
        'title'        =&gt; 'My Link',
        'access_level' =&gt; MANAGER,
        'url'          =&gt; 'my_link.php',
        'icon'         =&gt; 'fa-plug'
    ),
    array( 
        'title'        =&gt; 'My Link2',
        'access_level' =&gt; ADMINISTRATOR,
        'url'          =&gt; 'my_link2.php',
        'icon'         =&gt; 'fa-plug'
    )
);
</pre><p>
					 Note that if the caption is found in <code class="filename">custom_strings_inc.php</code> (see <a class="xref" href="#admin.customize.strings" title="7.1. Strings / Translations">Section 7.1, “Strings / Translations”</a>), it will be replaced by the corresponding translated string. Options will only be added to the menu if the current logged in user has the appropriate access level.
				</p><p>
					Use icons from <a class="ulink" href="https://fontawesome.io/icons/" target="_top">Font Awesome</a>. Add "fa-" prefix to icon name.
				</p><p>
					Access level is an optional field, and no check will be done if it is not set. Icon is an optional field, and 'fa-plug' will be used if it is not set.
				</p></dd><dt><span class="term">$g_html_valid_tags_single_line</span></dt><dd><p>
					These are the valid html tags for single line fields (e.g. issue summary). do NOT include a or img tags here. do NOT include tags that require attributes.
				</p></dd><dt><span class="term">$g_max_dropdown_length</span></dt><dd><p>
					Maximum length of the description in a dropdown menu (for search) set to 0 to disable truncations
				</p></dd><dt><span class="term">$g_wrap_in_preformatted_text</span></dt><dd><p>
					This flag controls whether pre-formatted text (delimited by HTML pre tags is wrapped to a maximum linelength (defaults to 100 chars in strings_api). If turned off, the display may be wide when viewing the text.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.auth"></a>5.21. Authentication</h2></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.auth.global"></a>5.21.1. Global authentication parameters</h3></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_login_method</span></dt><dd><p>
						Specifies which method will be used to authenticate. It should be one of the following values (defaults to <span class="emphasis"><em>MD5</em></span>): 
						</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
									MD5 - user's password is stored as a hash in the database
								</p></li><li class="listitem"><p>
									LDAP - authenticates against an LDAP (or Active Directory) server
								</p></li><li class="listitem"><p>
									BASIC_AUTH
								</p></li><li class="listitem"><p>
									HTTP_AUTH
								</p></li></ul></div><p>
						 In addition, the following deprecated values are supported for backwards-compatibility, and should no longer be used: 
						</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
									PLAIN - password is stored in plain, unencrypted text in the database
								</p></li><li class="listitem"><p>
									CRYPT
								</p></li><li class="listitem"><p>
									CRYPT_FULL_SALT
								</p></li></ul></div><p>
					</p><p>
						Note: you may not be able to easily switch encryption methods, so this should be carefully chosen at install time. However, MantisBT will attempt to "fall back" to older methods if possible.
					</p></dd><dt><span class="term">$g_reauthentication</span></dt><dd><p>
						Determines whether MantisBT will require the user to re-authenticate before granting access to the Admin areas after timeout expiration. Defaults to <span class="emphasis"><em>ON</em></span>
					</p></dd><dt><span class="term">$g_reauthentication_expiry</span></dt><dd><p>
						Duration of the reauthentication timeout, in seconds. Defaults to 5 minutes.
					</p></dd></dl></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.auth.ldap"></a>5.21.2. LDAP authentication method parameters</h3></div></div></div><p>
			The parameters below are only used if $g_login_method (see <a class="xref" href="#admin.config.auth.global" title="5.21.1. Global authentication parameters">Section 5.21.1, “Global authentication parameters”</a> above) is set to <code class="literal">LDAP</code>.
		</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_ldap_server</span></dt><dd><p>
						Specifies the LDAP or Active Directory server to connect to.
					</p><p>
						This must be a full LDAP URI (<code class="literal">protocol://hostname:port</code>)
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								<span class="emphasis"><em>Protocol</em></span> must be either: 
								</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
											<code class="literal">ldap</code> - unencrypted or opportunistic TLS (<a class="ulink" href="https://en.wikipedia.org/wiki/StartTLS" target="_top">STARTTLS</a>)
										</p></li><li class="listitem"><p>
											<code class="literal">ldaps</code> - TLS encryption
										</p></li></ul></div><p>

							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>Port</em></span> number is optional, and defaults to <code class="literal">389</code>.
							</p><p>
								If this doesn't work, try using one of the following standard port numbers: <code class="literal">636</code> (ldaps); for Active Directory Global Catalog forest-wide search, use <code class="literal">3268</code> (ldap) or <code class="literal">3269</code> (ldaps).
							</p></li></ul></div><p>
						Examples of valid URI:
					</p><pre class="programlisting">
ldap://ldap.example.com/
ldaps://ldap.example.com:3269/
</pre><div class="note"><h3 class="title">Note</h3><p>
							Multiple servers can be specified as a space-separated list.
						</p></div></dd><dt><span class="term">$g_ldap_use_starttls</span></dt><dd><p>
						Determines whether the connection will attempt an opportunistic upgrade to a TLS connection (STARTTLS).
					</p><p>
						Defaults to <code class="literal">ON</code>.
					</p><div class="warning"><h3 class="title">Warning</h3><p>
							For security, a failure aborts the entire connection, so make sure your server supports StartTLS if this setting is ON, and use the <code class="literal">ldap://</code> scheme (not <code class="literal">ldaps://</code>).
						</p></div></dd><dt><span class="term">$g_ldap_tls_protocol_min</span></dt><dd><p>
						An integer indicating the minimum version of the TLS protocol to allow. This maps to the <a class="ulink" href="https://www.php.net/manual/en/ldap.constants.php" target="_top"> LDAP_OPT_X_TLS_PROTOCOL_MIN</a> LDAP library option.
					</p><p>
						For example, <code class="literal">LDAP_OPT_X_TLS_PROTOCOL_TLS1_2</code>.
					</p><p>
						Defaults to <code class="literal">OFF</code> (protocol version not set).
					</p><div class="note"><h3 class="title">Note</h3><p>
							Requires PHP 7.1 or later.
						</p></div><div class="warning"><h3 class="title">Warning</h3><p>
							For security, a failure aborts the entire connection.
						</p></div></dd><dt><span class="term">$g_ldap_root_dn</span></dt><dd><p>
						The root distinguished name for LDAP searches. For example, <code class="literal">dc=example, dc=com</code>.
					</p></dd><dt><span class="term">$g_ldap_organization</span></dt><dd><p>
						LDAP search filter for the organization. For example, <code class="literal">(organizationname=*Traffic)</code>. Defaults to <code class="literal">''</code> (empty string).
					</p></dd><dt><span class="term">$g_ldap_protocol_version</span></dt><dd><p>
						The LDAP Protocol Version to use (2, 3 or 0). This maps to the LDAP_OPT_PROTOCOL_VERSION ldap library option.
					</p><p>
						Defaults to <code class="literal">3</code>.
					</p><div class="note"><h3 class="title">Note</h3><p>
							If <code class="literal">0</code>, then the protocol version is not set, and you get whatever default the underlying LDAP library uses.
						</p><p>
							In almost all cases you should use <code class="literal">3</code>. LDAPv3 was introduced back in 1997, and LDAPv2 was deprecated in 2003 by RFC3494.
						</p></div></dd><dt><span class="term">$g_ldap_network_timeout</span></dt><dd><p>
						Duration of the timeout for TCP connection to the LDAP server (in seconds). This maps to LDAP_OPT_NETWORK_TIMEOUT ldap library option. Defaults to <code class="literal">0</code> (infinite).
					</p><p>
						Set this to a low value when the hostname defined in $g_ldap_server resolves to multiple IP addresses, allowing rapid failover to the next available LDAP server.
					</p></dd><dt><span class="term">$g_ldap_follow_referrals</span></dt><dd><p>
						Determines whether the LDAP library automatically follows referrals returned by LDAP servers or not. This maps to LDAP_OPT_REFERRALS ldap library option. Defaults to <code class="literal">ON</code>.
					</p><p>
						For Active Directory, this should be set to <code class="literal">OFF</code>. If you have only one LDAP server, setting to this to OFF is advisable to prevent any man-in-the-middle attacks.
					</p></dd><dt><span class="term">$g_ldap_bind_dn</span></dt><dd><p>
						The distinguished name of the service account to use for binding to the LDAP server. For example, <code class="literal">cn=ldap,ou=Administrators,dc=example,dc=com</code>. Leave empty for anonymous binding.
					</p></dd><dt><span class="term">$g_ldap_bind_passwd</span></dt><dd><p>
						The password for the service account used to establish the connection to the LDAP server. For anonymous binding, leave empty.
					</p></dd><dt><span class="term">$g_ldap_uid_field</span></dt><dd><p>
						The LDAP field for username. Defaults to <code class="literal">uid</code>.
					</p><p>
						For Active Directory, set to <code class="literal">sAMAccountName</code>.
					</p></dd><dt><span class="term">$g_ldap_email_field</span></dt><dd><p>
						The LDAP field for e-mail address. Defaults to <code class="literal">mail</code>.
					</p></dd><dt><span class="term">$g_ldap_realname_field</span></dt><dd><p>
						The LDAP field for the user's real name (i.e. common name). Defaults to <code class="literal">cn</code>.
					</p></dd><dt><span class="term">$g_use_ldap_realname</span></dt><dd><p>
						Use the realname specified in LDAP (ON) rather than the one stored in the database (OFF). Defaults to <code class="literal">OFF</code>.
					</p><div class="note"><h3 class="title">Note</h3><p>
							MantisBT will update the database with the data retrieved from LDAP when ON.
						</p></div></dd><dt><span class="term">$g_use_ldap_email</span></dt><dd><p>
						Use the email address specified in LDAP (ON) rather than the one stored in the database (OFF). Defaults to <code class="literal">OFF</code>.
					</p><div class="note"><h3 class="title">Note</h3><p>
							MantisBT will update the database with the data retrieved from LDAP when ON.
						</p></div></dd><dt><span class="term">$g_ldap_simulation_file_path</span></dt><dd><p>
						This configuration option allows replacing the ldap server with a comma-delimited text file for development or testing purposes.
					</p><p>
						The LDAP simulation file format is as follows:
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								No headers
							</p></li><li class="listitem"><p>
								One line per user
							</p></li><li class="listitem"><p>
								Each line has 4 comma-delimited fields
							</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
										username
									</p></li><li class="listitem"><p>
										realname
									</p></li><li class="listitem"><p>
										e-mail
									</p></li><li class="listitem"><p>
										password
									</p></li></ul></div></li><li class="listitem"><p>
								Any extra fields are ignored
							</p></li></ul></div><div class="warning"><h3 class="title">Warning</h3><p>
							On production systems, this option should be set to <code class="literal">''</code> (This is the default).
						</p></div></dd></dl></div></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.status"></a>5.22. Status Settings</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_bug_submit_status</span></dt><dd><p>
					Status to assign to the bug when submitted. Default value is NEW_.
				</p></dd><dt><span class="term">$g_bug_assigned_status</span></dt><dd><p>
					Status to assign to the bug when assigned. Default value is ASSIGNED.
				</p></dd><dt><span class="term">$g_bug_reopen_status</span></dt><dd><p>
					Status to assign to the bug when reopened. Default value is FEEDBACK.
				</p></dd><dt><span class="term">$g_bug_feedback_status</span></dt><dd><p>
					Status to assign to the bug when feedback is required from the issue reporter. Once the reporter adds a note the status moves back from feedback to $g_bug_assigned_status or $g_bug_submit_status based on whether the bug assigned or not.
				</p></dd><dt><span class="term">$g_reassign_on_feedback</span></dt><dd><p>
					When a note is added to a bug currently in $g_bug_feedback_status, and the note author is the bug's reporter, this option will automatically set the bug status to $g_bug_submit_status or $g_bug_assigned_status if the bug is assigned to a developer. Default value is ON.
				</p></dd><dt><span class="term">$g_bug_duplicate_resolution</span></dt><dd><p>
					Default resolution to assign to a bug when it is resolved as being a duplicate of another issue. Default value is DUPLICATE.
				</p></dd><dt><span class="term">$g_bug_reopen_resolution</span></dt><dd><p>
					Resolution to assign to the bug when reopened. Default value is REOPENED.
				</p></dd><dt><span class="term">$g_auto_set_status_to_assigned</span></dt><dd><p>
					Automatically set status to $g_bug_assigned_status whenever a bug is assigned to a person. Installations where assigned status is to be used when the defect is in progress, rather than just put in a person's queue should set it to OFF. Default is ON. For the status change to be effective, these conditions must be met: 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								Bug has no handler, and a new handler is selected
							</p></li><li class="listitem"><p>
								The assignment is not part of a explicit status change
							</p></li><li class="listitem"><p>
								Current bug status is lower than defined "assigned" status
							</p></li><li class="listitem"><p>
								"Assigned" status is reachable by workflow configuration
							</p></li></ul></div><p>
					 If the conditions are not met, the assignment is still made, but status will not be modified.
				</p></dd><dt><span class="term">$g_bug_resolved_status_threshold</span></dt><dd><p>
					Bug is resolved, ready to be closed or reopened. In some custom installations a bug maybe considered as resolved when it is moved to a custom (FIXED OR TESTED) status.
				</p></dd><dt><span class="term">$g_bug_resolution_fixed_threshold</span></dt><dd><p>
					Threshold resolution which denotes that a bug has been resolved and successfully fixed by developers. Resolutions above and including this threshold and below $g_bug_resolution_not_fixed_threshold are considered to be resolved successfully. Default value is FIXED.
				</p></dd><dt><span class="term">$g_bug_resolution_not_fixed_threshold</span></dt><dd><p>
					Threshold resolution which denotes that a bug has been resolved without being successfully fixed by developers. Resolutions above this threshold are considered to be resolved in an unsuccessful way. Default value is UNABLE_TO_REPRODUCE.
				</p></dd><dt><span class="term"> $g_bug_readonly_status_threshold $g_update_readonly_bug_threshold </span></dt><dd><p>
					Bug becomes readonly if its status is &gt;= $g_bug_readonly_status_threshold. The bug becomes read/write again if re-opened and its status becomes less than this threshold. The default is RESOLVED. Once the bug becomes readonly, a user with an access level greater than or equal to $g_update_readonly_bug_threshold can still edit the bug.
				</p></dd><dt><span class="term">$g_status_enum_workflow</span></dt><dd><p>
					'status_enum_workflow' defines the workflow, and reflects a simple 2-dimensional matrix. For each existing status, you define which statuses you can go to from that status, e.g. from NEW_ you might list statuses '10:new,20:feedback,30:acknowledged' but not higher ones.The default is no workflow, where all states are accessible from any others.
				</p></dd><dt><span class="term">$g_report_bug_threshold</span></dt><dd><p>
					This is the access level required to open a bug. The default is REPORTER.
				</p></dd><dt><span class="term">$g_update_bug_threshold</span></dt><dd><p>
					This is the access level generally required to update the content of a bug. The default is UPDATER.
				</p></dd><dt><span class="term">$g_handle_bug_threshold</span></dt><dd><p>
					This is the access level generally required to be access level needed to be listed in the assign to field. The default is DEVELOPER. If a more restrictive setting can be determined from $g_set_status_threshold, it will be used.
				</p></dd><dt><span class="term">$g_update_bug_status_threshold $g_set_status_threshold </span></dt><dd><p>
					These settings control the access level required to promote a bug to a new status once the bug is opened.$g_set_status_threshold is an array indexed by the status value that allows a distinct setting for each status. It defaults to blank.If the appropriate status is not defined above, $g_update_bug_status_threshold is used instead. The default is DEVELOPER.
				</p></dd><dt><span class="term">$g_bugnote_user_edit_threshold</span></dt><dd><p>
					Threshold at which a user can edit his/her own bugnotes. The default value is equal to the configuration setting $g_update_bugnote_threshold.
				</p></dd><dt><span class="term">$g_bugnote_user_delete_threshold</span></dt><dd><p>
					Threshold at which a user can delete his/her own bugnotes. The default value is equal to the configuration setting $g_delete_bugnote_threshold.
				</p></dd><dt><span class="term">$g_bugnote_user_change_view_state_threshold</span></dt><dd><p>
					Threshold at which a user can change the view status of his/her own bugnotes. The default value is equal to the configuration setting $g_change_view_status_threshold.
				</p></dd><dt><span class="term">$g_allow_reporter_reopen</span></dt><dd><p>
					If set, the bug reporter is allowed to reopen their own bugs once resolved, regardless of their access level. This allows the reporter to disagree with the resolution. The default is ON.
				</p></dd><dt><span class="term">$g_allow_parent_of_unresolved_to_close</span></dt><dd><p>
					If set, no check is performed on the status of a bug's children, which allows the parent to be closed whether or not the children have been resolved. The default is OFF.
				</p></dd><dt><span class="term">$g_bug_readonly_status_threshold</span></dt><dd><p>
					Bug becomes readonly if its status is &gt;= this status. The bug becomes read/write again if re-opened and its status becomes less than this threshold.
				</p></dd><dt><span class="term">$g_bug_closed_status_threshold</span></dt><dd><p>
					Bug is closed. In some custom installations a bug may be considered as closed when it is moved to a custom (COMPLETED or IMPLEMENTED) status.
				</p></dd></dl></div><p>
		See also: <a class="xref" href="#admin.customize.status" title="7.5. Customizing Status Values">Section 7.5, “Customizing Status Values”</a>
	</p></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.filters"></a>5.23. Filters</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_filter_by_custom_fields</span></dt><dd><p>
					Show custom fields in the filter dialog and use these in filtering. Defaults to ON.
				</p></dd><dt><span class="term">$g_filter_custom_fields_per_row</span></dt><dd><p>
					The number of filter fields to display per row. The default is 8.
				</p></dd><dt><span class="term">$g_view_filters = SIMPLE_DEFAULT;</span></dt><dd><p>
					Controls the display of the filter pages. Possible values are: 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								SIMPLE_ONLY - only allow use of simple view
							</p></li><li class="listitem"><p>
								ADVANCED_ONLY - only allow use of advanced view (allows multiple value selections)
							</p></li><li class="listitem"><p>
								SIMPLE_DEFAULT - defaults to simple view, but shows a link for advanced
							</p></li><li class="listitem"><p>
								ADVANCED_DEFAULT - defaults to advanced view, but shows a link for simple
							</p></li></ul></div><p>

				</p></dd><dt><span class="term">$g_use_dynamic_filters = ON;</span></dt><dd><p>
					This switch enables the use of AJAX to dynamically load and create filter form controls upon request. This method will reduce the amount of data that needs to be transferred upon each page load dealing with filters and thus will result in speed improvements and bandwidth reduction.
				</p></dd><dt><span class="term">$g_create_permalink_threshold</span></dt><dd><p>
					The threshold required for users to be able to create permalinks (default DEVELOPER). To turn this feature off use NOBODY.
				</p></dd><dt><span class="term">$g_create_short_url</span></dt><dd><p>
					The service to use to create a short URL. The %s will be replaced by the long URL. By default https://www.tinyurl service is used to shorten URLs.
				</p></dd><dt><span class="term">$g_view_filters</span></dt><dd><p>
					Controls the display of the filter pages.
				</p></dd><dt><span class="term">$g_use_dynamic_filters</span></dt><dd><p>
					This switch enables the use of AJAX to dynamically load and create filter form controls upon request. This method will reduce the amount of data that needs to be transferred upon each page load dealing with filters and thus will result in speed improvements and bandwidth reduction.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.misc"></a>5.24. Misc</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_user_login_valid_regex</span></dt><dd><p>
					The regular expression to use when validating new user login names. The default regular expression allows a-z, A-Z, 0-9, +, -, dot, space and underscore. If you change this, you may want to update the ERROR_USER_NAME_INVALID string in the language files to explain the rules you are using on your site.
				</p><p>
					See <a class="ulink" href="https://en.wikipedia.org/wiki/Regular_Expression" target="_top">Wikipedia</a> for more details about regular expressions. For testing regular expressions, use <a class="ulink" href="https://rubular.com/" target="_top">Rubular</a>.
				</p></dd><dt><span class="term">$g_monitor_bug_threshold</span></dt><dd><p>
					Access level needed to monitor issues. The default value is REPORTER.
				</p></dd><dt><span class="term">$g_show_monitor_list_threshold</span></dt><dd><p>
					Access level needed to show the list of users monitoring an issue. The default value is DEVELOPER.
				</p></dd><dt><span class="term">$g_monitor_add_others_bug_threshold</span></dt><dd><p>
					Access level needed to add other users to the list of users monitoring an issue. The default value is DEVELOPER.
				</p><p>
					This setting should not be lower than <span class="emphasis"><em>$g_show_monitor_list_threshold</em></span>.
				</p></dd><dt><span class="term">$g_monitor_delete_others_bug_threshold</span></dt><dd><p>
					Access level needed to delete other users from the list of users monitoring an issue. The default value is DEVELOPER.
				</p><p>
					This setting should not be lower than <span class="emphasis"><em>$g_show_monitor_list_threshold</em></span>.
				</p></dd><dt><span class="term">$g_print_reports_threshold</span></dt><dd><p>
					Grants users access to the Print Reports functionality (Word/HTML) from the View Issues page. The default value is UPDATER.
				</p></dd><dt><span class="term">$g_export_issues_threshold</span></dt><dd><p>
					Access level required to export issues to CSV and Excel formats from the View Issues page. The default value is VIEWER.
				</p></dd><dt><span class="term">$g_allow_reporter_close</span></dt><dd><p>
					Allow reporters to close the bugs they reported.
				</p></dd><dt><span class="term">$g_delete_bug_threshold</span></dt><dd><p>
					Allow the specified access level and above to delete bugs.
				</p></dd><dt><span class="term">$g_bug_move_access_level</span></dt><dd><p>
					Allow the specified access level and above to move bugs between projects.
				</p></dd><dt><span class="term">$g_allow_account_delete</span></dt><dd><p>
					Allow users to delete their own accounts.
				</p></dd><dt><span class="term">$g_allow_anonymous_login</span></dt><dd><p>
					Enable anonymous access to Mantis. You must also specify $g_anonymous_account as the account which anonymous users will browse Mantis with. The default setting is OFF.
				</p></dd><dt><span class="term">$g_anonymous_account</span></dt><dd><p>
					Define the account which anonymous users will assume when using Mantis. This account is considered by Mantis to be protected from modification. In other words, this account can only be modified by users with an access level equal to or higher than $g_manage_user_threshold. Anonymous users will not be able to adjust preferences or change account settings like normal users can.
				</p><p>
					You will need to create a new account to use for this $g_anonymous_account setting. When creating the account you should specify a password, email address and so forth in the same way you'd create any other account. It is suggested that the access level for this account be set to VIEWER or some other read only level.
				</p><p>
					The anonymous user account will not receive standard notifications and can not monitor issues.
				</p><p>
					The default setting is blank/undefined. You only need to define this setting when $g_allow_anonymous_login is set to ON.
				</p></dd><dt><span class="term">$g_bug_link_tag</span></dt><dd><p>
					If a number follows this tag it will create a link to a bug. Default is '#'. 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								'#': a link would be #45
							</p></li><li class="listitem"><p>
								'bug:' a link would be bug:98
							</p></li></ul></div><p>

				</p></dd><dt><span class="term">$g_bugnote_link_tag</span></dt><dd><p>
					If a number follows this tag it will create a link to a bug note. Default is '~'. 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								'~': a link would be ~45
							</p></li><li class="listitem"><p>
								'bugnote:' a link would be bugnote:98
							</p></li></ul></div><p>

				</p></dd><dt><span class="term">$g_enable_project_documentation</span></dt><dd><p>
					Specifies whether to enable support for project documents or not. Default is OFF. This feature is deprecated and is expected to be moved to a plugin in the future.
				</p></dd><dt><span class="term">$g_admin_site_threshold</span></dt><dd><p>
					Threshold at which a user is considered to be a site administrator. These users have the highest level of access to your Mantis installation. This access level is required to change key Mantis settings (such as server paths) and perform other administrative duties. You may need to change this value from the default of ADMINISTRATOR if you have defined a new access level to replace the default ADMINISTRATOR level in constant_inc.php.
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						This is a potentially dangerous configuration option. Users at or above this threshold value will have permission to all aspects of Mantis including the admin/ directory. With this access level, users can damage your installation of Mantis, destroy your database or have elevated access to your server.
					</p><p>
						DO NOT CHANGE THIS VALUE UNLESS YOU ABSOLUTELY KNOW WHAT YOU'RE DOING. BE VERY CAREFUL WITH CHANGING THIS CONFIGURATION VALUE FROM THE DEFAULT SETTING.
					</p></div></dd><dt><span class="term">$g_manage_configuration_threshold</span></dt><dd><p>
					The threshold required for users to be able to manage configuration of a project. This includes workflow, email notifications, columns to view, and others. Default is MANAGER.
				</p></dd><dt><span class="term">$g_view_configuration_threshold</span></dt><dd><p>
					Threshold for users to view the raw system configurations as stored in the database. The default value is ADMINISTRATOR.
				</p></dd><dt><span class="term">$g_set_configuration_threshold</span></dt><dd><p>
					Threshold for users to set the system configurations generically via MantisBT web interface. The default value is ADMINISTRATOR.
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						Users who have access to set configuration via the interface MUST be trusted. This is due to the fact that these users can leverage the interface to <span class="emphasis"><em>inject PHP code</em></span> into the system, which is a potential security risk.
					</p></div></dd><dt><span class="term">$g_csv_separator</span></dt><dd><p>
					The separator to use for CSV exports. The default value is the comma (<code class="literal">,</code>).
				</p></dd><dt><span class="term">$g_csv_injection_protection</span></dt><dd><p>
					When this setting is <span class="emphasis"><em>ON</em></span> (default), any data that could be interpreted as a formula by a spreadsheet program such as Excel (i.e. starting with <code class="literal">=</code>, <code class="literal">@</code>, <code class="literal">-</code> or <code class="literal">+</code>), will be prefixed with a tab character (<code class="literal">\t</code>) in order to prevent CSV injection.
				</p><p>
					Sometimes this may not be appropriate (e.g. if the CSV needs to be consumed programmatically). In that case, $g_csv_injection_protection can be set to <span class="emphasis"><em>OFF</em></span>, resulting in raw data to be exported.
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						Setting this to <span class="emphasis"><em>OFF</em></span> is a security risk. An attacker could upload a crafted CSV file containing formulas that will be executed when opened with Excel, as described in <a class="ulink" href="http://georgemauer.net/2017/10/07/csv-injection.html" target="_top">this article</a>.
					</p></div></dd><dt><span class="term">$g_view_bug_threshold</span></dt><dd><p>
					Access level needed to view bugs.
				</p></dd><dt><span class="term">$g_update_bug_assign_threshold</span></dt><dd><p>
					Access level needed to show the Assign To: button bug_view*_page or the Assigned list in bug_update*_page. This allows control over who can route bugs This defaults to $g_handle_bug_threshold.
				</p></dd><dt><span class="term">$g_private_bugnote_threshold</span></dt><dd><p>
					Access level needed to view private bugnotes.
				</p></dd><dt><span class="term">$g_view_handler_threshold</span></dt><dd><p>
					Access level needed to view handler.
				</p></dd><dt><span class="term">$g_view_history_threshold</span></dt><dd><p>
					Access level needed to view history.
				</p></dd><dt><span class="term">$g_bug_reminder_threshold</span></dt><dd><p>
					Access level needed to send a reminder from the bug view pages set to NOBODY to disable the feature.
				</p></dd><dt><span class="term">$g_upload_project_file_threshold</span></dt><dd><p>
					Access level needed to upload files to the project documentation section You can set this to NOBODY to prevent uploads to projects.
				</p></dd><dt><span class="term">$g_upload_bug_file_threshold</span></dt><dd><p>
					Access level needed to upload files to attach to a bug You can set this to NOBODY to prevent uploads to bugs but note that the reporter of the bug will still be able to upload unless you set $g_allow_reporter_upload or $g_allow_file_upload to OFF See also: $g_upload_project_file_threshold, $g_allow_file_upload, $g_allow_reporter_upload.
				</p></dd><dt><span class="term">$g_add_bugnote_threshold</span></dt><dd><p>
					Add bugnote threshold.
				</p></dd><dt><span class="term">$g_update_bugnote_threshold</span></dt><dd><p>
					Threshold at which a user can edit the bugnotes of other users.
				</p></dd><dt><span class="term">$g_view_proj_doc_threshold</span></dt><dd><p>
					Threshold needed to view project documentation Note: setting this to ANYBODY will let any user download attachments from private projects, regardless of their being a member of it.
				</p></dd><dt><span class="term">$g_manage_site_threshold</span></dt><dd><p>
					Site manager.
				</p></dd><dt><span class="term">$g_manage_project_threshold</span></dt><dd><p>
					Threshold needed to manage a project: edit project details (not to add/delete projects) ...etc.
				</p></dd><dt><span class="term">$g_manage_news_threshold</span></dt><dd><p>
					Threshold needed to add/delete/modify news.
				</p></dd><dt><span class="term">$g_delete_project_threshold</span></dt><dd><p>
					Threshold required to delete a project.
				</p></dd><dt><span class="term">$g_create_project_threshold</span></dt><dd><p>
					Threshold needed to create a new project.
				</p></dd><dt><span class="term">$g_private_project_threshold</span></dt><dd><p>
					Threshold needed to be automatically included in private projects.
				</p></dd><dt><span class="term">$g_project_user_threshold</span></dt><dd><p>
					Threshold needed to manage user access to a project.
				</p></dd><dt><span class="term">$g_delete_bugnote_threshold</span></dt><dd><p>
					Threshold at which a user can delete the bugnotes of other users. The default value is equal to the configuration setting $g_delete_bug_threshold.
				</p></dd><dt><span class="term">$g_move_bug_threshold</span></dt><dd><p>
					Move bug threshold.
				</p></dd><dt><span class="term">$g_stored_query_use_threshold</span></dt><dd><p>
					Threshold needed to be able to use stored queries.
				</p></dd><dt><span class="term">$g_stored_query_create_threshold</span></dt><dd><p>
					Threshold needed to be able to create stored queries.
				</p></dd><dt><span class="term">$g_stored_query_create_shared_threshold</span></dt><dd><p>
					Threshold needed to be able to create shared stored queries.
				</p></dd><dt><span class="term">$g_update_readonly_bug_threshold</span></dt><dd><p>
					Threshold needed to update readonly bugs. Readonly bugs are identified via $g_bug_readonly_status_threshold.
				</p></dd><dt><span class="term">$g_view_changelog_threshold</span></dt><dd><p>
					Threshold for viewing changelog.
				</p></dd><dt><span class="term">$g_roadmap_view_threshold</span></dt><dd><p>
					Threshold for viewing roadmap.
				</p></dd><dt><span class="term">$g_roadmap_update_threshold</span></dt><dd><p>
					Threshold for updating roadmap, target_version, etc.
				</p></dd><dt><span class="term">$g_update_bug_status_threshold</span></dt><dd><p>
					Status change thresholds.
				</p></dd><dt><span class="term">$g_reopen_bug_threshold</span></dt><dd><p>
					Access level needed to re-open bugs.
				</p></dd><dt><span class="term">$g_report_issues_for_unreleased_versions_threshold</span></dt><dd><p>
					Access level needed to assign bugs to unreleased product versions.
				</p></dd><dt><span class="term">$g_set_bug_sticky_threshold</span></dt><dd><p>
					Access level needed to set a bug sticky.
				</p></dd><dt><span class="term">$g_set_status_threshold</span></dt><dd><p>
					This array sets the access thresholds needed to enter each status listed. if a status is not listed, it falls back to $g_update_bug_status_threshold.
				</p></dd><dt><span class="term">$g_allow_no_category</span></dt><dd><p>
					Allow a bug to have no category.
				</p></dd><dt><span class="term">$g_limit_view_unless_threshold</span></dt><dd><p>
					Threshold at which a user can view all issues in the project (as allowed by other permissions). Not meeting this threshold means the user can only see the issues they reported, are handling or monitoring. A value of ANYBODY means that all users have full visibility (as default) This is a replacement for old option: $g_limit_reporters.
				</p></dd><dt><span class="term">$g_allow_reporter_upload</span></dt><dd><p>
					Reporter can upload Allow reporters to upload attachments to bugs they reported.
				</p></dd><dt><span class="term">$g_bug_count_hyperlink_prefix</span></dt><dd><p>
					Bug Count Linking This is the prefix to use when creating links to bug views from bug counts (eg. on the main page and the summary page). Default is a temporary filter.
				</p></dd><dt><span class="term">$g_default_manage_tag_prefix</span></dt><dd><p>
					Default tag prefix used to filter the list of tags in manage_tags_page.php. Change this to 'A' (or any other letter) if you have a lot of tags in the system and loading the manage tags page takes a long time.
				</p></dd><dt><span class="term">$g_access_levels_enum_string</span></dt><dd><p>
					Status from $g_status_index-1 to 79 are used for the onboard customization (if enabled) directly use MantisBT to edit them.
				</p></dd><dt><span class="term">$g_project_status_enum_string</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_project_view_state_enum_string</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_view_state_enum_string</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_priority_enum_string</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_severity_enum_string</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_reproducibility_enum_string</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_status_enum_string</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_resolution_enum_string</span></dt><dd><p>
					The values in this list are also used to define variables in the language files (e.g., $s_new_bug_title referenced in bug_change_status_page.php ). Embedded spaces are converted to underscores (e.g., "working on" references $s_working_on_bug_title). They are also expected to be English names for the states
				</p></dd><dt><span class="term">$g_projection_enum_string</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_eta_enum_string</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_sponsorship_enum_string</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_custom_field_type_enum_string</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_file_type_icons</span></dt><dd><p>
					Maps a file extension to a file type icon. These icons are printed next to project documents and bug attachments.
				</p></dd><dt><span class="term">$g_file_download_content_type_overrides</span></dt><dd><p>
					Content types which will be overridden when downloading files.
				</p></dd><dt><span class="term">$g_status_icon_arr</span></dt><dd><p>
					Icon associative arrays. Status to icon mapping.
				</p></dd><dt><span class="term">$g_sort_icon_arr</span></dt><dd><p>
					Sort direction to icon mapping.
				</p></dd><dt><span class="term">$g_rss_enabled</span></dt><dd><p>
					This flag enables or disables RSS syndication. In the case where RSS syndication is not used, it is recommended to set it to OFF.
				</p></dd><dt><span class="term">$g_recently_visited_count</span></dt><dd><p>
					This controls whether to show the most recently visited issues by the current user or not. If set to 0, this feature is disabled. Otherwise it is the maximum number of issues to keep in the recently visited list.
				</p></dd><dt><span class="term">$g_tag_separator</span></dt><dd><p>
					String that will separate tags as entered for input.
				</p></dd><dt><span class="term">$g_tag_view_threshold</span></dt><dd><p>
					Access level required to view tags attached to a bug.
				</p></dd><dt><span class="term">$g_tag_attach_threshold</span></dt><dd><p>
					Access level required to attach tags to a bug.
				</p></dd><dt><span class="term">$g_tag_detach_threshold</span></dt><dd><p>
					Access level required to detach tags from a bug.
				</p></dd><dt><span class="term">$g_tag_detach_own_threshold</span></dt><dd><p>
					Access level required to detach tags attached by the same user.
				</p></dd><dt><span class="term">$g_tag_create_threshold</span></dt><dd><p>
					Access level required to create new tags.
				</p></dd><dt><span class="term">$g_tag_edit_threshold</span></dt><dd><p>
					Access level required to edit tag names and descriptions.
				</p></dd><dt><span class="term">$g_tag_edit_own_threshold</span></dt><dd><p>
					Access level required to edit descriptions by the creating user.
				</p></dd><dt><span class="term">$g_enable_profiles</span></dt><dd><p>
					Enable Profiles.
				</p></dd><dt><span class="term">$g_add_profile_threshold</span></dt><dd><p>
					Add profile threshold.
				</p></dd><dt><span class="term">$g_manage_global_profile_threshold</span></dt><dd><p>
					Threshold needed to be able to create and modify global profiles.
				</p></dd><dt><span class="term">$g_allow_freetext_in_profile_fields</span></dt><dd><p>
					Allows the users to enter free text when reporting/updating issues for the profile related fields (i.e. platform, os, os build).
				</p></dd><dt><span class="term">$g_plugins_enabled</span></dt><dd><p>
					Enable/disable plugins.
				</p></dd><dt><span class="term">$g_plugin_path</span></dt><dd><p>
					Absolute path to plugin files.
				</p></dd><dt><span class="term">$g_manage_plugin_threshold</span></dt><dd><p>
					Threshold needed to manage plugins.
				</p></dd><dt><span class="term">$g_plugin_mime_types</span></dt><dd><p>
					A mapping of file extensions to mime types, used when serving resources from plugins.
				</p></dd><dt><span class="term">$g_plugins_force_installed</span></dt><dd><p>
					Force installation and protection of certain plugins. Note that this is not the preferred method of installing plugins, which should generally be done directly through the plugin management interface. However, this method will prevent users with admin access from uninstalling plugins through the plugin management interface.
				</p><p>
					Entries in the array must be in the form of a key/value pair consisting of the plugin basename and priority.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.cookies"></a>5.25. Cookies</h2></div></div></div><p>
	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_cookie_path</span></dt><dd><p>
					Specifies the path under which a cookie is visible.
				</p><p>
					All scripts in this directory and its sub-directories will be able to access MantisBT cookies.
				</p><p>
					Default value is '/'. It is recommended to set this to the actual MantisBT path.
				</p></dd><dt><span class="term">$g_cookie_domain</span></dt><dd><p>
					The domain that the MantisBT cookies are available to.
				</p></dd><dt><span class="term">$g_cookie_samesite</span></dt><dd><p>
					Specifies the <a class="ulink" href="https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite" target="_top">SameSite attribute</a> to use for the MantisBT cookies.
				</p><p>
					Valid values are <code class="literal">Strict</code>, <code class="literal">Lax</code> (default) or <code class="literal">None</code>.
				</p><p>
					If this setting is changed, users with a non-expired Session cookie (see <span class="emphasis"><em>$g_string_cookie</em></span> below) may need to log out and log back in, to switch the cookie's secure attribute to the new value.
				</p><div class="note"><h3 class="title">Note</h3><p>
						While <code class="literal">Strict</code> provides stronger protection against CSRF attacks, it actually prevents the user's session from being recognized when clicking a link from a notification e-mail, causing MantisBT to start an anonymous session even if the user is already logged in.
					</p></div></dd><dt><span class="term">$g_cookie_prefix</span></dt><dd><p>
					Prefix for all MantisBT cookies
				</p><p>
					This must be an identifier which does not include spaces or periods, and should be unique per MantisBT installation, especially if $g_cookie_path is not restricting the cookies' scope to the actual MantisBT directory.
				</p><p>
					It applies to the cookies listed below. Their actual names are calculated by prepending the prefix, and it is not expected for the user to need to change these.
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							$g_bug_list_cookie
						</p></li><li class="listitem"><p>
							$g_collapse_settings_cookie
						</p><p>
							Stores the open/closed state of the collapsible sections.
						</p></li><li class="listitem"><p>
							$g_logout_cookie
						</p></li><li class="listitem"><p>
							$g_manage_config_cookie
						</p><p>
							Stores the filter criteria for the Manage Config Report page.
						</p></li><li class="listitem"><p>
							$g_manage_users_cookie
						</p><p>
							Stores the filter criteria for the Manage Users page.
						</p></li><li class="listitem"><p>
							$g_project_cookie
						</p></li><li class="listitem"><p>
							$g_string_cookie
						</p></li><li class="listitem"><p>
							$g_view_all_cookie
						</p></li></ul></div></dd><dt><span class="term">$g_string_cookie</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_project_cookie</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_view_all_cookie</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_collapse_settings_cookie</span></dt><dd><p>
					Collapse settings cookie. Stores the open/closed state of the collapsible sections.
				</p></dd><dt><span class="term">$g_manage_users_cookie</span></dt><dd><p>
					Stores the filter criteria for the Manage User page
				</p></dd><dt><span class="term">$g_manage_config_cookie</span></dt><dd><p>
					Stores the filter criteria for the Manage Config Report page
				</p></dd><dt><span class="term">$g_logout_cookie</span></dt><dd><p>
					TODO
				</p></dd><dt><span class="term">$g_bug_list_cookie</span></dt><dd><p>
					TODO
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.speed"></a>5.26. Speed Optimisation</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_compress_html</span></dt><dd><p>
					This option is used to enable buffering/compression of HTML output if the user's browser supports it. Default value is ON. This option will be ignored in the following scenarios:
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							php.ini has <span class="emphasis"><em>zlib.output_compression</em></span> enabled.
						</p></li><li class="listitem"><p>
							php.ini has <span class="emphasis"><em>output_handler</em></span> set to a handler.
						</p></li><li class="listitem"><p>
							<a class="ulink" href="https://www.php.net/manual/en/book.zlib.php" target="_top">zlib extension</a> is not enabled. The Windows version of PHP has built-in support for this extension.
						</p></li></ul></div></dd><dt><span class="term">$g_use_persistent_connections</span></dt><dd><p>
					Use persistent database connections, setting this to ON will open the database once per connection, rather than once per page. There might be some scalability issues here and that is why it is defaulted to OFF.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.reminders"></a>5.27. Reminders</h2></div></div></div><p>
		Sending reminders is a feature where a user can notify / remind other users about a bug. In the past, only selected users like the managers, or developers would get notified about bugs. However, these people can not invite other people (through MantisBT) to look at or monitor these bugs.
	</p><p>
		This feature is useful if the Manager needs to get feedback from testers / requirements team about a certain bug. It avoid needing this person to do this manual outside MantisBT. It also records the history of such reminders.
	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_store_reminders</span></dt><dd><p>
					Specifies if reminders should be stored as bugnotes. The bugnote will still reflect that it is a reminder and list the names of users that got it. Default is ON.
				</p></dd><dt><span class="term">$g_reminder_recipients_monitor_bug</span></dt><dd><p>
					Specifies if users who receive reminders about a bug, should be automatically added to the monitor list of that bug. Default is ON.
				</p></dd><dt><span class="term">$g_mentions_enabled</span></dt><dd><p>
					Enables or disables the @ mentions feature. Default is ON. When a user is @ mentioned in an issue or a note, they receive an email notification to get their attention. Users can be @ mentioned using their username and not realname.
				</p><p>
					This feature works with fields like summary, description, additional info, steps to reproduce and notes.
				</p></dd><dt><span class="term">$g_mentions_tag</span></dt><dd><p>
					The tag to use for prefixing mentions. Default is '@'.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.bughistory"></a>5.28. Bug History</h2></div></div></div><p>
		Bug history is a feature where MantisBT tracks all modifications that are made to bugs. These include everything starting from its creation, till it is closed. For each change, the bug history will record the time stamp, user who made the change, field that changed, old value, and new value.
	</p><p>
		Independent of the these settings, MantisBT will always track the changes to a bug and add them to its history.
	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_history_default_visible</span></dt><dd><p>
					Make the bug history visible by default. If this option is not enabled, then the user will have to click on the Bug History link to see the bug history. Default is ON.
				</p></dd><dt><span class="term">$g_history_order</span></dt><dd><p>
					Show bug history entries in ascending or descending order. Default value is 'ASC'.
				</p></dd></dl></div><p>
		In this context, MantisBT records individual changes to text fields (<span class="emphasis"><em>Description</em></span>, <span class="emphasis"><em>Steps to Reproduce</em></span>, <span class="emphasis"><em>Additional Information</em></span> as well as <span class="emphasis"><em>Bug Notes</em></span>). These revisions are controlled by the following settings.
	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_bug_revision_view_threshold</span></dt><dd><p>
					Access level required to view bug history revisions. Defaults to DEVELOPER.
				</p><div class="note"><h3 class="title">Note</h3><p>
						Users can always see revisions for the issues and bugnotes they reported.
					</p></div></dd><dt><span class="term">$g_bug_revision_drop_threshold</span></dt><dd><p>
					Access level required to drop bug history revisions. Defaults to MANAGER.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.sponsorship"></a>5.29. Sponsorship</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_enable_sponsorship</span></dt><dd><p>
					enable/disable the whole issue sponsorship feature. The default os OFF.
				</p></dd><dt><span class="term">$g_sponsorship_currency</span></dt><dd><p>
					The currency string used for all sponsorships. The default is 'US$'.
				</p></dd><dt><span class="term">$g_minimum_sponsorship_amount</span></dt><dd><p>
					The minimum sponsorship amount that can be entered. If the user enters a value less than this, an error will be flagged. The default is 5.
				</p></dd><dt><span class="term">$g_view_sponsorship_total_threshold</span></dt><dd><p>
					The access level threshold needed to view the total sponsorship for an issue by all users. The default is VIEWER.
				</p></dd><dt><span class="term">$g_view_sponsorship_details_threshold</span></dt><dd><p>
					The access level threshold needed to view the details of the sponsorship (i.e., who will donate what) for an issue by all users. The default is VIEWER.
				</p></dd><dt><span class="term">$g_sponsor_threshold</span></dt><dd><p>
					The access level threshold needed to allow user to sponsor issues. The default is REPORTER. Note that sponsoring user must have their email set in their profile.
				</p></dd><dt><span class="term">$g_handle_sponsored_bugs_threshold</span></dt><dd><p>
					The access level required to be able to handle sponsored issues. The default is DEVELOPER.
				</p></dd><dt><span class="term">$g_assign_sponsored_bugs_threshold</span></dt><dd><p>
					The access level required to be able to assign a sponsored issue to a user with access level greater or equal to 'handle_sponsored_bugs_threshold'. The default is MANAGER.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.customfields"></a>5.30. Custom Fields</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_manage_custom_fields_threshold</span></dt><dd><p>
					Access level needed to manage custom fields. The default is ADMINISTRATOR.
				</p></dd><dt><span class="term">$g_custom_field_link_threshold</span></dt><dd><p>
					Access level needed to link a custom field to a project. The default is MANAGER.
				</p></dd><dt><span class="term">$g_custom_field_edit_after_create</span></dt><dd><p>
					This flag determines whether to start editing a custom field immediately after creating it, or return to the definition list. The default is ON (edit the custom field after creating).
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.myview"></a>5.31. My View Settings</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_my_view_boxes</span></dt><dd><p>
					This is an array of values defining the order that the boxes to be shown. A box that is not to be shown can have its value set to 0. The default is: 
</p><pre class="programlisting">
$g_my_view_boxes = array(
	'assigned'      =&gt; '1',
	'unassigned'    =&gt; '2',
	'reported'      =&gt; '3',
	'resolved'      =&gt; '4',
	'recent_mod'    =&gt; '5',
	'monitored'     =&gt; '6',
	'feedback'      =&gt; '0',
	'verify'        =&gt; '0',
	'my_comments'   =&gt; '0'
);
</pre><p>
					 If you want to change the definition, copy the default value and apply the changes.
				</p></dd><dt><span class="term">$g_my_view_bug_count</span></dt><dd><p>
					Number of bugs shown in each box. The default is 10.
				</p></dd><dt><span class="term">$g_default_home_page</span></dt><dd><p>
					Default page to transfer to after Login or Set Project. The default is 'my_view_page.php'. An alternative would be 'view_all_bugs_page.php' or 'main_page.php'.
				</p></dd><dt><span class="term">$g_logout_redirect_page</span></dt><dd><p>
					Specify where the user should be sent after logging out.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.relationship"></a>5.32. Relationship Graphs</h2></div></div></div><p>
		MantisBT can display a graphical representation of the relationships between issues. Two types of interactive visualizations are available, <span class="emphasis"><em>dependencies</em></span> and a full <span class="emphasis"><em>relationships</em></span> graph.
	</p><div class="important"><h3 class="title">Important</h3><p>
			This feature relies on the external <span class="emphasis"><em>dot</em></span> and <span class="emphasis"><em>neato</em></span> tools from the <a class="ulink" href="https://www.graphviz.org/" target="_top">GraphViz</a> library, which must be installed separately.
		</p><p>
			Most Linux distributions have a GraphViz package available for easy download and install.
		</p><p>
			Under Windows, the software needs to be installed manually. The following post-installation steps <a class="ulink" href="https://mantisbt.org/bugs/view.php?id=27584#c64693" target="_top">may be required</a> for proper operations: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Update the system PATH to point to GraphViz's <code class="literal">bin</code> directory
					</p></li><li class="listitem"><p>
						Initialize the graph engine by running <code class="literal">dot -c</code> from an <span class="emphasis"><em>Administrator</em></span> command prompt.
					</p></li></ul></div><p>

		</p></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_relationship_graph_enable</span></dt><dd><p>
					This enables the relationship graphs feature where issues are represented by nodes and relationships as links between such nodes. Possible values are ON or OFF. Default is OFF.
				</p></dd><dt><span class="term">$g_relationship_graph_fontname</span></dt><dd><p>
					Font name and size, as required by Graphviz. If Graphviz fails to run for you, you are probably using a font name that gd PHP extension can't find. On Linux, try the name of the font file without the extension. The default value is 'Arial'.
				</p></dd><dt><span class="term">$g_relationship_graph_fontsize</span></dt><dd><p>
					Font size, default is 8.
				</p></dd><dt><span class="term">$g_relationship_graph_orientation</span></dt><dd><p>
					Default dependency orientation. If you have issues with lots of children or parents, leave as 'horizontal', otherwise, if you have lots of "chained" issue dependencies, change to 'vertical'. Default is 'horizontal'.
				</p></dd><dt><span class="term">$g_relationship_graph_max_depth</span></dt><dd><p>
					Max depth for relation graphs. This only affects relationship graphs, dependency graphs are drawn to the full depth. The default value is 2.
				</p></dd><dt><span class="term">$g_relationship_graph_view_on_click</span></dt><dd><p>
					If set to ON, clicking on an issue on the relationship graph will open the bug view page for that issue, otherwise, will navigate to the relationship graph for that issue.
				</p></dd><dt><span class="term">$g_dot_tool</span></dt><dd><p>
					The full path for the dot tool. The webserver must have execute permission to this program in order to generate relationship graphs. This configuration option is not relevant for Windows. The default value is '/usr/bin/dot'.
				</p></dd><dt><span class="term">$g_neato_tool</span></dt><dd><p>
					The full path for the neato tool. The webserver must have execute permission to this program in order to generate relationship graphs. This configuration option is not relevant for Windows. The default value is '/usr/bin/neato'.
				</p></dd><dt><span class="term">$g_backward_year_count</span></dt><dd><p>
					Number of years in the past that custom date fields will display in drop down boxes.
				</p></dd><dt><span class="term">$g_forward_year_count</span></dt><dd><p>
					Number of years in the future that custom date fields will display in drop down boxes.
				</p></dd><dt><span class="term">$g_custom_group_actions</span></dt><dd><p>
					This extensibility model allows developing new group custom actions. This can be implemented with a totally custom form and action pages or with a pre-implemented form and action page and call-outs to some functions. These functions are to be implemented in a predefined file whose name is based on the action name. For example, for an action to add a note, the action would be EXT_ADD_NOTE and the file implementing it would be bug_actiongroup_add_note_inc.php. See implementation of this file for details.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.wiki"></a>5.33. Wiki Integration</h2></div></div></div><p>

	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_wiki_enable</span></dt><dd><p>
					Set to ON to enable Wiki integration. Defaults to OFF.
				</p></dd><dt><span class="term">$g_wiki_engine</span></dt><dd><p>
					The following Wiki Engine values are supported: 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								<span class="emphasis"><em>dokuwiki</em></span>: <a class="ulink" href="https://www.dokuwiki.org/" target="_top">DokuWiki</a>
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>mediawiki</em></span>: <a class="ulink" href="https://www.mediawiki.org/" target="_top">MediaWiki</a>
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>twiki</em></span>: <a class="ulink" href="http://twiki.org/" target="_top">TWiki</a>
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>wackowiki</em></span>: <a class="ulink" href="https://wackowiki.org/" target="_top">WackoWiki</a>
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>wikka</em></span>: <a class="ulink" href="http://wikkawiki.org/" target="_top">WikkaWiki</a>
							</p></li><li class="listitem"><p>
								<span class="emphasis"><em>xwiki</em></span>: <a class="ulink" href="http://www.xwiki.org/" target="_top">XWiki</a>
							</p></li></ul></div><p>

				</p></dd><dt><span class="term">$g_wiki_root_namespace</span></dt><dd><p>
					Wiki namespace to be used as root for all pages relating to this MantisBT installation.
				</p></dd><dt><span class="term">$g_wiki_engine_url</span></dt><dd><p>
					URL under which the wiki engine is hosted.
				</p><p>
					Must be on the same server as MantisBT, requires a trailing '/'.
				</p><p>
					If left empty (default), the URL is derived from the global MantisBT path ($g_path, see <a class="xref" href="#admin.config.path" title="5.3. Path">Section 5.3, “Path”</a>), replacing the URL's path component by the wiki engine string (i.e. if $g_path = 'http://example.com/mantis/' and $g_wiki_engine = 'dokuwiki', the wiki URL will be 'http://example.com/dokuwiki/').
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.subprojects"></a>5.34. Sub-Projects</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_subprojects_enabled</span></dt><dd><p>
					Whether sub-projects feature should be enabled. Before turning this flag OFF, make sure all sub-projects are moved to top level projects, otherwise they won't be accessible. The default value is ON.
				</p></dd><dt><span class="term">$g_subprojects_inherit_versions</span></dt><dd><p>
					Whether sub-projects should inherit versions from parent projects. For project X which is a sub-project of A and B, it will have versions from X, A and B. The default value is ON.
				</p></dd><dt><span class="term">$g_subprojects_inherit_categories</span></dt><dd><p>
					Whether sub-projects should inherit categories from parent projects. For project X which is a sub-project of A and B, it will have categories from X, A and B. The default value is ON.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.fields"></a>5.35. Field Visibility</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_enable_eta</span></dt><dd><p>
					Enable or disable usage of 'ETA' field. Default value is OFF.
				</p></dd><dt><span class="term">$g_enable_projection</span></dt><dd><p>
					Enable or disable usage of 'Projection' field. Default value is OFF.
				</p></dd><dt><span class="term">$g_enable_product_build</span></dt><dd><p>
					Enable or disable usage of 'Product Build' field. Default is OFF.
				</p></dd><dt><span class="term">$g_bug_report_page_fields</span></dt><dd><p>
					An array of optional fields to show on the bug report page.
				</p><p>
					The following optional fields are allowed: additional_info, attachments, category_id, due_date, eta, handler, monitors, os, os_build, platform, priority, product_build, product_version, reproducibility, resolution, severity, status, steps_to_reproduce, tags, target_version, view_state.
				</p><p>
					The summary and description fields are always shown and do not need to be listed in this option. Fields not listed above cannot be shown on the bug report page. Visibility of custom fields is handled via the Manage =&gt; Custom Fields administrator page.
				</p><p>
					Note that <span class="emphasis"><em>monitors</em></span> is not an actual field; adding it to the list will let authorized reporters (see <span class="emphasis"><em>monitor_add_others_bug_threshold</em></span> in <a class="xref" href="#admin.config.misc" title="5.24. Misc">Section 5.24, “Misc”</a>) select users to add to the issue's monitoring list. Monitors will only be notified of the submission if both their e-mail preferencess and the <span class="emphasis"><em>notify_flags</em></span> configuration (see <a class="xref" href="#admin.config.email" title="5.8. Email">Section 5.8, “Email”</a>) allows it, i.e. 
</p><pre class="programlisting">$g_notify_flags['new']['monitor'] = ON;</pre><p>

				</p><p>
					This setting can be set on a per-project basis by using the Manage =&gt; Configuration administrator page.
				</p></dd><dt><span class="term">$g_bug_view_page_fields</span></dt><dd><p>
					An array of optional fields to show on the issue view page and other pages that include issue details.
				</p><p>
					The following optional fields are allowed: additional_info, attachments, category_id, date_submitted, description, due_date, eta, fixed_in_version, handler, id, last_updated, os, os_build, platform, priority, product_build, product_version, project, projection, reporter, reproducibility, resolution, severity, status, steps_to_reproduce, summary, tags, target_version, view_state.
				</p><p>
					Fields not listed above cannot be shown on the bug view page. Visibility of custom fields is handled via the Manage =&gt; Custom Fields administrator page.
				</p><p>
					This setting can be set on a per-project basis by using the Manage =&gt; Configuration administrator page.
				</p></dd><dt><span class="term">$g_bug_update_page_fields</span></dt><dd><p>
					An array of optional fields to show on the bug update page.
				</p><p>
					The following optional fields are allowed: additional_info, category_id, date_submitted, description, due_date, eta, fixed_in_version, handler, id, last_updated, os, os_build, platform, priority, product_build, product_version, project, projection, reporter, reproducibility, resolution, severity, status, steps_to_reproduce, summary, target_version, view_state.
				</p><p>
					Fields not listed above cannot be shown on the bug update page. Visibility of custom fields is handled via the Manage =&gt; Custom Fields administrator page.
				</p><p>
					This setting can be set on a per-project basis by using the Manage =&gt; Configuration administrator page.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.logging"></a>5.36. System Logging and Debugging</h2></div></div></div><p>
		This section describes settings which can be used to troubleshoot MantisBT operations as well as assist during development.
	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_show_timer</span></dt><dd><p>
					Time page loads. The page execution timer shows at the bottom of each page.
				</p><p>
					Default is OFF.
				</p></dd><dt><span class="term">$g_show_memory_usage</span></dt><dd><p>
					Show memory usage for each page load in the footer.
				</p><p>
					Default is OFF.
				</p></dd><dt><span class="term">$g_debug_email</span></dt><dd><p>
					Used for debugging e-mail notifications. When it is '', the emails are sent normally. If set to an e-mail address, all messages are sent to it, with the original recipients (To, Cc, Bcc) included in the message body.
				</p><p>
					Default is ''.
				</p></dd><dt><span class="term">$g_show_queries_count</span></dt><dd><p>
					Shows the total number/unique number of queries executed to serve the page.
				</p><p>
					Default is OFF.
				</p></dd><dt><span class="term">$g_display_errors</span></dt><dd><p>
					Errors Display Method. Defines what <a class="ulink" href="https://www.php.net/errorfunc.constants" target="_top"> errors</a> are displayed and how. Available options are:
				</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">DISPLAY_ERROR_HALT</span></dt><dd><p>
								Stop and display the error message (including variables and backtrace if <span class="emphasis"><em>$g_show_detailed_errors</em></span> is ON).
							</p></dd><dt><span class="term">DISPLAY_ERROR_INLINE</span></dt><dd><p>
								Display a one line error and continue execution.
							</p></dd><dt><span class="term">DISPLAY_ERROR_NONE</span></dt><dd><p>
								Suppress the error (no display). This is the default behavior for unspecified <a class="ulink" href="https://www.php.net/errorfunc.constants" target="_top"> errors constants</a>.
							</p></dd></dl></div><p>
					The default settings are recommended for use in production, and will only display MantisBT fatal errors, suppressing output of all other error types.
				</p><p>
					Recommended <code class="filename">config_inc.php</code> settings for developers: 
</p><pre class="programlisting">
$g_display_errors = array(
	E_WARNING           =&gt; DISPLAY_ERROR_HALT,
	E_ALL               =&gt; DISPLAY_ERROR_INLINE,
);
</pre><p>

				</p><div class="note"><h3 class="title">Note</h3><p>
						The system automatically sets <span class="emphasis"><em>$g_display_errors</em></span> to the above recommended development values when the server's name is <span class="emphasis"><em>localhost</em></span>.
					</p></div><p>
					Less intrusive settings, recommended for testing purposes: 
</p><pre class="programlisting">
$g_display_errors = array(
	E_USER_WARNING =&gt; DISPLAY_ERROR_INLINE,
	E_WARNING      =&gt; DISPLAY_ERROR_INLINE,
);
</pre><p>

				</p><div class="note"><h3 class="title">Note</h3><p>
						<code class="literal">E_USER_ERROR</code>, <code class="literal">E_RECOVERABLE_ERROR</code> and <code class="literal">E_ERROR</code> will always be set to <span class="emphasis"><em>DISPLAY_ERROR_HALT</em></span> internally, regardless of the actual configured value. This ensures that program execution stops, to prevent potential integrity issues and/or MantisBT from functioning incorrectly.
					</p></div></dd><dt><span class="term">$g_show_detailed_errors</span></dt><dd><p>
					Shows a list of variables and their values whenever an error is triggered. Only applies to error types configured to <code class="literal">DISPLAY_ERROR_HALT</code> in <span class="emphasis"><em>$g_display_errors</em></span>.
				</p><p>
					Default is OFF.
				</p><div class="warning"><h3 class="title">Warning</h3><p>
						Setting this to ON is a potential security hazard, as it can expose sensitive information. Only enable this setting for debugging purposes when you really need it.
					</p></div></dd><dt><span class="term">$g_stop_on_errors</span></dt><dd><p>
					Debug messages. If this option is turned OFF, page redirects will function if a non-fatal error occurs. For debugging purposes, you can set this to ON so that any non-fatal error will prevent page redirection, allowing you to see the errors.
				</p><p>
					Default is OFF.
				</p><div class="note"><h3 class="title">Note</h3><p>
						This should only be turned on when debugging.
					</p></div></dd><dt><span class="term">$g_log_level</span></dt><dd><p>
					The system logging interface is used to extract detailed debugging information for the MantisBT system. It can also serve as an audit trail for users' actions.
				</p><p>
					This controls the type of logging information recorded. Refer to <span class="emphasis"><em>$g_log_destination</em></span> for details on where to save the logs.
				</p><p>
					The available log channels are:
				</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">LOG_NONE</span></dt><dd><p>
								Disable logging
							</p></dd><dt><span class="term">LOG_AJAX</span></dt><dd><p>
								logs AJAX events
							</p></dd><dt><span class="term">LOG_DATABASE</span></dt><dd><p>
								logs database events and executed SQL queries
							</p></dd><dt><span class="term">LOG_EMAIL</span></dt><dd><p>
								logs issue id, message type and recipients for all emails sent
							</p></dd><dt><span class="term">LOG_EMAIL_VERBOSE</span></dt><dd><p>
								Enables extra logging for troubleshooting internals of email queuing and sending.
							</p></dd><dt><span class="term">LOG_EMAIL_RECIPIENT</span></dt><dd><p>
								logs the details of email recipient determination. Each user id is listed as well as why they are added, or deleted from the recipient list
							</p></dd><dt><span class="term">LOG_FILTERING</span></dt><dd><p>
								logs filter operations
							</p></dd><dt><span class="term">LOG_LDAP</span></dt><dd><p>
								logs the details of LDAP operations
							</p></dd><dt><span class="term">LOG_WEBSERVICE</span></dt><dd><p>
								logs the details of Web Services operations (e.g. SOAP API)
							</p></dd><dt><span class="term">LOG_PLUGIN</span></dt><dd><p>
								Enables logging from plugins.
							</p></dd><dt><span class="term">LOG_ALL</span></dt><dd><p>
								combines all of the above log levels
							</p></dd></dl></div><p>
					Default is LOG_NONE.
				</p><div class="note"><h3 class="title">Note</h3><p>
						Multiple log channels can be combined using <a class="ulink" href="https://www.php.net/language.operators.bitwise" target="_top"> PHP bitwise operators </a>, e.g. 
</p><pre class="programlisting">$g_log_level = LOG_EMAIL | LOG_EMAIL_RECIPIENT;</pre><p>
						 or 
</p><pre class="programlisting">$g_log_level = LOG_ALL &amp; ~LOG_DATABASE;</pre><p>

					</p></div></dd><dt><span class="term">$g_log_destination</span></dt><dd><p>
					Specifies where the log data goes. The following five options are available:
				</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">''</span></dt><dd><p>
								The empty string means <a class="ulink" href="https://www.php.net/error_log" target="_top"> default PHP error log settings </a>
							</p></dd><dt><span class="term">'none'</span></dt><dd><p>
								Don't output the logs, but would still trigger EVENT_LOG plugin event.
							</p></dd><dt><span class="term">'file'</span></dt><dd><p>
								Log to a specific file, specified as an absolute path, e.g. <code class="literal">'file:/var/log/mantis.log'</code> (Unix) or <code class="literal">'file:c:/temp/mantisbt.log'</code> (Windows)
							</p><div class="note"><h3 class="title">Note</h3><p>
									This file must be writable by the web server running MantisBT.
								</p></div></dd><dt><span class="term">'page'</span></dt><dd><p>
								Display log output at bottom of the page. See also <span class="emphasis"><em>$g_show_log_threshold</em></span> to restrict who can see log data.
							</p></dd></dl></div><p>
					Default is '' (empty string).
				</p></dd><dt><span class="term">$g_show_log_threshold</span></dt><dd><p>
					Indicates the access level required for a user to see the log output (if <span class="emphasis"><em>$g_log_destination</em></span> is 'page').
				</p><p>
					Default is ADMINISTRATOR.
				</p><div class="note"><h3 class="title">Note</h3><p>
						This threshold is compared against the user's <span class="emphasis"><em>global access level</em></span> rather than the one from the currently active project.
					</p></div></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.timetracking"></a>5.37. Time Tracking</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_time_tracking_enabled</span></dt><dd><p>
					Turns Time Tracking features ON or OFF - Default is OFF
				</p></dd><dt><span class="term">$g_time_tracking_without_note</span></dt><dd><p>
					Allow time tracking to be recorded without writing some text in the associated bugnote - Default is ON
				</p></dd><dt><span class="term">$g_time_tracking_with_billing</span></dt><dd><p>
					Adds calculation links to workout how much time has been spent between a particular time frame. Currently it will allow you to enter a cost/hour and will work out some billing information. This will become more extensive in the future. Currently it is more of a proof of concept.
				</p></dd><dt><span class="term">$g_time_tracking_billing_rate</span></dt><dd><p>
					Default billing rate per hour - Default is 0
				</p></dd><dt><span class="term">$g_time_tracking_stopwatch</span></dt><dd><p>
					Instead of a text field turning this option on places a stopwatch on the page with <span class="guibutton">Start/Stop</span> and <span class="guibutton">Reset</span> buttons next to it. A bit gimmicky, but who cares.
				</p></dd><dt><span class="term">$g_time_tracking_view_threshold</span></dt><dd><p>
					Access level required to view time tracking information - Default DEVELOPER.
				</p></dd><dt><span class="term">$g_time_tracking_edit_threshold</span></dt><dd><p>
					Access level required to add/edit time tracking information (If you give a user $g_time_tracking_edit_threshold you must give them $g_time_tracking_view_threshold as well) - Default DEVELOPER.
				</p></dd><dt><span class="term">$g_time_tracking_reporting_threshold</span></dt><dd><p>
					Access level required to run reports (not completed yet) - Default MANAGER.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.api"></a>5.38. API</h2></div></div></div><p>
		MantisBT exposes a webservice API which allows remote clients to interact with MantisBT and perform many of the usual tasks, such as reporting issues, running filtered searches and retrieving attachments.
	</p><p>
		The SOAP API is enabled by default and available at <code class="literal">/api/soap/mantisconnect.php</code> below the MantisBT root. A WSDL file which describes the web service is available at <code class="literal">/api/soap/mantisconnect.php?wsdl</code> below the MantisBT root.
	</p><p>
		The REST API is enabled by default. A Swagger sandbox and documentation for REST API is available at <code class="literal">/api/rest/swagger/</code> below the MantisBT root.
	</p><p>
		The following options are used to control the behaviour of the MantisBT SOAP API:
	</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_webservice_rest_enabled</span></dt><dd><p>
					Whether the REST API is enabled or not. Note that this flag only impacts API Token based auth. Hence, even if the API is disabled, it can still be used from the Web UI using cookie based authentication. Default ON.
				</p></dd><dt><span class="term">$g_webservice_readonly_access_level_threshold</span></dt><dd><p>
					Minimum global access level required to access webservice for readonly operations.
				</p></dd><dt><span class="term">$g_webservice_readwrite_access_level_threshold</span></dt><dd><p>
					Minimum global access level required to access webservice for read/write operations.
				</p></dd><dt><span class="term">$g_webservice_admin_access_level_threshold</span></dt><dd><p>
					Minimum global access level required to access the administrator webservices.
				</p></dd><dt><span class="term">$g_webservice_specify_reporter_on_add_access_level_threshold</span></dt><dd><p>
					Minimum project access level required for caller to be able to specify reporter when adding issues or issue notes. Defaults to DEVELOPER.
				</p></dd><dt><span class="term">$g_webservice_priority_enum_default_when_not_found</span></dt><dd><p>
					The following enum id is used when the webservices get enum labels that are not defined in the associated MantisBT installation. In this case, the enum id is set to the value specified by the corresponding configuration option.
				</p></dd><dt><span class="term">$g_webservice_severity_enum_default_when_not_found</span></dt><dd><p>
					The following enum id is used when the webservices get enum labels that are not defined in the associated MantisBT installation. In this case, the enum id is set to the value specified by the corresponding configuration option.
				</p></dd><dt><span class="term">$g_webservice_status_enum_default_when_not_found</span></dt><dd><p>
					The following enum id is used when the webservices get enum labels that are not defined in the associated MantisBT installation. In this case, the enum id is set to the value specified by the corresponding configuration option.
				</p></dd><dt><span class="term">$g_webservice_resolution_enum_default_when_not_found</span></dt><dd><p>
					The following enum id is used when the webservices get enum labels that are not defined in the associated MantisBT installation. In this case, the enum id is set to the value specified by the corresponding configuration option.
				</p></dd><dt><span class="term">$g_webservice_projection_enum_default_when_not_found</span></dt><dd><p>
					The following enum id is used when the webservices get enum labels that are not defined in the associated MantisBT installation. In this case, the enum id is set to the value specified by the corresponding configuration option.
				</p></dd><dt><span class="term">$g_webservice_eta_enum_default_when_not_found</span></dt><dd><p>
					The following enum id is used when the webservices get enum labels that are not defined in the associated MantisBT installation. In this case, the enum id is set to the value specified by the corresponding configuration option.
				</p></dd><dt><span class="term">$g_webservice_error_when_version_not_found</span></dt><dd><p>
					If ON and the supplied version is not found, then a SoapException will be raised.
				</p></dd><dt><span class="term">$g_webservice_version_when_not_found</span></dt><dd><p>
					Default version to be used if the specified version is not found and $g_webservice_error_when_version_not_found == OFF. (at the moment this value does not depend on the project).
				</p></dd></dl></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.api.disable"></a>5.38.1. Disabling the webservice API</h3></div></div></div><p>
			If you wish to temporarily disable the webservice API it is sufficient to set the specific access thresholds to NOBODY:
		</p><p>
			<code class="literal">$g_webservice_readonly_access_level_threshold = $g_webservice_readwrite_access_level_threshold = $g_webservice_admin_access_level_threshold = NOBODY;</code>
		</p><p>
			While the SOAP API will still be accessible, it will not allow users to retrieve or modify data.
		</p></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.antispam"></a>5.39. Anti-Spam Configuration</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_antispam_max_event_count</span></dt><dd><p>
					Max number of events to allow for users with default access level (see $g_default_new_account_access_level) when signup is enabled. Use 0 for no limit. Default is 10.
				</p></dd><dt><span class="term">$g_antispam_time_window_in_seconds</span></dt><dd><p>
					Time window to enforce max events within. Default is 3600 seconds (1 hour).
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.duedate"></a>5.40. Due Date</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_due_date_update_threshold</span></dt><dd><p>
					Threshold to update due date submitted. Default is NOBODY.
				</p></dd><dt><span class="term">$g_due_date_view_threshold</span></dt><dd><p>
					Threshold to see due date. Default is NOBODY.
				</p></dd><dt><span class="term">$g_due_date_default</span></dt><dd><p>
					Default due date value for newly submitted issues. A valid <a class="ulink" href="https://php.net/manual/en/datetime.formats.relative.php" target="_top">relative date format</a> e.g. <code class="literal">today</code> or <code class="literal">+2 days</code>, or empty string for no due date set (default).
				</p></dd><dt><span class="term">$g_due_date_warning_levels</span></dt><dd><p>
					Due date warning levels. A variable number of Levels (defined as a number of seconds going backwards from the current timestamp, compared to an issue's due date) can be defined. Levels must be defined in ascending order. 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								The first entry (array key 0) defines <span class="emphasis"><em>Overdue</em></span>. Normally and by default, its value is <code class="literal">0</code>, meaning that issues will be marked overdue as soon as their due date has passed. However, it is also possible to set it to a higher value to flag overdue issues earlier, or even use a negative value to allow a "grace period" after due date.
							</p></li><li class="listitem"><p>
								Array keys 1 and 2 offer two levels of <span class="emphasis"><em>Due soon</em></span>: orange and green. By default, only the first one is set, to 7 days.
							</p></li></ul></div><p>
					 Out of the box, MantisBT allows for 3 warning levels. Additional ones may be defined, but in that case new <code class="literal">due-N</code> CSS rules (where N is the array's index) must be created otherwise the extra levels will not be highlighted in the UI.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.users"></a>5.41. User Management</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_impersonate_user_threshold</span></dt><dd><p>
					The threshold for a user to be able to impersonate another user, or NOBODY to disable impersonation. Default ADMINISTRATOR.
				</p></dd><dt><span class="term">$g_manage_user_threshold</span></dt><dd><p>
					The threshold for a user to manage user accounts. Default ADMINISTRATOR.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.view"></a>5.42. View Page Settings</h2></div></div></div><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_issue_activity_note_attachments_seconds_threshold</span></dt><dd><p>
					If a user submits a note with an attachments (with the specified # of seconds) the attachment is linked to the note. Or 0 for disabling this feature.
				</p></dd></dl></div></div><div xml:lang="en-US" class="section" lang="en-US"><div class="titlepage"><div><div><h2 class="title"><a id="admin.config.issues"></a>5.43. Issues visibility</h2></div></div></div><p>
		By default, all issues are visible to any user within a project. To limit the visibility of issues there are several mechanisms.
	</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.issues.private"></a>5.43.1. Public/Private view status</h3></div></div></div><p>
			A view status flag can be set, for an issue, to be either public or private. Private issues are accesible by the user who created it, and by those users that meet a threshold defined in <code class="literal">$g_private_bug_threshold</code>.
		</p><p>
			Refer to the following configuration options related to issue view status configurations:
		</p><div class="variablelist"><dl class="variablelist"><dt><span class="term">$g_private_bug_threshold</span></dt><dd><p>
						The threshold for a user to be able to view any private issue within a project.
					</p></dd><dt><span class="term">$g_set_view_status_threshold</span></dt><dd><p>
						The threshold for a user to be able to set an issue to Private/Public.
					</p></dd><dt><span class="term">$g_change_view_status_threshold</span></dt><dd><p>
						The threshold for a user to be able to update the view status while updating an issue.
					</p></dd></dl></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.issues.limitedview"></a>5.43.2. Limited view configuration</h3></div></div></div><p>
			The <code class="literal">$g_limit_view_unless_threshold</code> option allows the administrator to configure access limitations for users, letting them view only those issues that they are involved with, i.e. if: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						They reported the issue,
					</p></li><li class="listitem"><p>
						It is assigned to them,
					</p></li><li class="listitem"><p>
						Or they are monitoring the issue.
					</p></li></ul></div><p>

		</p><p>
			This configuration option can be set individually for each project. It defaults to ANYBODY, effectively disabling the limitation (i.e. users can see all issues).
		</p><p>
			The value for this option is an access level threshold, so that those users that meet that threshold have an unrestricted view of any issue in the project. A user that doesn't meet this threshold, will have a restricted view of only those issues in the conditions previously described.
		</p><p>
			Note that this visibility does not override other restrictions as <span class="emphasis"><em>private issues</em></span> or <span class="emphasis"><em>pivate projects</em></span> user assignments.
		</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.config.issues.limitreporters"></a>5.43.3. "Limit reporters" configuration (deprecated)</h3></div></div></div><p>
			When the option <code class="literal">$g_limit_reporters</code> is enabled, users that are reporters in a project, or lower access level, are only allowed to see the issues they reported. Issues reported by other users are not accessible by them.
		</p><p>
			This option is only supported for ALL_PROJECTS, this means that it's a global setting that affects all projects
		</p><p>
			Note that the definition of <span class="emphasis"><em>reporter</em></span> in this context is the actual access level for which a user is able to report issues, and is determined by <code class="literal">$g_report_bug_threshold</code>. Additionally, that threshold can have different values in each project. Being dependant on that threshold, the behaviour of this option is not well defined when the reporting threshold is configured as discrete values with gaps, instead of a simple threshold. In that scenario, the visibilty is determined by the minimum access level contained in the <code class="literal">$g_report_bug_threshold</code> access levels array.
		</p><div class="note"><h3 class="title">Note</h3><p>
				This option option is deprecated in favour of <code class="literal">$g_limit_view_unless_threshold</code>. The new option will be available by default on new installations, or after disabling <code class="literal">$g_limit_reporters</code> if enabled in an existing instance.
			</p></div></div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.pages"></a>Chapter 6. Page descriptions</h1></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.login"></a>6.1. Login page</h2></div></div></div><p>
			Just enter your username and password and hit the login button. There is also a Save Login checkbox to have the package remember that you are logged in between browser sessions. You will have to have cookies enabled to login.If the account doesn't exist, the account is disabled, or the password is incorrect then you will remain at the login page. An error message will be displayed.The administrator may allow users to sign up for their own accounts. If so, a link to Signup for your own account will be available.The administrator may also have anonymous login allowed. Anonymous users will be logged in under a common account.You will be allowed to select a project to work in after logging in. You can make a project your default selection from the Select Project screen or from your Account Options.SignupHere you can signup for a new account. You must supply a valid email address and select a unique username. Your randomly generated password will be emailed to your email account. If MantisBT is setup so that the email password is not to be emailed, newly generated accounts will have an empty password.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.main"></a>6.2. Main page</h2></div></div></div><p>
			This is the first page you see upon logging in. It shows you the latest news updates for the bugtracker. This is a simple news module (based off of work by Scott Roberts) and is to keep users abreast of changes in the bugtracker or project. Some news postings are specific to projects and others are global across the entire bugtracker. This is set at the time of posting in the Edit News section.The number of news posts is controlled by a global variable. When the number of posts is more than the limit, a link to show "older news" is displayed at the bottom. Similarly a "newer news" is displayed when you have clicked on "older news".There is an Archives option at the bottom of the page to view all listings.ArchivesA title/date/poster listing of ALL past news articles will be listed here. Clicking on the link will bring up the specified article. This listing will also only display items that are either global or specific to the selected project.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.filter"></a>6.3. View Issues page</h2></div></div></div><p>
			Here we can view the issue listings. The page has a set of viewing filters at the top and the issues are listed below.FiltersThe filters control the behavior of the issues list. The filters are saved between browsing sessions but do not currently save sort order or direction.If the number of issues exceeds the "Show" count in the filter a set of navigation to go to "First", "Last", "Previous", "Next" and specific page numbers are added.The Search field will look for simple keyword matches in the summary, description, steps to reproduce, additional information, issue id, or issue text id fields. It does not search through issue notes. Issue List - The issues are listed in a table and the attributes are listed in the following order: priority, id, number of issue notes, category, severity, status, last updated, and summary. Each (except for number of issue notes) can be clicked on to sort by that column. Clicking again will reverse the direction of the sort. The default is to sort by last modification time, where the last modified issue appears at the top. The issue id is a link that leads to a more detailed report about the issue. You can also add issue notes here. The number in the issue note count column will be bold if an issue note has been added in the specified time frame. The addition of an issue note will make the issue note link of the issue appear in the unvisited state. The text in the "Severity" column will be bold if the severity is major, crash, or block and the issue not resolved. The text in the "Updated" column will be bold if the issue has changed in the last "Changed(hrs)" field which is specified in the viewing filters. Each table row is color coded according to the issue status. The colors can be customised through MantisBT configuration pages (see <a class="xref" href="#admin.config" title="Chapter 5. Configuration">Chapter 5, <em>Configuration</em></a> for details). Severities block - prevents further work/progress from being made crash - crashes the application or blocking, major - major issue, minor - minor issue, tweak - needs tweaking, text - error in the text, trivial - being nit picky, feature - requesting new feature - Status new - new issue, feedback - issue requires more information from reporter, acknowledged - issue has been looked at but not confirmed or assigned, confirmed - confirmed and reproducible (typically set by an Updater or other Developer), assigned - assigned to a Developer, resolved - issue should be fixed, waiting on confirmation of fix, closed - issue is closed, Moving the mouse over the status text will show the resolution as a title. This is rendered by some browsers as a bubble and in others as a status line text.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.issueview"></a>6.4. Issue View page</h2></div></div></div><p>
			Here is the simple listing of the issue report. Most of the fields are self-explanatory. "Assigned To" will contain the developer assigned to handle the issue. Priority is fully functional but currently does nothing of importance. Duplicate ID is used when an issue is a duplicate of another. It links to the duplicate issue which allows users to read up on the original issue report. Below the issue report is a set of buttons that a user can select to work on the issue.
		</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
					Update Issue - brings up a page to edit all aspects of the issue
				</p></li><li class="listitem"><p>
					Assign to - in conjunction with the dropdown list next top the button, this is a shortcut to change the assignment of an issue
				</p></li><li class="listitem"><p>
					Change Status to - in conjunction with the dropdown list next top the button, this is a shortcut to change the status of an issue. Another page (Change Status) will be presented to allow the user to add notes or change relevant information
				</p></li><li class="listitem"><p>
					Monitor / Unmonitor Issue - allows the user to monitor any additions to the issue by email
				</p></li><li class="listitem"><p>
					Create Clone - create a copy of the current issue. This presents the user with a new issue reporting form with all of the information in the current issue filled in. Upon submission, a new issue, related to the current issue, will be created.
				</p></li><li class="listitem"><p>
					Reopen Issue - Allows the user to re-open a resolved issue
				</p></li><li class="listitem"><p>
					Move Issue - allows the user to move the issue to another project
				</p></li><li class="listitem"><p>
					Delete Issue - Allows the user to delete the issue permanently. It is recommended against deleting issues unless the entry is frivolous. Instead issues should be set to resolved and an appropriate resolution category chosen.
				</p></li></ul></div><p>
			A panel is provided to view and update the sponsorship of an issue.Another panel is provided to view, delete and add relationships for an issue. Issues can have a parent/child relationship, where the user is warned about resolving a parent issue before all of the children are resolved. A peer relationship is also possible.Below this, there may be a form for uploading file attachments. The Administrator needs to configure the bugtracker to handle file uploads. If uploading to disk is selected, each project needs to set its own upload path. Issue notes are shown at the bottom of the issue report. A panel to add issue notes is also shown.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.issuestatus"></a>6.5. Issue Change Status page</h2></div></div></div><p>
			This page is used to change the status of an issue. A user can add an issue note to describe the reason for change.In addition, the following fields may be displayed for update: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Resolution and Duplicate ID - for issues being resolved or closed
					</p></li><li class="listitem"><p>
						Issue Handler (Assigned to)
					</p></li><li class="listitem"><p>
						any Custom Fields that are to be visible on update or resolution
					</p></li><li class="listitem"><p>
						Fixed in Version - for issues being resolved
					</p></li><li class="listitem"><p>
						Close Immediately - to immediately close a resolved issue
					</p></li></ul></div><p>

		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.issueedit"></a>6.6. Issue Edit page</h2></div></div></div><p>
			The layout of this page resembles the Simple Issue View page, but here you can update various issue fields. The Reporter, Category, Severity, and Reproducibility fields are editable but shouldn't be unless there is a gross mis-categorization.Also modifiable are the Assigned To, Priority, Projection, ETA, Resolution, and Duplicate ID fields.As per version 0.18.0, the user can also add an issue note as part of an issue update.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.account"></a>6.7. My Account Page</h2></div></div></div><p>
			This page changes user alterable parameters for the system. These selections are user specific. This allows the user to change their password, username, real name and email address. It also reports the user's access levels on the current project and default access level used for public projects.
		</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.account.prefs"></a>6.7.1. Preferences</h3></div></div></div><p>
				This sets the following information: 
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							Default project
						</p></li><li class="listitem"><p>
							whether the pages used for reporting, viewing, and updating are the simple or advanced views
						</p></li><li class="listitem"><p>
							the delay in minutes between refreshes of the view all issues page
						</p></li><li class="listitem"><p>
							the delay in seconds when redirecting from a confirmation page to the display page
						</p></li><li class="listitem"><p>
							the time order in which notes will be sorted
						</p></li><li class="listitem"><p>
							whether to filter email messages based on type of message and severity
						</p></li><li class="listitem"><p>
							the number of notes to append to notification emails
						</p></li><li class="listitem"><p>
							the default language for the system. The additional setting of "auto" will use the browser's default language for the system.
						</p></li></ul></div><p>

			</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.account.profiles"></a>6.7.2. Profiles</h3></div></div></div><p>
				Profiles are shortcuts to define the values for Platform, OS, and version. This page allows you to define and edit personal shortcuts.
			</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.account.managecolumns"></a>6.7.3. Manage Columns</h3></div></div></div><p>
				Provides the ability to select the fields to be displayed in View Issues, Print Issues, CSV and Excel exports. The changes apply to the currently selected projects or All Projects for setting the defaults. It is also possible to copy such settings from/to other projects.
			</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.account.apitokens"></a>6.7.4. API Tokens</h3></div></div></div><p>
				Provides the ability to generate and revoke tokens that can be used by applications and services to access MantisBT via its APIs. This page also provides information about the creation and last used timestamps for such tokens.
			</p></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.manage"></a>6.8. System Management Pages</h2></div></div></div><p>
			A number of pages exist under the "Manage" link. These will only be visible to those who have an appropriate access level.
		</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.manage.users"></a>6.8.1. Users</h3></div></div></div><p>
				This page allow an administrator to manage the users in the system.It essentially supplies a list of users defined in the system. The user names are linked to a page where you can change the user's name, access level, and projects to which they are assigned. You can also reset their passwords through this page.At the top, there is also a list of new users (who have created an account in the last week), and accounts where the user has yet to log in.New users are created using the "Create User" link above the list of existing users. Note that the username must be unique in the system. Further, note that the user's real name (as displayed on the screen) cannot match another user's user name.
			</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.manage.projects"></a>6.8.2. Manage Projects Page</h3></div></div></div><p>
				This page allows the user to manage the projects listed in the system.Each project is listed along with a link to manage that specific project. The specific project pages allow the user to change: 
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							the project name
						</p></li><li class="listitem"><p>
							the project description
						</p></li><li class="listitem"><p>
							its status
						</p></li><li class="listitem"><p>
							whether the project is public or private. Private projects are only visible to users who are assigned to it or users who have the access level to automatically have access to private projects (eg: administrators).
						</p></li><li class="listitem"><p>
							file directory used to store attachments for issues and documents associated with the project. This folder is located on the webserver, it can be absolute path or path relative to the main MantisBT folder. Note that this is only used if the files are stored on disk.
						</p></li><li class="listitem"><p>
							common subprojects. These are other projects who can be considered a sub-project of this one. They can be shared amongst multiple projects. For example, a "documentation" project may be shared amongst several development projects.
						</p></li><li class="listitem"><p>
							project categories. These are used to sub-divide the issues stored in the system.
						</p></li><li class="listitem"><p>
							project versions. These are used to create ChangeLog reports and can be used to filter issues. They are used for both the Found In and Fixed In versions.
						</p></li><li class="listitem"><p>
							Custom Fields linked to this project
						</p></li><li class="listitem"><p>
							Users linked to this project. Here is the place where a user's access level may be upgraded or downgraded depending on their particular role in the project.
						</p></li></ul></div><p>

			</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.manage.customfields"></a>6.8.3. Manage Custom Fields</h3></div></div></div><p>
				This page is the base point for managing custom fields. It lists the custom fields defined in the system. There is also a place to enter a new field name to create a new field.
			</p><p>
				The "Edit" links take you to a page where you can define the details of a custom field. These include it's name, type, value, and display information. On the edit page, the following information is defined to control the custom field:
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						name
					</p></li><li class="listitem"><p>
						type
					</p></li><li class="listitem"><p>
						Value constraints (Possible values, default value, regular expression, minimum length, maximum length).
					</p></li><li class="listitem"><p>
						Access (who can read and write the field based on their access level).
					</p></li><li class="listitem"><p>
						Display control (where the field will show up and must be filled in
					</p></li></ul></div><p>
				All fields are compared in length to be greater than or equal to the minimum length, and less than or equal to the minimum length, unless these values are 0 in which case the check is skipped. All fields are also compared against the regular expression; if the value matches, then it is valid. For example, the expression <code class="literal">^-?([0-9])*$</code> can be used to constrain an integer.
			</p><p>
				Please refer to <a class="xref" href="#admin.customize.customfields" title="7.2. Custom Fields">Section 7.2, “Custom Fields”</a> for further details about Custom Fields and all the above-mentioned properties.
			</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.manage.profiles"></a>6.8.4. Global Profiles</h3></div></div></div><p>
				This page allows the definition of global profiles accessible to all users of the system. It is similar to the user definition of a profile consisting of Platform, OS and Version.
			</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.pages.manage.config"></a>6.8.5. Configuration</h3></div></div></div><p>
				This set of pages control the configuration of the MantisBT system. Note that the configuration items displayed may be on a project by project basis.These pages serve two purposes. First, they will display the settings for the particular aspects of the system. If authorized, they will allow a user to change the parameters. They also have settings for what access level is required to change these settings ON A PROJECT basis. In general, this should be left alone, but administrators may want to delegate some of these settings to managers.
			</p><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.pages.manage.config.thresholds"></a>6.8.5.1. Workflow Thresholds</h4></div></div></div><p>
					This page covers the adjustment of the settings for many of the workflow related parameters. For most of these, the fields are self explanatory and relate to a similarly named setting in the configuration file. At the right of each row is a selector that allows the administrator to lower the access level required to change the particular parameter.The values changeable on this page are:
				</p><p><span class="formalpara-title">Issues. </span>
						</p><div class="informaltable"><table class="informaltable" border="1"><colgroup><col /><col /><col /></colgroup><thead><tr><th>Title</th><th>Variable</th><th>Description</th></tr></thead><tbody><tr><td>Report an Issue</td><td>$g_report_bug_threshold</td><td>threshold to report an issue</td></tr><tr><td>Status to which a new issue is set</td><td>$g_bug_submit_status</td><td>status issue is set to when submitted</td></tr><tr><td>Update an Issue</td><td>$g_update_bug_threshold</td><td>threshold to update an issue</td></tr><tr><td>Allow Reporter to close an issue</td><td>$g_allow_reporter_close</td><td>allow reporter to close issues they reported</td></tr><tr><td>Monitor an issue</td><td>$g_monitor_bug_threshold</td><td>threshold to monitor an issue</td></tr><tr><td>Handle Issue</td><td>$g_handle_bug_threshold</td><td>threshold to handle (be assigned) an issue</td></tr><tr><td>Assign Issue</td><td>$g_update_bug_assign_threshold</td><td>threshold to be in the assign to list</td></tr><tr><td>Move Issue</td><td>$g_move_bug_threshold</td><td>threshold to move an issue to another project. This setting is for all projects. </td></tr><tr><td>Delete Issue</td><td>$g_delete_bug_threshold</td><td>threshold to delete an issue</td></tr><tr><td>Reopen Issue</td><td>$g_reopen_bug_threshold</td><td>threshold to reopen an issue</td></tr><tr><td>Allow reporter to reopen Issue</td><td>$g_allow_reporter_reopen</td><td>allow reporter to reopen issues they reported</td></tr><tr><td>Status to which a reopened Issue is set</td><td>$g_bug_reopen_status</td><td>status issue is set to when reopened</td></tr><tr><td>Resolution to which a reopened Issue is set</td><td>$g_bug_reopen_resolution</td><td>resolution issue is set to when reopened</td></tr><tr><td>Status where an issue is considered resolved</td><td>$g_bug_resolved_status_threshold</td><td>status where bug is resolved</td></tr><tr><td>Status where an issue becomes read-only</td><td>$g_bug_readonly_status_threshold</td><td>status where bug is read-only (see update_readonly_bug_threshold) </td></tr><tr><td>Update readonly issue</td><td>$g_update_readonly_bug_threshold</td><td>threshold to update an issue marked as read-only</td></tr><tr><td>Update Issue Status</td><td>$g_update_bug_status_threshold</td><td>threshold to update an issue's status</td></tr><tr><td>View Private Issues</td><td>$g_private_bug_threshold</td><td>threshold to view a private issue</td></tr><tr><td>Set View Status</td><td>$g_set_view_status_threshold</td><td>threshold to set an issue to Private/Public</td></tr><tr><td>Update View Status</td><td>$g_change_view_status_threshold</td><td>threshold needed to update the view status while updating an issue or an issue note </td></tr><tr><td>Show list of users monitoring issue</td><td>$g_show_monitor_list_threshold</td><td>threshold to see who is monitoring an issue</td></tr><tr><td>Add monitors to an issue</td><td>$g_monitor_add_others_bug_threshold</td><td>threshold to add users to the list of users monitoring an issue</td></tr><tr><td>Remove monitors from an issue</td><td>$g_monitor_delete_others_bug_threshold</td><td>threshold to remove users from the list of users monitoring an issue</td></tr><tr><td>Set status on assignment of handler</td><td>$g_auto_set_status_to_assigned</td><td>change status when an issue is assigned</td></tr><tr><td>Status to set auto-assigned issues to</td><td>$g_bug_assigned_status</td><td>status to use when an issue is auto-assigned</td></tr><tr><td>Limit reporter's access to their own issues (deprecated option)</td><td>$g_limit_reporters</td><td>reporters can see only issues they reported. This setting is for all projects. </td></tr><tr><td>Limit access only to those issues reported, handled, or monitored by the user</td><td>$g_limit_view_unless_threshold</td><td>threshold that, if not met, hides other users' issues. </td></tr></tbody></table></div><p>
					</p><p><span class="formalpara-title">Notes. </span>
						</p><div class="informaltable"><table class="informaltable" border="1"><colgroup><col /><col /><col /></colgroup><thead><tr><th>Title</th><th>Variable</th><th>Description</th></tr></thead><tbody><tr><td>Add Notes</td><td>$g_add_bugnote_threshold</td><td>threshold to add an issue note</td></tr><tr><td>Update Others' Notes</td><td>$g_update_bugnote_threshold</td><td>threshold at which a user can edit issue notes created by other users</td></tr><tr><td>Update Own Notes</td><td>$g_bugnote_user_edit_threshold</td><td>threshold at which a user can edit issue notes created by themselves</td></tr><tr><td>Delete Others' Notes</td><td>$g_delete_bugnote_threshold</td><td>threshold at which a user can delete issue notes created by other users</td></tr><tr><td>Delete Own Notes</td><td>$g_bugnote_user_delete_threshold</td><td>threshold at which a user can delete issue notes created by themselves</td></tr><tr><td>View private notes</td><td>$g_private_bugnote_threshold</td><td>threshold to view a private issue note</td></tr><tr><td>Change view state of own notes</td><td>$g_bugnote_user_change_view_state_threshold</td><td>threshold at which a user can change the view state of issue notes created by themselves</td></tr></tbody></table></div><p>
					</p><p><span class="formalpara-title">Others. </span>
						</p><div class="informaltable"><table class="informaltable" border="1"><colgroup><col /><col /><col /></colgroup><thead><tr><th>Title</th><th>Variable</th><th>Description</th></tr></thead><tbody><tr><td>View Change Log</td><td>$g_view_changelog_threshold</td><td>threshold to view the changelog</td></tr><tr><td>View Roadmap</td><td>$g_roadmap_view_threshold</td><td>threshold to view the roadmap</td></tr><tr><td>View Summary</td><td>$g_view_summary_threshold</td><td>threshold to view the summary</td></tr><tr><td>View Assigned To</td><td>$g_view_handler_threshold</td><td>threshold to see who is handling an issue</td></tr><tr><td>View Issue History</td><td>$g_view_history_threshold</td><td>threshold to view the issue history</td></tr><tr><td>Send Reminders</td><td>$g_bug_reminder_threshold</td><td>threshold to send a reminder</td></tr></tbody></table></div><p>
					</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.pages.manage.config.transitions"></a>*******. Workflow Transitions</h4></div></div></div><p>
					This page covers the status workflow. For most of these, the fields are self explanatory and relate to a similarly named setting in the configuration file. At the right of each row is a selector that allows the administrator to lower the access level required to change the particular parameter.The values changeable on this page are:
				</p><div class="table"><a id="idm4163"></a><div class="table-title">Table 6.1. Issues</div><div class="table-contents"><table class="table" summary="Issues" border="1"><colgroup><col /><col /><col /></colgroup><thead><tr><th>Title</th><th>Variable</th><th>Description</th></tr></thead><tbody><tr><td>Status to which a new issue is set</td><td>$g_bug_submit_status</td><td>status issue is set to when submitted</td></tr><tr><td>Status where an issue is considered resolved</td><td>$g_bug_resolved_status_threshold</td><td>status where issue is resolved</td></tr><tr><td>Status to which a reopened Issue is set</td><td>$g_bug_reopen_status</td><td>status issue is set to when reopened</td></tr></tbody></table></div></div><br class="table-break" /><p>
					The matrix that follows has checkmarks where the transitions are allowed from the status on the left edge to the status listed across the top. This corresponds to the $g_enum_workflow array.At the bottom, there is a list of access levels that are required to change the status to the value listed across the top. This can be used, for instance, to restrict those who can close an issue to a specific level, say a manager. This corresponds to the $g_set_status_threshold array and the $g_report_bug_threshold setting.
				</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.pages.manage.config.email"></a>6.8.5.3. Email Notifications</h4></div></div></div><p>
					This page sets the system defaults for sending emails on issue related events. MantisBT uses flags and a threshold system to generate emails on events. For each new event, email is sent to: 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								the reporter
							</p></li><li class="listitem"><p>
								the handler (or Assigned to)
							</p></li><li class="listitem"><p>
								anyone monitoring the issue
							</p></li><li class="listitem"><p>
								anyone who has ever added a issue note the issue
							</p></li><li class="listitem"><p>
								anyone assigned to the project whose access level matches a range
							</p></li></ul></div><p>
					 From this list, those recipients who meet the following criteria are eliminated: 
					</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								the originator of the change, if $g_email_receive_own is OFF
							</p></li><li class="listitem"><p>
								the recipient either no longer exists, or is disabled
							</p></li><li class="listitem"><p>
								the recipient has turned their email_on_&lt;new status&gt; preference OFF
							</p></li><li class="listitem"><p>
								the recipient has no email address entered
							</p></li></ul></div><p>
					 The matrix on this page selects who will receive messages for each of the events listed down the left hand side. The first four columns correspond to the first four points listed above. The next columns correspond to the access levels defined. Note that because a minimum and maximum threshold are used, a discontinuous selection is not allowed.
				</p></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.monitor"></a>6.9. Monitor Issue</h2></div></div></div><p>
			The monitor issues feature allows users to subscribe to certain issues and hence get copied on all notification emails that are sent for these issues.Depending on the configuration, sending a reminder to a user about an issue can add this issue to the user's list of monitored issues. Users who reported the issue or are assigned the issue typically don't need to monitor the issue to get the notifications. This is because by default they get notified on changes related to the issue anyway. However, administrators can change the configuration to disable notifications to reporters or handlers in specific scenarios.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.reopen"></a>6.10. Reopen Issue</h2></div></div></div><p>
			Re-open issue button is visible in the issue view pages if the user has the appropriate access level and the issue is resolved/closed. Re-opening a issue will allow users to enter issue notes for the re-opening reason. The issue will automatically be put into the Feedback status.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.delete"></a>6.11. Delete Issue</h2></div></div></div><p>
			The delete issues button appears on the issue view pages for the users who have the appropriate access level. This allows you to delete an existing issue. This should only be used on frivolous or test issues. A confirmation screen will prompt you if you really want to delete the issue. Updaters, Developers, Managers, and Administrators can remove issues (you can also configure this).
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.close"></a>6.12. Close Issue</h2></div></div></div><p>
			This is a button that appears on the issue view pages for users that are authorized to close issues. Depending on the configuration, users may be able to close issues without having to resolve them first, or may be able to only close resolved issues. After the button is clicked, the user is redirected to a page where an issue note maybe added.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.assigntome"></a>6.13. Assign to Me</h2></div></div></div><p>
			This button appears in the issue view pages in case of users with access level that is equal to handle_bug_threshold or higher. When this button is clicked the issue is assigned to the user.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.resolve"></a>6.14. Resolve Issue</h2></div></div></div><p>
			This option on the View Issues page allows you to resolve the issue. It will lead you to a page where you can set the resolution state and a duplicate id (if applicable). After choosing that the user can choose to enter an issue note detailing the reason for the closure. The issue is then set to the Resolved state. The reporter should check off on the issue by using the Close Issue button.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.pages.news"></a>6.15. News Syndication</h2></div></div></div><p>
			MantisBT supports news syndication using RSS v2.0 protocol. MantisBT also supports authenticated news feeds for private projects or installations where anonymous access is not enabled. Authenticated feeds takes a user name and a key token that are used to authenticate the user and generate the feed results in the context of the user's access rights (i.e. the same as what the user would see if they were to logged into MantisBT).To get access to the News RSS as anonymous user, visit the following page: http://www.example.com/mantisbt/news_rss.php While a user is logged in, the RSS links provided in the UI will always provide links to the authenticated feeds, if no user is logged in (i.e. anonymous), then anonymous links will be provided.
		</p></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.customize"></a>Chapter 7. Customizing MantisBT</h1></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.customize.strings"></a>7.1. Strings / Translations</h2></div></div></div><p>
			All the strings used in MantisBT including error messages, as well as those defined in plugins, can be customized or translated differently. This is achieved by overriding them in the <span class="emphasis"><em>Custom Strings File</em></span> (<code class="filename">config/custom_strings_inc.php</code>), which is automatically detected and included by MantisBT code.
		</p><p>
			Defining custom strings in this file provides a simple upgrade path, and avoids having to re-apply changes to modified core language files when upgrading MantisBT to the next release.
		</p><div class="note"><h3 class="title">Note</h3><p>
				The standard MantisBT language strings are sometimes reused in different contexts. If you are planning to override some strings to meet your specific requirements, make sure to analyze where and how they are used to avoid unexpected issues.
			</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.strings.format"></a>7.1.1. Custom Strings File Format</h3></div></div></div><p>
				This is a regular PHP script, containing variable assignments and optionally some control structures to conditionally define strings based on specific criteria (see <a class="xref" href="#admin.customize.customfields.localize" title="7.2.5. Localizing Custom Field Names">Section 7.2.5, “Localizing Custom Field Names”</a> for an example).
			</p><pre class="programlisting">
&lt;?php
$s_CODE = STRING;
$MANTIS_ERROR[ERROR_NUMBER] = STRING;
</pre><p>
				Where 
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							<span class="emphasis"><em>CODE</em></span> = language string code, as called by <code class="literal">lang_get()</code> function. Search in <code class="filename">lang/strings_english.txt</code> for existing codes.
						</p></li><li class="listitem"><p>
							<span class="emphasis"><em>ERROR_NUMBER</em></span> = error number or constant, see <code class="filename">constant_inc.php</code>.
						</p></li><li class="listitem"><p>
							<span class="emphasis"><em>STRING</em></span> = string value / translation.
						</p></li></ul></div><p>

			</p><div class="note"><h3 class="title">Note</h3><p>
					The <code class="filename">custom_strings_inc.php</code> file should only contain variable assignments and basic PHP control structures. In particular, <span class="emphasis"><em>calling MantisBT core functions in it is not recommended</em></span>, as it could lead to unexpected behavior and even errors depending on context.
				</p><p>
					If you <span class="emphasis"><em>must</em></span> use API calls, then anything that expects an active database connection needs to be protected, e.g.
				</p><pre class="programlisting">
&lt;?php
if( db_is_connected() ) {
	if( helper_get_current_project() == 1 ) {
		$s_summary = 'Title';
	}
}
</pre></div><div class="warning"><h3 class="title">Warning</h3><p>
					NEVER call <code class="literal">lang_get_current()</code> from the <code class="filename">custom_strings_inc.php</code>. Doing so will reset the active_language, causing the code to return incorrect translations if the default language is different from English. Always use the <code class="literal">$g_active_language</code> global variable instead.
				</p></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.customize.customfields"></a>7.2. Custom Fields</h2></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.overview"></a>7.2.1. Overview</h3></div></div></div><p>
				Different teams typically like to capture different information as users report issues, in some cases, the data required is even different from one project to another. Hence, MantisBT provides the ability for managers and administrators to define custom fields as way to extend MantisBT to deal with information that is specific to their teams or their projects. The aim is for this to keep MantisBT native fields to a minimum. Following are some facts about the implementation of custom fields in MantisBT:
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Custom fields are defined system wide.
					</p></li><li class="listitem"><p>
						Custom fields can be linked to multiple projects.
					</p></li><li class="listitem"><p>
						The sequence of displaying custom fields can be different per project.
					</p></li><li class="listitem"><p>
						Custom fields must be defined by users with access level ADMINISTRATOR.
					</p></li><li class="listitem"><p>
						Custom fields can be linked to projects by users with access level MANAGER or above (by default, this can be configurable).
					</p></li><li class="listitem"><p>
						Number of custom fields is not restricted.
					</p></li><li class="listitem"><p>
						Users can define filters that include custom fields.
					</p></li><li class="listitem"><p>
						Custom fields can be included in View Issues, Print Issues, and CSV exports.
					</p></li><li class="listitem"><p>
						Enumeration custom fields can have a set of static values or values that are calculated dynamically based on a custom function.
					</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.definitions"></a>7.2.2. Custom Field Definition</h3></div></div></div><p>
				The definition of a custom field includes the following logical attributes: 
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							Caption variable name. This value is supplied to the lang_get() API; it is therefore mandatory to set this to a <a class="ulink" href="https://www.php.net/manual/en/language.variables.basics.php" target="_top">valid PHP identifier</a> (i.e. only letters, numbers and underscores; no spaces) if you intend to translate the field label (see <a class="xref" href="#admin.customize.customfields.localize" title="7.2.5. Localizing Custom Field Names">Section 7.2.5, “Localizing Custom Field Names”</a>).
						</p><div class="note"><h3 class="title">Note</h3><p>
								If the specified variable is not found in the language files or in <code class="filename">custom_strings_inc.php</code>, then it will be displayed as-is.
							</p></div></li><li class="listitem"><p>
							Custom field type, can be one of:
						</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
									<code class="literal">string</code>, for strings of up to 255 characters.
								</p></li><li class="listitem"><p>
									<code class="literal">numeric</code>, for numerical integer values.
								</p></li><li class="listitem"><p>
									<code class="literal">float</code>, for real (float / double) numbers.
								</p></li><li class="listitem"><p>
									<code class="literal">email</code>, for storing email addresses.
								</p></li><li class="listitem"><p>
									<code class="literal">enumeration</code> is used when a user selects one entry from a list. The user interface for this type is a combo-box.
								</p></li><li class="listitem"><p>
									<code class="literal">checkbox</code> is like enumeration, but the options are shown as checkboxes and the user is allowed to tick more than one item.
								</p><p>
									The default value and the possible value can contain multiple values like <strong class="userinput"><code>RED|YELLOW|BLUE</code></strong>.
								</p></li><li class="listitem"><p>
									<code class="literal">radio</code> is like enumeration, but the list is shown as radio buttons and the user is only allowed to tick a single option.
								</p><p>
									The possible values can be <strong class="userinput"><code>RED|YELLOW|BLUE</code></strong>, and default <strong class="userinput"><code>YELLOW</code></strong>.
								</p><div class="note"><h3 class="title">Note</h3><p>
										The default value can't contain multiple values.
									</p></div></li><li class="listitem"><p>
									<code class="literal">list</code> is like enumeration but the list is shown as a list box where the user is only allowed to select one option.
								</p><p>
									The possible values can be <strong class="userinput"><code>RED|YELLOW|BLUE</code></strong>, and default <strong class="userinput"><code>YELLOW</code></strong>.
								</p><div class="note"><h3 class="title">Note</h3><p>
										The default value can't contain multiple values.
									</p></div></li><li class="listitem"><p>
									<code class="literal">multi-selection list</code> is like enumeration, but the list is shown as a list box where the user is allowed to select multiple options.
								</p><p>
									The possible values can be <strong class="userinput"><code>RED|YELLOW|BLUE</code></strong>, and default <strong class="userinput"><code>RED|BLUE</code></strong>.
								</p><div class="note"><h3 class="title">Note</h3><p>
										Multiple values are allowed as default.
									</p></div></li><li class="listitem"><p>
									<code class="literal">date</code>, for date values.
								</p><p>
									The default value can be <span class="emphasis"><em>empty</em></span>, a numeric <span class="emphasis"><em>UNIX timestamp</em></span>, or a date in a <a class="ulink" href="https://www.php.net/manual/en/datetime.formats.php" target="_top">valid format</a>, including relative indications such as <strong class="userinput"><code>tomorrow</code></strong>, <strong class="userinput"><code>next week</code></strong>, <strong class="userinput"><code>last month</code></strong>, <strong class="userinput"><code>+3 days</code></strong>, <strong class="userinput"><code>last day of this month</code></strong>, etc.
								</p><div class="note"><h3 class="title">Note</h3><p>
										The legacy format where the dynamic date had to be wrapped in curly brackets (e.g. <code class="literal">{tomorrow}</code>) is still supported for backwards-compatibility, but no longer necessary. This is considered a deprecated feature, that will be removed in a future released of MantisBT.
									</p></div></li></ul></div></li><li class="listitem"><p>
							Possible values for the Custom Field (e.g. <strong class="userinput"><code>RED|YELLOW|BLUE</code></strong>). Use the pipe (<code class="literal">|</code>) character to separate the enumeration's values. It is possible for one of the values to be empty (e.g. <strong class="userinput"><code>|RED|YELLOW|BLUE</code></strong>, note the leading <code class="literal">|</code>).
						</p><p>
							The set of values can also be calculated at runtime. For example, <code class="literal">=versions</code> would automatically resolve into all the versions defined for the current project. See <a class="xref" href="#admin.customize.customfields.dynamic" title="7.2.7. Dynamic values for Enumeration Custom Fields">Section 7.2.7, “Dynamic values for Enumeration Custom Fields”</a> for more information.
						</p></li><li class="listitem"><p>
							Default value - see details above for a sample default value for each type.
						</p></li><li class="listitem"><p>
							Minimum/maximum length for the custom field value (use 0 to disable). Note that these metrics are not really relevant to custom fields that are based on an enumeration of possible values.
						</p></li><li class="listitem"><p>
							Regular expression to use for validating user input (use <a class="ulink" href="https://www.php.net/manual/en/reference.pcre.pattern.syntax.php" target="_top">PCRE syntax</a>).
						</p></li><li class="listitem"><p>
							Read Access level: Minimum access level for users to be able to <span class="emphasis"><em>see</em></span> the value of the custom field.
						</p></li><li class="listitem"><p>
							Write Access level: Minimum access level for users to be able to <span class="emphasis"><em>edit</em></span> the value of the custom field.
						</p></li><li class="listitem"><p>
							Display when reporting issues? - If this custom field should be shown on the Report Issue page.
						</p></li><li class="listitem"><p>
							Display when updating issues? - If this custom field should be shown on the Update Issue page.
						</p></li><li class="listitem"><p>
							Display when resolving issues? - If this custom field should be shown when resolving an issue. For example, a "root cause" custom field would make sense to set when resolving the issue.
						</p></li><li class="listitem"><p>
							Display when closing issues? - If this custom field should be shown when closing an issue.
						</p></li><li class="listitem"><p>
							Required on Report - If this custom field is a mandatory field on the Report Issue page.
						</p></li><li class="listitem"><p>
							Required on Update - If this custom field is a mandatory field on the Update Issue page.
						</p></li><li class="listitem"><p>
							Required on Resolve - If this custom field is a mandatory field when resolving an issue.
						</p></li><li class="listitem"><p>
							Required on Close - If this custom field is a mandatory field when closing an issue.
						</p></li></ul></div><p>

			</p><p>
				If the value of a custom field for a certain defect is not found, the default value is assumed.
			</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.editing"></a>7.2.3. Adding/Editing Custom Fields</h3></div></div></div><p>
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							The logged in user needs $g_manage_custom_fields_threshold access level.
						</p></li><li class="listitem"><p>
							Select "Manage" from the main menu.
						</p></li><li class="listitem"><p>
							Select "Custom Fields" from the management menu.
						</p></li><li class="listitem"><p>
							In case of edit, click on the name of an existing custom field to edit its information.
						</p></li><li class="listitem"><p>
							In case of adding a new one, enter the name of the new custom field then click "New Custom Field".
						</p></li></ul></div><p>

			</p><div class="note"><h3 class="title">Note</h3><p>
					Added custom fields will not show up in any of the issues until the added custom field is linked to the appropriate projects.
				</p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.linking"></a>7.2.4. Linking/Unlinking/Ordering Existing Custom Fields in Projects</h3></div></div></div><p>
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							The logged in user needs to have access level that is greater than or equal to $g_custom_field_link_threshold and $g_manage_project_threshold.
						</p></li><li class="listitem"><p>
							Select "Manage" from the main menu.
						</p></li><li class="listitem"><p>
							Select "Projects".
						</p></li><li class="listitem"><p>
							Select the name of the project to manage.
						</p></li><li class="listitem"><p>
							Scroll down to the "Custom Fields" box.
						</p></li><li class="listitem"><p>
							Select the field to add from the list, then click "Add This Existing Custom Field".
						</p></li><li class="listitem"><p>
							To change the order of the custom fields, edit the "Sequence" value and click update. Custom fields with smaller values are displayed first.
						</p></li><li class="listitem"><p>
							To unlink a custom field, click on "Remove" link next to the field. Unlinking a custom field will not delete the values that are associated with the issues for this field. These values are only deleted if the custom field definition is removed (not unlinked!) from the database. This is useful if you decide to re-link the custom field. These values may also re-appear if issues are moved to another project which has this field linked.
						</p></li></ul></div><p>

			</p><p><span class="formalpara-title">Moving Issues. </span>
					When an issue is moved from one project to another, custom fields that are not defined for the new project are not deleted. These fields will re-appear with their correct values if the issue is moved back to the original project, or if these custom fields are linked to the new project.
				</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.localize"></a>7.2.5. Localizing Custom Field Names</h3></div></div></div><p>
				It is possible to localize the custom fields' labels. This can be done as follows: 
				</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>
							Define the custom field (see <a class="xref" href="#admin.customize.customfields.definitions" title="7.2.2. Custom Field Definition">Section 7.2.2, “Custom Field Definition”</a>), keeping in mind that its name must be a <a class="ulink" href="https://www.php.net/manual/en/language.variables.basics.php" target="_top">valid PHP identifier</a>.
						</p><p>
							As an example, we will use <span class="emphasis"><em>my_start_date</em></span> for a custom field of type "Date", storing the date when work on an issue was initiated.
						</p></li><li class="listitem"><p>
							Set the localization strings 
							</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
										In the MantisBT <code class="filename">config</code> directory, locate and edit <code class="filename">custom_strings_inc.php</code> (see <a class="xref" href="#admin.customize.strings" title="7.1. Strings / Translations">Section 7.1, “Strings / Translations”</a>), create it if it does not exist.
									</p></li><li class="listitem"><p>
										Localize the custom field's label <span class="emphasis"><em>my_start_date</em></span> by adding the following code 
</p><pre class="programlisting">
&lt;?php
switch( $g_active_language ) {
	case 'french':
		$s_my_start_date = 'Date de début';
		break;

	default:
		# Default language, as defined in config/config_inc.php
		# ($g_default_language, English in this case)
		$s_my_start_date = 'Start Date';
		break;
}
</pre><p>

									</p></li></ul></div><p>

						</p></li></ol></div><p>

			</p><div class="note"><h3 class="title">Note</h3><p>
					Had we decided to use <span class="emphasis"><em>start_date</em></span> as the custom field's name, then it would not have been necessary to modify <code class="filename">custom_strings_inc.php</code> (see <a class="xref" href="#admin.customize.strings" title="7.1. Strings / Translations">Section 7.1, “Strings / Translations”</a>), since MantisBT would have used the existing, already localized string from the standard language files. To check for standard strings, inspect <code class="filename">lang/strings_english.txt</code>.
				</p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.defaults"></a>7.2.6. Dynamic default values</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.customize.customfields.defaults.date"></a>*******. Dynamic defaults for Date fields</h4></div></div></div><p>
					Custom fields of type date can be defaulted to either specific or relative dates. Typically, relative dates is the scenario that makes sense in most of the cases.
				</p><p>
					The format for specific dates is an integer which indicates the number of seconds since the <a class="ulink" href="https://en.wikipedia.org/wiki/Unix_time" target="_top"> Unix Epoch</a> (January 1 1970 00:00:00 UTC), which is the format consumed by the PHP <a class="ulink" href="https://www.php.net/manual/en/function.date.php" target="_top">date()</a> method.
				</p><p>
					The relative scenario expects default values like {tomorrow}, {yesterday}, {+2 days}, {-3 days}, {next week}, etc. The curly brackets indicate that this is a logical value which is then evaluated using the PHP <a class="ulink" href="https://www.php.net/manual/en/function.strtotime.php" target="_top">strtotime()</a> function.
				</p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfields.dynamic"></a>7.2.7. Dynamic values for Enumeration Custom Fields</h3></div></div></div><p>
				As discussed earlier, one of the possible types of a custom field is "enumeration". This type of custom field allows the user to select one value from a provided list of possible values. The standard way of defining such custom fields is to provide a '|' separated list of possible values. However, this approach has two limitations: the list is static, and the maximum length of the list must be no longer than 255 characters. Hence, the need for the ability to construct the list of possible values dynamically.
			</p><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.customize.customfields.dynamic.default"></a>*******. Dynamic possible values included by default</h4></div></div></div><p>
					MantisBT ships with some dynamic possible values, these include the following:
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							<code class="literal">=categories</code> a list of categories defined in the current project (or the project to which the issue belongs).
						</p></li><li class="listitem"><p>
							<code class="literal">=versions</code> a list of all versions defined in the current project (or the project to which the issue belongs).
						</p></li><li class="listitem"><p>
							<code class="literal">=future_versions</code> a list of all versions that belong to the current project with <span class="emphasis"><em>released</em></span> flag set to false.
						</p></li><li class="listitem"><p>
							<code class="literal">=released_versions</code> a list of all versions that belong to the current project with <span class="emphasis"><em>released</em></span> flag set to true.
						</p></li></ul></div><div class="note"><h3 class="title">Note</h3><p>
						The <code class="literal">=</code> before the list of options tells MantisBT that this is a dynamic list, rather than a static one with a single option.
					</p></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a id="admin.customize.customfields.dynamic.custom"></a>7.2.7.2. Defining Custom Dynamic Possible Values</h4></div></div></div><p>
					If the user selects <code class="literal">=versions</code>, the actual custom function that is executed is <span class="emphasis"><em>custom_function_*_enum_versions()</em></span>. The reason why the "enum_" is not included is to have a fixed prefix for all custom functions used for this purpose and protect against users using custom functions that were not intended for this purpose.
				</p><p>
					For example, you would not want the user to use <span class="emphasis"><em>custom_function_*_issue_delete_notify()</em></span> which may be overridden by the web master to delete associated data in other databases.
				</p><p>
					Following is a sample custom function that is used to populate a field with the categories belonging to the currently selected project:
				</p><pre class="programlisting">
/**
 * Construct an enumeration for all categories for the current project.
 *
 * The enumeration will be empty if current project is ALL PROJECTS.
 * Enumerations format is: "abc|lmn|xyz"
 * To use this in a custom field type "=categories" in the possible values field.
 */
function custom_function_override_enum_categories() {
	$t_categories = category_get_all_rows( helper_get_current_project() );

	$t_enum = array();
	foreach( $t_categories as $t_category ) {
		$t_enum[] = $t_category['category'];
	}

	$t_possible_values = implode( '|', $t_enum );

	return $t_possible_values;
}
</pre><div class="note"><h3 class="title">Note</h3><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
								The custom function doesn't take any parameters.
							</p></li><li class="listitem"><p>
								The custom function returns the possible values in the format (A|B|C).
							</p></li><li class="listitem"><p>
								The custom function uses the current project.
							</p></li><li class="listitem"><p>
								The custom function builds on top of the already existing APIs.
							</p></li></ul></div></div><p>
					To define your own function <code class="literal">mine</code>, you will have to define it with the following signature:
				</p><pre class="programlisting">
/**
 * Use this in a custom field type "=mine" in the possible values field.
 */
function custom_function_override_enum_mine() {
	# Populate $t_enum values as appropriate here
	$t_enum = array();

	$t_possible_values = implode( '|', $t_enum );

	return $t_possible_values;
}
</pre><div class="note"><h3 class="title">Note</h3><p>
						Notice the <span class="emphasis"><em>override</em></span> in the function name. This is because this method is defined by the MantisBT administrator and not part of the MantisBT source. It is OK to override a method that doesn't exist.
					</p></div><p>
					As usual, when MantisBT is upgraded to future releases, the custom functions will not be overwritten. The difference between the "default" implementation and the "override" implementation is explained in more details in <a class="xref" href="#admin.customize.customfuncs" title="7.6. Custom Functions">Section 7.6, “Custom Functions”</a>.
				</p></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.customize.enums"></a>7.3. Enumerations</h2></div></div></div><p>
			Enumerations are used in MantisBT to represent a set of possible values for an attribute. Enumerations are used for access levels, severities, priorities, project statuses, project view state, reproducibility, resolution, ETA, and projection. MantisBT provides the administrator with the flexibility of altering the values in these enumerations. The rest of this topic explains how enumerations work, and then how they can be customised.
		</p><p><span class="formalpara-title">How do enumerations work? </span>
				<code class="filename">core/constant_inc.php</code> defines the constants that correspond to those in the enumeration. These are useful to refer to these enumerations in the configs and the code. 
</p><pre class="programlisting">
define( 'VIEWER', 10 );
define( 'REPORTER', 25 );
define( 'UPDATER',  40 );
define( 'DEVELOPER', 55 );
define( 'MANAGER', 70 );
define( 'ADMINISTRATOR', 90 );
</pre><p>

			</p><p>
			<code class="filename">config_defaults_inc.php</code> includes the defaults for the enumerations. The configuration options that are defaulted here are used in specifying which enumerations are active and should be used in MantisBT. 
</p><pre class="programlisting">
$g_access_levels_enum_string =
	'10:viewer,25:reporter,40:updater,55:developer,70:manager,90:administrator';
</pre><p>
			 </p><div class="note"><h3 class="title">Note</h3><p>
					The strings included in the enumerations here are just for documentation purposes, they are not actually shown to the user (due to the need for localisation). Hence, if an entry in this enumeration is not found in the corresponding localised string (i.e. 70:manager), then it will be printed to the user as @70@.
				</p></div><p>

		</p><p>
			The Language Files (e.g. <code class="filename">lang/strings_german.txt</code>) provide the localised strings (German in this case) for enumerations. But again, the <span class="emphasis"><em>master list</em></span> is the enumeration in the configs themselves, the ones in the language files are just used for finding the localised equivalent for an entry. Hence, if a user changes the config to have only two types of users developers and administrators, then only those will be prompted to the users even if the enumerations in the language files still includes the full list. 
</p><pre class="programlisting">
$s_access_levels_enum_string =
	'10:Betrachter,25:Reporter,40:Updater,55:Entwickler,70:Manager,90:Administrator';
</pre><p>

		</p><p><span class="formalpara-title">How can they be customised? </span>
				Let say we want to remove access level "Updater" and add access level "Senior Developer".
			</p><p>
			The file <code class="filename">config/custom_constants_inc.php</code> is supported for the exclusive purpose of allowing administrators to define their own constants while maintaining a simple upgrade path for future releases of MantisBT. Note that this file is not distributed with MantisBT and you will need to create it if you need such customisation. In our example, we need to define a constant for the new access level. 
</p><pre class="programlisting">
define( 'SENIOR_DEVELOPER', 60 );
</pre><p>

		</p><p>
			In <code class="filename">config/config_inc.php</code>
</p><pre class="programlisting">
// Remove Updater and add Senior Developer
$g_access_levels_enum_string =
	'10:viewer,25:reporter,55:developer,60:senior_developer,70:manager,90:administrator';

// Give access to Senior developers to create/delete custom field.
$g_manage_custom_fields_threshold = SENIOR_DEVELOPER;
</pre><p>

		</p><p>
			Update <code class="filename">custom_strings_inc.php</code> (see <a class="xref" href="#admin.customize.strings" title="7.1. Strings / Translations">Section 7.1, “Strings / Translations”</a>) 
</p><pre class="programlisting">
$s_access_levels_enum_string =
	'10:Betrachter,25:Reporter,40:Updater,55:Entwickler,60:Senior Developer,70:Manager,90:Administrator';
</pre><p>
			 </p><div class="note"><h3 class="title">Note</h3><p>
					We don't need to remove the <span class="emphasis"><em>Updater</em></span> entry from the localisation file if the current language is 'English'.
				</p></div><p>

		</p><p><span class="formalpara-title">Conclusion. </span>
				We have covered how enumerations work in general, and how to customise one of them. If you are interested in customising other enumerations, a good starting point would be to go to <span class="emphasis"><em>MantisBT Enum Strings</em></span> section in <code class="filename">config_defaults_inc.php</code>. This section defines all enumerations that are used by MantisBT.
			</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.customize.email"></a>7.4. Email Notifications</h2></div></div></div><p>
			See <a class="xref" href="#admin.config.email" title="5.8. Email">Section 5.8, “Email”</a> in the Configuration section.
		</p><p>
			Examples: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Notify only managers of new issues. 
</p><pre class="programlisting">
$g_notify_flags['new'] = array(
	'threshold_min' =&gt; MANAGER,
	'threshold_max' =&gt; MANAGER,
);
</pre><p>

					</p></li><li class="listitem"><p>
						Notify Developers and managers of all project events, except, exclude developers from the 'closed' events. 
</p><pre class="programlisting">
$g_default_notify_flags = array(
	'threshold_min' =&gt; DEVELOPER,
	'threshold_max' =&gt; MANAGER,
);
$g_notify_flags['closed'] = array(
	'threshold_min' =&gt; MANAGER,
	'threshold_max' =&gt; MANAGER,
);
</pre><p>

					</p></li><li class="listitem"><p>
						Exclude those who contributed issue notes from getting messages about other changes in the issue. 
</p><pre class="programlisting">
$g_default_notify_flags['bugnotes'] = OFF;
</pre><p>

					</p></li><li class="listitem"><p>
						Exclude those monitoring issues from seeing the 'closed' message 
</p><pre class="programlisting">
$g_notify_flags['closed']['monitor'] = OFF;
</pre><p>

					</p></li><li class="listitem"><p>
						Only notify developers when issue notes are added. 
</p><pre class="programlisting">
$g_notify_flags['bugnote'] = array(
	'threshold_min' =&gt; DEVELOPER,
	'threshold_max' =&gt; DEVELOPER,
);
</pre><p>

					</p></li><li class="listitem"><p>
						Notify managers of changes in sponsorship. 
</p><pre class="programlisting">
$g_notify_flags['sponsor'] = array(
	'threshold_min' =&gt; MANAGER,
	'threshold_max' =&gt; MANAGER,
);
</pre><p>

					</p></li><li class="listitem"><p>
						Notify originator and managers of changes in ownership ("Assigned To:"). 
</p><pre class="programlisting">
$g_notify_flags['owner'] = array(
	'threshold_min' =&gt; MANAGER,
	'threshold_max' =&gt; MANAGER,
	'reporter'      =&gt; ON,
);
</pre><p>

					</p></li><li class="listitem"><p>
						I'm paranoid about mail. Only send information on issues to those involved in them. Don't send mail people already know about. Also send new issue notifications to managers so they can screen them. 
</p><pre class="programlisting">
$g_email_receive_own = OFF;
$g_default_notify_flags = array(
	'reporter'      =&gt; ON,
	'handler'       =&gt; ON,
	'monitor'       =&gt; ON,
	'bugnotes'      =&gt; ON,
	'category'      =&gt; ON,
	'threshold_min' =&gt; NOBODY,
	'threshold_max' =&gt; NOBODY
);
$g_notify_flags['new'] = array(
	'threshold_min' =&gt; MANAGER,
	'threshold_max' =&gt; MANAGER,
);
</pre><p>

					</p></li><li class="listitem"><p>
						How do I send all messages to an email logger.
					</p><p>
						You will need to create a dummy user with the appropriate access level for the notices you want to log. Once this user is added to projects, they will receive mail using the appropriate rules.
					</p></li></ul></div><p>

		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.customize.status"></a>7.5. Customizing Status Values</h2></div></div></div><p>
			This section describes how to add a custom status. 
			</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>
						Define a constant to map the new status to.
					</p><p>
						In subfolder config, locate and edit file <span class="emphasis"><em>custom_constants_inc.php</em></span>; (create it if it does not exist) 
</p><pre class="programlisting">
&lt;?php
	# Custom status code
	define( 'TESTING', 60 );
</pre><p>

					</p></li><li class="listitem"><p>
						Define the new status in the enumeration, as well as the corresponding color code.
					</p><p>
						In subfolder config, edit your <span class="emphasis"><em>config_inc.php</em></span> 
</p><pre class="programlisting">
# Revised enum string with new 'testing' status
$g_status_enum_string = '10:new,20:feedback,30:acknowledged,40:confirmed,50:assigned,<span class="emphasis"><em>60:testing,</em></span>80:resolved,90:closed';

# Status color additions
$g_status_colors['<span class="emphasis"><em>testing</em></span>'] = '#ACE7AE';
</pre><p>
						 Note that the key in the $g_status_colors array must be equal to the value defined for the new status code in $g_status_enum_string.
					</p></li><li class="listitem"><p>
						Define the required translation strings for the new status, for each language used in the installation. 
						</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
									<span class="emphasis"><em>s_status_enum_string</em></span>: status codes translation (refer to the original language strings for standard values)
								</p></li><li class="listitem"><p>
									<span class="emphasis"><em>s_XXXX_bug_title</em></span>: title displayed in the change status page
								</p></li><li class="listitem"><p>
									<span class="emphasis"><em>s_XXXX_bug_button</em></span>: label for the submit button in the change status page
								</p></li><li class="listitem"><p>
									<span class="emphasis"><em>s_email_notification_title_for_status_bug_XXXX</em></span>: title for notification e-mails
								</p></li></ul></div><p>
						 where XXXX is the name of the new status as it was defined in <span class="emphasis"><em>g_status_enum_string</em></span> above. If XXXX contains spaces, they should be replaced by underscores in the language strings names (e.g. for '35:pending user', use '$s_pending_user_bug_button')
					</p><p>
						In the <code class="filename">config</code> subfolder, locate and edit <code class="filename">custom_strings_inc.php</code> (see <a class="xref" href="#admin.customize.strings" title="7.1. Strings / Translations">Section 7.1, “Strings / Translations”</a>), create it if it does not exist 
</p><pre class="programlisting">
&lt;?php
# Translation for Custom Status Code: <span class="emphasis"><em>testing</em></span>
switch( $g_active_language ) {

	case 'french':
		$s_status_enum_string = '10:nouveau,20:commentaire,30:accepté,40:confirmé,50:affecté,60:à tester,80:résolu,90:fermé';

		$s_testing_bug_title = 'Mettre le bogue en test';
		$s_testing_bug_button = 'A tester';

		$s_email_notification_title_for_status_bug_testing = 'Le bogue suivant est prêt à être TESTE.';
		break;

	default: # english
		$s_status_enum_string = '10:new,20:feedback,30:acknowledged,40:confirmed,50:assigned,60:testing,80:resolved,90:closed';

		$s_testing_bug_title = 'Mark issue Ready for Testing';
		$s_testing_bug_button = 'Ready for Testing';

		$s_email_notification_title_for_status_bug_testing = 'The following issue is ready for TESTING.';
		break;
}
</pre><p>

					</p></li><li class="listitem"><p>
						Add the new status to the workflow as required.
					</p><p>
						This can either be done from the Manage Workflow Transitions page (see <a class="xref" href="#admin.lifecycle.workflow.transitions" title="4.3.1. Workflow Transitions">Section 4.3.1, “Workflow Transitions”</a>) or by manually editing <span class="emphasis"><em>config_inc.php</em></span> as per the example below: 
</p><pre class="programlisting">
$g_status_enum_workflow[NEW_]         ='30:acknowledged,20:feedback,40:confirmed,50:assigned,80:resolved';
$g_status_enum_workflow[FEEDBACK]     ='30:acknowledged,40:confirmed,50:assigned,80:resolved';
$g_status_enum_workflow[ACKNOWLEDGED] ='40:confirmed,20:feedback,50:assigned,80:resolved';
$g_status_enum_workflow[CONFIRMED]    ='50:assigned,20:feedback,30:acknowledged,80:resolved';
$g_status_enum_workflow[ASSIGNED]     ='60:testing,20:feedback,30:acknowledged,40:confirmed,80:resolved';
$g_status_enum_workflow[TESTING]      ='80:resolved,20:feedback,50:assigned';
$g_status_enum_workflow[RESOLVED]     ='90:closed,20:feedback,50:assigned';
$g_status_enum_workflow[CLOSED]       ='20:feedback,50:assigned';
</pre><p>

					</p></li><li class="listitem"><p>
						Check and update existing workflow configurations
					</p><p>
						If you do not perform this step and have existing workflow definitions, it will not be possible to transition to and from your new status.
					</p><p>
						Go to the Workflow Transitions page (manage_config_workflow_page.php), and update the workflow as appropriate. Make sure that you have picked the correct Project in the selection list).
					</p><p>
						Hint: to identify whether you have any workflows that should be updated, open the Manage Configuration Report page (adm_config_report.php) and filter on 'All Users', [any] project and config option = 'status_enum_workflow'. All of the listed projects should be reviewed to eventually include transitions to and from the newly added states.
					</p></li></ol></div><p>

		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.customize.customfuncs"></a>7.6. Custom Functions</h2></div></div></div><p>
			Custom functions are used to extend the functionality of MantisBT by integrating user-written functions into the issue processing at strategic places. This allows the system administrator to change the functionality without touching MantisBT's core.
		</p><p>
			Default Custom Functions are defined in the API file <code class="filename">core/custom_function_api.php</code> , and are named <span class="emphasis"><em>custom_function_default_descriptive_name</em></span>, where <span class="emphasis"><em>descriptive_name</em></span> describes the particular function. See <a class="xref" href="#admin.customize.customfuncs.defined" title="7.6.1. Default Custom Functions">Section 7.6.1, “Default Custom Functions”</a> for a description of the specific functions.
		</p><p>
			User versions of these functions (overrides) are named like <span class="emphasis"><em>custom_function_override_descriptive_name</em></span>, and placed in a file called <code class="filename">custom_functions_inc.php</code> that must be saved in MantisBT's config directory. In normal processing, the system will look for override functions and execute them instead of the provided default functions.
		</p><p>
			The simplest way to create a custom function is to copy the default one from the api to your override file (<code class="filename">custom_functions_inc.php</code>), and rename it (i.e. replacing 'default' by 'override'). The specific functionality you need can then be coded into the override function.
		</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfuncs.defined"></a>7.6.1. Default Custom Functions</h3></div></div></div><p>
				Refer to <code class="filename">core/custom_functions_api.php</code> for further details.
			</p><div class="informaltable"><table class="informaltable" border="1"><colgroup><col /><col /><col /></colgroup><thead><tr><th>Custom Function Name</th><th>Description</th><th>Return value</th></tr></thead><tbody><tr><td>custom_function_default_auth_can_change_password()</td><td>Determines whether MantisBT can update the password</td><td>True if yes, False if not</td></tr><tr><td>custom_function_default_changelog_include_issue( $p_issue_id )</td><td>Determines whether the specified issue should be included in the Changelog or not.</td><td>True to include, False to exclude</td></tr><tr><td>custom_function_default_changelog_print_issue( $p_issue_id, $p_issue_level = 0 )</td><td>Prints one entry in the Changelog</td><td>None</td></tr><tr><td>custom_function_default_enum_categories()</td><td>Build a list of all categories for the current project</td><td>Enumeration, delimited by "|"</td></tr><tr><td>custom_function_default_enum_future_versions()</td><td>Build a list of all future versions for the current project</td><td>Enumeration, delimited by "|"</td></tr><tr><td>custom_function_default_enum_released_versions()</td><td>Build a list of all released versions for the current project</td><td>Enumeration, delimited by "|"</td></tr><tr><td>custom_function_default_enum_versions()</td><td>Build a list of all versions for the current project</td><td>Enumeration, delimited by "|"</td></tr><tr><td>custom_function_default_format_issue_summary( $p_issue_id, $p_context = 0 )</td><td>Format the bug summary</td><td>Formatted string</td></tr><tr><td>custom_function_default_get_columns_to_view( $p_columns_target = COLUMNS_TARGET_VIEW_PAGE, $p_user_id = null )</td><td>Defines which columns should be displayed</td><td>Array of the column names</td></tr><tr><td>custom_function_default_issue_create_notify( $p_issue_id )</td><td>Notify after an issue has been created</td><td>In case of invalid data, this function should call trigger_error()</td></tr><tr><td>custom_function_default_issue_create_validate( $p_new_issue_data )</td><td>Validate field settings before creating an issue</td><td>In case of invalid data, this function should call trigger_error()</td></tr><tr><td>custom_function_default_issue_delete_notify( $p_issue_data )</td><td>Notify after an issue has been deleted</td><td>In case of invalid data, this function should call trigger_error()</td></tr><tr><td>custom_function_default_issue_delete_validate( $p_issue_id )</td><td>Validate field settings before deleting an issue</td><td>In case of invalid data, this function should call trigger_error()</td></tr><tr><td>custom_function_default_issue_update_notify( $p_issue_id )</td><td>Notify after an issue has been updated</td><td>In case of invalid data, this function should call trigger_error()</td></tr><tr><td>custom_function_default_issue_update_validate( $p_issue_id, $p_new_issue_data, $p_bugnote_text )</td><td>Validate field issue data before updating</td><td>In case of invalid data, this function should call trigger_error()</td></tr><tr><td>custom_function_default_print_bug_view_page_custom_buttons( $p_bug_id )</td><td>Prints the custom buttons on the current view page</td><td>None</td></tr><tr><td>custom_function_default_print_column_title( $p_column, $p_columns_target = COLUMNS_TARGET_VIEW_PAGE, array $p_sort_properties = null )</td><td>Print a column's title based on its name</td><td>None</td></tr><tr><td>custom_function_default_print_column_value( $p_column, $p_bug, $p_columns_target = COLUMNS_TARGET_VIEW_PAGE )</td><td>Print a column's value based on its name</td><td>None</td></tr><tr><td>custom_function_default_roadmap_include_issue( $p_issue_id )</td><td>Determines whether the specified issue should be included in the Roadmap or not.</td><td>True to include, False to exclude</td></tr><tr><td>custom_function_default_roadmap_print_issue( $p_issue_id, $p_issue_level = 0 )</td><td>Prints one entry in the Roadmap</td><td>None</td></tr></tbody></table></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.customize.customfuncs.example"></a>7.6.2. Example Custom Function Override</h3></div></div></div><p>
				The following function is used to validate an issue before it is resolved.
			</p><pre class="programlisting" width="102">&lt;?php

/**
 * Hook to validate Validate field settings before resolving
 * verify that the resolution is not set to OPEN
 * verify that the fixed in version is set (if versions of the product exist)
 */
function custom_function_override_issue_update_validate( $p_issue_id, $p_bug_data, $p_bugnote_text ) {
	if( $p_bug_data-&gt;status == RESOLVED ) {
		if( $p_bug_data-&gt;resolution == OPEN ) {
			error_parameters( 'the resolution cannot be open to resolve the issue' );
			trigger_error( ERROR_VALIDATE_FAILURE, ERROR );
		}
		$t_version_count = count( version_get_all_rows( $p_bug_data-&gt;project_id ) );
		if( ( $t_version_count &gt; 0 ) &amp;&amp; ( $p_bug_data-&gt;fixed_in_version == '' ) ) {
			error_parameters( 'fixed in version must be set to resolve the issue' );
			trigger_error( ERROR_VALIDATE_FAILURE, ERROR );
		}
	}
}

?&gt;</pre><p>
				The errors will also need to be defined, by modifying the following files
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						<code class="filename">custom_constants_inc.php</code>
					</p><pre class="programlisting">
define( 'ERROR_VALIDATE_FAILURE', 2000 );
</pre></li><li class="listitem"><p>
						<code class="filename">custom_strings_inc.php</code> (see <a class="xref" href="#admin.customize.strings" title="7.1. Strings / Translations">Section 7.1, “Strings / Translations”</a>)
					</p><pre class="programlisting">
$MANTIS_ERROR['ERROR_VALIDATE_FAILURE'] = 'This change cannot be made because %s';
</pre></li></ul></div></div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.auth"></a>Chapter 8. Authentication</h1></div></div></div><p>
		MantisBT supports several authentication methods out of the box. In addition, there is work in progress relating to supporting authentication plug-ins. Once these are implemented, authentication against any protocol or repository of user names and passwords will be possible without having to touch MantisBT core code.
	</p><p>
		It is important to note that MantisBT does not yet support hybrid authentication scenarios. For example, internal staff authenticating against LDAP while customers authenticate against the MantisBT database with MD5 hash.
	</p><p>
		See $g_login_method in <a class="xref" href="#admin.config.auth.global" title="5.21.1. Global authentication parameters">Section 5.21.1, “Global authentication parameters”</a> for more details about how to configure MantisBT to use one of these authentication techniques.
	</p><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.auth.standard"></a>8.1. Standard Authentication</h2></div></div></div><p>
			With Standard login method, MantisBT users are authenticated against records in the MantisBT database, where the passwords are stored as a hash.
		</p><p>
			Note: while technically unlimited, the password's length is arbitrarily restricted to 1024 characters (PASSWORD_MAX_SIZE_BEFORE_HASH constant).
		</p><p>
			Values for $g_login_method: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						<span class="emphasis"><em><a class="ulink" href="https://en.wikipedia.org/wiki/MD5" target="_top">MD5</a></em></span> is the default method
					</p></li><li class="listitem"><p>
						Support for additional methods could be added in the future
					</p></li></ul></div><p>

		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.auth.ldap"></a>8.2. LDAP and Microsoft Active Directory</h2></div></div></div><p>
			Value for $g_login_method: <span class="emphasis"><em>LDAP</em></span>
		</p><p>
			Authentication is made against an <a class="ulink" href="https://en.wikipedia.org/wiki/LDAP" target="_top">LDAP</a> or <a class="ulink" href="https://en.wikipedia.org/wiki/Active_Directory" target="_top">Active Directory</a> server.
		</p><p>
			The LDAP parameters should be setup as explained in <a class="xref" href="#admin.config.auth.ldap" title="5.21.2. LDAP authentication method parameters">Section 5.21.2, “LDAP authentication method parameters”</a>.
		</p><p>
			An MD5 hash of the user's password will be stored in the database upon successful login, allowing fall-back to Standard Authentication when the LDAP server is not available.
		</p><p>
			The user's ID and password is checked against the Directory; if the credentials are valid, then the user is allowed to login and their user account in MantisBT is created automatically.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.auth.basic"></a>8.3. Basic Authentication</h2></div></div></div><p>
			Value for $g_login_method: <span class="emphasis"><em>BASIC_AUTH</em></span>
		</p><p>
			When MantisBT is configured to use basic auth, it automatically detects the logged in user and checks if they are already registered in MantisBT, if not, then a new account is automatically created for the username.
		</p><p>
			The password length is limited to the size of the underlying database field (DB_FIELD_SIZE_PASSWORD constant), currently 32 characters.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.auth.http"></a>8.4. HTTP Authentication</h2></div></div></div><p>
			Value for $g_login_method: <span class="emphasis"><em>HTTP_AUTH</em></span>
		</p><p>
			TODO
		</p><p>
			The password length is limited to the size of the underlying database field (DB_FIELD_SIZE_PASSWORD constant), currently 32 characters.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.auth.deprecated"></a>8.5. Deprecated authentication methods</h2></div></div></div><p>
			The following methods of authentication are deprecated, and supported for backwards-compatibility reasons only. It is strongly recommended to update MantisBT installations relying on these to use <a class="xref" href="#admin.auth.standard" title="8.1. Standard Authentication">Section 8.1, “Standard Authentication”</a> instead.
		</p><p>
			Deprecated values for $g_login_method: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						CRYPT
					</p></li><li class="listitem"><p>
						CRYPT_FULL_SALT
					</p></li><li class="listitem"><p>
						PLAIN
					</p></li></ul></div><p>
			 With CRYPT-based methods, the password's length is limited as per Standard Authentication. With PLAIN, its size is restricted as for Basic Authentication.
		</p></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.troubleshooting"></a>Chapter 9. Troubleshooting</h1></div></div></div><p>
		This chapter provides the Administrator with additional information related to Application Errors and common problems in MantisBT.
	</p><p>
		Useful additional reference information and support may also be found on the <a class="ulink" href="https://mantisbt.org/" target="_top">MantisBT website</a>, more specifically the <a class="ulink" href="https://mantisbt.org/forums/" target="_top">Forums</a> and the <a class="ulink" href="https://mantisbt.org/bugs/" target="_top">Bugtracker</a>.
	</p><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.troubleshooting.errors"></a>9.1. Application Errors</h2></div></div></div><p>
			Additional information about common MantisBT errors.
		</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a id="admin.troubleshooting.errors.2800"></a>9.1.1. Error 2800 - Invalid form security token</h3></div></div></div><p>
				This error may only occur when Form Validation is enabled with $g_form_security_validation = ON (see <a class="xref" href="#admin.config.webserver" title="5.4. Webserver">Section 5.4, “Webserver”</a>). There are several known cases that could trigger it: 
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							Multiple submissions of a form by clicking on the submit button several times (user error)
						</p></li><li class="listitem"><p>
							Invalid or unauthorized submission of a form, e.g. by hand-crafting the URL (CSRF attack)
						</p></li><li class="listitem"><p>
							Expired PHP session
						</p></li></ul></div><p>
				 In the first two instances, MantisBT's behavior is by design, and the response as expected. For expired sessions however, the user is impacted by system behavior, which could not only cause confusion, but also potential loss of submitted form data. What happens is driven by several php.ini configuration settings: 
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							The ratio <a class="ulink" href="https://www.php.net/session.gc-probability" target="_top"> session.gc_probability</a> divided by <a class="ulink" href="https://www.php.net/session.gc-divisor" target="_top"> session.gc_divisor</a>, which determines the probability that the garbage collection process will start when a session is initialized.
						</p></li><li class="listitem"><p>
							<a class="ulink" href="https://www.php.net/session.gc-maxlifetime" target="_top"> session.gc_maxlifetime</a> which specifies (as the name does not indicate) the <span class="emphasis"><em>minimum</em></span> validity of session data.
						</p></li></ul></div><p>
				 With PHP default values, sessions created more than 1440 seconds (24 minutes) ago have a 1% chance to be invalidated each time a new session is initialized. This explains the seemingly random occurrence of this error.
			</p><p>
				Unfortunately, this problem cannot be fixed without a major rework of the way sessions and form security are handled in MantisBT.
			</p><p>
				As a workaround, the Administrator can 
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							Increase the value of <a class="ulink" href="https://www.php.net/session.gc-maxlifetime" target="_top">session.gc_maxlifetime</a>
						</p></li><li class="listitem"><p>
							Set $g_form_security_validation = OFF. <span class="emphasis"><em>Note that for security reasons, it is strongly recommended not to do this.</em></span>
						</p></li></ul></div><p>
				 Users may also install local tools to avoid loss of form data, such as <a class="ulink" href="https://chrome.google.com/webstore/detail/typio-form-recovery/djkbihbnjhkjahbhjaadbepppbpoedaa" target="_top"> Typio Form Recovery </a> Chrome extension, or <a class="ulink" href="https://stephanmahieu.github.io/fhc-home/" target="_top"> Form History Control </a> add-on for Firefox and Chrome.
			</p><p>
				Further references and reading: 
				</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
							MantisBT issues <a class="ulink" href="https://mantisbt.org/bugs/view.php?id=12381" target="_top">12381</a>, <a class="ulink" href="https://mantisbt.org/bugs/view.php?id=12492" target="_top">12492</a>, <a class="ulink" href="https://mantisbt.org/bugs/view.php?id=13106" target="_top">13106</a>, <a class="ulink" href="https://mantisbt.org/bugs/view.php?id=13246" target="_top">13246</a>
						</p></li><li class="listitem"><p>
							<a class="ulink" href="https://mantisbt.org/forums/search.php?keywords=2800" target="_top">MantisBT forums</a>
						</p></li></ul></div><p>

			</p></div></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.project"></a>Chapter 10. Project Management</h1></div></div></div><p>
		This section covers the project management features of MantisBT. This includes features like change log, roadmap, time tracking, reporting and others.
	</p><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.project.changelog"></a>10.1. Change Log</h2></div></div></div><p>
			MantisBT doesn't just track the status of issues, it also relates issues to versions. Each project can have several versions, which are marked with attributes like released and obsolete. Users typically report issues against released issues and developers typically fix issues in not released versions. With every new release comes question like: what's new? what has been fixed? Customers wonder if the new release is of interest to them and whether they should take an upgrade. Well, the change log is specifically tailored to answer these kind of questions.
		</p><p>
			In order for an issue to show up in the change log, it has to satisfy certain criteria. The criteria is that the issue has to be resolved with a 'fixed' resolution and has to have the 'fixed_in_version' field set. Users sometimes wonder why resolved or closed issues don't show up in the change log, and the answer is that the 'fixed_in_version' field is not set. Without the 'fixed_in_version', it is not possible for MantisBT to include the issues in the appropriate section of the changelog. Note that it is possible to set the 'fixed_in_version' for multiple issues using the 'Update Fixed in Version' group action on the View Issues page (just below the issues list). This option is only available when the selected project is not 'All Projects'. Once a version is marked as obsolete, it is now longer included in the change log.
		</p><p>
			MantisBT also provides the ability to customize the criteria used for an issue to be included in the change log. For example, for installations that use a custom set of resolutions, it is possible to select multiple resolutions as valid candidates for the change log. This can be done using custom functions (see custom functions documentation for more details). The custom function below overrides the MantisBT default behavior to include issues with both FIXED and IMPLEMENTED (a custom resolution) resolutions in the change log. 
</p><pre class="programlisting">&lt;?php
# --------------------
# Checks the provided bug and determines whether it should be included in the changelog
# or not.
# returns true: to include, false: to exclude.
function custom_function_override_changelog_include_issue( $p_issue_id ) {
    $t_issue = bug_get( $p_issue_id );

    return ( ( $t_issue-&gt;resolution == FIXED || $t_issue-&gt;resolution == IMPLEMENTED ) &amp;&amp;
        ( $t_issue-&gt;status &gt;= config_get( 'bug_resolved_status_threshold' ) ) );
}
</pre><p>

		</p><p>
			MantisBT also provides the ability to customize the details to include from the issue and in what format. This can be done using the following custom function. 
</p><pre class="programlisting">
&lt;?php
# --------------------
# Prints one entry in the changelog.
function custom_function_override_changelog_print_issue( $p_issue_id, $p_issue_level = 0 ) {
    $t_bug = bug_get( $p_issue_id );

    if( $t_bug-&gt;category_id ) {
        $t_category_name = category_get_name( $t_bug-&gt;category_id );
    } else {
        $t_category_name = '';
    }

    $t_category = is_blank( $t_category_name ) ? '' : '&amp;lt;b&amp;gt;[' . $t_category_name . ']&amp;lt;/b&amp;gt; ';
    echo str_pad( '', $p_issue_level * 6, '&amp;#160;' ), '- ', string_get_bug_view_link( $p_issue_id ), ': ', $t_category, string_display_line_links( $t_bug-&gt;summary );

    if( $t_bug-&gt;handler_id != 0 ) {
        echo ' (', prepare_user_name( $t_bug-&gt;handler_id ), ')';
    }

    echo ' - ', get_enum_element( 'status', $t_bug-&gt;status ), '.&amp;lt;br /&amp;gt;';
}
</pre><p>

		</p><p>
			By combining both customization features, it is also possible to do more advanced customization scenarios. For example, users can add a 'ChangelogSummary' custom field and include all issues that have such field in the change log. Through customizing what information being included for a qualifying issue, users can also include the 'ChangelogSummary' text rather than the native summary field.
		</p><p>
			In some cases, users know that they fixed an issue and that the fix will be included in the next release, however, they don't know yet the name of the release. In such case, the recommended approach is to always have a version defined that corresponds to the next release, which is typically called 'Next Release'. Once the release is cut and has a concrete name, then 'Next Release' can be renamed to the appropriate name and a new 'Next Release' can then be created. For teams that manage releases from multiple branches for the same project, then more than one next release can be possible. For example, 'Next Dev Release' and 'Next Stable Release'.
		</p><p>
			Another common requirement is to be able to link to the change log of a specific project from the project's main website. There is a variety of ways to do that: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						To link to the changelog of version "ver1" of project "myproject": 
</p><pre class="programlisting">
http://www.example.com/mantisbt/changelog_page.php?project=myproject&amp;version=ver1
</pre><p>

					</p></li><li class="listitem"><p>
						To link to the changelog of all non-obsolete versions of project 'myproject': 
</p><pre class="programlisting">
http://www.example.com/mantisbt/changelog_page.php?project=myproject
</pre><p>

					</p></li><li class="listitem"><p>
						To link to the changelog of project with id 1. The project id can be figured out by going to the management page for the project and getting the value of project_id field form the URL. 
</p><pre class="programlisting">
http://www.example.com/mantisbt/changelog_page.php?project_id=1
</pre><p>

					</p></li><li class="listitem"><p>
						To link to the changelog of version with id 1. The version id is unique across all projects and hence in this case it is not necessary to include the project id/name. The version id can be figured out by going to the manage project page and editing the required version. The version_id will be included in the URL. 
</p><pre class="programlisting">
http://www.example.com/mantisbt/changelog_page.php?version_id=1
</pre><p>

					</p></li></ul></div><p>

		</p><p>
			Another approach is to go to the project page and from there users can get to multiple other locations relating to the project include the change log. This can be done by a URL like the following: 
</p><pre class="programlisting">
http://www.example.com/mantisbt/project_page.php?project_id=1
</pre><p>

		</p><p>
			It is possible to customize the access level required for viewing the change log page. This can be done using the $g_view_changelog_threshold configuration option.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.project.roadmap"></a>10.2. Roadmap</h2></div></div></div><p>
			One of the very important scenarios in project management is where the project managers (or team leads) triage the issues to set their priorities, target version, and possibly assign the issues to specific developers or take other actions on the issue. By setting the target version of an issue to a version that is not yet released, the issue shows up on the project roadmap, providing user with information about when to expect the issues to be resolved. The roadmap page has a section for each release showing information like planned issues, issues done and percentage of issues completed. Issues that are fixed in a specific version, but didn't have the target_version field set, will not show up in the roadmap. This allows the ability to control the issues that are significant enough to show in the roadmap, while all resolved fields can be found in the change log. Note that it is possible to set the 'target_version' for multiple issues using the 'Update Target Version' group action that is available through the View Issues page (below the issues list). This option is only available when the current project is not 'All Projects'. Although it is not a typical scenario, it is worth mentioning that once a version is marked as obsolete, it is not included in the roadmap.
		</p><p>
			Note that the roadmap only includes future versions, once a version is marked as released, it no longer is included in the roadmap. For information about such releases, the change log feature should be used. For an issue to be shown on the roadmap, it has to have the target version set. It does not matter whether the feature is resolved or not. Resolved features will be decorated with a strikethrough and will be counted as done.
		</p><p>
			MantisBT provides the ability to customize the criteria for issues to show up on the roadmap. The default criteria is that the issue has to belong to a version that is not yet released and that the issues is not a duplicate. However, such criteria can be customized by using custom functions as below. 
</p><pre class="programlisting">
&lt;?php
# --------------------
# Checks the provided bug and determines whether it should be included in the roadmap or not.
# returns true: to include, false: to exclude.
function custom_function_override_roadmap_include_issue( $p_issue_id ) {
    return ( true );
}
</pre><p>

		</p><p>
			It is also possible to customize the details included about an issues and the presentation of such details. This can be done through the following custom function: 
</p><pre class="programlisting">
&lt;?php
# --------------------
# Prints one entry in the roadmap.
function custom_function_override_roadmap_print_issue( $p_issue_id, $p_issue_level = 0 ) {
    $t_bug = bug_get( $p_issue_id );

    if( bug_is_resolved( $p_issue_id ) ) {
        $t_strike_start = '&amp;lt;strike&amp;gt;';
        $t_strike_end = '&amp;lt;/strike&amp;gt;';
    } else {
        $t_strike_start = $t_strike_end = '';
    }

    if( $t_bug-&gt;category_id ) {
        $t_category_name = category_get_name( $t_bug-&gt;category_id );
    } else {
        $t_category_name = '';
    }

    $t_category = is_blank( $t_category_name ) ? '' : '&amp;lt;b&amp;gt;[' . $t_category_name . ']&amp;lt;/b&amp;gt; ';

    echo str_pad( '', $p_issue_level * 6, '&amp;#160;' ), '- ', $t_strike_start, string_get_bug_view_link( $p_issue_id ), ': ', $t_category, string_display_line_links( $t_bug-&gt;summary );

    if( $t_bug-&gt;handler_id != 0 ) {
        echo ' (', prepare_user_name( $t_bug-&gt;handler_id ), ')';
    }

    echo ' - ', get_enum_element( 'status', $t_bug-&gt;status ), $t_strike_end, '.&amp;lt;br /&amp;gt;';
}
</pre><p>

		</p><p>
			Some teams manage different branches for each of their projects (e.g. development and maintenance branches). As part of triaging the issue, they may decide that an issue should be targeted to multiple branches. Hence, frequently the request comes up to be able to target a single issue to multiple releases. The current MantisBT approach is that an issues represents an implementation or a fix for an issue on a specific branch. Since sometimes applying and verifying a fix to the two branches does not happen at the same time and in some cases the approach for fixing an issue is different based on the branch. Hence, the way to manage such scenario is to have the main issue for the initial fix and have related issues which capture the work relating to applying the fix to other branches. The issues for porting the fix can contain any discussions relating to progress, reflect the appropriate status and can go through the standard workflow process independent of the original issues.
		</p><p>
			Another common requirement is to be able to link to the roadmap of a specific project from the project's main website. There is a variety of ways to do that: 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						To link to the roadmap of version "ver1" of project "myproject": 
</p><pre class="programlisting">
http://www.example.com/mantisbt/roadmap_page.php?project=myproject&amp;version=ver1
</pre><p>

					</p></li><li class="listitem"><p>
						To link to the roadmap of all non-obsolete versions of project 'myproject': 
</p><pre class="programlisting">
http://www.example.com/mantisbt/roadmap_page.php?project=myproject
</pre><p>

					</p></li><li class="listitem"><p>
						To link to the roadmap of project with id 1. The project id can be figured out by going to the management page for the project and getting the value of project_id field form the URL. 
</p><pre class="programlisting">
http://www.example.com/mantisbt/roadmap_page.php?project_id=1
</pre><p>

					</p></li><li class="listitem"><p>
						To link to the roadmap of version with id 1. The version id is unique across all projects and hence in this case it is not necessary to include the project id/name. The version id can be figured out by going to the manage project page and editing the required version. The version_id will be included in the URL. 
</p><pre class="programlisting">
http://www.example.com/mantisbt/roadmap_page.php?version_id=1
</pre><p>

					</p></li></ul></div><p>

		</p><p>
			Another approach is to go to the project page and from there users can get to multiple other locations relating to the project include the roadmap. This can be done by a URL like the following: 
</p><pre class="programlisting">
http://www.example.com/mantisbt/project_page.php?project_id=1
</pre><p>

		</p><p>
			The access level required to view and modify the roadmap can be configured through $g_roadmap_view_threshold and $g_roadmap_update_threshold respectively. Modifying the roadmap is the ability to set the target versions for issues. Users who have such access can set the target versions while reporting new issues or by updating existing issues.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.project.timetracking"></a>10.3. Time Tracking</h2></div></div></div><p>
			To activate the Time Tracking feature you have to set the configuration option "time_tracking_enabled" to ON. To activating the Time Tracking you can : 
			</p><div class="itemizedlist"><ul class="itemizedlist"><li class="listitem"><p>
						Static solution : change the variable '$g_time_tracking_enabled' in the configuration file 'config_defaults_inc.php', this will change the configuration for all the MantisBT instance ;
					</p></li><li class="listitem"><p>
						Dynamic and "project by project" solution : Use the administration page "Manage Configuration" and set the variable 'time_tracking_enabled' to '1' for which user and which project of you choice.
					</p></li></ul></div><p>

		</p><p>
			All Time Tracking configuration options are described in the configuration section off this guide.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.project.graphs"></a>10.4. Graphs</h2></div></div></div><p>
			Assigned to me: TODO
		</p><p>
			Release Delta: TODO
		</p><p>
			Category: TODO
		</p><p>
			Severity: TODO
		</p><p>
			Severity / Status: TODO
		</p><p>
			Daily Delta: TODO
		</p><p>
			Reported by Me: TODO
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.project.summary"></a>10.5. Summary Page</h2></div></div></div><p>
			By Status: TODO
		</p><p>
			By Severity: TODO
		</p><p>
			By Category: TODO
		</p><p>
			Time Stats for Resolved Issues (days): TODO
		</p><p>
			Developer Status: TODO
		</p><p>
			Reporter by Resolution: TODO
		</p><p>
			Developer by Resolution: TODO
		</p><p>
			By Date: TODO
		</p><p>
			Most Active: TODO
		</p><p>
			Longest Open: TODO
		</p><p>
			By Resolution: TODO
		</p><p>
			By Priority: TODO
		</p><p>
			Reporter Status: TODO
		</p><p>
			Reporter Effectiveness: TODO
		</p></div></div><div xml:lang="en-US" class="chapter" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="admin.contributing"></a>Chapter 11. Contributing to MantisBT</h1></div></div></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.contributing.develop"></a>11.1. Talent and Time</h2></div></div></div><p>
			One of the greatest ways to contribute to MantisBT is to contribute your talent and time. For MantisBT to keep growing we need such support in all areas related to the software development cycle. This includes: business analysts, developers, web designers, graphics designers, technical writers, globalization developers, translators, testers, super users, packagers and active users. If you would like to contribute in any of these capacities please contact us through the "Contact Us" page.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.contributing.share"></a>11.2. Recommend MantisBT to Others</h2></div></div></div><p>
			It feels great when we get feedback from the user community about how MantisBT boosted their productivity, and benefited their organization. A lot of the feedback I get is via email, some on mailing lists, and some on forums. I would encourage such users to blog about it, tell their friends about MantisBT, and recommend MantisBT to other organizations. MantisBT is driven by it's community, the greater the community, the greater the ideas, the greater of a product it becomes.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.contributing.blog"></a>11.3. Blog about MantisBT</h2></div></div></div><p>
			If you have a blog, then talk about MantisBT, review it's features and help us spread the word. A lot of users also like to blog about how they customized MantisBT to fit their needs or to integrate with other tools that they use in their work environment.
		</p></div><div class="section"><div class="titlepage"><div><div><h2 class="title"><a id="admin.contributing.integrate"></a>11.4. Integrate with MantisBT</h2></div></div></div><p>
			If you have a product that can be integrated with MantisBT to provide value for MantisBT users, that would be a great place to contribute and benefit both your project's and the MantisBT community.
		</p><p>
			A great example in this area are integrations with content management systems (e.g. *Nuke, Xoops), project management (PHPProjekt), and TestLink for Test Management. MantisBT can easily be integrated with projects in any programming language whether it is hosted on the same webserver or anywhere else in the world. This can be achieved through its SOAP API and MantisConnect client libraries. MantisConnect comes with client libraries and samples in languages like PHP, .NET, Java and Cocoa.
		</p></div></div><div xml:lang="en-US" class="appendix" lang="en-US"><div class="titlepage"><div><div><h1 class="title"><a id="appe-Admin_Guide-Revision_History"></a>Appendix A. Revision History</h1></div></div></div><p>
		<div class="revhistory"><table summary="Revision History"><tr><th align="left" valign="top" colspan="3"><strong>Revision History</strong></th></tr><tr><td align="left">Revision 2.26-0</td><td align="left">Sun Oct 15 2023</td><td align="left"><span class="author"><span class="firstname">Damien</span> <span class="surname">Regad</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.26.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.25-0</td><td align="left">Mon Mar 8 2021</td><td align="left"><span class="author"><span class="firstname">Damien</span> <span class="surname">Regad</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.25.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.24-1</td><td align="left">Sun May 3 2020</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.24.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.24-0</td><td align="left">Sun Mar 15 2020</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.24.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.23-0</td><td align="left">Sun Dec 9 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.23.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.22-1</td><td align="left">Thu Sep 26 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.22.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.22-0</td><td align="left">Sun Aug 25 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.22.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.21-2</td><td align="left">Mon Aug 19 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.21.2</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.21-1</td><td align="left">Thu Jun 13 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.21.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.21-0</td><td align="left">Sat Apr 20 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.21.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.20-0</td><td align="left">Sat Mar 16 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.20.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.19-0</td><td align="left">Wed Jan 2 2019</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.19.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.18-0</td><td align="left">Tue Oct 16 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.18.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.17-1</td><td align="left">Mon Sep 24 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.17.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.17-0</td><td align="left">Mon Sep 3 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.17.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.16-0</td><td align="left">Sun Jul 29 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.16.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.15-0</td><td align="left">Tue Jun 5 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.15.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.14-0</td><td align="left">Sun Apr 29 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.14.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.13-1</td><td align="left">Wed Apr 4 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.13.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.13-0</td><td align="left">Sun Apr 1 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.13.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.12-0</td><td align="left">Sat Mar 3 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.12.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.11-0</td><td align="left">Tue Feb 6 2018</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.11.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.10-0</td><td align="left">Sat Dec 30 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.10.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.9-0</td><td align="left">Sun Dec 3 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.9.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.8-0</td><td align="left">Sat Oct 28 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.8.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.7-0</td><td align="left">Sun Oct 8 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.7.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.6-0</td><td align="left">Sun Sep 3 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.6.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.5-1</td><td align="left">Sat Jun 17 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.5.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.5-0</td><td align="left">Sun Jun 4 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.5.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.4-1</td><td align="left">Sat May 20 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.4.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.4-0</td><td align="left">Sun Apr 30 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.4.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.3-3</td><td align="left">Sun Apr 30 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.3.2</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.3-2</td><td align="left">Sun Apr 17 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.3.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.3-1</td><td align="left">Fri Mar 31 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.3.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.2-3</td><td align="left">Wed Mar 22 2017</td><td align="left"><span class="author"><span class="firstname">Damien</span> <span class="surname">Regad</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.2.2</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.2-2</td><td align="left">Sun Mar 12 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.2.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.2-1</td><td align="left">Sun Feb 26 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.2.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.1-2</td><td align="left">Sun Feb 26 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.1.1</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.1-1</td><td align="left">Tue Jan 31 2017</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.1.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.0-2</td><td align="left">Fri Dec 30 2016</td><td align="left"><span class="author"><span class="firstname">Victor</span> <span class="surname">Boctor</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.0.0</td></tr></table>

				</td></tr><tr><td align="left">Revision 2.0-1</td><td align="left">Sat Nov 26 2016</td><td align="left"><span class="author"><span class="firstname">Damien</span> <span class="surname">Regad</span></span></td></tr><tr><td align="left" colspan="3">
					<table border="0" summary="Simple list" class="simplelist"><tr><td>Release 2.0.0-rc.2</td></tr></table>

				</td></tr></table></div>

	</p></div></div></body></html>