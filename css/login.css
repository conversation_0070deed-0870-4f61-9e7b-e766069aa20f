#secure-session-label span.input {
	width: 57%;
}
#secure-session-label span.input input {
	margin-right: .25em;
}
span#session-msg {
	padding-top: .25em;
	line-height: 1em;
	font-size: .8em;
}
#captcha-field {
	float: left;
}
div.captcha-image {
	position: relative;
	display: block;
	float: left;
	margin: 0 0 0 1em;
	padding: 0;
}
#login-links,
#captcha-refresh {
	list-style: none;
	margin: 0;
	padding: 0;
}
#login-links {
	position: absolute;
	top: .5em;
	right: 0;
}
#login-links li,
#captcha-refresh li {
	float: left;
	padding: 0 .25em;
}
#login-links li:before,
#captcha-refresh li:before {
	content: '[';
}
#login-links li:after,
#captcha-refresh li:after {
	content: ']';
}
#login-links li a,
#captcha-refresh li a {
	padding: 0 .35em;
}

#captcha-image {
	cursor: pointer;
	padding-right:3px;
}

@keyframes rotating {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}
.captcha_loading_image {
	animation: rotating 2s linear infinite;
}
