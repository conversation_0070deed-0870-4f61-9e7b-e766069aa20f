@media print {
	#view-issue-page .bugnote .btn { display: none; }
	#view-issue-page .bugnote .btn-sm { display: none; }
	.noprint { display: none; }
}

span.print { font-size: 8pt; }
span.required 		{ font-size: 8pt; color: #bb0000; }

td.category, th.category,
td.category label, th.category label
{
    background-color: #edf3f4 !important;
    color: #000000;
    font-weight: bold;
    vertical-align : top;
}
td.category .small, th.category .small	{ font-weight: normal;  }

.small-caption	{
    border: 1px solid #fff;
    font-size: 8pt;
    padding: 0 5px;
}
tr.spacer			{ background-color: #ffffff !important; color: #000000; height: 5px; }


/**
 * view_all_bug_page.php
 */
#buglist td, #buglist th	{ text-align: center; }
#buglist .column-summary,
#buglist .column-description,
#buglist .column-notes,
#buglist .column-steps-to-reproduce,
#buglist .column-additional-information,
#buglist .cftype-string,
#buglist .cftype-textarea
	{ text-align: left; }

.sticky-separator,
.test-langs th
	{ background-color: lightgrey; }

/* manage_plugin_page.php */
span.dependency_dated		{ color: maroon; }
span.dependency_met			{ color: green; }
span.dependency_unmet		{ color: red; }
span.dependency_upgrade		{ color: orange; }

tr.bugnote .bugnote-note { background-color: #e8e8e8; color: #000000; width: 75%; vertical-align: top; }

.bugnote-private { background-color: #fcf8e3 !important;}

.nowrap
{
	white-space: nowrap;
}

.issue-status
{
	border-bottom: 1px dotted black;
}

.bug-attachments {
	margin: 0;
	padding: 0;
}

.bug-attachments > ul {
	list-style: none;
	margin: 0.5em;
	padding: 0;
}

.bug-attachments > ul > li {
	margin: 0.5em 0;
	padding: 0;
}

.bug-attachments > ul > li > div {
	margin: 0;
	padding: 0;
}

.bug-attachment-preview-text, .bug-attachment-preview-image {
	margin: 1em;
	padding: 1em;
}

.bug-attachment-preview-image img {
	max-width: 100%;
}

.bug-attachment-preview-text {
	white-space: pre-wrap;
}

#dates label[for=start_date], #dates > label[for=end_date] {
	float: left;
	width: 3em;
}

/* manage_config_* colors */
.color-global		{ background-color: LightBlue; }
.color-project		{ background-color: LightGreen; }

td.due-0, td.overdue { background-color: red; color: #ffffff; font-weight: bold; }
td.due-1             { background-color: darkorange; color: #ffffff; font-weight: bold; }
td.due-2             { background-color: green; color: #ffffff; font-weight: bold; }
td.print-overdue     { font-weight: bold; }

.collapse-link { cursor: pointer; }

.table-nonfluid {
   width: auto !important;
}

.login-logo {
	text-align: center;
}

/* strike-through resolved issues: #22492 */
.resolved  {     
    text-decoration: line-through;
}

.error-details .code {
	font-family: monospace;
}

table.filters td.category {
	color: #337ab7;
}

.listjs-table .sort:after {
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid transparent;
  content: "";
  position: relative;
  top: -10px;
  right: -5px;
}

.listjs-table .sort.desc:after {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #707070;
  content: "";
  position: relative;
  top: 4px;
  right: -5px;
}

.listjs-table .sort.asc:after {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid #707070;
  content: "";
  position: relative;
  top: -4px;
  right: -5px;
}

.listjs-table .sort:hover {
	text-decoration: underline;
}

.test-langs ol.plugins-toc {
    column-count: auto;
    column-width: 130px;
    column-gap: 60px;
    margin: 10px 15px 10px 30px;
}

.editable_access_level {
    display: inline-grid;
    grid-template-columns: 1fr 20px 1fr;
}
